# 风控配置自动生成Dify工作流Prompt工程指南

## 1. Prompt设计原则

### 1.1 核心原则
- **精确性**: 使用明确的指令和格式要求
- **一致性**: 保持输出格式的统一性
- **完整性**: 确保覆盖所有必要的风控组件
- **可复用性**: 优先识别和复用已存在的组件

### 1.2 结构化输出
所有LLM节点都要求严格的JSON格式输出，避免额外的文字说明。

## 2. 策略解析节点Prompt

### 2.1 System Prompt
```
你是一个风控策略专家，擅长分析策略文档并提取检测规则。请严格按照JSON格式输出，不要添加任何额外的文字说明。
```

### 2.2 User Prompt模板
```
请分析以下策略文档，提取出所有的检测规则和条件。

策略文档：
{{#document_preprocessor.cleaned_document#}}

事件信息：
- 名称：{{#document_preprocessor.event_info.name#}}
- 代码：{{#document_preprocessor.event_info.code#}}

请按以下JSON格式输出解析结果：
{
  "detection_modules": [
    {
      "type": "设备检测|IP检测|名单检测|账号检测|业务检测|行为检测",
      "name": "检测模块名称",
      "rules": ["规则1", "规则2"]
    }
  ],
  "accumulation_conditions": [
    {
      "rule_number": 1,
      "description": "规则描述",
      "dimension": "DeviceId|ClientIp|UserId",
      "operator": ">|>=|<|<=|==",
      "threshold": 数值,
      "time_window": 时间窗口分钟数,
      "action": "REJECT|REVIEW|PASS",
      "inner_reason": "内部原因",
      "outer_reason": "外部话术"
    }
  ],
  "attribute_conditions": [
    {
      "attribute_name": "vipLevel|nobilityLevel|planetUserLevel",
      "operator": ">|>=|<|<=|==",
      "value": 数值,
      "description": "条件描述"
    }
  ],
  "time_windows": [1440, 60, 10080],
  "user_level_conditions": {
    "vip_level": {"operator": ">=", "value": 25},
    "nobility_level": {"operator": ">=", "value": 33},
    "planet_user_level": {"operator": ">=", "value": 36}
  }
}
```

### 2.3 关键识别模式
- **时间窗口识别**: "1天内"→1440分钟, "1小时内"→60分钟, "10天"→14400分钟
- **维度识别**: "同设备"→DeviceId, "同IP"→ClientIp, "同用户"→UserId
- **阈值识别**: "人数>3"→threshold:3, operator:">"
- **动作识别**: "REJECT"、"REVIEW"、"PASS"

## 3. 组件识别节点Prompt

### 3.1 System Prompt
```
你是风控系统架构专家，擅长将策略需求转换为具体的系统组件配置。请严格按照JSON格式输出。
```

### 3.2 User Prompt模板
```
基于以下策略解析结果，识别需要创建的风控组件。

解析结果：
{{#strategy_parser.strategy_analysis#}}

事件信息：
{{#document_preprocessor.event_info#}}

风控系统组件说明：
1. 累计因子(Factor): 用于统计计算，如"24小时内同设备用户数"
   - group_key: 统计维度，如"Event,DeviceId,type"
   - agg_key: 聚合键，通常是"UserId"
   - function: 统计函数，如"COUNT_DISTINCT"
   - time_span: 时间窗口（分钟）
   - condition: 过滤条件，如"#Event=='event-code' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')"

2. 内部属性(LOCAL Attribute): 基于因子计算
   - dependent: 格式为"因子ID::::维度1::::维度2"
   - function: "getFactorValue"

3. 外部属性(REMOTE Attribute): 调用外部服务
   - dependent: 外部服务参数，如"UserId::::cache+="
   - function: 服务函数名，如"userData"

请输出需要创建的组件清单：
{组件配置JSON格式...}
```

### 3.3 组件映射规则
- **设备检测** → 需要DeviceId维度的因子和属性
- **IP检测** → 需要ClientIp维度的因子和属性
- **用户等级检测** → 需要userData远程属性
- **累计条件** → 每个条件对应一个因子和一个本地属性
- **时间窗口** → 直接映射到因子的time_span字段

## 4. 配置生成节点Prompt

### 4.1 System Prompt
```
你是风控系统配置专家，负责生成完整的风控组件配置。请严格按照数据库表结构生成配置，优先复用已存在的组件。
```

### 4.2 User Prompt模板
```
基于以下信息生成最终的风控组件配置：

需要创建的组件：
{{#component_identifier.component_list#}}

已存在可复用的组件：
- 事件：{{#database_query.existing_event#}}
- 因子：{{#database_query.existing_factors#}}
- 属性：{{#database_query.existing_attributes#}}

复用建议：
{{#database_query.reuse_suggestions#}}

事件信息：
{{#document_preprocessor.event_info#}}

风控系统数据模型参考：
- risk_event: name, code, business_code, rule_group_id, reply, comment, default_level, can_punish, types
- risk_factor: group_key, agg_key, _function, time_span, _condition, comment, business, window_type
- risk_attribute: name, type(LOCAL/REMOTE), dependent, _function, result_key
- risk_rule_atom: name, _condition, dependent, reply, inner_reason, outer_reason, priority, accuracy, status
- risk_rule_group: name, _condition, dependent, status, comment, priority

请生成完整的配置，优先复用已存在的组件：
{最终配置JSON格式...}
```

### 4.3 复用决策逻辑
- **事件复用**: 如果存在相同code的事件，标记为reuse
- **因子复用**: 如果存在相似时间窗口和维度的因子，标记为reuse
- **属性复用**: 如果存在相同name的属性，标记为reuse
- **新建组件**: 不存在可复用组件时，标记为create

## 5. Prompt优化技巧

### 5.1 提高准确性
1. **使用具体示例**: 在prompt中提供具体的输入输出示例
2. **明确约束条件**: 指定字段的取值范围和格式要求
3. **分步骤指导**: 将复杂任务分解为多个步骤
4. **错误处理**: 提供常见错误的处理指导

### 5.2 提高一致性
1. **标准化术语**: 统一使用风控领域的标准术语
2. **固定格式**: 严格要求JSON输出格式
3. **验证机制**: 在后续节点中验证输出格式
4. **模板复用**: 使用统一的prompt模板

### 5.3 提高效率
1. **上下文优化**: 只传递必要的上下文信息
2. **批量处理**: 在单个prompt中处理多个相关任务
3. **缓存机制**: 复用相似查询的结果
4. **并行处理**: 独立任务可以并行执行

## 6. 常见问题和解决方案

### 6.1 输出格式问题
**问题**: LLM输出包含额外的文字说明
**解决**: 在system prompt中强调"严格按照JSON格式输出，不要添加任何额外的文字说明"

### 6.2 字段映射错误
**问题**: 策略条件映射到错误的数据库字段
**解决**: 在prompt中提供详细的字段映射表和示例

### 6.3 复用逻辑不准确
**问题**: 无法正确识别可复用的组件
**解决**: 改进相似度匹配算法，提供更详细的复用判断标准

### 6.4 时间窗口转换错误
**问题**: 时间单位转换不正确
**解决**: 在prompt中明确时间单位转换规则，提供转换表

## 7. 测试和验证

### 7.1 单元测试
- 测试每个LLM节点的输出格式
- 验证关键字段的提取准确性
- 检查边界条件的处理

### 7.2 集成测试
- 测试完整工作流的端到端功能
- 验证组件间的数据传递
- 检查最终配置的完整性

### 7.3 回归测试
- 使用历史策略文档进行回归测试
- 对比自动生成和手动配置的差异
- 持续优化prompt和逻辑

## 8. 持续改进

### 8.1 反馈收集
- 收集用户对生成配置的反馈
- 记录常见的错误模式
- 分析失败案例的根本原因

### 8.2 Prompt迭代
- 基于反馈优化prompt内容
- 增加新的识别模式和规则
- 改进错误处理机制

### 8.3 知识库更新
- 维护风控组件的知识库
- 更新数据库表结构变化
- 同步业务规则的变更

通过系统化的Prompt工程，可以确保Dify工作流生成高质量、一致性的风控配置，大大提高策略配置的效率和准确性。
