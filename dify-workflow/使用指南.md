# 风控配置自动生成Dify工作流使用指南

## 1. 概述

本工作流基于Dify平台，使用LLM自动解析风控策略文档，生成完整的风控系统配置，包括事件、累计因子、内部外部属性、策略原子、策略组等组件。

## 2. 工作流部署

### 2.1 导入工作流
1. 登录Dify平台
2. 进入工作流管理页面
3. 点击"导入工作流"
4. 上传 `risk-config-generator-workflow.json` 文件
5. 确认导入成功

### 2.2 配置数据库连接
1. 在Dify平台中配置数据库连接信息
2. 修改 `database_query` 节点中的数据库配置
3. 确保数据库连接正常

### 2.3 配置LLM模型
1. 确保已配置GPT-4或Claude-3.5-Sonnet模型
2. 检查API密钥是否正确
3. 测试模型连接

## 3. 输入格式

### 3.1 必填参数
- **strategy_document**: 策略文档（字符串）
- **event_name**: 事件名称（如：会员中心-抽奖）
- **event_code**: 事件代码（如：member-center-lottery）

### 3.2 可选参数
- **business_code**: 业务代码（默认：activity）
- **access_by**: 接入人姓名

### 3.3 策略文档格式示例

```
接入说明：
风控接入说明
活动策略：场景分级3级。

设备检测：设备模型
1、（活动各环节共累积阈值），活动全程，同设备成功参与该活动（请求返回PASS\REVIEW）人数>3，REJECT。
对内外话术：同设备参与该活动的账号较多，请更换新设备尝试。

2、设备命中异常标签：疑似伪造设备（MVP/比心vip >= 25 或 爵位>= 33 或 星爵>= 36级降级；其余返回REJECT），注销频度异常关联设备，低安装包版本&的操作系统设备，低设备内存且新生成设备指纹、低设备内存且剩余内存异常，REVIEW。

IP检测：
3、同IP一天内参与该活动人数>8，REJECT。
对内外话术：同IP参与该活动的账号较多，请更换网络环境尝试。

名单检测：
4、命中黑名单，REJECT。

账号检测：
5、用户等级检测：MVP/比心vip < 12 且 爵位 < 3，进行严格检测。

一、接入事件：
名称：会员中心-抽奖      code：member-center-lottery
参数文档：https://risk.yupaopao.com/index.html#/home/<USER>
结果处理：
返回 PASS：正常
返回 REVIEW：同PASS
返回 REJECT：获得低价值礼物
```

## 4. 输出格式

### 4.1 主要输出
工作流将输出完整的风控配置JSON，包含以下部分：

```json
{
  "event": {
    "action": "create|reuse",
    "reuse_id": "复用的ID(如果是复用)",
    "data": {
      "name": "会员中心-抽奖",
      "code": "member-center-lottery",
      "business_code": "activity",
      "access_by": "张三",
      "default_level": "PASS",
      "can_punish": true,
      "types": "1",
      "comment": "自动生成的事件配置"
    }
  },
  "factors": [
    {
      "action": "create|reuse",
      "reuse_id": "复用的ID(如果是复用)",
      "data": {
        "group_key": "Event,DeviceId,type",
        "agg_key": "UserId",
        "_function": "COUNT_DISTINCT",
        "time_span": 1440,
        "_condition": "#Event=='member-center-lottery' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')",
        "comment": "24小时内同设备参与用户数",
        "business": 1,
        "window_type": 1
      }
    }
  ],
  "attributes": [
    {
      "action": "create|reuse",
      "reuse_id": "复用的ID(如果是复用)",
      "data": {
        "name": "deviceUserCount24h",
        "type": "LOCAL",
        "dependent": "\"1\"::::Event::::DeviceId::::type",
        "_function": "getFactorValue",
        "result_key": ""
      }
    }
  ],
  "atom_rules": [
    {
      "action": "create",
      "data": {
        "name": "会员中心-抽奖-设备聚集检测",
        "_condition": "userData != null && userData.vipLevel < 12 && deviceUserCount24h > 3",
        "dependent": "userData,deviceUserCount24h",
        "reply": "您的设备存在异常，建议更换设备进行尝试",
        "inner_reason": "设备聚集行为检测",
        "outer_reason": "您的设备存在异常，建议更换设备进行尝试",
        "priority": 0,
        "accuracy": 100,
        "status": "DISABLE",
        "can_fuse": true,
        "type": 0
      }
    }
  ],
  "group_rule": {
    "action": "create",
    "data": {
      "name": "会员中心-抽奖-检测",
      "_condition": "",
      "dependent": "",
      "status": "DISABLE",
      "comment": "自动生成的策略组",
      "priority": 0
    }
  }
}
```

### 4.2 验证结果
```json
{
  "validation_result": {
    "valid": true,
    "errors": [],
    "warnings": ["建议信息"],
    "suggestions": ["优化建议"]
  }
}
```

## 5. 后续处理

### 5.1 SQL脚本生成
使用 `sql-generator.py` 将配置转换为SQL脚本：

```python
from sql_generator import RiskConfigSQLGenerator

generator = RiskConfigSQLGenerator("your-name")
sql_script = generator.generate_complete_sql_script(config)

# 保存到文件
with open('risk_config.sql', 'w', encoding='utf-8') as f:
    f.write(sql_script)
```

### 5.2 数据库执行
1. 在测试环境执行生成的SQL脚本
2. 验证数据插入是否正确
3. 手动关联策略原子和策略组
4. 更新事件的rule_group_id字段

### 5.3 配置验证
1. 检查因子统计是否正常
2. 验证属性计算是否正确
3. 测试策略原子逻辑
4. 确认策略组配置

## 6. 最佳实践

### 6.1 策略文档编写
1. 使用标准化的格式和术语
2. 明确指定检测维度（设备、IP、用户）
3. 清晰描述阈值条件和时间窗口
4. 提供详细的内外部话术

### 6.2 配置优化
1. 优先复用已存在的组件
2. 合理设置时间窗口避免性能问题
3. 策略原子初始状态设为DISABLE
4. 逐步启用和调优策略

### 6.3 监控和维护
1. 监控因子计算性能
2. 定期检查策略命中率
3. 根据业务变化调整配置
4. 保持文档和配置同步

## 7. 故障排除

### 7.1 常见问题
1. **LLM解析错误**: 检查策略文档格式，确保关键信息清晰
2. **数据库连接失败**: 验证数据库配置和网络连接
3. **SQL执行错误**: 检查生成的SQL语法和数据完整性
4. **配置验证失败**: 根据错误信息修正配置参数

### 7.2 调试方法
1. 查看工作流执行日志
2. 逐个节点检查输出结果
3. 使用测试数据验证功能
4. 对比手动配置和自动生成的差异

## 8. 扩展功能

### 8.1 自定义检测类型
可以扩展工作流支持更多检测类型：
- 地理位置检测
- 时间模式检测
- 社交关系检测
- 设备指纹检测

### 8.2 集成其他系统
- 与JIRA集成自动创建工单
- 与监控系统集成告警通知
- 与CI/CD集成自动部署
- 与文档系统集成配置记录

通过本工作流，可以大大提高风控策略配置的效率和准确性，减少人工配置错误，实现策略文档到系统配置的自动化转换。
