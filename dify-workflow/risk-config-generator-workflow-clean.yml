app:
  description: 风控配置自动生成工作流，基于策略文档自动生成完整的风控系统配置
  icon: 🛡️
  icon_background: '#FFEAD5'
  mode: workflow
  name: 风控配置自动生成器

kind: app
version: 0.1.1

workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: code
      id: start-1735721234567
      source: start
      sourceHandle: source
      target: code-1735721234568
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: code
        targetType: llm
      id: code-1735721234568-llm-1735721234568-5
      source: code-1735721234568
      sourceHandle: source
      target: llm-1735721234568-5
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: llm-1735721234568-5-llm-1735721234569
      source: llm-1735721234568-5
      sourceHandle: source
      target: llm-1735721234569
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: llm-1735721234569-llm-1735721234570
      source: llm-1735721234569
      sourceHandle: source
      target: llm-1735721234570
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: llm-1735721234570-code-1735721234571
      source: llm-1735721234570
      sourceHandle: source
      target: code-1735721234571
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: code
        targetType: llm
      id: code-1735721234571-llm-1735721234572
      source: code-1735721234571
      sourceHandle: source
      target: llm-1735721234572
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: llm-1735721234572-code-1735721234573
      source: llm-1735721234572
      sourceHandle: source
      target: code-1735721234573
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: code
        targetType: end
      id: code-1735721234573-end
      source: code-1735721234573
      sourceHandle: source
      target: end
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: 输入策略文档
        selected: false
        title: 开始
        type: start
        variables:
        - description: 策略文档内容，包含完整的风控策略描述
          label: 策略文档
          max_length: 20000
          required: true
          type: text-input
          variable: strategy_document
      height: 116
      id: start
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import re\nimport json\n\ndef main(strategy_document: str) -> dict:\n    # 清理文档格式\n    cleaned_document = re.sub(r'\\\\n', '\\n', strategy_document)\n    cleaned_document = re.sub(r'\\s+', ' ', cleaned_document)\n    \n    # 提取事件信息\n    event_info = extract_event_info(cleaned_document)\n    \n    # 文档分段\n    sections = {\n        \"device_detection\": extract_section(cleaned_document, \"设备检测\"),\n        \"ip_detection\": extract_section(cleaned_document, \"IP检测\"),\n        \"blacklist_detection\": extract_section(cleaned_document, \"名单检测\"),\n        \"account_detection\": extract_section(cleaned_document, \"账号检测\"),\n        \"business_detection\": extract_section(cleaned_document, \"业务检测\"),\n        \"behavior_detection\": extract_section(cleaned_document, \"行为检测\")\n    }\n    \n    return {\n        \"cleaned_document\": cleaned_document,\n        \"event_info\": event_info,\n        \"document_sections\": sections\n    }\n\ndef extract_event_info(document: str) -> dict:\n    \"\"\"从文档中提取事件信息\"\"\"\n    event_info = {\n        \"name\": \"\",\n        \"code\": \"\",\n        \"business_code\": \"activity\",\n        \"access_by\": \"\",\n        \"needs_generation\": False\n    }\n    \n    # 尝试提取事件名称\n    name_pattern = r'名称[：:]\\s*([^\\s\\n]+)'\n    name_match = re.search(name_pattern, document)\n    if name_match:\n        event_info[\"name\"] = name_match.group(1).strip()\n    \n    # 尝试提取事件代码\n    code_pattern = r'code[：:]\\s*([^\\s\\n]+)'\n    code_match = re.search(code_pattern, document)\n    if code_match:\n        event_info[\"code\"] = code_match.group(1).strip()\n    \n    # 如果没有找到事件信息，标记需要生成\n    if not event_info[\"name\"] or not event_info[\"code\"]:\n        event_info[\"needs_generation\"] = True\n        # 尝试从标题或活动描述中提取\n        title_patterns = [\n            r'([^\\n]*活动[^\\n]*)',\n            r'([^\\n]*抽奖[^\\n]*)',\n            r'([^\\n]*策略[^\\n]*)',\n            r'^([^\\n]{5,30})'\n        ]\n        for pattern in title_patterns:\n            match = re.search(pattern, document)\n            if match:\n                potential_title = match.group(1).strip()\n                if potential_title and len(potential_title) > 3:\n                    event_info[\"potential_title\"] = potential_title\n                    break\n    \n    return event_info\n\ndef extract_section(document: str, section_name: str) -> str:\n    \"\"\"提取文档中的特定段落\"\"\"\n    pattern = rf'{section_name}[：:](.*?)(?=\\n[一二三四五六七八九十]|\\n[A-Z]|$)'\n    match = re.search(pattern, document, re.DOTALL | re.IGNORECASE)\n    return match.group(1).strip() if match else \"\""
        code_language: python3
        desc: 清理文档格式，提取或标记需要生成事件信息，分段处理
        outputs:
          cleaned_document:
            children: null
            type: string
          document_sections:
            children: null
            type: object
          event_info:
            children: null
            type: object
        selected: false
        title: 文档预处理
        type: code
        variables:
        - value_selector:
          - start
          - strategy_document
          variable: strategy_document
      height: 54
      id: code-1735721234568
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 生成事件名称和代码（如果文档中没有的话）
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: qwen-plus-latest
          provider: tongyi
        prompt_template:
        - id: system
          role: system
          text: 你是一个风控系统专家，擅长为风控策略生成合适的事件名称和代码。请严格按照JSON格式输出。
        - id: user
          role: user
          text: "基于以下策略文档信息，生成事件名称和代码：\n\n文档信息：\n{{#code-1735721234568.event_info#}}\n\n策略文档片段：\n{{#code-1735721234568.cleaned_document#}}\n\n要求：\n1. 如果event_info中已有name和code且不为空，直接返回原值\n2. 如果需要生成（needs_generation为true），则：\n   - 事件名称：简洁明了，体现业务场景，如\"会员中心-抽奖\"、\"春节活动-签到\"\n   - 事件代码：全部小写，单词用-连接，如\"member-center-lottery\"、\"spring-festival-checkin\"\n3. 业务代码默认为\"activity\"\n\n请按以下JSON格式输出：\n{\n  \"name\": \"事件名称\",\n  \"code\": \"事件代码\",\n  \"business_code\": \"activity\",\n  \"access_by\": \"\",\n  \"generated\": true/false\n}"
        selected: false
        title: 事件信息生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm-1735721234568-5
      position:
        x: 688
        y: 282
      positionAbsolute:
        x: 688
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 使用LLM解析策略文档，提取检测规则和条件
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: qwen-plus-latest
          provider: tongyi
        prompt_template:
        - id: system
          role: system
          text: 你是一个风控策略专家，擅长分析策略文档并提取检测规则。请严格按照JSON格式输出，不要添加任何额外的文字说明。
        - id: user
          role: user
          text: "请分析以下策略文档，提取出所有的检测规则和条件。\n\n策略文档：\n{{#code-1735721234568.cleaned_document#}}\n\n事件信息：\n{{#llm-1735721234568-5.text#}}\n\n请按以下JSON格式输出解析结果：\n{\n  \"detection_modules\": [\n    {\n      \"type\": \"设备检测|IP检测|名单检测|账号检测|业务检测|行为检测\",\n      \"name\": \"检测模块名称\",\n      \"rules\": [\"规则1\", \"规则2\"]\n    }\n  ],\n  \"accumulation_conditions\": [\n    {\n      \"rule_number\": 1,\n      \"description\": \"规则描述\",\n      \"dimension\": \"DeviceId|ClientIp|UserId\",\n      \"operator\": \">|>=|<|<=|==\",\n      \"threshold\": 数值,\n      \"time_window\": 时间窗口分钟数,\n      \"action\": \"REJECT|REVIEW|PASS\",\n      \"inner_reason\": \"内部原因\",\n      \"outer_reason\": \"外部话术\"\n    }\n  ],\n  \"attribute_conditions\": [\n    {\n      \"attribute_name\": \"vipLevel|nobilityLevel|planetUserLevel\",\n      \"operator\": \">|>=|<|<=|==\",\n      \"value\": 数值,\n      \"description\": \"条件描述\"\n    }\n  ],\n  \"time_windows\": [1440, 60, 10080],\n  \"user_level_conditions\": {\n    \"vip_level\": {\"operator\": \">=\", \"value\": 25},\n    \"nobility_level\": {\"operator\": \">=\", \"value\": 33},\n    \"planet_user_level\": {\"operator\": \">=\", \"value\": 36}\n  }\n}"
        selected: false
        title: 策略解析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm-1735721234569
      position:
        x: 992
        y: 282
      positionAbsolute:
        x: 992
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 基于解析结果识别需要创建的风控组件
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: qwen-plus-latest
          provider: tongyi
        prompt_template:
        - id: system
          role: system
          text: 你是风控系统架构专家，擅长将策略需求转换为具体的系统组件配置。请严格按照JSON格式输出。
        - id: user
          role: user
          text: "基于以下策略解析结果，识别需要创建的风控组件。\n\n解析结果：\n{{#llm-1735721234569.text#}}\n\n事件信息：\n{{#llm-1735721234568-5.text#}}\n\n请输出需要创建的组件清单：\n{\n  \"factors\": [\n    {\n      \"name\": \"因子名称\",\n      \"group_key\": \"Event,DeviceId,type\",\n      \"agg_key\": \"UserId\",\n      \"function\": \"COUNT_DISTINCT\",\n      \"time_span\": 1440,\n      \"condition\": \"#Event=='事件代码' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')\",\n      \"comment\": \"因子说明\"\n    }\n  ],\n  \"local_attributes\": [\n    {\n      \"name\": \"属性名称\",\n      \"dependent\": \"\\\"因子ID\\\"::::Event::::DeviceId::::type\",\n      \"function\": \"getFactorValue\",\n      \"comment\": \"属性说明\"\n    }\n  ],\n  \"remote_attributes\": [\n    {\n      \"name\": \"userData\",\n      \"dependent\": \"UserId::::cache+=\",\n      \"function\": \"userData\",\n      \"result_key\": \"*\",\n      \"comment\": \"用户数据属性\"\n    }\n  ],\n  \"atom_rules\": [\n    {\n      \"name\": \"规则名称\",\n      \"condition\": \"检测条件表达式\",\n      \"dependent\": \"属性1,属性2\",\n      \"action\": \"REJECT|REVIEW|PASS\",\n      \"inner_reason\": \"内部原因\",\n      \"outer_reason\": \"外部话术\",\n      \"comment\": \"规则说明\"\n    }\n  ],\n  \"group_rule\": {\n    \"name\": \"策略组名称\",\n    \"comment\": \"自动生成的策略组\"\n  }\n}"
        selected: false
        title: 组件识别
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm-1735721234570
      position:
        x: 1296
        y: 282
      positionAbsolute:
        x: 1296
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(component_list: str, event_info: str) -> dict:\n    \"\"\"查询数据库检查可复用的组件\"\"\"\n    \n    try:\n        components = json.loads(component_list)\n    except:\n        components = {\"factors\": [], \"local_attributes\": [], \"remote_attributes\": [], \"atom_rules\": []}\n    \n    try:\n        event_data = json.loads(event_info)\n    except:\n        event_data = {\"code\": \"\"}\n    \n    # 模拟数据库查询结果\n    existing_event = query_event_by_code(event_data.get('code', ''))\n    existing_factors = query_similar_factors(components.get('factors', []))\n    existing_attributes = query_existing_attributes(components.get('local_attributes', []) + components.get('remote_attributes', []))\n    \n    # 生成复用建议\n    reuse_suggestions = {\n        'can_reuse_event': existing_event is not None,\n        'reusable_factors_count': len(existing_factors),\n        'reusable_attributes_count': len(existing_attributes),\n        'recommendations': []\n    }\n    \n    if existing_event:\n        reuse_suggestions['recommendations'].append(f\"可以复用已存在的事件: {existing_event.get('name', '')}\")\n    \n    if existing_factors:\n        reuse_suggestions['recommendations'].append(f\"找到 {len(existing_factors)} 个可复用的因子\")\n    \n    if existing_attributes:\n        reuse_suggestions['recommendations'].append(f\"找到 {len(existing_attributes)} 个可复用的属性\")\n    \n    if not existing_factors and not existing_attributes:\n        reuse_suggestions['recommendations'].append(\"未找到可复用的组件，将创建全新的配置\")\n    \n    return {\n        'existing_event': existing_event,\n        'existing_factors': existing_factors,\n        'existing_attributes': existing_attributes,\n        'reuse_suggestions': reuse_suggestions\n    }\n\ndef query_event_by_code(event_code: str) -> dict:\n    \"\"\"查询已存在的事件\"\"\"\n    if event_code in ['member-center-lottery', 'test-lottery']:\n        return {'id': 1, 'name': '已存在的事件', 'code': event_code, 'found': True}\n    return None\n\ndef query_similar_factors(factor_specs: list) -> list:\n    \"\"\"查询相似的因子\"\"\"\n    similar_factors = []\n    for spec in factor_specs:\n        if spec.get('time_span') == 1440:\n            similar_factors.append({'id': 361, 'group_key': 'Event,DeviceId,type', 'time_span': 1440, 'comment': '通用同设备、同类型一天内参与人数'})\n    return similar_factors\n\ndef query_existing_attributes(attr_specs: list) -> list:\n    \"\"\"查询已存在的属性\"\"\"\n    existing_attrs = []\n    for spec in attr_specs:\n        if spec.get('name') == 'userData':\n            existing_attrs.append({'id': 245, 'name': 'userData', 'type': 'REMOTE', 'function': 'userData'})\n    return existing_attrs"
        code_language: python3
        desc: 查询数据库检查可复用的组件
        outputs:
          existing_attributes:
            children: null
            type: array[object]
          existing_event:
            children: null
            type: object
          existing_factors:
            children: null
            type: array[object]
          reuse_suggestions:
            children: null
            type: object
        selected: false
        title: 数据库查询
        type: code
        variables:
        - value_selector:
          - llm-1735721234570
          - text
          variable: component_list
        - value_selector:
          - llm-1735721234568-5
          - text
          variable: event_info
      height: 54
      id: code-1735721234571
      position:
        x: 1600
        y: 282
      positionAbsolute:
        x: 1600
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
        desc: 基于复用检查结果生成最终的组件配置
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: qwen-plus-latest
          provider: tongyi
        prompt_template:
        - id: system
          role: system
          text: 你是风控系统配置专家，负责生成完整的风控组件配置。请严格按照数据库表结构生成配置，优先复用已存在的组件。
        - id: user
          role: user
          text: "基于以下信息生成最终的风控组件配置：\n\n需要创建的组件：\n{{#llm-1735721234570.text#}}\n\n已存在可复用的组件：\n- 事件：{{#code-1735721234571.existing_event#}}\n- 因子：{{#code-1735721234571.existing_factors#}}\n- 属性：{{#code-1735721234571.existing_attributes#}}\n\n复用建议：\n{{#code-1735721234571.reuse_suggestions#}}\n\n事件信息：\n{{#llm-1735721234568-5.text#}}\n\n请生成完整的配置，优先复用已存在的组件：\n{\n  \"event\": {\n    \"action\": \"create|reuse\",\n    \"reuse_id\": \"复用的ID(如果是复用)\",\n    \"data\": {\n      \"name\": \"事件名称\",\n      \"code\": \"事件代码\",\n      \"business_code\": \"activity\",\n      \"access_by\": \"\",\n      \"default_level\": \"PASS\",\n      \"can_punish\": true,\n      \"types\": \"1\",\n      \"comment\": \"自动生成的事件配置\"\n    }\n  },\n  \"factors\": [\n    {\n      \"action\": \"create|reuse\",\n      \"reuse_id\": \"复用的ID(如果是复用)\",\n      \"data\": {\n        \"group_key\": \"Event,DeviceId,type\",\n        \"agg_key\": \"UserId\",\n        \"_function\": \"COUNT_DISTINCT\",\n        \"time_span\": 1440,\n        \"_condition\": \"#Event=='事件代码' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')\",\n        \"comment\": \"因子说明\",\n        \"business\": 1,\n        \"window_type\": 1\n      }\n    }\n  ],\n  \"attributes\": [\n    {\n      \"action\": \"create|reuse\",\n      \"reuse_id\": \"复用的ID(如果是复用)\",\n      \"data\": {\n        \"name\": \"属性名称\",\n        \"type\": \"LOCAL|REMOTE\",\n        \"dependent\": \"依赖配置\",\n        \"_function\": \"getFactorValue|userData|等\",\n        \"result_key\": \"结果键\"\n      }\n    }\n  ],\n  \"atom_rules\": [\n    {\n      \"action\": \"create\",\n      \"data\": {\n        \"name\": \"规则名称\",\n        \"_condition\": \"检测条件表达式\",\n        \"dependent\": \"依赖的属性列表(逗号分隔)\",\n        \"reply\": \"外部话术\",\n        \"inner_reason\": \"内部原因\",\n        \"outer_reason\": \"外部话术\",\n        \"priority\": 0,\n        \"accuracy\": 100,\n        \"status\": \"DISABLE\",\n        \"can_fuse\": true,\n        \"type\": 0\n      }\n    }\n  ],\n  \"group_rule\": {\n    \"action\": \"create\",\n    \"data\": {\n      \"name\": \"策略组名称\",\n      \"_condition\": \"\",\n      \"dependent\": \"\",\n      \"status\": \"DISABLE\",\n      \"comment\": \"自动生成的策略组\",\n      \"priority\": 0\n    }\n  }\n}"
        selected: false
        title: 配置生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: llm-1735721234572
      position:
        x: 1904
        y: 282
      positionAbsolute:
        x: 1904
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\n\ndef main(final_config: str) -> dict:\n    \"\"\"验证生成的配置是否正确\"\"\"\n    \n    try:\n        config = json.loads(final_config)\n    except:\n        return {\n            'validation_result': {\n                'valid': False,\n                'errors': ['配置JSON格式错误'],\n                'warnings': [],\n                'suggestions': []\n            },\n            'validated_config': None\n        }\n    \n    errors = []\n    warnings = []\n    suggestions = []\n    \n    # 验证事件配置\n    event_config = config.get('event', {})\n    if not event_config.get('data', {}).get('name'):\n        errors.append('事件名称不能为空')\n    if not event_config.get('data', {}).get('code'):\n        errors.append('事件代码不能为空')\n    \n    # 验证因子配置\n    factors = config.get('factors', [])\n    for i, factor in enumerate(factors):\n        factor_data = factor.get('data', {})\n        if not factor_data.get('group_key'):\n            errors.append(f'因子{i+1}的group_key不能为空')\n        if not factor_data.get('_function'):\n            errors.append(f'因子{i+1}的_function不能为空')\n        if not factor_data.get('time_span'):\n            errors.append(f'因子{i+1}的time_span不能为空')\n    \n    # 验证属性配置\n    attributes = config.get('attributes', [])\n    for i, attr in enumerate(attributes):\n        attr_data = attr.get('data', {})\n        if not attr_data.get('name'):\n            errors.append(f'属性{i+1}的name不能为空')\n        if not attr_data.get('type'):\n            errors.append(f'属性{i+1}的type不能为空')\n        if attr_data.get('type') not in ['LOCAL', 'REMOTE']:\n            errors.append(f'属性{i+1}的type必须是LOCAL或REMOTE')\n    \n    # 验证策略原子配置\n    atom_rules = config.get('atom_rules', [])\n    for i, rule in enumerate(atom_rules):\n        rule_data = rule.get('data', {})\n        if not rule_data.get('name'):\n            errors.append(f'策略原子{i+1}的name不能为空')\n        if not rule_data.get('_condition'):\n            errors.append(f'策略原子{i+1}的_condition不能为空')\n    \n    # 生成建议\n    if len(factors) == 0:\n        warnings.append('没有生成任何因子，请检查策略文档中是否包含累计条件')\n    if len(atom_rules) == 0:\n        warnings.append('没有生成任何策略原子，请检查策略文档中是否包含检测规则')\n    \n    suggestions.append('建议在部署前先在测试环境验证配置')\n    suggestions.append('建议逐步启用策略原子，先设置为TEST状态')\n    \n    validation_result = {\n        'valid': len(errors) == 0,\n        'errors': errors,\n        'warnings': warnings,\n        'suggestions': suggestions\n    }\n    \n    return {\n        'validation_result': validation_result,\n        'validated_config': config if len(errors) == 0 else None\n    }"
        code_language: python3
        desc: 验证生成的配置是否正确
        outputs:
          validated_config:
            children: null
            type: object
          validation_result:
            children: null
            type: object
        selected: false
        title: 配置验证
        type: code
        variables:
        - value_selector:
          - llm-1735721234572
          - text
          variable: final_config
      height: 54
      id: code-1735721234573
      position:
        x: 2208
        y: 282
      positionAbsolute:
        x: 2208
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: 输出最终的风控配置结果
        outputs:
        - value_selector:
          - code-1735721234573
          - validated_config
          variable: final_config
        - value_selector:
          - code-1735721234573
          - validation_result
          variable: validation_result
        - value_selector:
          - code-1735721234571
          - reuse_suggestions
          variable: reuse_suggestions
        selected: false
        title: 输出结果
        type: end
      height: 90
      id: end
      position:
        x: 2512
        y: 282
      positionAbsolute:
        x: 2512
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
