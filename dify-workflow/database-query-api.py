"""
风控组件数据库查询API
用于Dify工作流中的数据库查询节点
"""

import requests
import json
import mysql.connector
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskComponentQueryAPI:
    """风控组件查询API"""
    
    def __init__(self, db_config: Dict[str, str]):
        """
        初始化数据库连接配置
        
        Args:
            db_config: 数据库配置字典，包含host, user, password, database等
        """
        self.db_config = db_config
    
    def get_db_connection(self):
        """获取数据库连接"""
        return mysql.connector.connect(**self.db_config)
    
    def query_event_by_code(self, event_code: str) -> Dict[str, Any]:
        """
        根据事件代码查询已存在的事件
        
        Args:
            event_code: 事件代码
            
        Returns:
            查询结果字典
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            query = "SELECT * FROM risk_event WHERE code = %s"
            cursor.execute(query, (event_code,))
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if result:
                logger.info(f"找到已存在的事件: {event_code}")
                return {
                    'found': True,
                    'data': result
                }
            else:
                logger.info(f"未找到事件: {event_code}")
                return {
                    'found': False,
                    'data': None
                }
                
        except Exception as e:
            logger.error(f"查询事件时出错: {e}")
            return {
                'found': False,
                'data': None,
                'error': str(e)
            }
    
    def query_similar_factors(self, factor_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        查询相似的因子
        
        Args:
            factor_spec: 因子规格字典
            
        Returns:
            相似因子列表
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 基于时间窗口和统计维度查找相似因子
            time_span = factor_spec.get('time_span', 0)
            group_key_pattern = factor_spec.get('group_key', '')
            
            # 允许时间窗口有一定误差（±60分钟）
            query = """
                SELECT * FROM risk_factor 
                WHERE ABS(time_span - %s) <= 60 
                AND (group_key LIKE %s OR group_key LIKE %s)
                LIMIT 5
            """
            
            # 提取维度关键词
            dimension_keywords = []
            if 'DeviceId' in group_key_pattern:
                dimension_keywords.append('%DeviceId%')
            if 'ClientIp' in group_key_pattern:
                dimension_keywords.append('%ClientIp%')
            if 'UserId' in group_key_pattern:
                dimension_keywords.append('%UserId%')
            
            similar_factors = []
            for keyword in dimension_keywords:
                cursor.execute(query, (time_span, keyword, keyword))
                results = cursor.fetchall()
                similar_factors.extend(results)
            
            cursor.close()
            conn.close()
            
            # 去重
            unique_factors = []
            seen_ids = set()
            for factor in similar_factors:
                if factor['id'] not in seen_ids:
                    unique_factors.append(factor)
                    seen_ids.add(factor['id'])
            
            logger.info(f"找到 {len(unique_factors)} 个相似因子")
            return unique_factors
            
        except Exception as e:
            logger.error(f"查询相似因子时出错: {e}")
            return []
    
    def query_attribute_by_name(self, attr_name: str) -> Optional[Dict[str, Any]]:
        """
        根据属性名称查询已存在的属性
        
        Args:
            attr_name: 属性名称
            
        Returns:
            属性数据字典或None
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            query = "SELECT * FROM risk_attribute WHERE name = %s"
            cursor.execute(query, (attr_name,))
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if result:
                logger.info(f"找到已存在的属性: {attr_name}")
                return result
            else:
                logger.info(f"未找到属性: {attr_name}")
                return None
                
        except Exception as e:
            logger.error(f"查询属性时出错: {e}")
            return None
    
    def query_similar_attributes(self, attr_spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        查询相似的属性
        
        Args:
            attr_spec: 属性规格字典
            
        Returns:
            相似属性列表
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            attr_type = attr_spec.get('type', 'LOCAL')
            function_name = attr_spec.get('function', '')
            
            query = """
                SELECT * FROM risk_attribute 
                WHERE type = %s AND _function = %s
                LIMIT 10
            """
            
            cursor.execute(query, (attr_type, function_name))
            results = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            logger.info(f"找到 {len(results)} 个相似属性")
            return results
            
        except Exception as e:
            logger.error(f"查询相似属性时出错: {e}")
            return []
    
    def query_atom_rule_by_name(self, rule_name: str) -> Optional[Dict[str, Any]]:
        """
        根据规则名称查询已存在的策略原子
        
        Args:
            rule_name: 规则名称
            
        Returns:
            策略原子数据字典或None
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            query = "SELECT * FROM risk_rule_atom WHERE name = %s"
            cursor.execute(query, (rule_name,))
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if result:
                logger.info(f"找到已存在的策略原子: {rule_name}")
                return result
            else:
                logger.info(f"未找到策略原子: {rule_name}")
                return None
                
        except Exception as e:
            logger.error(f"查询策略原子时出错: {e}")
            return None
    
    def query_group_rule_by_name(self, group_name: str) -> Optional[Dict[str, Any]]:
        """
        根据策略组名称查询已存在的策略组
        
        Args:
            group_name: 策略组名称
            
        Returns:
            策略组数据字典或None
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(dictionary=True)
            
            query = "SELECT * FROM risk_rule_group WHERE name = %s"
            cursor.execute(query, (group_name,))
            result = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if result:
                logger.info(f"找到已存在的策略组: {group_name}")
                return result
            else:
                logger.info(f"未找到策略组: {group_name}")
                return None
                
        except Exception as e:
            logger.error(f"查询策略组时出错: {e}")
            return None
    
    def generate_reuse_suggestions(self, existing_factors: List[Dict], 
                                 existing_attributes: List[Dict], 
                                 existing_event: Optional[Dict]) -> Dict[str, Any]:
        """
        生成复用建议
        
        Args:
            existing_factors: 已存在的因子列表
            existing_attributes: 已存在的属性列表
            existing_event: 已存在的事件
            
        Returns:
            复用建议字典
        """
        suggestions = {
            'can_reuse_event': existing_event is not None,
            'reusable_factors_count': len(existing_factors),
            'reusable_attributes_count': len(existing_attributes),
            'recommendations': []
        }
        
        if existing_event:
            suggestions['recommendations'].append(
                f"可以复用已存在的事件: {existing_event.get('name', '')}"
            )
        
        if existing_factors:
            suggestions['recommendations'].append(
                f"找到 {len(existing_factors)} 个可复用的因子，建议优先使用"
            )
        
        if existing_attributes:
            suggestions['recommendations'].append(
                f"找到 {len(existing_attributes)} 个可复用的属性，建议优先使用"
            )
        
        if not existing_factors and not existing_attributes:
            suggestions['recommendations'].append(
                "未找到可复用的组件，将创建全新的配置"
            )
        
        return suggestions

# 示例使用
if __name__ == "__main__":
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'risk_user',
        'password': 'password',
        'database': 'ypp_fengkong'
    }
    
    # 创建查询API实例
    api = RiskComponentQueryAPI(db_config)
    
    # 测试查询事件
    event_result = api.query_event_by_code('member-center-lottery')
    print("事件查询结果:", event_result)
    
    # 测试查询相似因子
    factor_spec = {
        'time_span': 1440,
        'group_key': 'Event,DeviceId,type'
    }
    similar_factors = api.query_similar_factors(factor_spec)
    print("相似因子数量:", len(similar_factors))
    
    # 测试查询属性
    attr_result = api.query_attribute_by_name('userData')
    print("属性查询结果:", attr_result is not None)
