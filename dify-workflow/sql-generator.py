"""
风控配置SQL脚本生成器
基于Dify工作流生成的配置，生成对应的SQL插入脚本
"""

import json
from datetime import datetime
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskConfigSQLGenerator:
    """风控配置SQL生成器"""
    
    def __init__(self, author: str = "dify-workflow"):
        """
        初始化SQL生成器
        
        Args:
            author: 创建者名称
        """
        self.author = author
        self.current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def generate_sql_scripts(self, config: Dict[str, Any]) -> List[str]:
        """
        生成完整的SQL脚本
        
        Args:
            config: 风控配置字典
            
        Returns:
            SQL脚本列表
        """
        sql_scripts = []
        
        # 生成因子SQL
        factors = config.get('factors', [])
        for factor in factors:
            if factor.get('action') == 'create':
                sql = self.generate_factor_sql(factor.get('data', {}))
                sql_scripts.append(sql)
        
        # 生成属性SQL
        attributes = config.get('attributes', [])
        for attr in attributes:
            if attr.get('action') == 'create':
                sql = self.generate_attribute_sql(attr.get('data', {}))
                sql_scripts.append(sql)
        
        # 生成策略原子SQL
        atom_rules = config.get('atom_rules', [])
        for rule in atom_rules:
            if rule.get('action') == 'create':
                sql = self.generate_atom_rule_sql(rule.get('data', {}))
                sql_scripts.append(sql)
        
        # 生成策略组SQL
        group_rule = config.get('group_rule', {})
        if group_rule.get('action') == 'create':
            sql = self.generate_group_rule_sql(group_rule.get('data', {}))
            sql_scripts.append(sql)
        
        # 生成事件SQL
        event = config.get('event', {})
        if event.get('action') == 'create':
            sql = self.generate_event_sql(event.get('data', {}))
            sql_scripts.append(sql)
        
        return sql_scripts
    
    def generate_factor_sql(self, factor_data: Dict[str, Any]) -> str:
        """生成因子插入SQL"""
        sql = f"""
INSERT INTO risk_factor (
    group_key, agg_key, _function, time_span, _condition, 
    comment, author, modifier, business, window_type,
    created_at, updated_at
) VALUES (
    '{factor_data.get('group_key', '')}',
    '{factor_data.get('agg_key', 'UserId')}',
    '{factor_data.get('_function', 'COUNT_DISTINCT')}',
    {factor_data.get('time_span', 1440)},
    '{factor_data.get('_condition', '')}',
    '{factor_data.get('comment', '')}',
    '{self.author}',
    '{self.author}',
    {factor_data.get('business', 1)},
    {factor_data.get('window_type', 1)},
    '{self.current_time}',
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_attribute_sql(self, attr_data: Dict[str, Any]) -> str:
        """生成属性插入SQL"""
        sql = f"""
INSERT INTO risk_attribute (
    name, type, dependent, _function, result_key,
    author, modifier, created_at, updated_at, biz_type_flag
) VALUES (
    '{attr_data.get('name', '')}',
    '{attr_data.get('type', 'LOCAL')}',
    '{attr_data.get('dependent', '')}',
    '{attr_data.get('_function', 'getFactorValue')}',
    '{attr_data.get('result_key', '')}',
    '{self.author}',
    '{self.author}',
    '{self.current_time}',
    '{self.current_time}',
    0
);"""
        return sql.strip()
    
    def generate_atom_rule_sql(self, rule_data: Dict[str, Any]) -> str:
        """生成策略原子插入SQL"""
        sql = f"""
INSERT INTO risk_rule_atom (
    name, priority, accuracy, status, _condition, dependent,
    reply, inner_reason, outer_reason, comment, business_code,
    valid_time, can_fuse, type, author, modifier,
    created_at, updated_at
) VALUES (
    '{rule_data.get('name', '')}',
    {rule_data.get('priority', 0)},
    {rule_data.get('accuracy', 100)},
    '{rule_data.get('status', 'DISABLE')}',
    '{rule_data.get('_condition', '')}',
    '{rule_data.get('dependent', '')}',
    '{rule_data.get('reply', '')}',
    '{rule_data.get('inner_reason', '')}',
    '{rule_data.get('outer_reason', '')}',
    '{rule_data.get('comment', '')}',
    '{rule_data.get('business_code', '')}',
    {rule_data.get('valid_time', 0)},
    {1 if rule_data.get('can_fuse', True) else 0},
    {rule_data.get('type', 0)},
    '{self.author}',
    '{self.author}',
    '{self.current_time}',
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_group_rule_sql(self, group_data: Dict[str, Any]) -> str:
        """生成策略组插入SQL"""
        sql = f"""
INSERT INTO risk_rule_group (
    name, priority, _condition, dependent, status,
    comment, reply, business_code, author, modifier,
    created_at, updated_at
) VALUES (
    '{group_data.get('name', '')}',
    {group_data.get('priority', 0)},
    '{group_data.get('_condition', '')}',
    '{group_data.get('dependent', '')}',
    '{group_data.get('status', 'DISABLE')}',
    '{group_data.get('comment', '')}',
    '{group_data.get('reply', '')}',
    '{group_data.get('business_code', '')}',
    '{self.author}',
    '{self.author}',
    '{self.current_time}',
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_event_sql(self, event_data: Dict[str, Any]) -> str:
        """生成事件插入SQL"""
        sql = f"""
INSERT INTO risk_event (
    name, business_code, code, rule_group_id, reply, comment,
    access_by, default_level, can_punish, types, access_mode,
    requires_response, author, modifier, created_at, updated_at
) VALUES (
    '{event_data.get('name', '')}',
    '{event_data.get('business_code', 'activity')}',
    '{event_data.get('code', '')}',
    '{event_data.get('rule_group_id', '')}',
    '{event_data.get('reply', '')}',
    '{event_data.get('comment', '')}',
    '{event_data.get('access_by', '')}',
    '{event_data.get('default_level', 'PASS')}',
    {1 if event_data.get('can_punish', True) else 0},
    '{event_data.get('types', '1')}',
    {event_data.get('access_mode', 1)},
    {1 if event_data.get('requires_response', True) else 0},
    '{self.author}',
    '{self.author}',
    '{self.current_time}',
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_rule_relation_sql(self, rule_id: int, group_id: int) -> str:
        """生成规则关系插入SQL"""
        sql = f"""
INSERT INTO risk_rule_relation (
    rule_id, group_id, created_at
) VALUES (
    {rule_id},
    {group_id},
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_event_param_sql(self, event_code: str, param_code: str, memo: str) -> str:
        """生成事件参数插入SQL"""
        sql = f"""
INSERT INTO risk_event_param (
    code, memo, event_code, author, create_time
) VALUES (
    '{param_code}',
    '{memo}',
    '{event_code}',
    '{self.author}',
    '{self.current_time}'
);"""
        return sql.strip()
    
    def generate_complete_sql_script(self, config: Dict[str, Any]) -> str:
        """
        生成完整的SQL脚本文件
        
        Args:
            config: 风控配置字典
            
        Returns:
            完整的SQL脚本字符串
        """
        scripts = []
        
        # 添加脚本头部注释
        header = f"""
-- 风控配置自动生成SQL脚本
-- 生成时间: {self.current_time}
-- 生成者: {self.author}
-- 事件: {config.get('event', {}).get('data', {}).get('name', 'Unknown')}

-- 开始事务
START TRANSACTION;

"""
        scripts.append(header)
        
        # 生成各组件SQL
        sql_list = self.generate_sql_scripts(config)
        scripts.extend(sql_list)
        
        # 生成标准事件参数
        event_code = config.get('event', {}).get('data', {}).get('code', '')
        if event_code:
            standard_params = [
                ('UserId', '参与人的uid'),
                ('ClientIp', '参与人的ip'),
                ('DeviceId', '参与人的设备号'),
                ('AppId', '当前AppId'),
                ('clientDetail', '客户端详细信息JSON对象')
            ]
            
            scripts.append("\n-- 插入标准事件参数")
            for param_code, memo in standard_params:
                param_sql = self.generate_event_param_sql(event_code, param_code, memo)
                scripts.append(param_sql)
        
        # 添加脚本尾部
        footer = """

-- 提交事务
COMMIT;

-- 注意事项:
-- 1. 执行前请确认数据库连接正确
-- 2. 建议先在测试环境执行
-- 3. 执行后需要手动关联策略原子和策略组
-- 4. 需要手动更新事件的rule_group_id字段
"""
        scripts.append(footer)
        
        return '\n'.join(scripts)

# 示例使用
if __name__ == "__main__":
    # 示例配置
    sample_config = {
        "event": {
            "action": "create",
            "data": {
                "name": "会员中心-抽奖",
                "code": "member-center-lottery",
                "business_code": "activity",
                "access_by": "张三",
                "comment": "自动生成的事件配置"
            }
        },
        "factors": [
            {
                "action": "create",
                "data": {
                    "group_key": "Event,DeviceId,type",
                    "agg_key": "UserId",
                    "_function": "COUNT_DISTINCT",
                    "time_span": 1440,
                    "_condition": "#Event=='member-center-lottery' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')",
                    "comment": "24小时内同设备参与用户数"
                }
            }
        ],
        "attributes": [
            {
                "action": "create",
                "data": {
                    "name": "deviceUserCount24h",
                    "type": "LOCAL",
                    "dependent": "\"1\"::::Event::::DeviceId::::type",
                    "_function": "getFactorValue"
                }
            }
        ],
        "atom_rules": [
            {
                "action": "create",
                "data": {
                    "name": "会员中心-抽奖-设备聚集检测",
                    "_condition": "userData != null && userData.vipLevel < 12 && deviceUserCount24h > 3",
                    "dependent": "userData,deviceUserCount24h",
                    "reply": "您的设备存在异常，建议更换设备进行尝试",
                    "inner_reason": "设备聚集行为检测",
                    "outer_reason": "您的设备存在异常，建议更换设备进行尝试"
                }
            }
        ],
        "group_rule": {
            "action": "create",
            "data": {
                "name": "会员中心-抽奖-检测",
                "comment": "自动生成的策略组"
            }
        }
    }
    
    # 生成SQL脚本
    generator = RiskConfigSQLGenerator("dify-workflow")
    complete_script = generator.generate_complete_sql_script(sample_config)
    
    print("生成的SQL脚本:")
    print(complete_script)
