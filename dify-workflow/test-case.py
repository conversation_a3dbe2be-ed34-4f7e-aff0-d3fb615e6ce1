"""
风控配置自动生成工作流测试用例
用于验证Dify工作流的功能和输出质量
"""

import json
import requests
from typing import Dict, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WorkflowTester:
    """工作流测试器"""
    
    def __init__(self, dify_api_url: str, api_key: str):
        """
        初始化测试器
        
        Args:
            dify_api_url: Dify API地址
            api_key: API密钥
        """
        self.dify_api_url = dify_api_url
        self.api_key = api_key
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    def run_workflow(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行工作流
        
        Args:
            inputs: 输入参数
            
        Returns:
            工作流输出结果
        """
        try:
            response = requests.post(
                f"{self.dify_api_url}/workflows/run",
                headers=self.headers,
                json={
                    "inputs": inputs,
                    "response_mode": "blocking",
                    "user": "test-user"
                }
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"工作流执行失败: {response.status_code} - {response.text}")
                return {"error": response.text}
                
        except Exception as e:
            logger.error(f"工作流执行异常: {e}")
            return {"error": str(e)}
    
    def test_basic_strategy_parsing(self):
        """测试基础策略解析功能"""
        logger.info("开始测试基础策略解析功能")
        
        test_document = """
        接入说明：
        风控接入说明
        活动策略：场景分级3级。
        
        设备检测：设备模型
        1、（活动各环节共累积阈值），活动全程，同设备成功参与该活动（请求返回PASS\\REVIEW）人数>3，REJECT。
        对内外话术：同设备参与该活动的账号较多，请更换新设备尝试。
        
        IP检测：
        2、同IP一天内参与该活动人数>8，REJECT。
        对内外话术：同IP参与该活动的账号较多，请更换网络环境尝试。
        
        账号检测：
        3、用户等级检测：MVP/比心vip < 12 且 爵位 < 3，进行严格检测。
        
        一、接入事件：
        名称：会员中心-抽奖      code：member-center-lottery
        """
        
        inputs = {
            "strategy_document": test_document,
            "event_name": "会员中心-抽奖",
            "event_code": "member-center-lottery",
            "business_code": "activity",
            "access_by": "测试人员"
        }
        
        result = self.run_workflow(inputs)
        
        # 验证结果
        self.validate_basic_parsing_result(result)
        
        return result
    
    def test_complex_strategy_parsing(self):
        """测试复杂策略解析功能"""
        logger.info("开始测试复杂策略解析功能")
        
        test_document = """
        接入说明：
        风控接入说明
        活动策略：场景分级3级。
        
        设备检测：设备模型
        1、（活动各环节共累积阈值），活动全程，同设备成功参与该活动（请求返回PASS\\REVIEW）人数>3，REJECT。
        对内外话术：同设备参与该活动的账号较多，请更换新设备尝试。
        
        2、设备命中异常标签：疑似伪造设备（MVP/比心vip >= 25 或 爵位>= 33 或 星爵>= 36级降级；其余返回REJECT），注销频度异常关联设备，低安装包版本&的操作系统设备，低设备内存且新生成设备指纹、低设备内存且剩余内存异常，REVIEW。
        
        IP检测：
        3、同IP一天内参与该活动人数>8，REJECT。
        对内外话术：同IP参与该活动的账号较多，请更换网络环境尝试。
        
        4、1小时内同IP登录失败次数>=5，REVIEW。
        
        名单检测：
        5、命中黑名单，REJECT。
        
        账号检测：
        6、用户等级检测：MVP/比心vip < 12 且 爵位 < 3 且 星爵 < 11，进行严格检测。
        
        业务检测：
        7、10天内同用户参与该活动次数>5，REJECT。
        
        行为检测：
        8、1分钟内同用户请求次数>10，REJECT。
        
        一、接入事件：
        名称：春节活动-抽奖      code：spring-festival-lottery
        参数文档：https://risk.yupaopao.com/index.html#/home/<USER>
        结果处理：
        返回 PASS：正常
        返回 REVIEW：同PASS
        返回 REJECT：获得低价值礼物
        """
        
        inputs = {
            "strategy_document": test_document,
            "event_name": "春节活动-抽奖",
            "event_code": "spring-festival-lottery",
            "business_code": "activity",
            "access_by": "测试人员"
        }
        
        result = self.run_workflow(inputs)
        
        # 验证结果
        self.validate_complex_parsing_result(result)
        
        return result
    
    def validate_basic_parsing_result(self, result: Dict[str, Any]):
        """验证基础解析结果"""
        logger.info("验证基础解析结果")
        
        # 检查是否有错误
        if "error" in result:
            logger.error(f"工作流执行出错: {result['error']}")
            return False
        
        # 检查输出结构
        outputs = result.get("data", {}).get("outputs", {})
        
        # 验证配置验证结果
        validation_result = outputs.get("config_validator", {}).get("validation_result", {})
        if not validation_result.get("valid", False):
            logger.error(f"配置验证失败: {validation_result.get('errors', [])}")
            return False
        
        # 验证生成的配置
        final_config = outputs.get("config_generator", {}).get("final_config", {})
        
        # 检查事件配置
        event_config = final_config.get("event", {})
        assert event_config.get("data", {}).get("name") == "会员中心-抽奖"
        assert event_config.get("data", {}).get("code") == "member-center-lottery"
        
        # 检查因子配置
        factors = final_config.get("factors", [])
        assert len(factors) >= 2  # 至少应该有设备和IP两个因子
        
        # 检查属性配置
        attributes = final_config.get("attributes", [])
        assert len(attributes) >= 2  # 至少应该有对应的属性
        
        # 检查策略原子配置
        atom_rules = final_config.get("atom_rules", [])
        assert len(atom_rules) >= 2  # 至少应该有设备和IP检测规则
        
        # 检查策略组配置
        group_rule = final_config.get("group_rule", {})
        assert group_rule.get("data", {}).get("name") == "会员中心-抽奖-检测"
        
        logger.info("基础解析结果验证通过")
        return True
    
    def validate_complex_parsing_result(self, result: Dict[str, Any]):
        """验证复杂解析结果"""
        logger.info("验证复杂解析结果")
        
        # 检查是否有错误
        if "error" in result:
            logger.error(f"工作流执行出错: {result['error']}")
            return False
        
        # 检查输出结构
        outputs = result.get("data", {}).get("outputs", {})
        
        # 验证策略解析结果
        strategy_analysis = outputs.get("strategy_parser", {}).get("strategy_analysis", {})
        
        # 检查检测模块数量
        detection_modules = strategy_analysis.get("detection_modules", [])
        assert len(detection_modules) >= 5  # 应该识别出至少5种检测类型
        
        # 检查累计条件数量
        accumulation_conditions = strategy_analysis.get("accumulation_conditions", [])
        assert len(accumulation_conditions) >= 6  # 应该识别出至少6个累计条件
        
        # 检查时间窗口
        time_windows = strategy_analysis.get("time_windows", [])
        expected_windows = [1, 60, 1440, 14400]  # 1分钟、1小时、1天、10天
        for window in expected_windows:
            assert window in time_windows
        
        # 检查用户等级条件
        user_level_conditions = strategy_analysis.get("user_level_conditions", {})
        assert "vip_level" in user_level_conditions
        assert "nobility_level" in user_level_conditions
        assert "planet_user_level" in user_level_conditions
        
        logger.info("复杂解析结果验证通过")
        return True
    
    def generate_test_report(self, test_results: Dict[str, Any]):
        """生成测试报告"""
        logger.info("生成测试报告")
        
        report = {
            "test_summary": {
                "total_tests": len(test_results),
                "passed_tests": sum(1 for r in test_results.values() if not r.get("error")),
                "failed_tests": sum(1 for r in test_results.values() if r.get("error"))
            },
            "test_details": test_results,
            "recommendations": []
        }
        
        # 生成建议
        if report["test_summary"]["failed_tests"] > 0:
            report["recommendations"].append("存在测试失败，请检查工作流配置")
        
        if report["test_summary"]["passed_tests"] == report["test_summary"]["total_tests"]:
            report["recommendations"].append("所有测试通过，工作流运行正常")
        
        return report

# 示例使用
if __name__ == "__main__":
    # 配置测试参数
    DIFY_API_URL = "https://api.dify.ai/v1"  # 替换为实际的Dify API地址
    API_KEY = "your-api-key"  # 替换为实际的API密钥
    
    # 创建测试器
    tester = WorkflowTester(DIFY_API_URL, API_KEY)
    
    # 运行测试
    test_results = {}
    
    try:
        # 基础功能测试
        logger.info("开始基础功能测试")
        basic_result = tester.test_basic_strategy_parsing()
        test_results["basic_parsing"] = basic_result
        
        # 复杂功能测试
        logger.info("开始复杂功能测试")
        complex_result = tester.test_complex_strategy_parsing()
        test_results["complex_parsing"] = complex_result
        
        # 生成测试报告
        report = tester.generate_test_report(test_results)
        
        # 输出报告
        print("\n" + "="*50)
        print("测试报告")
        print("="*50)
        print(json.dumps(report, indent=2, ensure_ascii=False))
        
        # 保存报告到文件
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info("测试完成，报告已保存到 test_report.json")
        
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
        print(f"测试失败: {e}")
