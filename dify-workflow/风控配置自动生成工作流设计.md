# 风控配置自动生成Dify工作流设计

## 1. 工作流概述

### 目标
基于策略文档自动生成完整的风控系统配置，包括事件、累计因子、内部外部属性、策略原子、策略组等组件。

### 输入
- 策略文档（结构化文本）
- 事件基本信息（名称、code等）

### 输出
- 完整的风控配置JSON
- 需要创建的组件清单
- 可复用的组件清单
- SQL执行脚本

## 2. 工作流架构

```
输入策略文档
    ↓
[1] 文档预处理节点
    ↓
[2] 策略解析节点 (LLM)
    ↓
[3] 组件识别节点 (LLM)
    ↓
[4] 数据库查询节点 (复用检查)
    ↓
[5] 组件生成节点 (LLM)
    ↓
[6] 配置验证节点
    ↓
[7] 输出格式化节点
    ↓
输出完整配置
```

## 3. 节点详细设计

### 节点1: 文档预处理节点
**类型**: Code节点
**功能**: 
- 清理文档格式
- 提取关键信息段落
- 标准化文本结构

**输入变量**:
- `strategy_document`: 原始策略文档
- `event_name`: 事件名称
- `event_code`: 事件代码

**输出变量**:
- `cleaned_document`: 清理后的文档
- `event_info`: 事件基本信息
- `document_sections`: 文档分段结果

### 节点2: 策略解析节点
**类型**: LLM节点
**模型**: GPT-4或Claude-3.5-Sonnet
**功能**: 解析策略文档，识别各种检测规则和条件

**Prompt模板**:
```
你是一个风控策略专家，请分析以下策略文档，提取出所有的检测规则和条件。

策略文档：
{{cleaned_document}}

事件信息：
- 名称：{{event_info.name}}
- 代码：{{event_info.code}}

请按以下JSON格式输出解析结果：
{
  "detection_modules": [
    {
      "type": "设备检测|IP检测|名单检测|账号检测|业务检测|行为检测",
      "name": "检测模块名称",
      "rules": ["规则1", "规则2"]
    }
  ],
  "accumulation_conditions": [
    {
      "rule_number": 1,
      "description": "规则描述",
      "dimension": "DeviceId|ClientIp|UserId",
      "operator": ">|>=|<|<=|==",
      "threshold": 数值,
      "time_window": "时间窗口(分钟)",
      "action": "REJECT|REVIEW|PASS",
      "inner_reason": "内部原因",
      "outer_reason": "外部话术"
    }
  ],
  "attribute_conditions": [
    {
      "attribute_name": "属性名称",
      "operator": ">|>=|<|<=|==",
      "value": 数值,
      "description": "条件描述"
    }
  ],
  "time_windows": [1440, 60, 10080],
  "user_level_conditions": {
    "vip_level": {"operator": ">=", "value": 25},
    "nobility_level": {"operator": ">=", "value": 33},
    "planet_user_level": {"operator": ">=", "value": 36}
  }
}
```

### 节点3: 组件识别节点
**类型**: LLM节点
**功能**: 基于解析结果识别需要创建的风控组件

**Prompt模板**:
```
基于以下策略解析结果，识别需要创建的风控组件。

解析结果：
{{strategy_analysis}}

风控系统组件说明：
1. 累计因子(Factor): 用于统计计算，如"24小时内同设备用户数"
2. 内部属性(LOCAL Attribute): 基于因子计算的本地属性
3. 外部属性(REMOTE Attribute): 调用外部服务获取的属性
4. 策略原子(AtomRule): 具体的检测规则
5. 策略组(GroupRule): 策略的组织单位

请输出需要创建的组件清单：
{
  "factors": [
    {
      "name": "因子名称",
      "group_key": "统计维度",
      "agg_key": "聚合键",
      "function": "COUNT_DISTINCT|COUNT|SUM",
      "time_span": 时间窗口分钟数,
      "condition": "过滤条件",
      "comment": "因子说明"
    }
  ],
  "local_attributes": [
    {
      "name": "属性名称",
      "dependent": "依赖的因子ID和维度",
      "function": "getFactorValue",
      "comment": "属性说明"
    }
  ],
  "remote_attributes": [
    {
      "name": "属性名称",
      "dependent": "外部服务参数",
      "function": "服务函数名",
      "result_key": "结果键",
      "comment": "属性说明"
    }
  ],
  "atom_rules": [
    {
      "name": "规则名称",
      "condition": "检测条件表达式",
      "dependent": "依赖的属性列表",
      "action": "REJECT|REVIEW|PASS",
      "inner_reason": "内部原因",
      "outer_reason": "外部话术",
      "comment": "规则说明"
    }
  ],
  "group_rule": {
    "name": "策略组名称",
    "comment": "策略组说明"
  }
}
```

### 节点4: 数据库查询节点
**类型**: Code节点
**功能**: 查询数据库检查可复用的组件

**代码逻辑**:
```python
import requests
import json

def query_existing_components(component_list):
    """查询数据库中已存在的组件"""
    
    # 查询已存在的因子
    existing_factors = []
    for factor in component_list.get('factors', []):
        # 调用API查询相似因子
        similar_factors = query_similar_factors(factor)
        if similar_factors:
            existing_factors.extend(similar_factors)
    
    # 查询已存在的属性
    existing_attributes = []
    for attr in component_list.get('local_attributes', []) + component_list.get('remote_attributes', []):
        existing_attr = query_attribute_by_name(attr['name'])
        if existing_attr:
            existing_attributes.append(existing_attr)
    
    # 查询已存在的事件
    existing_event = query_event_by_code(event_code)
    
    return {
        'existing_factors': existing_factors,
        'existing_attributes': existing_attributes,
        'existing_event': existing_event,
        'reuse_suggestions': generate_reuse_suggestions()
    }

def query_similar_factors(factor_spec):
    """查询相似的因子"""
    # 实现因子相似度匹配逻辑
    pass

def query_attribute_by_name(attr_name):
    """根据名称查询属性"""
    # 实现属性查询逻辑
    pass
```

### 节点5: 组件生成节点
**类型**: LLM节点
**功能**: 基于复用检查结果生成最终的组件配置

**Prompt模板**:
```
基于以下信息生成最终的风控组件配置：

需要创建的组件：
{{component_list}}

已存在可复用的组件：
{{existing_components}}

风控系统数据模型参考：
- risk_event: 事件表，包含name, code, business_code, rule_group_id等字段
- risk_factor: 因子表，包含group_key, agg_key, _function, time_span, _condition等字段
- risk_attribute: 属性表，包含name, type, dependent, _function等字段
- risk_rule_atom: 策略原子表，包含name, _condition, dependent, reply等字段
- risk_rule_group: 策略组表，包含name, _condition, dependent等字段

请生成完整的配置，优先复用已存在的组件：
{
  "event": {
    "action": "create|reuse",
    "data": {事件配置}
  },
  "factors": [
    {
      "action": "create|reuse",
      "reuse_id": "复用的ID(如果是复用)",
      "data": {因子配置}
    }
  ],
  "attributes": [
    {
      "action": "create|reuse",
      "reuse_id": "复用的ID(如果是复用)",
      "data": {属性配置}
    }
  ],
  "atom_rules": [
    {
      "action": "create",
      "data": {策略原子配置}
    }
  ],
  "group_rule": {
    "action": "create",
    "data": {策略组配置}
  },
  "rule_relations": [
    {
      "rule_id": "策略原子ID",
      "group_id": "策略组ID"
    }
  ]
}
```

### 节点6: 配置验证节点
**类型**: Code节点
**功能**: 验证生成的配置是否正确

### 节点7: 输出格式化节点
**类型**: Code节点
**功能**: 格式化最终输出，生成SQL脚本

## 4. 工作流配置文件

接下来我将提供具体的Dify工作流JSON配置文件。
