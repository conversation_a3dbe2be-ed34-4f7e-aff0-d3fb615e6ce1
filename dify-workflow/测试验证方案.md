# 风控配置自动生成系统测试验证方案

## 1. 测试策略概述

### 1.1 测试目标
- 验证Dify工作流的功能完整性和准确性
- 确保生成的风控配置符合系统要求
- 验证组件复用逻辑的正确性
- 测试系统的稳定性和性能

### 1.2 测试范围
- 策略文档解析准确性
- 风控组件生成完整性
- 数据库查询和复用逻辑
- 配置验证和SQL生成
- 端到端工作流测试

### 1.3 测试环境
- **开发环境**: 用于功能开发和单元测试
- **测试环境**: 用于集成测试和系统测试
- **预生产环境**: 用于用户验收测试
- **生产环境**: 用于生产部署验证

## 2. 测试用例设计

### 2.1 基础功能测试用例

#### 测试用例1: 简单策略解析
**测试目标**: 验证基本策略文档的解析能力

**输入数据**:
```
策略文档: 包含1-2个简单检测规则
事件名称: 测试活动-抽奖
事件代码: test-activity-lottery
```

**预期结果**:
- 正确识别检测模块类型
- 准确提取阈值条件
- 正确转换时间窗口
- 生成对应的因子和属性

**验证标准**:
- 解析结果JSON格式正确
- 关键字段值准确
- 无遗漏的必要组件

#### 测试用例2: 复杂策略解析
**测试目标**: 验证复杂策略文档的解析能力

**输入数据**:
```
策略文档: 包含6种检测类型，10+个规则
事件名称: 复杂活动-抽奖
事件代码: complex-activity-lottery
```

**预期结果**:
- 识别所有检测模块
- 正确解析用户等级条件
- 处理多种时间窗口
- 生成完整的组件配置

#### 测试用例3: 边界条件测试
**测试目标**: 验证边界条件的处理能力

**输入数据**:
```
- 空策略文档
- 格式错误的文档
- 缺少关键信息的文档
- 包含特殊字符的文档
```

**预期结果**:
- 优雅处理错误情况
- 返回有意义的错误信息
- 不会导致系统崩溃

### 2.2 组件复用测试用例

#### 测试用例4: 事件复用测试
**测试目标**: 验证已存在事件的复用逻辑

**前置条件**: 数据库中存在相同code的事件

**预期结果**:
- 正确识别已存在的事件
- 标记为复用而非新建
- 保持原有事件配置

#### 测试用例5: 因子复用测试
**测试目标**: 验证相似因子的复用逻辑

**前置条件**: 数据库中存在相似时间窗口和维度的因子

**预期结果**:
- 正确匹配相似因子
- 计算相似度得分
- 优先选择最匹配的因子

#### 测试用例6: 属性复用测试
**测试目标**: 验证属性复用的准确性

**前置条件**: 数据库中存在相同名称的属性

**预期结果**:
- 准确识别可复用属性
- 避免重复创建
- 保持依赖关系正确

### 2.3 数据质量测试用例

#### 测试用例7: 配置完整性验证
**测试目标**: 验证生成配置的完整性

**验证项目**:
- 所有必填字段都有值
- 字段值符合数据类型要求
- 依赖关系正确建立
- 外键引用有效

#### 测试用例8: SQL脚本验证
**测试目标**: 验证生成的SQL脚本正确性

**验证项目**:
- SQL语法正确
- 字段映射准确
- 数据类型匹配
- 约束条件满足

#### 测试用例9: 业务逻辑验证
**测试目标**: 验证生成配置的业务逻辑正确性

**验证项目**:
- 检测条件表达式正确
- 阈值设置合理
- 时间窗口配置正确
- 动作映射准确

## 3. 自动化测试实现

### 3.1 单元测试框架
```python
import unittest
import json
from dify_workflow_tester import WorkflowTester

class TestStrategyParsing(unittest.TestCase):
    
    def setUp(self):
        self.tester = WorkflowTester(
            dify_api_url="https://api.dify.ai/v1",
            api_key="test-api-key"
        )
    
    def test_basic_strategy_parsing(self):
        """测试基础策略解析"""
        inputs = {
            "strategy_document": self.load_test_document("basic_strategy.txt"),
            "event_name": "测试活动-抽奖",
            "event_code": "test-activity-lottery"
        }
        
        result = self.tester.run_workflow(inputs)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertNotIn("error", result)
        
        # 验证解析结果
        strategy_analysis = result["data"]["outputs"]["strategy_parser"]["strategy_analysis"]
        self.assertGreater(len(strategy_analysis["detection_modules"]), 0)
        self.assertGreater(len(strategy_analysis["accumulation_conditions"]), 0)
    
    def test_component_generation(self):
        """测试组件生成"""
        # 实现组件生成测试逻辑
        pass
    
    def test_database_query(self):
        """测试数据库查询"""
        # 实现数据库查询测试逻辑
        pass
    
    def load_test_document(self, filename):
        """加载测试文档"""
        with open(f"test_data/{filename}", "r", encoding="utf-8") as f:
            return f.read()

if __name__ == "__main__":
    unittest.main()
```

### 3.2 集成测试脚本
```python
class IntegrationTestSuite:
    """集成测试套件"""
    
    def __init__(self):
        self.test_cases = self.load_test_cases()
        self.results = []
    
    def run_all_tests(self):
        """运行所有集成测试"""
        for test_case in self.test_cases:
            result = self.run_single_test(test_case)
            self.results.append(result)
        
        return self.generate_report()
    
    def run_single_test(self, test_case):
        """运行单个测试用例"""
        try:
            # 执行工作流
            workflow_result = self.execute_workflow(test_case["inputs"])
            
            # 验证结果
            validation_result = self.validate_result(
                workflow_result, 
                test_case["expected"]
            )
            
            return {
                "test_case": test_case["name"],
                "status": "PASS" if validation_result["valid"] else "FAIL",
                "details": validation_result
            }
            
        except Exception as e:
            return {
                "test_case": test_case["name"],
                "status": "ERROR",
                "error": str(e)
            }
    
    def validate_result(self, actual, expected):
        """验证测试结果"""
        validation_errors = []
        
        # 验证配置完整性
        if not self.validate_config_completeness(actual):
            validation_errors.append("配置不完整")
        
        # 验证数据质量
        if not self.validate_data_quality(actual):
            validation_errors.append("数据质量不符合要求")
        
        # 验证业务逻辑
        if not self.validate_business_logic(actual, expected):
            validation_errors.append("业务逻辑不正确")
        
        return {
            "valid": len(validation_errors) == 0,
            "errors": validation_errors
        }
```

### 3.3 性能测试脚本
```python
import time
import concurrent.futures
from statistics import mean, median

class PerformanceTestSuite:
    """性能测试套件"""
    
    def test_response_time(self):
        """测试响应时间"""
        response_times = []
        
        for i in range(10):
            start_time = time.time()
            result = self.run_workflow(self.get_test_input())
            end_time = time.time()
            
            response_times.append(end_time - start_time)
        
        return {
            "average_response_time": mean(response_times),
            "median_response_time": median(response_times),
            "max_response_time": max(response_times),
            "min_response_time": min(response_times)
        }
    
    def test_concurrent_requests(self):
        """测试并发请求处理能力"""
        concurrent_count = 5
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = []
            start_time = time.time()
            
            for i in range(concurrent_count):
                future = executor.submit(self.run_workflow, self.get_test_input())
                futures.append(future)
            
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
            end_time = time.time()
        
        return {
            "concurrent_requests": concurrent_count,
            "total_time": end_time - start_time,
            "success_count": sum(1 for r in results if "error" not in r),
            "failure_count": sum(1 for r in results if "error" in r)
        }
```

## 4. 测试数据管理

### 4.1 测试数据集
```
test_data/
├── basic_strategies/
│   ├── simple_device_detection.txt
│   ├── simple_ip_detection.txt
│   └── simple_account_detection.txt
├── complex_strategies/
│   ├── multi_module_strategy.txt
│   ├── complex_conditions_strategy.txt
│   └── edge_case_strategy.txt
├── edge_cases/
│   ├── empty_document.txt
│   ├── malformed_document.txt
│   └── special_characters.txt
└── expected_results/
    ├── basic_expected.json
    ├── complex_expected.json
    └── edge_case_expected.json
```

### 4.2 测试数据维护
- 定期更新测试数据集
- 添加新的业务场景测试用例
- 维护预期结果的准确性
- 版本控制测试数据变更

## 5. 测试执行和报告

### 5.1 测试执行流程
1. **环境准备**: 配置测试环境和数据
2. **测试执行**: 运行自动化测试套件
3. **结果收集**: 收集测试结果和日志
4. **报告生成**: 生成详细的测试报告
5. **问题跟踪**: 记录和跟踪发现的问题

### 5.2 测试报告模板
```json
{
  "test_summary": {
    "total_tests": 50,
    "passed_tests": 45,
    "failed_tests": 3,
    "error_tests": 2,
    "pass_rate": "90%",
    "execution_time": "15 minutes"
  },
  "test_categories": {
    "unit_tests": {"pass": 20, "fail": 0, "error": 0},
    "integration_tests": {"pass": 15, "fail": 2, "error": 1},
    "performance_tests": {"pass": 8, "fail": 1, "error": 1},
    "edge_case_tests": {"pass": 2, "fail": 0, "error": 0}
  },
  "failed_tests": [
    {
      "test_name": "complex_strategy_parsing",
      "error_message": "时间窗口转换错误",
      "expected": "1440分钟",
      "actual": "1天"
    }
  ],
  "performance_metrics": {
    "average_response_time": "3.2秒",
    "max_response_time": "8.5秒",
    "concurrent_capacity": "5个并发请求"
  },
  "recommendations": [
    "优化时间窗口转换逻辑",
    "增加错误处理机制",
    "提高并发处理能力"
  ]
}
```

## 6. 持续集成和部署

### 6.1 CI/CD集成
- 在代码提交时自动运行测试
- 测试失败时阻止部署
- 生成测试覆盖率报告
- 自动化测试结果通知

### 6.2 监控和告警
- 监控工作流执行成功率
- 设置响应时间告警阈值
- 跟踪配置生成质量指标
- 定期生成质量报告

通过完整的测试验证方案，可以确保风控配置自动生成系统的质量和可靠性，为生产环境的稳定运行提供保障。
