# 数据库配置指南

## 1. 环境变量配置

在Dify中配置数据库连接需要设置以下环境变量：

### 1.1 在Dify平台配置环境变量

1. 进入你的Dify工作空间
2. 找到"设置" → "环境变量"
3. 添加以下环境变量：

```
DB_HOST=你的数据库主机地址
DB_PORT=3306
DB_USER=你的数据库用户名
DB_PASSWORD=你的数据库密码
DB_NAME=ypp_fengkong
```

### 1.2 具体配置示例

```
DB_HOST=*************
DB_PORT=3306
DB_USER=risk_user
DB_PASSWORD=your_password_here
DB_NAME=ypp_fengkong
```

## 2. 数据库权限要求

确保配置的数据库用户具有以下权限：

```sql
-- 对风控数据库的查询权限
GRANT SELECT ON ypp_fengkong.risk_event TO 'risk_user'@'%';
GRANT SELECT ON ypp_fengkong.risk_factor TO 'risk_user'@'%';
GRANT SELECT ON ypp_fengkong.risk_attribute TO 'risk_user'@'%';
GRANT SELECT ON ypp_fengkong.risk_rule_atom TO 'risk_user'@'%';
GRANT SELECT ON ypp_fengkong.risk_rule_group TO 'risk_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 3. 网络连接配置

### 3.1 防火墙设置
确保Dify服务器可以访问数据库服务器的3306端口：

```bash
# 在数据库服务器上开放3306端口
sudo ufw allow 3306
```

### 3.2 MySQL配置
确保MySQL允许远程连接：

```sql
-- 检查bind-address配置
SHOW VARIABLES LIKE 'bind_address';

-- 如果需要，修改 /etc/mysql/mysql.conf.d/mysqld.cnf
-- bind-address = 0.0.0.0
```

## 4. 连接测试

### 4.1 手动测试连接
在Dify服务器上测试数据库连接：

```bash
mysql -h 你的数据库主机 -P 3306 -u risk_user -p ypp_fengkong
```

### 4.2 Python测试脚本
```python
import mysql.connector
import os

try:
    connection = mysql.connector.connect(
        host=os.getenv('DB_HOST'),
        port=int(os.getenv('DB_PORT', '3306')),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD'),
        database=os.getenv('DB_NAME')
    )
    
    if connection.is_connected():
        print("数据库连接成功")
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM risk_event")
        result = cursor.fetchone()
        print(f"事件表记录数: {result[0]}")
        cursor.close()
        connection.close()
    
except Exception as e:
    print(f"连接失败: {e}")
```

## 5. 安全建议

### 5.1 数据库用户权限最小化
```sql
-- 创建专用的只读用户
CREATE USER 'dify_readonly'@'%' IDENTIFIED BY 'strong_password_here';

-- 只授予必要的查询权限
GRANT SELECT ON ypp_fengkong.risk_event TO 'dify_readonly'@'%';
GRANT SELECT ON ypp_fengkong.risk_factor TO 'dify_readonly'@'%';
GRANT SELECT ON ypp_fengkong.risk_attribute TO 'dify_readonly'@'%';
GRANT SELECT ON ypp_fengkong.risk_rule_atom TO 'dify_readonly'@'%';
GRANT SELECT ON ypp_fengkong.risk_rule_group TO 'dify_readonly'@'%';

FLUSH PRIVILEGES;
```

### 5.2 连接池配置
在生产环境中，建议配置连接池：

```python
# 在代码节点中可以这样配置
connection_config = {
    'host': os.getenv('DB_HOST'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'charset': 'utf8mb4',
    'autocommit': True,
    'pool_name': 'risk_pool',
    'pool_size': 5,
    'pool_reset_session': True
}
```

## 6. 故障排除

### 6.1 常见错误

**错误1**: `mysql.connector.errors.DatabaseError: 2003 (HY000): Can't connect to MySQL server`
- 检查数据库服务器是否运行
- 检查网络连接和防火墙设置
- 验证主机地址和端口

**错误2**: `mysql.connector.errors.ProgrammingError: 1045 (28000): Access denied`
- 检查用户名和密码
- 验证用户权限
- 确认用户可以从Dify服务器IP连接

**错误3**: `mysql.connector.errors.ProgrammingError: 1049 (42000): Unknown database`
- 检查数据库名称是否正确
- 确认数据库存在

### 6.2 调试方法

1. **检查环境变量**：
   ```python
   import os
   print("DB_HOST:", os.getenv('DB_HOST'))
   print("DB_PORT:", os.getenv('DB_PORT'))
   print("DB_USER:", os.getenv('DB_USER'))
   print("DB_NAME:", os.getenv('DB_NAME'))
   ```

2. **测试基本连接**：
   ```python
   import mysql.connector
   try:
       conn = mysql.connector.connect(
           host='your_host',
           user='your_user',
           password='your_password'
       )
       print("基本连接成功")
       conn.close()
   except Exception as e:
       print(f"连接失败: {e}")
   ```

3. **检查表结构**：
   ```sql
   SHOW TABLES LIKE 'risk_%';
   DESCRIBE risk_event;
   ```

## 7. 性能优化

### 7.1 索引优化
确保查询相关的字段有适当的索引：

```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_risk_event_code ON risk_event(code);
CREATE INDEX idx_risk_factor_time_span ON risk_factor(time_span);
CREATE INDEX idx_risk_factor_group_key ON risk_factor(group_key);
CREATE INDEX idx_risk_attribute_name ON risk_attribute(name);
```

### 7.2 查询优化
- 使用LIMIT限制返回结果数量
- 避免SELECT *，只查询需要的字段
- 使用合适的WHERE条件过滤数据

通过以上配置，你的Dify工作流就可以连接到真实的风控数据库了！
