{"version": "0.1.0", "kind": "workflow", "graph": {"nodes": [{"id": "start", "type": "start", "data": {"title": "开始", "variables": [{"variable": "strategy_document", "type": "string", "required": true, "label": "策略文档"}, {"variable": "event_name", "type": "string", "required": true, "label": "事件名称"}, {"variable": "event_code", "type": "string", "required": true, "label": "事件代码"}, {"variable": "business_code", "type": "string", "required": false, "label": "业务代码", "default": "activity"}, {"variable": "access_by", "type": "string", "required": false, "label": "接入人"}]}}, {"id": "document_preprocessor", "type": "code", "data": {"title": "文档预处理", "code": "import re\nimport json\n\ndef main(strategy_document: str, event_name: str, event_code: str, business_code: str = \"activity\", access_by: str = \"\") -> dict:\n    # 清理文档格式\n    cleaned_document = re.sub(r'\\\\n', '\\n', strategy_document)\n    cleaned_document = re.sub(r'\\s+', ' ', cleaned_document)\n    \n    # 提取事件信息\n    event_info = {\n        \"name\": event_name,\n        \"code\": event_code,\n        \"business_code\": business_code,\n        \"access_by\": access_by\n    }\n    \n    # 文档分段\n    sections = {\n        \"device_detection\": extract_section(cleaned_document, \"设备检测\"),\n        \"ip_detection\": extract_section(cleaned_document, \"IP检测\"),\n        \"blacklist_detection\": extract_section(cleaned_document, \"名单检测\"),\n        \"account_detection\": extract_section(cleaned_document, \"账号检测\"),\n        \"business_detection\": extract_section(cleaned_document, \"业务检测\"),\n        \"behavior_detection\": extract_section(cleaned_document, \"行为检测\")\n    }\n    \n    return {\n        \"cleaned_document\": cleaned_document,\n        \"event_info\": event_info,\n        \"document_sections\": sections\n    }\n\ndef extract_section(document: str, section_name: str) -> str:\n    \"\"\"提取文档中的特定段落\"\"\"\n    pattern = rf'{section_name}[：:](.*?)(?=\\n[一二三四五六七八九十]|\\n[A-Z]|$)'\n    match = re.search(pattern, document, re.DOTALL | re.IGNORECASE)\n    return match.group(1).strip() if match else \"\"\n", "outputs": {"cleaned_document": "string", "event_info": "object", "document_sections": "object"}}}, {"id": "strategy_parser", "type": "llm", "data": {"title": "策略解析", "model": {"provider": "openai", "name": "gpt-4-turbo-preview", "mode": "chat", "completion_params": {"temperature": 0.1, "max_tokens": 4000}}, "prompt_template": [{"role": "system", "text": "你是一个风控策略专家，擅长分析策略文档并提取检测规则。请严格按照JSON格式输出，不要添加任何额外的文字说明。"}, {"role": "user", "text": "请分析以下策略文档，提取出所有的检测规则和条件。\n\n策略文档：\n{{#document_preprocessor.cleaned_document#}}\n\n事件信息：\n- 名称：{{#document_preprocessor.event_info.name#}}\n- 代码：{{#document_preprocessor.event_info.code#}}\n\n请按以下JSON格式输出解析结果：\n{\n  \"detection_modules\": [\n    {\n      \"type\": \"设备检测|IP检测|名单检测|账号检测|业务检测|行为检测\",\n      \"name\": \"检测模块名称\",\n      \"rules\": [\"规则1\", \"规则2\"]\n    }\n  ],\n  \"accumulation_conditions\": [\n    {\n      \"rule_number\": 1,\n      \"description\": \"规则描述\",\n      \"dimension\": \"DeviceId|ClientIp|UserId\",\n      \"operator\": \">|>=|<|<=|==\",\n      \"threshold\": 数值,\n      \"time_window\": 时间窗口分钟数,\n      \"action\": \"REJECT|REVIEW|PASS\",\n      \"inner_reason\": \"内部原因\",\n      \"outer_reason\": \"外部话术\"\n    }\n  ],\n  \"attribute_conditions\": [\n    {\n      \"attribute_name\": \"vipLevel|nobilityLevel|planetUserLevel\",\n      \"operator\": \">|>=|<|<=|==\",\n      \"value\": 数值,\n      \"description\": \"条件描述\"\n    }\n  ],\n  \"time_windows\": [1440, 60, 10080],\n  \"user_level_conditions\": {\n    \"vip_level\": {\"operator\": \">=\", \"value\": 25},\n    \"nobility_level\": {\"operator\": \">=\", \"value\": 33},\n    \"planet_user_level\": {\"operator\": \">=\", \"value\": 36}\n  }\n}"}], "outputs": {"strategy_analysis": "object"}}}, {"id": "component_identifier", "type": "llm", "data": {"title": "组件识别", "model": {"provider": "openai", "name": "gpt-4-turbo-preview", "mode": "chat", "completion_params": {"temperature": 0.1, "max_tokens": 4000}}, "prompt_template": [{"role": "system", "text": "你是风控系统架构专家，擅长将策略需求转换为具体的系统组件配置。请严格按照JSON格式输出。"}, {"role": "user", "text": "基于以下策略解析结果，识别需要创建的风控组件。\n\n解析结果：\n{{#strategy_parser.strategy_analysis#}}\n\n事件信息：\n{{#document_preprocessor.event_info#}}\n\n风控系统组件说明：\n1. 累计因子(Factor): 用于统计计算，如\"24小时内同设备用户数\"\n   - group_key: 统计维度，如\"Event,DeviceId,type\"\n   - agg_key: 聚合键，通常是\"UserId\"\n   - function: 统计函数，如\"COUNT_DISTINCT\"\n   - time_span: 时间窗口（分钟）\n   - condition: 过滤条件，如\"#Event=='event-code' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')\"\n\n2. 内部属性(LOCAL Attribute): 基于因子计算\n   - dependent: 格式为\"因子ID::::维度1::::维度2\"\n   - function: \"getFactorValue\"\n\n3. 外部属性(REMOTE Attribute): 调用外部服务\n   - dependent: 外部服务参数，如\"UserId::::cache+=\"\n   - function: 服务函数名，如\"userData\"\n\n请输出需要创建的组件清单：\n{\n  \"factors\": [\n    {\n      \"name\": \"因子名称\",\n      \"group_key\": \"Event,DeviceId,type\",\n      \"agg_key\": \"UserId\",\n      \"function\": \"COUNT_DISTINCT\",\n      \"time_span\": 1440,\n      \"condition\": \"#Event=='{{#document_preprocessor.event_info.code#}}' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')\",\n      \"comment\": \"因子说明\"\n    }\n  ],\n  \"local_attributes\": [\n    {\n      \"name\": \"属性名称\",\n      \"dependent\": \"\\\"因子ID\\\"::::Event::::DeviceId::::type\",\n      \"function\": \"getFactorValue\",\n      \"comment\": \"属性说明\"\n    }\n  ],\n  \"remote_attributes\": [\n    {\n      \"name\": \"userData\",\n      \"dependent\": \"UserId::::cache+=\",\n      \"function\": \"userData\",\n      \"result_key\": \"*\",\n      \"comment\": \"用户数据属性\"\n    }\n  ],\n  \"atom_rules\": [\n    {\n      \"name\": \"规则名称\",\n      \"condition\": \"检测条件表达式\",\n      \"dependent\": \"属性1,属性2\",\n      \"action\": \"REJECT|REVIEW|PASS\",\n      \"inner_reason\": \"内部原因\",\n      \"outer_reason\": \"外部话术\",\n      \"comment\": \"规则说明\"\n    }\n  ],\n  \"group_rule\": {\n    \"name\": \"{{#document_preprocessor.event_info.name#}}-检测\",\n    \"comment\": \"自动生成的策略组\"\n  }\n}"}], "outputs": {"component_list": "object"}}}, {"id": "database_query", "type": "code", "data": {"title": "数据库查询复用检查", "code": "import requests\nimport json\n\ndef main(component_list: dict, event_info: dict) -> dict:\n    \"\"\"查询数据库检查可复用的组件\"\"\"\n    \n    # 查询已存在的事件\n    existing_event = query_event_by_code(event_info.get('code'))\n    \n    # 查询相似的因子\n    existing_factors = []\n    for factor in component_list.get('factors', []):\n        similar_factors = query_similar_factors(factor)\n        existing_factors.extend(similar_factors)\n    \n    # 查询已存在的属性\n    existing_attributes = []\n    all_attributes = component_list.get('local_attributes', []) + component_list.get('remote_attributes', [])\n    for attr in all_attributes:\n        existing_attr = query_attribute_by_name(attr.get('name'))\n        if existing_attr:\n            existing_attributes.append(existing_attr)\n    \n    # 生成复用建议\n    reuse_suggestions = generate_reuse_suggestions(existing_factors, existing_attributes, existing_event)\n    \n    return {\n        'existing_event': existing_event,\n        'existing_factors': existing_factors,\n        'existing_attributes': existing_attributes,\n        'reuse_suggestions': reuse_suggestions\n    }\n\ndef query_event_by_code(event_code: str) -> dict:\n    \"\"\"根据事件代码查询已存在的事件\"\"\"\n    # 这里应该调用实际的数据库API\n    # 示例返回格式\n    return {\n        'found': False,\n        'data': None\n    }\n\ndef query_similar_factors(factor_spec: dict) -> list:\n    \"\"\"查询相似的因子\"\"\"\n    # 实现因子相似度匹配逻辑\n    return []\n\ndef query_attribute_by_name(attr_name: str) -> dict:\n    \"\"\"根据名称查询属性\"\"\"\n    # 实现属性查询逻辑\n    return None\n\ndef generate_reuse_suggestions(factors, attributes, event) -> dict:\n    \"\"\"生成复用建议\"\"\"\n    return {\n        'can_reuse_event': event is not None,\n        'reusable_factors_count': len(factors),\n        'reusable_attributes_count': len(attributes)\n    }\n", "outputs": {"existing_event": "object", "existing_factors": "array", "existing_attributes": "array", "reuse_suggestions": "object"}}}, {"id": "config_generator", "type": "llm", "data": {"title": "配置生成", "model": {"provider": "openai", "name": "gpt-4-turbo-preview", "mode": "chat", "completion_params": {"temperature": 0.1, "max_tokens": 4000}}, "prompt_template": [{"role": "system", "text": "你是风控系统配置专家，负责生成完整的风控组件配置。请严格按照数据库表结构生成配置，优先复用已存在的组件。"}, {"role": "user", "text": "基于以下信息生成最终的风控组件配置：\n\n需要创建的组件：\n{{#component_identifier.component_list#}}\n\n已存在可复用的组件：\n- 事件：{{#database_query.existing_event#}}\n- 因子：{{#database_query.existing_factors#}}\n- 属性：{{#database_query.existing_attributes#}}\n\n复用建议：\n{{#database_query.reuse_suggestions#}}\n\n事件信息：\n{{#document_preprocessor.event_info#}}\n\n风控系统数据模型参考：\n- risk_event: name, code, business_code, rule_group_id, reply, comment, default_level, can_punish, types\n- risk_factor: group_key, agg_key, _function, time_span, _condition, comment, business, window_type\n- risk_attribute: name, type(LOCAL/REMOTE), dependent, _function, result_key\n- risk_rule_atom: name, _condition, dependent, reply, inner_reason, outer_reason, priority, accuracy, status\n- risk_rule_group: name, _condition, dependent, status, comment, priority\n\n请生成完整的配置，优先复用已存在的组件：\n{\n  \"event\": {\n    \"action\": \"create|reuse\",\n    \"reuse_id\": \"复用的ID(如果是复用)\",\n    \"data\": {\n      \"name\": \"{{#document_preprocessor.event_info.name#}}\",\n      \"code\": \"{{#document_preprocessor.event_info.code#}}\",\n      \"business_code\": \"{{#document_preprocessor.event_info.business_code#}}\",\n      \"access_by\": \"{{#document_preprocessor.event_info.access_by#}}\",\n      \"default_level\": \"PASS\",\n      \"can_punish\": true,\n      \"types\": \"1\",\n      \"comment\": \"自动生成的事件配置\"\n    }\n  },\n  \"factors\": [\n    {\n      \"action\": \"create|reuse\",\n      \"reuse_id\": \"复用的ID(如果是复用)\",\n      \"data\": {\n        \"group_key\": \"Event,DeviceId,type\",\n        \"agg_key\": \"UserId\",\n        \"_function\": \"COUNT_DISTINCT\",\n        \"time_span\": 1440,\n        \"_condition\": \"#Event=='{{#document_preprocessor.event_info.code#}}' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')\",\n        \"comment\": \"因子说明\",\n        \"business\": 1,\n        \"window_type\": 1\n      }\n    }\n  ],\n  \"attributes\": [\n    {\n      \"action\": \"create|reuse\",\n      \"reuse_id\": \"复用的ID(如果是复用)\",\n      \"data\": {\n        \"name\": \"属性名称\",\n        \"type\": \"LOCAL|REMOTE\",\n        \"dependent\": \"依赖配置\",\n        \"_function\": \"getFactorValue|userData|等\",\n        \"result_key\": \"结果键\"\n      }\n    }\n  ],\n  \"atom_rules\": [\n    {\n      \"action\": \"create\",\n      \"data\": {\n        \"name\": \"规则名称\",\n        \"_condition\": \"检测条件表达式\",\n        \"dependent\": \"依赖的属性列表(逗号分隔)\",\n        \"reply\": \"外部话术\",\n        \"inner_reason\": \"内部原因\",\n        \"outer_reason\": \"外部话术\",\n        \"priority\": 0,\n        \"accuracy\": 100,\n        \"status\": \"DISABLE\",\n        \"can_fuse\": true,\n        \"type\": 0\n      }\n    }\n  ],\n  \"group_rule\": {\n    \"action\": \"create\",\n    \"data\": {\n      \"name\": \"{{#document_preprocessor.event_info.name#}}-检测\",\n      \"_condition\": \"\",\n      \"dependent\": \"\",\n      \"status\": \"DISABLE\",\n      \"comment\": \"自动生成的策略组\",\n      \"priority\": 0\n    }\n  }\n}"}], "outputs": {"final_config": "object"}}}, {"id": "config_validator", "type": "code", "data": {"title": "配置验证", "code": "import json\n\ndef main(final_config: dict) -> dict:\n    \"\"\"验证生成的配置是否正确\"\"\"\n    \n    errors = []\n    warnings = []\n    suggestions = []\n    \n    # 验证事件配置\n    event_config = final_config.get('event', {})\n    if not event_config.get('data', {}).get('name'):\n        errors.append('事件名称不能为空')\n    if not event_config.get('data', {}).get('code'):\n        errors.append('事件代码不能为空')\n    \n    # 验证因子配置\n    factors = final_config.get('factors', [])\n    for i, factor in enumerate(factors):\n        factor_data = factor.get('data', {})\n        if not factor_data.get('group_key'):\n            errors.append(f'因子{i+1}的group_key不能为空')\n        if not factor_data.get('_function'):\n            errors.append(f'因子{i+1}的_function不能为空')\n        if not factor_data.get('time_span'):\n            errors.append(f'因子{i+1}的time_span不能为空')\n    \n    # 验证属性配置\n    attributes = final_config.get('attributes', [])\n    for i, attr in enumerate(attributes):\n        attr_data = attr.get('data', {})\n        if not attr_data.get('name'):\n            errors.append(f'属性{i+1}的name不能为空')\n        if not attr_data.get('type'):\n            errors.append(f'属性{i+1}的type不能为空')\n        if attr_data.get('type') not in ['LOCAL', 'REMOTE']:\n            errors.append(f'属性{i+1}的type必须是LOCAL或REMOTE')\n    \n    # 验证策略原子配置\n    atom_rules = final_config.get('atom_rules', [])\n    for i, rule in enumerate(atom_rules):\n        rule_data = rule.get('data', {})\n        if not rule_data.get('name'):\n            errors.append(f'策略原子{i+1}的name不能为空')\n        if not rule_data.get('_condition'):\n            errors.append(f'策略原子{i+1}的_condition不能为空')\n    \n    # 生成建议\n    if len(factors) == 0:\n        warnings.append('没有生成任何因子，请检查策略文档中是否包含累计条件')\n    if len(atom_rules) == 0:\n        warnings.append('没有生成任何策略原子，请检查策略文档中是否包含检测规则')\n    \n    suggestions.append('建议在部署前先在测试环境验证配置')\n    suggestions.append('建议逐步启用策略原子，先设置为TEST状态')\n    \n    validation_result = {\n        'valid': len(errors) == 0,\n        'errors': errors,\n        'warnings': warnings,\n        'suggestions': suggestions\n    }\n    \n    return {\n        'validation_result': validation_result,\n        'validated_config': final_config if len(errors) == 0 else None\n    }\n", "outputs": {"validation_result": "object", "validated_config": "object"}}}], "edges": [{"id": "start-document_preprocessor", "source": "start", "target": "document_preprocessor"}, {"id": "document_preprocessor-strategy_parser", "source": "document_preprocessor", "target": "strategy_parser"}, {"id": "strategy_parser-component_identifier", "source": "strategy_parser", "target": "component_identifier"}, {"id": "component_identifier-database_query", "source": "component_identifier", "target": "database_query"}, {"id": "database_query-config_generator", "source": "database_query", "target": "config_generator"}, {"id": "config_generator-config_validator", "source": "config_generator", "target": "config_validator"}]}}