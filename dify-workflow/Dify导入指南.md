# Dify工作流导入指南

## 1. 文件说明

我已经为你创建了正确的Dify DSL格式文件：

- **`risk-config-generator-workflow-clean.yml`** - 这是Dify可以直接导入的DSL文件（最新版本）
- **`risk-config-generator-workflow.yml`** - 旧版本文件（已废弃）

## 2. 导入步骤

### 步骤1: 登录Dify平台
1. 访问你的Dify平台
2. 登录你的账号

### 步骤2: 导入工作流
1. 进入工作流管理页面
2. 点击"创建工作流"或"导入工作流"按钮
3. 选择"从DSL导入"
4. 上传 `risk-config-generator-workflow-clean.yml` 文件
5. 确认导入

### 步骤3: 配置模型
导入后需要配置LLM模型：

1. **事件信息生成节点** (llm-1735721234568-5)
   - 模型：qwen-plus-latest
   - 提供商：openai_api_compatible
   - 温度：0.3

2. **策略解析节点** (llm-1735721234569)
   - 模型：qwen-plus-latest
   - 提供商：openai_api_compatible
   - 温度：0.1

3. **组件识别节点** (llm-1735721234570)
   - 模型：qwen-plus-latest
   - 提供商：openai_api_compatible
   - 温度：0.1

4. **配置生成节点** (llm-1735721234572)
   - 模型：qwen-plus-latest
   - 提供商：openai_api_compatible
   - 温度：0.1

### 步骤4: 配置数据库连接
在Dify平台的环境变量中配置数据库连接：

```
DB_HOST=你的数据库主机地址
DB_PORT=3306
DB_USER=你的数据库用户名
DB_PASSWORD=你的数据库密码
DB_NAME=ypp_fengkong
```

详细配置请参考 `数据库配置指南.md`

### 步骤4: 测试工作流
1. 点击"测试"按钮
2. 输入测试数据：
   ```
   策略文档: 设备检测：1、同设备参与人数>3，REJECT。IP检测：2、同IP一天内参与人数>8，REJECT。
   ```
3. 运行测试，检查输出结果

## 3. 工作流结构

该工作流包含以下节点：

```
开始 → 文档预处理 → 事件信息生成 → 策略解析 → 组件识别 → 数据库查询 → 配置生成 → 配置验证 → 输出结果
```

### 节点说明：

1. **开始节点**: 接收策略文档
2. **文档预处理**: 清理文档格式，提取或标记事件信息
3. **事件信息生成**: 从文档提取或使用LLM生成事件名称和代码
4. **策略解析**: 使用LLM解析策略文档，识别检测规则
5. **组件识别**: 使用LLM识别需要创建的风控组件
6. **数据库查询**: 检查可复用的组件（目前是模拟数据）
7. **配置生成**: 使用LLM生成最终的风控配置
8. **配置验证**: 验证配置的正确性和完整性
9. **输出结果**: 输出最终的配置和验证结果

## 4. 输入参数

- **策略文档** (必填): 包含风控策略的文档内容，可以包含或不包含事件信息

## 5. 输出结果

- **final_config**: 完整的风控配置JSON
- **validation_result**: 配置验证结果
- **reuse_suggestions**: 组件复用建议

## 6. 注意事项

### 6.1 模型配置
- 确保你已在Dify中配置了OpenAI-API-compatible下的qwen-plus-latest模型
- 设置正确的API密钥和端点
- 调整温度参数以获得稳定输出

### 6.2 数据库连接
- 数据库查询节点已更新为连接真实数据库
- 需要在Dify环境变量中配置数据库连接信息
- 详细配置请参考 `数据库配置指南.md`

### 6.3 Prompt优化
- 根据实际业务需求调整Prompt内容
- 添加更多的示例和约束条件
- 优化输出格式和验证规则

## 7. 自定义配置

### 7.1 数据库连接配置
数据库查询节点已经更新为连接真实数据库，通过环境变量获取连接配置：

```python
# 环境变量配置
DB_HOST=你的数据库主机地址
DB_PORT=3306
DB_USER=你的数据库用户名
DB_PASSWORD=你的数据库密码
DB_NAME=ypp_fengkong
```

详细的数据库配置步骤请参考 `数据库配置指南.md`

### 7.2 添加更多验证规则
在 `code-1735721234573` 节点中添加业务特定的验证逻辑。

### 7.3 自定义输出格式
根据需要修改最终输出的格式和内容。

## 8. 故障排除

### 8.1 导入失败
- 检查YAML文件格式是否正确
- 确认Dify平台版本兼容性
- 查看错误日志获取详细信息

### 8.2 LLM调用失败
- 检查API密钥配置
- 确认模型访问权限
- 调整请求参数和重试机制

### 8.3 输出格式错误
- 检查Prompt模板是否正确
- 验证LLM输出的JSON格式
- 添加更多的格式验证逻辑

通过以上步骤，你就可以成功导入和使用这个风控配置自动生成工作流了！
