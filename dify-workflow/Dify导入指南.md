# Dify工作流导入指南

## 1. 文件说明

我已经为你创建了正确的Dify DSL格式文件：

- **`risk-config-generator-workflow.yml`** - 这是Dify可以直接导入的DSL文件

## 2. 导入步骤

### 步骤1: 登录Dify平台
1. 访问你的Dify平台
2. 登录你的账号

### 步骤2: 导入工作流
1. 进入工作流管理页面
2. 点击"创建工作流"或"导入工作流"按钮
3. 选择"从DSL导入"
4. 上传 `risk-config-generator-workflow.yml` 文件
5. 确认导入

### 步骤3: 配置模型
导入后需要配置LLM模型：

1. **策略解析节点** (llm-1735721234569)
   - 模型：GPT-4-turbo-preview 或 Claude-3.5-Sonnet
   - 温度：0.1
   - 最大Token：4000

2. **组件识别节点** (llm-1735721234570)
   - 模型：GPT-4-turbo-preview 或 Claude-3.5-Sonnet
   - 温度：0.1
   - 最大Token：4000

3. **配置生成节点** (llm-1735721234572)
   - 模型：GPT-4-turbo-preview 或 Claude-3.5-Sonnet
   - 温度：0.1
   - 最大Token：4000

### 步骤4: 测试工作流
1. 点击"测试"按钮
2. 输入测试数据：
   ```
   策略文档: 设备检测：1、同设备参与人数>3，REJECT。
   事件名称: 测试活动-抽奖
   事件代码: test-activity-lottery
   业务代码: activity
   接入人: 测试人员
   ```
3. 运行测试，检查输出结果

## 3. 工作流结构

该工作流包含以下节点：

```
开始 → 文档预处理 → 策略解析 → 组件识别 → 数据库查询 → 配置生成 → 配置验证 → 输出结果
```

### 节点说明：

1. **开始节点**: 接收策略文档和事件信息
2. **文档预处理**: 清理文档格式，提取关键信息
3. **策略解析**: 使用LLM解析策略文档，识别检测规则
4. **组件识别**: 使用LLM识别需要创建的风控组件
5. **数据库查询**: 检查可复用的组件（目前是模拟数据）
6. **配置生成**: 使用LLM生成最终的风控配置
7. **配置验证**: 验证配置的正确性和完整性
8. **输出结果**: 输出最终的配置和验证结果

## 4. 输入参数

- **策略文档** (必填): 包含风控策略的文档内容
- **事件名称** (必填): 如"会员中心-抽奖"
- **事件代码** (必填): 如"member-center-lottery"
- **业务代码** (可选): 默认为"activity"
- **接入人** (可选): 接入人员姓名

## 5. 输出结果

- **final_config**: 完整的风控配置JSON
- **validation_result**: 配置验证结果
- **reuse_suggestions**: 组件复用建议

## 6. 注意事项

### 6.1 模型配置
- 确保你有GPT-4或Claude的API访问权限
- 设置正确的API密钥
- 调整温度参数以获得稳定输出

### 6.2 数据库连接
- 当前数据库查询节点使用模拟数据
- 在生产环境中需要连接真实的风控数据库
- 修改 `code-1735721234571` 节点中的数据库查询逻辑

### 6.3 Prompt优化
- 根据实际业务需求调整Prompt内容
- 添加更多的示例和约束条件
- 优化输出格式和验证规则

## 7. 自定义配置

### 7.1 修改数据库查询
在 `code-1735721234571` 节点中，将模拟查询替换为真实的数据库连接：

```python
import mysql.connector

def query_event_by_code(event_code: str) -> dict:
    conn = mysql.connector.connect(
        host='your-db-host',
        user='your-username', 
        password='your-password',
        database='ypp_fengkong'
    )
    cursor = conn.cursor(dictionary=True)
    cursor.execute("SELECT * FROM risk_event WHERE code = %s", (event_code,))
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    return result
```

### 7.2 添加更多验证规则
在 `code-1735721234573` 节点中添加业务特定的验证逻辑。

### 7.3 自定义输出格式
根据需要修改最终输出的格式和内容。

## 8. 故障排除

### 8.1 导入失败
- 检查YAML文件格式是否正确
- 确认Dify平台版本兼容性
- 查看错误日志获取详细信息

### 8.2 LLM调用失败
- 检查API密钥配置
- 确认模型访问权限
- 调整请求参数和重试机制

### 8.3 输出格式错误
- 检查Prompt模板是否正确
- 验证LLM输出的JSON格式
- 添加更多的格式验证逻辑

通过以上步骤，你就可以成功导入和使用这个风控配置自动生成工作流了！
