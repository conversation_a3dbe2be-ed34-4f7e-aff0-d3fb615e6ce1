package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Description("风控结果对象")
public class RiskResult<T> implements Serializable {
    private static final long serialVersionUID = -8363298064746444443L;
    @Description("请求处理是否成功")
    private boolean success;
    @Description("备注信息")
    private String message;
    @Description("风控实体列表")
    private List<T> data;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public static RiskResult error(String message) {
        RiskResult result = new RiskResult();
        result.success = false;
        result.message = message;
        return result;
    }

    public static RiskResult success(List<RiskGrayEntity> data) {
        RiskResult result = new RiskResult();
        result.success = true;
        result.data = data;
        return result;
    }

    public static RiskResult success(boolean isHit) {
        RiskResult result = new RiskResult();
        result.success = true;

        Map<String,Object> info = new HashMap<>(2);
        info.put("isHit",isHit);
        List<Map<String,Object>> data = new ArrayList<>();
        data.add(info);

        result.data = data;
        return result;
    }

    public static RiskResult success() {
        RiskResult result = new RiskResult();
        result.success = true;
        return result;
    }
}
