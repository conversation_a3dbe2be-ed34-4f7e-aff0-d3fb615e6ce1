package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

@Description("风控名单实体")
public class RiskGrayEntity implements Serializable {
    private static final long serialVersionUID = -7343084119650287793L;
    @Description("组ID")
    private Long groupId;
    @Description("组名称")
    private String group;
    @Description("名单类型")
    private String type;
    @Description("过期时间")
    private Date expireTime;
    @Description("维度")
    private String dimension;
    @Description("维度值")
    private String value;

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
