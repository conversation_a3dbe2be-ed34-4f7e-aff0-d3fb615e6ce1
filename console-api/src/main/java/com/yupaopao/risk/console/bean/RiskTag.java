package com.yupaopao.risk.console.bean;


import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Description("风控/审核标签")
public class RiskTag implements Serializable {

    @Description("标签id")
    private Long id;

    @Description("标签名称")
    private String name;

    @Description("标签备注")
    private String comment;

    @Description("父级标签id")
    private Long pId;

    @Description("标签层级")
    private Integer level;

//    @Description("标签创建时间")
//    private Date createdAt;
//
//    @Description("标签更新时间")
//    private Date updatedAt;

    @Description("标签所属的系统标识")
    private Integer toSystem;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Long getpId() {
        return pId;
    }

    public void setpId(Long pId) {
        this.pId = pId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getToSystem() {
        return toSystem;
    }

    public void setToSystem(Integer toSystem) {
        this.toSystem = toSystem;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RiskTag)) return false;
        RiskTag riskTag = (RiskTag) o;
        return Objects.equals(getId(), riskTag.getId()) &&
                Objects.equals(getName(), riskTag.getName()) &&
                Objects.equals(getComment(), riskTag.getComment()) &&
                Objects.equals(getpId(), riskTag.getpId()) &&
                Objects.equals(getLevel(), riskTag.getLevel()) &&
                Objects.equals(getToSystem(), riskTag.getToSystem());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getName(), getComment(), getpId(), getLevel(), getToSystem());
    }
}
