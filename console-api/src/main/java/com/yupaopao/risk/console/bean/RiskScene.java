package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Description("风控/审核场景")
public class RiskScene implements Serializable {

    @Description("场景id")
    private Long id;

    @Description("场景名称")
    private String name;

    @Description("场景备注")
    private String comment;

//    @Description("场景创建时间")
//    private Date createdAt;
//
//    @Description("场景更新时间")
//    private Date updatedAt;

    @Description("场景所属的系统标识")
    private Integer toSystem;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getToSystem() {
        return toSystem;
    }

    public void setToSystem(Integer toSystem) {
        this.toSystem = toSystem;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RiskScene)) return false;
        RiskScene scene = (RiskScene) o;
        return Objects.equals(getId(), scene.getId()) &&
                Objects.equals(getName(), scene.getName()) &&
                Objects.equals(getComment(), scene.getComment()) &&
                Objects.equals(getToSystem(), scene.getToSystem());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getName(), getComment(), getToSystem());
    }
}
