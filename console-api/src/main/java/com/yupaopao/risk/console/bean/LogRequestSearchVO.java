package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.StringJoiner;

public class LogRequestSearchVO<T> implements Serializable {

    private String text;
    private T query;
    private Date startTime;
    private Date endTime;
    private int page = 1;
    private int size = 10;


    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public T getQuery() {
        return query;
    }

    public void setQuery(T query) {
        this.query = query;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", LogRequestSearchVO.class.getSimpleName() + "[", "]")
                .add("text=" + text)
                .add("query=" + query)
                .add("startTime=" + startTime)
                .add("endTime=" + endTime)
                .add("page=" + page)
                .add("size=" + size)
                .toString();
    }

    public static class HitLogRequestSearchVO extends LogRequestSearchVO<RiskHitLogRequest> {

    }

}
