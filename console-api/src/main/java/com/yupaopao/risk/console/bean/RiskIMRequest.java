package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Description("风控IM请求对象")
public class RiskIMRequest extends RiskRequestPage implements Serializable {
    private static final long serialVersionUID = -9178058713526064785L;
    @Description("事件")
    private String eventCode; // 事件 IM单聊、私聊、聊天室
    @Description("用户ID")
    private String userId;
    @Description("目标用户ID")
    private String targetUserId;
    @Description("聊天室ID")
    private String roomId;

    @Description("开始时间")
    private Date startTime;
    @Description("结束时间")
    private Date endTime;

    @Description("双方,单聊专有,默认false")
    private boolean both;
    @Description("按时间升序,默认false")
    private boolean asc;

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(String targetUserId) {
        this.targetUserId = targetUserId;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public boolean isBoth() {
        return both;
    }

    public void setBoth(boolean both) {
        this.both = both;
    }

    public boolean isAsc() {
        return asc;
    }

    public void setAsc(boolean asc) {
        this.asc = asc;
    }

    public static RiskResult valid(RiskIMRequest request){
        String eventCode = request.getEventCode();
        if(!RiskIMEventCodeEnum.exists(eventCode)){
            return RiskResult.error("事件非法");
        }
        if(request.isBoth()&&singleChat(eventCode)){ // 双方且是单聊
            if(StringUtils.isBlank(request.getUserId())){
                return RiskResult.error("用户ID不能为空");
            }
            if(StringUtils.isBlank(request.getTargetUserId())){
                return RiskResult.error("目标用户ID不能为空");
            }
        }
        return RiskResult.success();
    }

    /**
     * 单聊
     * @param eventCode
     * @return
     */
    public static boolean singleChat(String eventCode){
        if(StringUtils.isNotBlank(eventCode)&&(eventCode.equals(RiskIMEventCodeEnum.IM_MESSAGE.getCode())
                || eventCode.equals(RiskIMEventCodeEnum.PRIVATE_CHAT.getCode()))) {
            return true;
        }
        return false;
    }

    /**
     * 聊天室
     * @param eventCode
     * @return
     */
    public static boolean chatRoom(String eventCode){
        if(StringUtils.isNotBlank(eventCode)&&eventCode.equals(RiskIMEventCodeEnum.CHAT_ROOM.getCode())) {
            return true;
        }
        return false;
    }

}
