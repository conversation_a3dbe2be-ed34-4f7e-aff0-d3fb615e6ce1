package com.yupaopao.risk.console.bean;

import java.io.Serializable;

@Description("名单检测请求")
public class RiskGrayCheckRequest implements Serializable {

    /**
     * 名单类型(黑名单 | 白名单)
     */
    @Description("必填项 名单类型(黑名单|白名单) BLACK:黑名单, WHITE:白名单")
    private String type;

    /**
     * 维度
     */
    @Description("必填项 维度(MOBILENO|DEVICEID|USERID|CLIENTIP|IMAGE|mac|text)  MOBILENO (手机号码),DEVICEID(设备ID),USERID(用户Uid),CLIENTIP(客户端Ip),IMAGE(图片),mac(mac地址),text(文本)")
    private String dimension;

    /**
     * 维度值
     */
    @Description("必填项 维度值")
    private String value;

    /**
     * 业务场景标识
     */
    @Description("业务场景标识")
    private String code;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
