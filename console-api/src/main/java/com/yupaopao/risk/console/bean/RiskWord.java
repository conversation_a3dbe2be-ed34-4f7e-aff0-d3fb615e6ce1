package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

@Description("风控违禁词/审核敏感词")
public class RiskWord implements Serializable {

    @Description("违禁词id")
    private Long id;

    @Description("违禁词")
    private String content;

    @Description("权重")
    private Float weight;

    @Description("匹配类型（1 模糊匹配 2 精确匹配，3 全词匹配 ，4 跳跃匹配）")
    private Integer matchType;

    @Description("是否存在于白词条中（1 违禁词条, 2 白词条）")
    private Integer wordLocationType;

    @Description("创建时间")
    private Date createdAt;

    @Description("修改时间")
    private Date updatedAt;

    @Description("备注")
    private String comment;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Float getWeight() {
        return weight;
    }

    public void setWeight(Float weight) {
        this.weight = weight;
    }

    public Integer getMatchType() {
        return matchType;
    }

    public void setMatchType(Integer matchType) {
        this.matchType = matchType;
    }

    public Integer getWordLocationType() {
        return wordLocationType;
    }

    public void setWordLocationType(Integer wordLocationType) {
        this.wordLocationType = wordLocationType;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
