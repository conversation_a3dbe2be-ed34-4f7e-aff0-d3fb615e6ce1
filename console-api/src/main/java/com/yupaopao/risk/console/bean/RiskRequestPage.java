package com.yupaopao.risk.console.bean;

import java.io.Serializable;

@Description("风控请求分页对象")
public class RiskRequestPage implements Serializable {
    private static final long serialVersionUID = 3271917400720027930L;
    @Description("第几页")
    private int page = 1;
    @Description("每页大小")
    private int size = 10;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }
}
