package com.yupaopao.risk.console.bean;

import lombok.Data;

import java.io.Serializable;

@Description("风控IM实体")
@Data
public class RiskIMEntity implements Serializable {
    private static final long serialVersionUID = 7828222241918387117L;
    @Description("风控追踪ID")
    private String traceId;
    @Description("风控原因")
    private String reason;
    @Description("聊天内容")
    private String content;
    @Description("风控等级")
    private String level;
    @Description("违禁词")
    private String words;
    @Description("创建时间")
    private String createdAt;
    @Description("用户ID")
    private String userId;
    @Description("图片地址")
    private String image;
    @Description("音频地址")
    private String audio;
    @Description("视频地址")
    private String video;

    @Description("目标用户ID")
    private String targetUserId;
    @Description("聊天室ID")
    private String roomId;
    @Description("平台业务线id")
    private String appId;
    @Description("违禁一级标签")
    private String riskLabels;
    @Description("违禁二级标签")
    private String riskSubLabels;
    @Description("模板类型")
    private String templateType;
    @Description("模板描述")
    private String templateDesc;
}
