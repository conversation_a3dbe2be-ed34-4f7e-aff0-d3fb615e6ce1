package com.yupaopao.risk.console.api;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.console.bean.Description;
import com.yupaopao.risk.console.bean.RiskGrayRequest;
import com.yupaopao.risk.console.bean.RiskIsGrayRequest;
import com.yupaopao.risk.console.bean.RiskResult;


@Description("风控名单组服务")
public interface RiskGrayService {
    @Description("获取风控名单列表")
    RiskResult search(RiskGrayRequest request);

    @Description("获取命中黑白名单情况")
    Response<Boolean> isGray(RiskIsGrayRequest request);

}
