package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2020/4/27 20:20
 */
public class RiskEvent implements Serializable {
    private static final long serialVersionUID = 4590338989063000572L;

    private Long id;

    private String name;

    private String businessCode;

    private String code;

    private String ruleGroupId;

    private String reply;

    private String comment;

    private String author;

    private String modifier;

    private Date createdAt;

    private Date updatedAt;

    private String grayGroups;

    private String accessBy;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRuleGroupId() {
        return ruleGroupId;
    }

    public void setRuleGroupId(String ruleGroupId) {
        this.ruleGroupId = ruleGroupId;
    }

    public String getReply() {
        return reply;
    }

    public void setReply(String reply) {
        this.reply = reply;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getGrayGroups() {
        return grayGroups;
    }

    public void setGrayGroups(String grayGroups) {
        this.grayGroups = grayGroups;
    }

    public String getAccessBy() {
        return accessBy;
    }

    public void setAccessBy(String accessBy) {
        this.accessBy = accessBy;
    }
}
