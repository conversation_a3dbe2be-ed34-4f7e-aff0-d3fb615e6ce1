package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Set;

@Description("风控命中名单请求对象 ")
public class RiskIsGrayRequest implements Serializable {
    private static final long serialVersionUID = -8912104829790061136L;
    @Description("名单类型")
    private String type;
    @Description("名单维度")
    private String dimension;
    @Description("名单维度值")
    private String value;
    @Description("名称组ID列表")
    private Set<Long> groupIds;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static boolean validDimension(String dimension){
        return RiskGrayDimensionEnum.exists(dimension);
    }

    public static boolean validType(String type){
        return RiskGrayTypeEnum.exists(type);
    }

    public Set<Long> getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(Set<Long> groupIds) {
        this.groupIds = groupIds;
    }
}

