package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.List;

@Description("风控结果分页对象")
public class RiskResultPage<T> extends  RiskResult<T> implements Serializable {
    private static final long serialVersionUID = -8363298064746444443L;

    @Description("第几页")
    private int page;
    @Description("每页大小")
    private int size;
    @Description("总记录数")
    private long total;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }
}
