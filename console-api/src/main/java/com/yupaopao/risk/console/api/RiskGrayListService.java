package com.yupaopao.risk.console.api;

import com.yupaopao.risk.console.bean.Description;
import com.yupaopao.risk.console.bean.RiskGrayCheckRequest;
import com.yupaopao.risk.console.bean.RiskGrayListRequest;
import com.yupaopao.risk.console.bean.RiskResult;

import java.util.Map;

@Description("风控名单服务")
public interface RiskGrayListService {

    /**
     * 添加名单
     * @param request
     * @return
     */
    @Description("添加名单")
    RiskResult add(RiskGrayListRequest request);

    /**
     * 删除名单
     * @param request
     * @return
     */
    @Description("删除名单")
    RiskResult delete(RiskGrayListRequest request);

    /**
     * 检测名单
     * @param request
     * @return
     */
    @Description("检测名单")
    RiskResult<Map<String,Object>> detect(RiskGrayCheckRequest request);

}
