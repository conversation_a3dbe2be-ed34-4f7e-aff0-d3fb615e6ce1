package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.List;

@Description("风控违禁词/审核敏感词以及其属性")
public class RiskWordVO implements Serializable {

    @Description("一级标签")
    private List<RiskTag> firstTags;

    @Description("二级标签")
    private List<RiskTag> secondTags;

    @Description("场景")
    private List<RiskScene> scenes;

    @Description("违禁词")
    private RiskWord word;

    public List<RiskTag> getFirstTags() {
        return firstTags;
    }

    public void setFirstTags(List<RiskTag> firstTags) {
        this.firstTags = firstTags;
    }

    public List<RiskTag> getSecondTags() {
        return secondTags;
    }

    public void setSecondTags(List<RiskTag> secondTags) {
        this.secondTags = secondTags;
    }

    public List<RiskScene> getScenes() {
        return scenes;
    }

    public void setScenes(List<RiskScene> scenes) {
        this.scenes = scenes;
    }

    public RiskWord getWord() {
        return word;
    }

    public void setWord(RiskWord word) {
        this.word = word;
    }
}
