package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

@Description("控名单类型枚举")
public enum RiskGrayTypeEnum {
    BLACK("BLA<PERSON>K","黑名单"),
    WHITE("WHITE","白名单");

    @Description("编号")
    private String code; // 编号
    @Description("描述")
    private String desc; // 描述

    private static final Map<String, RiskGrayTypeEnum> map = new TreeMap<>();

    static {
        for (RiskGrayTypeEnum enum1 : RiskGrayTypeEnum.values()) {
            map.put(enum1.getCode(), enum1);
        }
    }


    RiskGrayTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据编号获取描述
     * @param code
     * @return
     */
    public static String getDesc(String code) {
        if (StringUtils.isNotBlank(code)){
            for (RiskGrayTypeEnum enum1 : RiskGrayTypeEnum.values()) {
                if (code.equals(enum1.getCode())) {
                    return enum1.desc;
                }
            }
        }
        return null;
    }

    /**
     * 存在
     * @param code
     * @return
     */
    public static boolean exists(String code){
        return StringUtils.isNotBlank(code)&&map.containsKey(code);
    }
}
