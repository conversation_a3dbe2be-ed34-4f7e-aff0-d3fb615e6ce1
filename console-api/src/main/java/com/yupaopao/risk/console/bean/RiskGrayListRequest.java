package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

@Description("名单请求")
public class RiskGrayListRequest implements Serializable {

    /**
     * 名单类型(黑名单 | 白名单)
     */
    @Description("必填项 名单类型(黑名单|白名单) BLACK:黑名单, WHITE:白名单")
    private String type;

    /**
     * 维度
     */
    @Description("必填项 维度(MOBILENO|DEVICEID|USERID|CLIENTIP|IMAGE|mac|text)  MOBILENO (手机号码),DEVICEID(设备ID),USERID(用户Uid),CLIENTIP(客户端Ip),IMAGE(图片),mac(mac地址),text(文本)")
    private String dimension;

    /**
     * 维度值
     */
    @Description("必填项 维度值")
    private String value;

    /**
     * 失效时间
     */
    @Description("选填项 失效时间")
    private Date expireTime;

    /**
     * 创建人
     */
    @Description("创建人， 检测名单时，为选填项/ 添加名单和删除时，为必填项")
    private String author;

    /**
     * 业务场景标识
     */
    @Description("业务场景标识")
    private String code;

    /**
     * 说明
     */
    @Description("选填项 说明")
    private String comment;


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
