package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

@Description("风控名单事件枚举")
public enum RiskIMEventCodeEnum {
    IM_MESSAGE("im-message", "IM单聊"),
    PRIVATE_CHAT("private-chat", "私聊"),
    CHAT_ROOM("chat-room", "聊天室"),
    USER_EMOJI("user-emoji", "自定义表情");
    @Description("编号")
    private String code;
    @Description("描述")
    private String desc;

    private static final Map<String, RiskIMEventCodeEnum> map = new TreeMap<>();

    static {
        for (RiskIMEventCodeEnum enum1 : RiskIMEventCodeEnum.values()) {
            map.put(enum1.getCode(), enum1);
        }
    }

    RiskIMEventCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据编号获取描述
     *
     * @param code
     * @return
     */
    public static String getDesc(String code) {
        if (StringUtils.isNotBlank(code)) {
            for (RiskIMEventCodeEnum enum1 : RiskIMEventCodeEnum.values()) {
                if (code.equals(enum1.getCode())) {
                    return enum1.desc;
                }
            }
        }
        return null;
    }

    /**
     * 存在
     *
     * @param code
     * @return
     */
    public static boolean exists(String code) {
        return StringUtils.isNotBlank(code) && map.containsKey(code);
    }

}
