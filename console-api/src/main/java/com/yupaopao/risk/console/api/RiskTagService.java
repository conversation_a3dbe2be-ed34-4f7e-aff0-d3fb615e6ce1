package com.yupaopao.risk.console.api;


import com.yupaopao.risk.console.bean.Description;
import com.yupaopao.risk.console.bean.RiskTag;

import java.util.List;

@Description("风控/审核标签查询服务")
public interface RiskTagService {

    @Description("根据风控/审核系统标识，获取风控/审核系统所有的一级标签")
    List<RiskTag> searchFirstTags(Integer toSystem);

    @Description()
    List<RiskTag> searchSecondTags(String firstTagName);

}
