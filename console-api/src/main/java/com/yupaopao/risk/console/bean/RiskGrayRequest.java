package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Description("风控名单请求对象")
public class RiskGrayRequest implements Serializable {
    private static final long serialVersionUID = -139492989904411212L;

    @Description("名单类型")
    private String type;
    @Description("名单维度")
    private String dimension;
    @Description("名单维度值")
    private String value;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static boolean validDimension(String dimension){
        return RiskGrayDimensionEnum.exists(dimension);
    }

    public static boolean validType(String type){
        if(StringUtils.isBlank(type)){ // 不传参
            return true;
        }else { // 传参就严格校验
            return RiskGrayTypeEnum.exists(type);
        }
    }

}
