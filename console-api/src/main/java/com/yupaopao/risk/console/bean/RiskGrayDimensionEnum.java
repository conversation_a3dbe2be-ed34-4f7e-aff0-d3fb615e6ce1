package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

@Description("风控名单维度枚举")
public enum RiskGrayDimensionEnum {
    USERID("USERID","用户ID"),
    DEVICEID("DEVICEID","设备ID"),
    CLIENTIP("CLIENTIP","客户端IP"),
    MOBILENO("MOBILENO","手机号码");

    @Description("编号")
    private String code;
    @Description("描述")
    private String desc;

    private static final Map<String, RiskGrayDimensionEnum> map = new TreeMap<>();

    static {
        for (RiskGrayDimensionEnum enum1 : RiskGrayDimensionEnum.values()) {
            map.put(enum1.getCode(), enum1);
        }
    }


    RiskGrayDimensionEnum(String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据编号获取描述
     * @param code
     * @return
     */
    public static String getDesc(String code) {
        if (StringUtils.isNotBlank(code)){
            for (RiskGrayDimensionEnum enum1 : RiskGrayDimensionEnum.values()) {
                if (code.equals(enum1.getCode())) {
                    return enum1.desc;
                }
            }
        }
        return null;
    }

    /**
     * 存在
     * @param code
     * @return
     */
    public static boolean exists(String code){
        return StringUtils.isNotBlank(code)&&map.containsKey(code);
    }

}
