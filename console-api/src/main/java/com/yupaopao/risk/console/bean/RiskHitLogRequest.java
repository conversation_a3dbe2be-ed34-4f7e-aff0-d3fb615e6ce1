package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.List;

@Description("风控命中日志查询请求对象")
public class RiskHitLogRequest implements Serializable {
    /**
     * 风控事件code
     */
    @Description("风控事件code")
    private String eventCode;

    /**
     * 用户UID
     */
    @Description("用户UID")
    private Long uid;

    /**
     * 手机号码
     */
    @Description("手机号码")
    private String mobileNo;

    /**
     * 检测结果
     */
    @Description("检测结果")
    private String level;

    /**
     * 设备ID列表
     */
    @Description("设备ID列表")
    private List<String> deviceIds;

    /**
     * 需要排除的uid列表
     */
    @Description("需要排除的uid列表")
    private List<Long> uids;

    /**
     * 需要排除的手机号码
     */
    @Description("需要排除的手机号码")
    private List<String> mobileNos;

    /**
     * 需要排除的事件code
     */
    @Description("需要排除的事件code")
    private List<String> eventCodes;


    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public List<String> getDeviceIds() {
        return deviceIds;
    }

    public void setDeviceIds(List<String> deviceIds) {
        this.deviceIds = deviceIds;
    }

    public List<Long> getUids() {
        return uids;
    }

    public void setUids(List<Long> uids) {
        this.uids = uids;
    }

    public List<String> getMobileNos() {
        return mobileNos;
    }

    public void setMobileNos(List<String> mobileNos) {
        this.mobileNos = mobileNos;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public List<String> getEventCodes() {
        return eventCodes;
    }

    public void setEventCodes(List<String> eventCodes) {
        this.eventCodes = eventCodes;
    }
}
