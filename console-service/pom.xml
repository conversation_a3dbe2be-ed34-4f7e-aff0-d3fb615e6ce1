<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>risk-console</artifactId>
        <groupId>com.yupaopao.risk</groupId>
        <version>1.17.22</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>console-service</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.yupaopao.bixin.user.operation</groupId>
            <artifactId>user-operation-api</artifactId>
            <version>1.1.11</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yupaopao.operation</groupId>
                    <artifactId>operation-common-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>engine-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>shoot-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>risk-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>punish-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>kafka-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <artifactId>user-api</artifactId>
            <groupId>com.yupaopao.platform</groupId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.mail</groupId>
            <artifactId>mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-dbutils</groupId>
            <artifactId>commons-dbutils</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.aries</groupId>
            <artifactId>aries-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>passport-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>user-growth-api</artifactId>
        </dependency>

        <!--  apollo open api -->
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>config-openapi</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>audit-complain-api</artifactId>
            <version>1.8.4</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>appeal-api</artifactId>
            <version>1.2.3</version>
        </dependency>
        <dependency>
            <groupId>com.ali.dingtalk</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.yuer</groupId>
            <artifactId>yuer-chatroom-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>data-api</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.payment</groupId>
            <artifactId>open-account-api</artifactId>
            <version>1.3.22</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.bixin</groupId>
            <artifactId>bixin-playmate-search-api</artifactId>
            <version>0.1.9</version>
        </dependency>

      <!--  <dependency>
            <groupId>com.yupaopao.bixin.qa.operation</groupId>
            <artifactId>kfrecords-operation-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>task-center-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.bixin</groupId>
            <artifactId>bixin-biggie-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.live</groupId>
            <artifactId>yuer-live-api</artifactId>
            <version>${yuer-live-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>order-component-core</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>order-search-api</artifactId>
            <version>0.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>config-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hankcs</groupId>
            <artifactId>hanlp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>passport-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>console-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>commons-ding</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>device-service-api</artifactId>
            <version>1.1.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.xxq</groupId>
            <artifactId>robot-api</artifactId>
            <version>0.0.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
