package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/3/13 9:16 PM
 */
public interface GrayGroupService extends BaseService<GrayGroup> {

    List<GrayGroup> selectByIds(List<Long> ids);

    List<GrayGroup> selectByName(String name);

    /**
     * 根据ID删除名单分组，当其下没有名单的情况下允许删除
     * @param id
     * @return
     */
    boolean delById(Long id);

    void cacheAll();

    GrayGroup getById(Long id);

}
