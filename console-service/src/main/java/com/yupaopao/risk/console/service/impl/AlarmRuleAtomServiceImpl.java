package com.yupaopao.risk.console.service.impl;

import com.google.common.collect.Lists;
import com.yupaopao.risk.common.mapper.AlarmRuleAtomMapper;
import com.yupaopao.risk.common.model.AlarmRuleAtom;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;
import com.yupaopao.risk.console.service.AlarmRuleAtomService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * author: liji<PERSON>jun
 * date: 2020/2/19 19:55
 */
@Service
public class AlarmRuleAtomServiceImpl extends BaseServiceImpl<AlarmRuleAtom, AlarmRuleAtomMapper> implements AlarmRuleAtomService {

    @Override
    public List<AlarmRuleAtomVO> listByRuleGroupId(Long ruleGroupId) {
        Example example = new Example(AlarmRuleAtom.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ruleGroupId", ruleGroupId);
        example.setOrderByClause("id");
        List<AlarmRuleAtom> list = getMapper().selectByExample(example);
        List<AlarmRuleAtomVO> voList = Lists.newArrayListWithExpectedSize(list.size());
        for (AlarmRuleAtom ruleAtom:list){
            voList.add(new AlarmRuleAtomVO(ruleAtom));
        }
        return voList;
    }
}
