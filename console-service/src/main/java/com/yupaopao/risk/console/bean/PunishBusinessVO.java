package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.PunishBusiness;
import com.yupaopao.risk.common.model.PunishChannel;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * PunishBusinessVO
 *
 * <AUTHOR>
 * @date 2021/8/9 15:51
 */
@ToString
@Data
public class PunishBusinessVO extends PunishBusiness {

    /**
     * 绑定渠道
     */
    List<PunishChannel> channels;
    /**
     * 权限code
     */
    private String permissionName;

}
