package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.ParamEvent;
import com.yupaopao.risk.common.model.ParamValidator;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ParamValidatorService extends BaseService<ParamValidator> {

    void fetchRelations(List<ParamValidator> paramValidators);

    ParamValidator getByName(String name);

    List<ParamValidator> getByIds(Set<Long> ids);

    Map<String, Object> simpleAll();
}
