package com.yupaopao.risk.console.service.impl;

import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.NamespaceReleaseDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenEnvClusterDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenItemDTO;
import com.ctrip.framework.apollo.openapi.dto.OpenReleaseDTO;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Service
@Slf4j
public class ApolloOpenApiServiceImpl implements ApolloOpenApiService, ApplicationContextAware {

    private ApplicationContext ctx;

    @Autowired
    private ApolloOpenApiClient openApiClient;

    /**
     *
     * @param configItem
     */
    @Override
    public String search(ApolloConfigItem configItem){
        if(configItem == null){
            log.warn("the update param is empty");
        }

        String env = this.getEnvFromActiveProfile();
        String cluster = this.getClusterNameFromActiveProfile();

        cluster = checkClusterInfo(configItem, env, cluster);

        OpenReleaseDTO openReleaseDTO = openApiClient.getLatestActiveRelease(configItem.getAppId(),env,cluster,configItem.getNamespace());
        Map<String,String> configurations = openReleaseDTO.getConfigurations();
        if(MapUtils.isNotEmpty(configurations)){
            for(Map.Entry<String,String> entry:configurations.entrySet()){
                if(Objects.equals(entry.getKey(),configItem.getItemKey())){
                    return entry.getValue();
                }
            }
        }

        return "";
    }

    /***
     * @param configItem
     * @return
     */
    public boolean updateItem(ApolloConfigItem configItem) {
        if (configItem == null || configItem.isEmpty()) {
            log.warn("the update param is empty");
            return false;
        }
        String env = this.getEnvFromActiveProfile();
        String cluster = this.getClusterNameFromActiveProfile();

        cluster = checkClusterInfo(configItem, env, cluster);

        OpenItemDTO itemDto = new OpenItemDTO();
        itemDto.setKey(configItem.getItemKey());
        itemDto.setValue(configItem.getItemValue());
        itemDto.setDataChangeLastModifiedBy(configItem.getModifyUser());
        //修改
        try {
            openApiClient.updateItem(configItem.getAppId(), env, cluster, configItem.getNamespace(), itemDto);
        } catch (Exception e) {
            //针对某些情况有cluster设置，但是需要修改的配置项不在该cluster中而是在default中的情况，重试一次
            cluster = ConstantsForApollo.CLUSTER_DEFAULT;
            try {
                openApiClient.updateItem(configItem.getAppId(), env, cluster, configItem.getNamespace(), itemDto);
            } catch (Exception e1) {
                log.error("apollo open api 修改配置异常", e);
                throw new RuntimeException("apollo配置修改异常");
            }

        }

        //发布
        NamespaceReleaseDTO releaseDTO = new NamespaceReleaseDTO();
        releaseDTO.setReleasedBy(configItem.getModifyUser());
        String releaseTitle = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + "-release";
        releaseDTO.setReleaseTitle(releaseTitle);
        releaseDTO.setReleaseComment(String.format("update itemKey: %s ,itemValue: %s", configItem.getItemKey(), configItem.getItemValue()));
        try {
            openApiClient.publishNamespace(configItem.getAppId(), env, cluster, configItem.getNamespace(), releaseDTO);
        } catch (Exception e) {
            log.error("apollo open api 发布配置异常", e);
            throw new RuntimeException("apollo配置发布异常");
        }
        log.info("update config success : {} , env: {}, cluster: {}", configItem, env, cluster);
        return true;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }

    private String checkClusterInfo(ApolloConfigItem configItem, String env, String cluster) {
        //检查当前环境是否有相应的cluster，没有返回default
        List<OpenEnvClusterDTO> envClusterList = openApiClient.getEnvClusterInfo(configItem.getAppId());
        if (CollectionUtils.isEmpty(envClusterList)) {
            log.warn("not env cluster info for appId: {} in apollo please check.", configItem.getAppId());
            throw new RuntimeException("param is not correct, please check the apollo configuration.");
        }
        Optional<OpenEnvClusterDTO> evnCluster = envClusterList.stream().filter(elem -> elem.getEnv().equalsIgnoreCase(env)).findFirst();
        if (!evnCluster.isPresent()) {
            log.warn("not env cluster info for appId: {}  with env: {} in apollo please check.", configItem.getAppId(), env);
            throw new RuntimeException("param is not correct, please check the apollo configuration.");
        }

        boolean clusterExist = evnCluster.get().getClusters() != null && evnCluster.get().getClusters().contains(cluster);
        if (!clusterExist) {
            log.warn("the cluster is not exist in appId: {} , env: {} , cluster: {}", configItem.getAppId(), env, cluster);
            return ConstantsForApollo.CLUSTER_DEFAULT;
        }
        return cluster;
    }

    /***
     * 根据 profile获取当前当前集群
     * @return
     */
    private String getClusterNameFromActiveProfile() {
        String profile = getActiveProfile();
        if (StringUtils.isEmpty(profile)) {
            return ConstantsForApollo.CLUSTER_DEFAULT;
        }
        return profile;
    }

    /***
     * 根据profile获取当前环境
     * @return
     */
    private String getEnvFromActiveProfile() {
        String profile = getActiveProfile();
        if (StringUtils.isEmpty(profile)) {
            return ConstantsForApollo.ENV_TEST;
        }
        String env = profileEvnMap.get(profile);
        return StringUtils.isEmpty(env) ? ConstantsForApollo.ENV_TEST : env;
    }

    private String getActiveProfile() {
        String[] profiles = ctx.getEnvironment().getActiveProfiles();
        if (profiles.length == 0) {
            return "";
        }
        Arrays.asList(profiles).stream().forEach(elem -> System.out.println(elem));
        return profiles[0];
    }

    private static Map<String, String> profileEvnMap = new HashMap<>();

    static {
        profileEvnMap.put("prod", ConstantsForApollo.ENV_PROD);
        profileEvnMap.put("uat", ConstantsForApollo.ENV_PROD);
        profileEvnMap.put("k8s", ConstantsForApollo.ENV_TEST);
        profileEvnMap.put("test", ConstantsForApollo.ENV_TEST);
        profileEvnMap.put("dev", ConstantsForApollo.ENV_TEST);
    }

}
