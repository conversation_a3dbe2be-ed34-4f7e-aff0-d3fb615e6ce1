package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.mapper.AlarmMapper;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.model.AlarmConfig;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.utils.RiskModelIdUtils;
import com.yupaopao.risk.common.vo.*;
import com.yupaopao.risk.console.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lijianjun
 * date: 2020/2/18 14:51
 */
@Service
@Slf4j
public class AlarmServiceImpl extends BaseServiceImpl<Alarm, AlarmMapper> implements AlarmService {

    @Autowired
    private AlarmConfigService alarmConfigService;
    @Autowired
    private AlarmRuleGroupService alarmRuleGroupService;
    @Autowired
    private EventService eventService;
    @Autowired
    private AtomRuleService atomRuleService;

    @Override
    public Alarm getByConfigId(Long configId) {
        Example example = new Example(Alarm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("configId", configId);
        criteria.andIn("state", Lists.newArrayList(CommonStatus.ENABLE.getCode(),CommonStatus.DISABLE.getCode()));
        RowBounds rowBounds = new RowBounds(0,1);
        List<Alarm> list = this.getMapper().selectByExampleAndRowBounds(example,rowBounds);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Transactional
    @Override
    public boolean saveAlarm(AlarmVO vo) {
        if(vo.getConfigId() > 0){
            vo.setRelatedTemplateCode(AlarmRelatedTemplateCode.RELATED.getCode());
        }else{
            //由于可能带回模板数据，先要清除之前的数据，让系统自动生成
            clearPreRuleData(vo.getConfig());
            vo.getConfig().setName(vo.getName()+"_template");
            vo.getConfig().setType(AlarmConfigType.CUSTOME.getCode());
            vo.getConfig().setId(null);
            Long configId = alarmConfigService.saveAlarmConfig(vo.getConfig());
            vo.setConfigId(configId);
        }
        this.insertSelective(vo);
        return true;
    }

    @Transactional
    @Override
    public boolean saveFuseAlarm(AlarmVO vo) {
        vo.getConfig().setName(vo.getName()+"_template");
        vo.getConfig().setType(AlarmConfigType.CUSTOME.getCode());
        vo.getConfig().setId(null);
        Long configId = alarmConfigService.saveAlarmConfig(vo.getConfig());
        vo.setConfigId(configId);
        this.insertSelective(vo);
        return true;
    }

    @Override
    public PageInfo<AlarmVO> searchAlarm(AlarmVO vo, int page, int size) {
        if (vo == null) {
            return new PageInfo<>();
        }
        List<Long> configIds = Lists.newArrayList();
        if((vo.getConfig()!=null) && (vo.getConfig().getCountType()!=null)){
            AlarmConfig alarmConfig = new AlarmConfig();
            alarmConfig.setCountType(vo.getConfig().getCountType());
            List<AlarmConfig> alarmConfigs =this.alarmConfigService.select(alarmConfig);
            if(CollectionUtils.isNotEmpty(alarmConfigs)){
                configIds.addAll(alarmConfigs.stream().map(AlarmConfig::getId).collect(Collectors.toList()));
            }
            if(CollectionUtils.isEmpty(configIds)){
                return new PageInfo<>();
            }
        }
        Example example = new Example(Alarm.class,false);
        Example. Criteria criteria =  example.createCriteria();
        criteria.andNotEqualTo("state",3);
        if(StringUtils.isNotBlank(vo.getName())){
            criteria.andLike("name","%"+vo.getName()+"%");
        }
        if(vo.getType()!=null){
            criteria.andEqualTo("type",vo.getType());
        }
        if(CollectionUtils.isNotEmpty(configIds)){
            criteria.andIn("configId",configIds);
        }
        if(StringUtils.isNotBlank(vo.getRiskResult())){
            criteria.andEqualTo("riskResult",vo.getRiskResult());
        }
        if(vo.getState()!=null){
            criteria.andEqualTo("state",vo.getState());
        }
        if(null != vo.getActionType()){
            criteria.andEqualTo("actionType",vo.getActionType());
        }
        if(null != vo.getRuleType()){
            criteria.andEqualTo("ruleType",vo.getRuleType());
        }
        if(StringUtils.isNotBlank(vo.getEventCodes())){
            criteria.andCondition("FIND_IN_SET('"+vo.getEventCodes()+"',event_codes)>0");
        }
        if(StringUtils.isNotBlank(vo.getRuleIds())){
            criteria.andCondition("FIND_IN_SET('"+vo.getRuleIds()+"',rule_ids)>0");
        }
        PageHelper.startPage(page,size," id desc");
        PageInfo pageInfo = new PageInfo(getMapper().selectByExample(example));
        pageInfo.setList(fetchRelation(pageInfo.getList()));
        return pageInfo;
    }

    private List<AlarmVO> fetchRelation(List<Alarm> alarms){
        List<AlarmVO> result = Lists.newArrayList();
        if(CollectionUtils.isEmpty(alarms)){
            return result;
        }
        alarms.forEach(alarm -> {
            AlarmVO vo = new AlarmVO(alarm);
            vo.setTypeName(AlarmType.getByCode(alarm.getType()).getName());
            vo.setRuleTypeName(vo.getRuleType() == RuleType.DEFAULT.getCode() ? "全类型" : RuleType.getByCode(alarm.getRuleType()).getName());
            fetchEvents(vo);
            fetchRules(vo);
            fetchConfig(vo);
            result.add(vo);
        });
        return result;
    }

    private void fetchEvents(AlarmVO vo){
        if(StringUtils.isBlank(vo.getEventCodes()) || Constants.ALARM_EVENTS_ALL.equals(vo.getEventCodes())){
            return;
        }
        String[] eventCodes = vo.getEventCodes().split(",");
        vo.setEvents(eventService.list(Lists.newArrayList(eventCodes)));
    }

    private void fetchRules(AlarmVO vo){
        if(StringUtils.isBlank(vo.getRuleIds()) || Constants.ALARM_RULES_ALL.equals(vo.getRuleIds())){
            return;
        }
        List<Long> ruleIds = RiskModelIdUtils.split(vo.getRuleIds());
        List<AtomRule> atomRuleList = atomRuleService.selectByIds(ruleIds);
        vo.setAtomRules(atomRuleList);
    }

    private void fetchConfig(AlarmVO vo){
        AlarmConfig alarmConfig = this.alarmConfigService.get(vo.getConfigId());
        if(alarmConfig != null){
            AlarmConfigVO alarmConfigVO = new AlarmConfigVO(alarmConfig);
            vo.setConfig(alarmConfigVO);
            alarmConfigVO.setCountTypeName(AlarmConfigCountType.codeOf(alarmConfig.getCountType()).getName());
        }
    }

    @Transactional
    @Override
    public boolean updateStatus(AlarmVO vo) {
        Alarm alarm = this.get(vo.getId());
        Alarm upAlarm = new Alarm();
        upAlarm.setId(vo.getId());
        upAlarm.setState(vo.getState());
        upAlarm.setModifier(vo.getModifier());
        if(vo.getState() == CommonStatus.DELETE.getCode()
                && alarm.getRelatedTemplateCode() == AlarmRelatedTemplateCode.NOT_RELATED.getCode()){
            AlarmConfig alarmConfig = new AlarmConfig();
            alarmConfig.setId(alarm.getConfigId());
            alarmConfig.setState(CommonStatus.DELETE.getCode());
            alarmConfig.setModifier(vo.getModifier());
            alarmConfigService.updateSelectiveById(alarmConfig);
        }
        this.updateSelectiveById(upAlarm);
        return true;
    }

    @Transactional
    @Override
    public boolean generateTemplate(AlarmVO vo) {
        Alarm alarm = this.get(vo.getId());
        if(alarm.getRelatedTemplateCode() == AlarmRelatedTemplateCode.RELATED.getCode()){
            throw new RiskException("该标签已经关联了模板，不能生成模板！");
        }
        Alarm upAlarm = new Alarm();
        upAlarm.setId(vo.getId());
        upAlarm.setModifier(vo.getModifier());
        upAlarm.setRelatedTemplateCode(AlarmRelatedTemplateCode.RELATED.getCode());
        AlarmConfig alarmConfig = new AlarmConfig();
        alarmConfig.setId(alarm.getConfigId());
        alarmConfig.setType(AlarmConfigType.TEMPLATE.getCode());
        alarmConfig.setModifier(vo.getModifier());
        this.updateSelectiveById(upAlarm);
        alarmConfigService.updateSelectiveById(alarmConfig);
        return true;
    }

    @Override
    public AlarmVO getDetailById(Long id) {
        AlarmVO alarmVO = new AlarmVO(this.get(id));
        AlarmConfigVO alarmConfigVO = new AlarmConfigVO(alarmConfigService.get(alarmVO.getConfigId()));
        alarmVO.setConfig(alarmConfigVO);
        alarmConfigVO.setRuleGroup(alarmRuleGroupService.listByConfigId(alarmConfigVO.getId()));
        fetchEvents(alarmVO);
        fetchRules(alarmVO);
        return alarmVO;
    }

    @Transactional
    @Override
    public boolean updateAlarm(AlarmVO vo) {
        //从关联模板 ->  新增了配置
        if(vo.getConfigId() == 0){
            //由于可能带回模板数据，先要清楚之前的数据，让系统自动生成
            clearPreRuleData(vo.getConfig());
            vo.getConfig().setName(vo.getName()+"_template");
            vo.getConfig().setType(AlarmConfigType.CUSTOME.getCode());
            vo.getConfig().setId(null);
            Long configId = alarmConfigService.saveAlarmConfig(vo.getConfig());
            vo.setConfigId(configId);
        }else if(vo.getConfig().getType() == AlarmConfigType.CUSTOME.getCode()){
            //修改了原有自定义配置
            vo.getConfig().setName(null);
            alarmConfigService.updateAlarmConfig(vo.getConfig());
        }else{
            Alarm alarm = this.get(vo.getId());
            //如果是从原有不关联模板变成了关联模板，原有自定义配置状态改成删除
            if(alarm.getRelatedTemplateCode() == AlarmRelatedTemplateCode.NOT_RELATED.getCode()){
                AlarmConfig alarmConfig = new AlarmConfig();
                alarmConfig.setId(alarm.getConfigId());
                alarmConfig.setState(CommonStatus.DELETE.getCode());
                alarmConfig.setModifier(vo.getModifier());
                alarmConfigService.updateSelectiveById(alarmConfig);
            }
        }

        if(vo.getConfig().getType() == AlarmConfigType.TEMPLATE.getCode()){
            vo.setRelatedTemplateCode(AlarmRelatedTemplateCode.RELATED.getCode());
        }else{
            vo.setRelatedTemplateCode(AlarmRelatedTemplateCode.NOT_RELATED.getCode());
        }
        this.updateSelectiveById(vo);
        return true;
    }

    @Transactional
    @Override
    public boolean updateFuseAlarm(AlarmVO vo) {
        alarmConfigService.updateAlarmConfig(vo.getConfig());
        vo.setRelatedTemplateCode(AlarmRelatedTemplateCode.NOT_RELATED.getCode());
        this.updateSelectiveById(vo);
        return true;
    }

    private List<Alarm> selectAllAlarm(AlarmVO vo){
        Example example = new Example(Alarm.class);
        Example.Criteria criteria = example.createCriteria();
        if(null != vo){
            if(null != vo.getActionType()){
                criteria.andEqualTo("actionType",vo.getActionType());
            }
        }
        criteria.andIn("state",Lists.newArrayList(CommonStatus.ENABLE.getCode(),CommonStatus.DISABLE.getCode()));
        example.setOrderByClause("id");
        List<Alarm> list = this.getMapper().selectByExample(example);
        return list == null ? Lists.newArrayList() : list;
    }

    @Override
    public List<Map<String, Object>> simpleAll(AlarmVO vo) {
        List<Map<String,Object>> list = Lists.newArrayList();
        for (Alarm alarm : selectAllAlarm(vo)) {
            Map<String, Object> result = new HashMap<>();
            result.put("id",alarm.getId());
            result.put("name",alarm.getName());
            list.add(result);
        }
        return list;
    }

    @Override
    public Alarm getByName(String name) {
        Example example = new Example(Alarm.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("state", Lists.newArrayList(CommonStatus.ENABLE.getCode(),CommonStatus.DISABLE.getCode()));
        criteria.andEqualTo("name",name);
        List<Alarm> list = this.getMapper().selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    private void clearPreRuleData(AlarmConfigVO configVO){
        for(AlarmRuleGroupVO ruleGroupVO : configVO.getRuleGroup()){
            ruleGroupVO.setId(null);
            ruleGroupVO.setCreateTime(null);
            ruleGroupVO.setUpdateTime(null);
            for(AlarmRuleAtomVO ruleAtomVO : ruleGroupVO.getRules()){
                ruleAtomVO.setId(null);
                ruleAtomVO.setCreateTime(null);
                ruleAtomVO.setUpdateTime(null);
            }
        }
    }

    @Override
    public List<AlarmVO> searchAlarm(Integer type,Integer countType,Integer state) {
        AlarmConfig alarmConfig = new AlarmConfig();
        alarmConfig.setCountType(countType);
        alarmConfig.setState(AlarmState.ENABLE.getType());
        List<AlarmConfig> alarmConfigs =  this.alarmConfigService.select(alarmConfig);

        if(CollectionUtils.isEmpty(alarmConfigs)){
            return null;
        }
        Example example = new Example(Alarm.class,false);
        Example. Criteria criteria =  example.createCriteria();

        List<Long> configIds = alarmConfigs.stream().map(AlarmConfig::getId).collect(Collectors.toList());
        criteria.andIn("configId",configIds);
        if(type!=null){
            criteria.andEqualTo("type",type);
        }
        if(state!=null){
            criteria.andEqualTo("state",state);
        }
        criteria.andLessThan("startTime", new Date());
        List<Alarm> alarms = getMapper().selectByExample(example);
        List<AlarmVO> alarmVOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(alarms)){
            Map<Long,AlarmConfig> alarmConfigMap = alarmConfigs.stream().collect(Collectors.toMap(AlarmConfig::getId,a -> a));
            Map<Long,List<AlarmRuleGroupVO>> ruleGroupMap = Maps.newHashMap();
            alarms.forEach(alarm -> {
                AlarmVO vo = new AlarmVO(alarm);
                fetchEvents(vo);
                fetchRules(vo);
                vo.setConfig(new AlarmConfigVO(alarmConfigMap.get(vo.getConfigId())));
                if(null == ruleGroupMap.get(alarm.getConfigId())){
                    List<AlarmRuleGroupVO> ruleGroupVOS =  alarmRuleGroupService.listByConfigId(alarm.getConfigId());
                    ruleGroupMap.put(alarm.getConfigId(),ruleGroupVOS);
                }
                vo.getConfig().setRuleGroup(ruleGroupMap.get(alarm.getConfigId()));
                alarmVOS.add(vo);
            });
        }
        return alarmVOS;
    }
}
