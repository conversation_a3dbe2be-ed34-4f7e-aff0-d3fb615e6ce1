package com.yupaopao.risk.console;

public class TopicConstants {

    public final static String KAFKA_MASERATI = "middleware.kafka-maserati";
    public final static String REDIS_RISK = "middleware.redis.risk";

    /**
     * 风控策略配置相关
     */
    public final static String KAFKA_MASERATI_RISK_EVENT = "maserati_ypp_fengkong.risk_event";
    public final static String KAFKA_MASERATI_RISK_RULE_ATOM = "maserati_ypp_fengkong.risk_rule_atom";
    public final static String KAFKA_MASERATI_RISK_RULE_GROUP = "maserati_ypp_fengkong.risk_rule_group";
    public final static String KAFKA_MASERATI_RISK_FACTOR = "maserati_ypp_fengkong.risk_factor";
    public final static String KAFKA_MASERATI_RISK_ATTRIBUTE = "maserati_ypp_fengkong.risk_attribute";
    public final static String KAFKA_MASERATI_RISK_GRAY_LIST = "maserati_ypp_fengkong.risk_gray_list";
    public final static String KAFKA_MASERATI_RISK_FENCING_SCENE = "maserati_ypp_fengkong.risk_fencing_scene";
    public final static String KAFKA_MASERATI_RISK_FENCING_TAG = "maserati_ypp_fengkong.risk_fencing_tag";
    public final static String KAFKA_MASERATI_RISK_FENCING_WORD = "maserati_ypp_fengkong.risk_fencing_word";
    public final static String KAFKA_MASERATI_RISK_PATROL_RULE = "maserati_ypp_fengkong.risk_patrol_rule";
    public final static String KAFKA_MASERATI_RISK_FENCING_LETTER = "maserati_ypp_fengkong.risk_fencing_letter";
    public final static String KAFKA_MASERATI_RISK_BIZ_TYPE = "maserati_ypp_fengkong.risk_biz_type";
    public final static String KAFKA_MASERATI_RISK_THIRD_CHANNEL = "maserati_ypp_fengkong.risk_third_channel";
    /**
     * 风控惩罚服务相关
     */
    public final static String KAFKA_MASERATI_PUNISH_VIOLATION = "maserati_ypp_fengkong.t_punish_violation";
    public final static String KAFKA_MASERATI_PUNISH_SUBSCRIBE_PKG = "maserati_ypp_fengkong.t_punish_subscribe_pkg";
    public final static String KAFKA_MASERATI_PUNISH_SUBSCRIBE_VIOLATION = "maserati_ypp_fengkong.t_punish_subscribe_violation";
    public final static String KAFKA_MASERATI_PUNISH_ABILITY = "maserati_ypp_fengkong.t_punish_ability";
    public final static String KAFKA_MASERATI_PUNISH_PKG = "maserati_ypp_fengkong.t_punish_pkg";
    public final static String KAFKA_MASERATI_PUNISH_VIOLATION_REASON = "maserati_ypp_fengkong.t_punish_violation_reason";

    /**
     * 其他
     */
    public final static String RISK_AUDIT_REISSUE = "risk-audit-reissue";
    public final static String RISK_BUSINESS_REISSUE = "risk-business-reissue";
    public final static String TOPIC_RISK_SYNC_RISK_GRAY = "risk-sync-risk-gray";
    public final static String KAFKA_RISK_COMMON_CHANGE_LOG = "RISK-COMMON-CHANGE-LOG";

}
