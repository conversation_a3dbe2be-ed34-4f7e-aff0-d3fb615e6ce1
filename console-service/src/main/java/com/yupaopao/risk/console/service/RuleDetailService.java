package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.bean.AtomRuleDTO;
import com.yupaopao.risk.console.bean.RuleHitQuery;

import java.util.List;

/**
 * 规则详情
 * author: wang<PERSON><PERSON>
 * date: 2021/6/30 15:03
 */
public interface RuleDetailService {
    int getEventCount(Long ruleId); // 根据规则ID获取关联事件数

    int getRiskCount(RuleHitQuery ruleHitQuery); // 获取命中次数

    Long getRiskUserCount(RuleHitQuery ruleHitQuery); // 获取命中用户数

    Long getRiskMobileCount(RuleHitQuery ruleHitQuery); // 获取命中手机号数

    List<EventVO> getEvents(Long ruleId); // 获取事件列表

    AtomRuleDTO getRuleDetail(Long ruleId); // 获取规则详情
}
