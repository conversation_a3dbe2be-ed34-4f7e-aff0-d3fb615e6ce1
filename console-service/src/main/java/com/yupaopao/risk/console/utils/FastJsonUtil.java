package com.yupaopao.risk.console.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;

//FASTJSON 工具类
public class

FastJsonUtil {
	
	//JSON转换
	public static String toJSONString(Object object){
		return JSON.toJSONString(object,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//JSON转换
	public static String toJSONString(Object object,Class<?> clazz){
		SimplePropertyPreFilter filter = new SimplePropertyPreFilter(clazz);
		return JSON.toJSONString(object,filter,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//包含指定属性
	public static String toJSONStringProperty(Object object,Class<?> clazz,String... properties){
		SimplePropertyPreFilter filter = new SimplePropertyPreFilter(clazz, properties);
		return JSON.toJSONString(object,filter,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//指定枚举配置 - 转换成{"index":100,"desc":"描述"}
	public static String toJSONStringEnum(Object object,Class<? extends Enum>... enumClasses){
		SerializeConfig config = new SerializeConfig();
		config.configEnumAsJavaBean(enumClasses);
		return JSON.toJSONString(object,config,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//包含指定属性且指定枚举配置
	public static String toJSONStringPropertyEnum(Object object,Class<?> clazz,String[] properties,Class<? extends Enum>... enumClasses){
		SimplePropertyPreFilter filter = new SimplePropertyPreFilter(clazz, properties);
		SerializeConfig config = new SerializeConfig();
		config.configEnumAsJavaBean(enumClasses);
		return JSON.toJSONString(object,config,filter,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//保留null
	public static String toJSONStringNull(Object object){
		return JSON.toJSONString(object,SerializerFeature.WriteMapNullValue);
	}
		
	//保留null且双引号KEY
	public static String toJSONStringNullQuote(Object object){
		return JSON.toJSONString(object,SerializerFeature.WriteMapNullValue,SerializerFeature.QuoteFieldNames);
	}
	
	//排除指定属性
	public static String toJSONStringExcludeProperty(Object object,final String... properties){
		PropertyFilter filter = new PropertyFilter() {
			
			@Override
			public boolean apply(Object object, String name, Object value) {
				
				for(String property:properties){
					if(name.equalsIgnoreCase(property)){
						return false;//false 排除字段
					}
				}
				return true;
			}
		};
		return JSON.toJSONString(object,filter,SerializerFeature.DisableCircularReferenceDetect);
	}
	
	//排除指定属性且指定枚举配置
	public static String toJSONStringExcludePropertyEnum(Object object,final String[] properties,Class<? extends Enum>... enumClasses){
		PropertyFilter filter = new PropertyFilter() {
			
			@Override
			public boolean apply(Object object, String name, Object value) {
				
				for(String property:properties){
					if(name.equalsIgnoreCase(property)){
						return false;//false 排除字段
					}
				}
				return true;
			}
		};
		SerializeConfig config = new SerializeConfig();
		config.configEnumAsJavaBean(enumClasses);
		return JSON.toJSONString(object,config,filter,SerializerFeature.DisableCircularReferenceDetect);
	}

}
