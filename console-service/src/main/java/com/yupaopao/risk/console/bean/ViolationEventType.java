package com.yupaopao.risk.console.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViolationEventType implements Serializable {

    private List<String> level;
    private List<String> secondTag;
    private List<ViolationEventPropertyMapping> riskActionPropertyMappings;
    private List<ViolationEventPropertyMapping> riskResultPropertyMappings;
}
