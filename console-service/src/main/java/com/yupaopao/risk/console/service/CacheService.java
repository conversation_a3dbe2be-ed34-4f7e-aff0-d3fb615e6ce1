package com.yupaopao.risk.console.service;


import com.yupaopao.risk.console.bean.CacheOperation;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

public interface CacheService {

    Object execute(CacheOperation cacheOperation);

    List<Hint> hints();

    @Setter
    @Getter
    @ToString
    class Hint {
        private String name;

        private List<String> operations;

        private String type;

        private String key;

        private String field;

        private String value;
    }

}
