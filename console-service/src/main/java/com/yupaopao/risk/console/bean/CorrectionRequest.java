package com.yupaopao.risk.console.bean;

import lombok.Data;

import java.io.Serializable;

@Data
public class CorrectionRequest implements Serializable {
    //服务标识，可选值：POST_TEXT|POST_IMG|POST_AUDIO|POST_AUDIOSTREAM|POST_EVENT|ACCOUNT_LOGIN|ACCOUNT_REGISTER
    private String serviceId;
    //误杀、漏杀类型，误杀传error,漏杀传miss
    private String type;
    //账号标识，文本、图片、天网服务会加黑白名单
    private String tokenId;
    //流水号
    private String requestId;
    //当条记录13位时间戳
    private String timeStamp;
    //期望处置结果，可选值：PASS|REJECT
    private String riskLevel;
}
