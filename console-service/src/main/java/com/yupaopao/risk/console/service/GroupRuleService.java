package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.enums.RuleStatus;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.GroupRule;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GroupRuleService extends BaseService<GroupRule> {

    List<GroupRule> relationFetch(List<GroupRule> list);

    List<GroupRule> relationFetchWithEvent(List<GroupRule> list);

    List<GroupRule> selectByIds(List<Long> ids);

    List<GroupRule> searchByIds(List<Long> ids);

    List<GroupRule> searchGroupAndAtomByIds(List<Long> ids);

    /**
     * 根据ID改变规则状态
     *
     * @param id
     * @param status
     * @return
     */
    boolean changeStatus(long id, RuleStatus status);

    List<Event> selectEventsByGroupRuleId(Long groupRuleId);

}
