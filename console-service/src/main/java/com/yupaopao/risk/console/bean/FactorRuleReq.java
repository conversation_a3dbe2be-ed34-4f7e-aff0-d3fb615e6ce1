package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.Attribute;

import java.util.List;

/**
 * 因子规则请求
 * author: wang<PERSON><PERSON>
 * date: 2021/7/1 18:00
 */
public class FactorRuleReq {
    private Long ruleId; // 规则ID
    private List<Attribute> attributes;

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }
}
