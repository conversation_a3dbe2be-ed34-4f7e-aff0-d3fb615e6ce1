package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.ParamEvent;
import com.yupaopao.risk.common.model.ParamValidator;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;
import java.util.Set;

public interface ParamEventService extends BaseService<ParamEvent> {
    /**
     * 根据规则Id获取所有的绑定关系
     *
     * @param paramValidatorIds 参数校验规则Id
     * @return
     */
    List<ParamEvent> getByParamValidatorIds(Set<Long> paramValidatorIds);

    /**
     * 根据EventCode获取相应的绑定关系
     *
     * @param eventCode
     * @return
     */
    List<ParamEvent> getByEventCode(String eventCode);

    /**
     * 根据事件code删除已绑定的规则
     *
     * @param eventCode
     * @return
     */
    boolean deleteByEventCode(String eventCode);

    void createMappingByEventCode(String eventCode, Set<Long> newParamValidatorIds, String currentUser);

}
