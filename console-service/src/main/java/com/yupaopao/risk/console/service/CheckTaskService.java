package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.common.model.SampleMark;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.ValidResult;
import com.yupaopao.risk.console.vo.CheckTaskVO;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2021/6/23 16:42
 */
public interface CheckTaskService extends BaseService<CheckTask> {

    PageInfo<CheckTask> search(User user, CheckTask record, Integer page, Integer size);

    Boolean add(CheckTask record, User user);

    ValidResult validSearchSql(String sql,String sampleType);

    CheckTaskVO detail(Long taskId);

    Boolean evaluate(CheckTask record,User user);

    <PERSON><PERSON><PERSON> cancel(Long taskId, User user);

    Boolean toMark(CheckTask record, User user);

    CheckTaskVO getCheckTask(Long taskId);

    boolean batchUpdateResult(List<SampleMark> sampleMarks,User user);

    void execute(CheckTask record);

    SampleMark create(Map<String, Object> record, String sampleType, Long taskId);

}


