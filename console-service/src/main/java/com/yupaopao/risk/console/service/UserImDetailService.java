package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.console.vo.LogSearchVO;

import java.util.List;
import java.util.Map;

public interface UserImDetailService {

    // 获取用户列表信息
    List<Map<String, Object>> getUsers(LogSearchVO.ImUserLogSearchVO vo);

    long countUsers(LogSearchVO.ImUserLogSearchVO vo);

    // 获取聊天内容
    PageInfo<Map<String, Object>> getContent(LogSearchVO.ImContentLogSearchVO vo);

    // 获取被举报人最近聊天记录的app
    String getRecentApp(LogSearchVO.ImContentLogSearchVO vo);

    // 获取Hit图片
    PageInfo<Map<String, Object>> getImage(LogSearchVO.HitLogImageSearchVO vo);

    // 获取Hit信息-扩展方式
    PageInfo<Map<String, Object>> getExtend(LogSearchVO.HitLogExtendSearchVO vo);

    // 获取Hit信息-聚合方式
    Map<String, Object> getAggregation(LogSearchVO.HitLogAggregationVO vo);

    // 获取举报原因
    List<Map<String,Object>> getComplainReasonList();

    // 获取apps
    List<Map<String,Object>> getApps();

    // 产生举报
    Map<String,Object> executeComplain(Map<String,Object> params);

}




