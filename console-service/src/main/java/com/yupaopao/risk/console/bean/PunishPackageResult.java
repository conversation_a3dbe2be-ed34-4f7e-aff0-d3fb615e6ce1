package com.yupaopao.risk.console.bean;

import com.yupaopao.platform.common.annotation.Description;
import com.yupaopao.risk.console.enums.PunishObjectType;
import com.yupaopao.risk.console.service.PunishCenterService;
import com.yupaopao.risk.punish.result.PunishDetail;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class PunishPackageResult<T> implements Serializable {

    private T req;

    private PunishObjectType punishObjectType;

    @Description("打包惩罚结果 1:成功; 2:失败; 3:部分成功; 4:超时; 5:调用异常")
    private Integer code;

    @Description("结果明细")
    private List<PunishDetail> detail;

    private PunishCenterService.PunishCenterRequest punishCenterRequest;

    public PunishPackageResult(T req, PunishObjectType punishObjectType,Integer code, List<PunishDetail> detail) {
        this.req = req;
        this.punishObjectType = punishObjectType;
        this.code = code;
        this.detail = detail;
    }

    public PunishPackageResult(T req, PunishCenterService.PunishCenterRequest punishCenterRequest, Integer code, List<PunishDetail> detail) {
        this.req = req;
        this.punishCenterRequest = punishCenterRequest;
        this.code = code;
        this.detail = detail;
    }
}
