package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.CheckTaskOperateLog;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/6/23 16:42
 */
public interface CheckTaskOpLogService extends BaseService<CheckTaskOperateLog> {

    /**
     * 获取抽检任务的创建人
     * @param taskId
     * @return
     */
    String getCreator(Long taskId);

    /**
     * 获取抽检任务关联的操作日志列表
     * @param taskId
     * @return
     */
    List<CheckTaskOperateLog> getCheckTaskOpLogList(Long taskId);

}


