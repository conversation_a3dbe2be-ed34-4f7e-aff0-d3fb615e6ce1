package com.yupaopao.risk.console.vo;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PunishRecordVO implements Serializable {
    private static final long serialVersionUID = -4576460312292085324L;

    @Description("处罚时间")
    private Date createTime;

    @Description("处罚来源")
    private String plateForm;

    @Description("对内展示原因")
    private String inReason;

    @Description("对外展示原因")
    private String reason;

    @Description("处罚类型")
    private String freezeTypeName;

    @Description("处罚场景")
    private String modeName;

    @Description("处罚凭证")
    private List<Map> evidences;

    @Description("处罚业务类型")
    private Integer type;

    @Description("处罚业务类型名")
    private String typeName;
}
