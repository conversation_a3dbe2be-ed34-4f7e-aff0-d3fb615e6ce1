package com.yupaopao.risk.console.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerServiceRecordVO implements Serializable {
    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务来源
     */
    private String taskSource;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 紧急程度，0=非紧急 1=紧急
     */
    private String urgentLevel;

    /**
     * 进线手机号码
     */
    private String phoneNumber;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 用户showNo
     */
    private Long showNo;

    /**
     * 任务创建人
     */
    private String taskCreator;

    /**
     * 任务处理人
     */
    private String taskAssignee;

    /**
     * 任务接收组
     */
    private String taskGroup;

    /**
     * 任务提示
     */
    private String taskHint;

    /**
     * 备注
     */
    private String taskRemark;

    /**
     * 任务状态 0=初始，待分配 1=处理中 2=预约中 3=已完成
     */
    private String taskStatus;

    /**
     * 额外信息，json格式
     */
    private String extraInfo;

    /**
     * 分配给当前处理人的时间
     */
    private Date assignTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
