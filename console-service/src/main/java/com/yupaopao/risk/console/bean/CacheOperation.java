package com.yupaopao.risk.console.bean;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Setter
@Getter
@ToString
public class CacheOperation implements Serializable {

    private Operation operation;

    private Type type;

    private String key;

    private String field;

    private String value;


    public enum Operation {
        GET,SET,DEL
    }

    public enum Type {
        STRING,LIST,HASH,SET,ZSET
    }

}
