package com.yupaopao.risk.console.service.impl;

import com.google.common.collect.Lists;
import com.yupaopao.risk.common.enums.AlarmConfigType;
import com.yupaopao.risk.common.enums.CommonStatus;
import com.yupaopao.risk.common.mapper.AlarmConfigMapper;
import com.yupaopao.risk.common.mapper.AlarmRuleAtomMapper;
import com.yupaopao.risk.common.mapper.AlarmRuleGroupMapper;
import com.yupaopao.risk.common.model.AlarmConfig;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AlarmConfigVO;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.console.service.AlarmConfigService;
import com.yupaopao.risk.console.service.AlarmRuleGroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2020/2/18 14:51
 */
@Service
public class AlarmConfigServiceImpl extends BaseServiceImpl<AlarmConfig, AlarmConfigMapper> implements AlarmConfigService {

    @Autowired
    private AlarmRuleGroupMapper ruleGroupMapper;
    @Autowired
    private AlarmRuleAtomMapper ruleAtomMapper;
    @Autowired
    private AlarmRuleGroupService alarmRuleGroupService;

    @Transactional
    @Override
    public Long saveAlarmConfig(AlarmConfigVO vo) {
        vo.setState(CommonStatus.ENABLE.getCode());
        if(vo.getMemo() == null){
            vo.setMemo("");
        }
        if(vo.getDdGroup() == null){
            vo.setDdGroup("");
        }
        this.getMapper().insertUseGeneratedKeys(vo);

        for(AlarmRuleGroupVO ruleGroupVO : vo.getRuleGroup()){
            ruleGroupVO.setConfigId(vo.getId());
            ruleGroupMapper.insertUseGeneratedKeys(ruleGroupVO);
            for(AlarmRuleAtomVO ruleAtomVO:ruleGroupVO.getRules()){
                ruleAtomVO.setRuleGroupId(ruleGroupVO.getId());
                ruleAtomMapper.insertSelective(ruleAtomVO);
            }
        }
        return vo.getId();
    }

    @Override
    public AlarmConfigVO getDetailById(Long id) {
        AlarmConfigVO alarmConfigVO = new AlarmConfigVO(this.get(id));
        alarmConfigVO.setRuleGroup(alarmRuleGroupService.listByConfigId(id));
        return alarmConfigVO;
    }

    @Transactional
    @Override
    public boolean updateAlarmConfig(AlarmConfigVO vo) {
        if(vo.getMemo() == null){
            vo.setMemo("");
        }
        if(vo.getDdGroup() == null){
            vo.setDdGroup("");
        }
        this.getMapper().updateByPrimaryKeySelective(vo);
        List<AlarmRuleGroupVO> list = alarmRuleGroupService.listByConfigId(vo.getId());
        List<Long> updateRuleGroupIds = Lists.newArrayListWithExpectedSize(vo.getRuleGroup().size());
        List<Long> updateRuleAtomIds = Lists.newArrayList();
        for(AlarmRuleGroupVO ruleGroupVO : vo.getRuleGroup()){
            if(ruleGroupVO.getId() != null){
                updateRuleGroupIds.add(ruleGroupVO.getId());
            }else{
                ruleGroupVO.setConfigId(vo.getId());
                ruleGroupMapper.insertUseGeneratedKeys(ruleGroupVO);
            }
            for(AlarmRuleAtomVO ruleAtomVO:ruleGroupVO.getRules()){
                if(ruleAtomVO.getId() != null){
                    updateRuleAtomIds.add(ruleAtomVO.getId());
                    ruleAtomMapper.updateByPrimaryKeySelective(ruleAtomVO);
                }else{
                    ruleAtomVO.setRuleGroupId(ruleGroupVO.getId());
                    ruleAtomMapper.insertSelective(ruleAtomVO);
                }
            }
        }
        for(AlarmRuleGroupVO ruleGroupVO : list){
            if(!updateRuleGroupIds.contains(ruleGroupVO.getId())){
                ruleGroupMapper.deleteByPrimaryKey(ruleGroupVO);
                for(AlarmRuleAtomVO ruleAtomVO:ruleGroupVO.getRules()){
                    ruleAtomMapper.deleteByPrimaryKey(ruleAtomVO);
                }
            }else{
                for(AlarmRuleAtomVO ruleAtomVO:ruleGroupVO.getRules()){
                    if(!updateRuleAtomIds.contains(ruleAtomVO.getId())){
                        ruleAtomMapper.deleteByPrimaryKey(ruleAtomVO);
                    }
                }
            }
        }
        return true;
    }

    private List<AlarmConfig> selectAllTemplate(){
        Example example = new Example(AlarmConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", AlarmConfigType.TEMPLATE.getCode());
        criteria.andEqualTo("state", CommonStatus.ENABLE.getCode());
        example.setOrderByClause("id");
        List<AlarmConfig> list = this.getMapper().selectByExample(example);
        return list == null ? Lists.newArrayList() : list;
    }

    @Override
    public List<Map<String,Object>> simpleAll() {
        List<Map<String,Object>> list = Lists.newArrayList();

        for (AlarmConfig config : selectAllTemplate()) {
            Map<String, Object> result = new HashMap<>();
            result.put("id",config.getId());
            result.put("name",config.getName());
            result.put("type",config.getType());
            list.add(result);
        }
        return list;
    }

    @Override
    public AlarmConfig getByName(String name) {
        Example example = new Example(AlarmConfig.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type", AlarmConfigType.TEMPLATE.getCode());
        criteria.andIn("state", Lists.newArrayList(CommonStatus.ENABLE.getCode(),CommonStatus.DISABLE.getCode()));
        criteria.andEqualTo("name",name);
        List<AlarmConfig> list = this.getMapper().selectByExample(example);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
