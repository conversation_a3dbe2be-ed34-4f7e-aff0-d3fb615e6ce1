package com.yupaopao.risk.console.service.impl;

import com.yupaopao.risk.common.enums.CheckTaskOpType;
import com.yupaopao.risk.common.mapper.CheckTaskOpLogMapper;
import com.yupaopao.risk.common.model.CheckTaskOperateLog;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.service.CheckTaskOpLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * author: lijianjun
 * date: 2021/6/23 16:42
 */
@Slf4j
@Service
public class CheckTaskOpLogServiceImpl extends BaseServiceImpl<CheckTaskOperateLog, CheckTaskOpLogMapper> implements CheckTaskOpLogService {


    @Override
    public String getCreator(Long taskId) {

        if(taskId==null){
            return "";
        }

        AtomicReference<String> creator = new AtomicReference<>("");
        CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
        operateLog.setTaskId(taskId);
        List<CheckTaskOperateLog> operateLogs = this.select(operateLog);
        if(CollectionUtils.isNotEmpty(operateLogs)){
            operateLogs.forEach(log->{
                log.setTypeName(CheckTaskOpType.getEnumByCode(log.getType()).getMsg());
                if(Objects.equals(log.getType(),CheckTaskOpType.CREATE.getCode())){
                    creator.set(log.getOperator());
                }
            });
        }

        return creator.get();
    }

    @Override
    public List<CheckTaskOperateLog> getCheckTaskOpLogList(Long taskId) {
        if(taskId==null){
            return Lists.newArrayList();
        }

        CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
        operateLog.setTaskId(taskId);

        return this.select(operateLog);
    }
}


