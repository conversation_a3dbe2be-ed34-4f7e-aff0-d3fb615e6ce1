package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.ViolationOperation;
import com.yupaopao.risk.common.model.ViolationStatistics;
import com.yupaopao.risk.common.service.BaseService;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ViolationService extends BaseService<ViolationOperation> {

    boolean update(ViolationOperation record);

    boolean batchUpdate(ViolationStatistics record);

    List<ViolationStatistics> statisticViolation(String msg_date);

    List<Map<String,String>> getViolationEventList();

    List<String> getViolationTagList();

    List<Map<String,String>> getViolationList();

}
