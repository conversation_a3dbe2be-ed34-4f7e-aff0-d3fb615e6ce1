package com.yupaopao.risk.console.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class TableRelation {

    private String tableName;
    private String setIdField;
    private String getIdField;
    private Map<String, List<Object>> inField;
    private Map<String,Object> eqField;

}
