package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.HotProhibitedWord;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.HotProhibitedWordVO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface HotProhibitedWordService extends BaseService<HotProhibitedWord> {

    /**
     * 获取隐藏的违禁词条和白词条
     * @return
     */
    List<String> getHideWordList();

    /**
     * 多条件查询
     * @param record 查询条件
     * @param page 第几页
     * @param size 页面大小
     * @return 查询结果
     */
    PageInfo<HotProhibitedWord> complexSearch(HotProhibitedWordVO record,Integer page,Integer size);

    /**
     * 多条件查询指定的违禁词条和白词条记录
     * @param word 查询违禁词条或白词条
     * @param startIndex 开始索引
     * @param endIndex 结束索引
     * @param page 第几页
     * @param size 页面大小
     * @return 指定的违禁词条和白词条记录
     */
    PageInfo<HotProhibitedWord> search(HotProhibitedWord word, Integer startIndex, Integer endIndex,Integer page,Integer size);

    /**
     * 多条件查询指定的违禁词条和白词条记录
     * @param word 查询违禁词条或白词条
     * @param startIndex 开始索引
     * @param endIndex 结束索引
     * @return 指定的违禁词条和白词条记录
     */
    List<HotProhibitedWord> search(HotProhibitedWord word, Integer startIndex, Integer endIndex);

    /**
     * 多条件查询指定的违禁词条和白词条记录
     * @param word 查询违禁词条或白词条
     * @return 指定的违禁词条和白词条记录
     */
    List<HotProhibitedWord> search(HotProhibitedWord word);

    /**
     * 批量插入
     * @param recordList
     * @return
     */
    int insertList(List<HotProhibitedWord> recordList);

    /**
     * 删除过期的记录
     * @param date
     * @return
     */
    int deleteExpireRecord(Date date);

    /**
     * 批量更新记录
     * @param recordList
     * @return
     */
    int updateList(List<HotProhibitedWord> recordList);

    /**
     * 批量更新记录隐藏值
     * @param record
     * @return
     */
    int update(HotProhibitedWord record);

    /**
     * 删除记录
     * @param record
     * @return
     */
    boolean deleteSelective(HotProhibitedWord record);

}
