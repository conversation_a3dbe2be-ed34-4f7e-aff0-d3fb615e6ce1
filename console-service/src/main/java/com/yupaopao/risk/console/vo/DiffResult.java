package com.yupaopao.risk.console.vo;

import lombok.*;

import java.io.Serializable;
import java.util.Map;

// 机审人审不一致日志
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DiffResult implements Serializable {
    private static final long serialVersionUID = 8456343421154997239L;

    private String businessCode; // 业务编号
    private String eventCode; // 事件编号

    //// 审核反馈预留信息
    private String traceId; // 追踪ID
    private String auditResult; // 审核结果(审核系统)
    private Map<String, String> content; // 文本内容、图片URL、音频URL、视频URL
    private String auditReason;

    //// 业务通知预留信息
    private String reason;
    private String level; // 风险级别

    //// 自定义
    private String traceType;
    private String createdAt; // 创建时间

    private Long feedbackCount; // 纠错反馈次数

}
