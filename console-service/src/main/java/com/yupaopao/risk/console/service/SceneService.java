package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface SceneService extends BaseService<Scene> {

    List<Scene> batchSearch(List<Long> sceneIds,Integer toSystem);

    List<Scene> batchSearch(List<Long> sceneIds);

    List<Scene> findByWordId(Long wordId);

    List<Scene> findByWordIdAndToSystems(Long wordId, Set<Integer> toSystems);

    List<Scene> findByToSystem(Integer toSystem);

    Set<String> searchNotScenes(Set<String> sceneNames);

    Set<String> searchNotIn(Set<String> sceneNames,Integer toSystem);

}
