package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.StrategyEvaluate;
import com.yupaopao.risk.common.model.StrategySampleMark;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.ValidResult;
import com.yupaopao.risk.console.vo.StrategyEvaluateVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface StrategyEvaluateService extends BaseService<StrategyEvaluate> {

    void freshCache();

    PageInfo<StrategyEvaluateVO> search(User user, StrategyEvaluateVO record, Integer page, Integer size);

    StrategyEvaluateVO getStrategyEvaluate(Long id);

    boolean batchUpdateResult(List<StrategySampleMark> stgySampleMarks, User user);

    Boolean add(StrategyEvaluateVO record, User user);

    ValidResult validSearchSql(String sql);

    StrategyEvaluateVO detail(Long taskId);

    Boolean evaluate(StrategyEvaluate record, User user);

    Boolean cancel(Long taskId, User user);

    Boolean toMark(StrategyEvaluate record, User user);

    void execute(StrategyEvaluateVO record);

    StrategySampleMark create(Map<String, Object> record, Long taskId);

    Map<String, Object> getAsyRuleHitResult(StrategyEvaluateVO record);

    Map<String, Object> getCepHitResult(StrategyEvaluateVO record);

    Map<String, Object> getOfflineHitResult(StrategyEvaluateVO record);

    Map<String, Object> getCustomizeSqlResult(StrategyEvaluateVO record);

    List<Map<String, String>> loadAllRules();

    List<Map<String, String>> loadRealRules();

    List<Map<String, String>> loadAsyRules();

    List<Map<String, String>> loadCepRules();

    List<Map<String, String>> loadOfflineRules();

}
