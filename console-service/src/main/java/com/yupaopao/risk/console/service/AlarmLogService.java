package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.AlarmLog;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmLogVO;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/18 14:50
 */
public interface AlarmLogService extends BaseService<AlarmLog> {

    PageInfo<AlarmLogVO> searchLog(AlarmLogVO alarmLogVO,Integer page,Integer size);

    boolean updateHandleState(AlarmLogVO vo);

    boolean updateForFuse(AtomRule atomRule,AlarmLog alarmLog);
}
