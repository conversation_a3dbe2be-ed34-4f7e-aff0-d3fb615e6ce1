package com.yupaopao.risk.console.bean;

import java.io.Serializable;

/**
 * TemplateWord 用于导入批量违禁词的模板
 * <AUTHOR>
 */
public class TemplateWord implements Serializable {

    private String content;
    private Integer weight;

    private String toSystem;
    private String wordLocationTypeName;
    private String firstTagNames;
    private String tagNames;

    private String sceneNames;
    private String matchTypeName;

    private String source;
    private String comment;
    private String author;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getToSystem() {
        return toSystem;
    }

    public void setToSystem(String toSystem) {
        this.toSystem = toSystem;
    }

    public String getWordLocationTypeName() {
        return wordLocationTypeName;
    }

    public void setWordLocationTypeName(String wordLocationTypeName) {
        this.wordLocationTypeName = wordLocationTypeName;
    }

    public String getFirstTagNames() {
        return firstTagNames;
    }

    public void setFirstTagNames(String firstTagNames) {
        this.firstTagNames = firstTagNames;
    }

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

    public String getSceneNames() {
        return sceneNames;
    }

    public void setSceneNames(String sceneNames) {
        this.sceneNames = sceneNames;
    }

    public String getMatchTypeName() {
        return matchTypeName;
    }

    public void setMatchTypeName(String matchTypeName) {
        this.matchTypeName = matchTypeName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }
}
