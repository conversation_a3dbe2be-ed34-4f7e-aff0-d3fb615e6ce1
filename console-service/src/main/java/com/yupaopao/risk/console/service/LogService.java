package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Log;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmLogVO;
import com.yupaopao.risk.console.bean.RiskLogReq;

public interface LogService extends BaseService<Log> {
    PageInfo<Log> search(RiskLogReq req, Integer page, Integer size);
}
