package com.yupaopao.risk.console;

public class ConstantsForApollo {

    //key
    public final static String DICT_LOAD_REFRESH = "dict.load"; //违禁词词典缓存更新
    public final static String ENGINE_RELOAD_REFRESH = "engine.reload"; //规则树缓存更新
    public final static String EVENT_CACHE_REFRESH = "enable.event.cache.key"; //事件缓存刷新
    public final static String FACTOR_COMPUTER_REFRESH = "factor.reload"; //累计因子缓存刷新
    public final static String JOB_GRAYLIST_TO_REDIS_REFRESH = "job.graylist2redis.reload"; // graylist 刷新到redis
    public final static String RECALL_CONSUMER_FLAG = "recall.consumer.flag"; //回溯消费明细开关
    public final static String BIZ_TYPE_RELOAD = "bizType.reload";

    public final static String PATROL_RULE_RELOAD_REFRESH = "patrol.rule.reload"; // 巡检规则缓存刷新
    public final static String COMPLAIN_RULE_RELOAD_REFRESH = "complain.rule.reload"; // 举报规则缓存刷新

    public final static String ASYNCHRONOUS_RULE_RELOAD_REFRESH = "asynchronous.rule.reload"; //异步规则缓存刷新

    public final static String HOT_WORD_CUT_WORD_CONFIG = "hot.word.cut.word.config"; // 提取im场景下热词切词属性配置
    public final static String LIVE_ROOM_HOT_WORD_CUT_WORD_CONFIG = "live.room.hot.word.cut.word.config"; // 提取live-room场景下热词切词属性配置
    public final static String CHAT_ROOM_HOT_WORD_CUT_WORD_CONFIG = "chat.room.hot.word.cut.word.config"; // 提取chat-room场景下热词切词属性配置

    //namespace
    public final static String APPLICATION = "application";
    public final static String EVENT_CACHE = "middleware.public-event-cache";

    //appId
    public final static String RISK_TEXT = "risk-text";
    public final static String RISK_ENGINE = "risk-engine";
    public final static String RISK_ACCESS = "risk-access";
    public final static String RISK_MAGIC = "risk-magic";
    public final static String RISK_SHOOT = "risk-shoot";

    //env
    public final static String ENV_PROD = "PRO";
    public final static String ENV_TEST = "TEST";

    //cluster
    public final static String CLUSTER_DEFAULT = "default";
}
