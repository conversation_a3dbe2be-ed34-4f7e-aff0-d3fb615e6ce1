package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

public class DataCleanVO implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 黑名单列表
     */
    private String blackGrayList;

    /**
     * 累计因子id列表
     */
    private String factorIds;

    /**
     * 备注
     */
    private String note;

    /**
     * 数美清洗情况，0=未执行，1=成功，2=失败
     */
    private Integer shumei;

    /**
     * 画像全量清洗情况，0=未执行，1=成功，2=失败
     */
    private Integer portrait;

    /**
     * 黑名单删除情况，0=未执行，1=成功，2=失败
     */
    private Integer blackGray;

    /**
     * 累计因子删除情况，0=未执行，1=成功，2=失败
     */
    private Integer factor;

    /**
     * 总体执行情况，0=未执行，1=成功，2=失败
     */
    private Integer result;

    /**
     * 异常情况明细
     */
    private String errorDetail;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getBlackGrayList() {
        return blackGrayList;
    }

    public void setBlackGrayList(String blackGrayList) {
        this.blackGrayList = blackGrayList;
    }

    public String getFactorIds() {
        return factorIds;
    }

    public void setFactorIds(String factorIds) {
        this.factorIds = factorIds;
    }

    public Integer getShumei() {
        return shumei;
    }

    public void setShumei(Integer shumei) {
        this.shumei = shumei;
    }

    public Integer getPortrait() {
        return portrait;
    }

    public void setPortrait(Integer portrait) {
        this.portrait = portrait;
    }

    public Integer getBlackGray() {
        return blackGray;
    }

    public void setBlackGray(Integer blackGray) {
        this.blackGray = blackGray;
    }

    public Integer getFactor() {
        return factor;
    }

    public void setFactor(Integer factor) {
        this.factor = factor;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getErrorDetail() {
        return errorDetail;
    }

    public void setErrorDetail(String errorDetail) {
        this.errorDetail = errorDetail;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
