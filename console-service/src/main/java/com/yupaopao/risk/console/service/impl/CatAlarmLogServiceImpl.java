package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.mapper.CatAlarmLogMapper;
import com.yupaopao.risk.common.model.CatAlarmLog;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.bean.CatAlarmLogVO;
import com.yupaopao.risk.console.service.CatAlarmLogService;
import com.yupaopao.risk.console.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
public class CatAlarmLogServiceImpl extends BaseServiceImpl<CatAlarmLog, CatAlarmLogMapper> implements CatAlarmLogService {

    @Override
    public PageInfo<CatAlarmLogVO> searchLog(CatAlarmLogVO catAlarmLogVO, int page, int size) {
        Example example = new Example(CatAlarmLog.class);
        Example.Criteria criteria = example.createCriteria();
        if(catAlarmLogVO.getAlarmStartTime() != null){
            criteria.andGreaterThanOrEqualTo("alarmTime", DateUtils.formatDate(catAlarmLogVO.getAlarmStartTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        if(catAlarmLogVO.getAlarmEndTime() != null){
            criteria.andLessThanOrEqualTo("alarmTime",DateUtils.formatDate(catAlarmLogVO.getAlarmEndTime(),DateUtils.YYYY_MM_DD_HH_MM_SS));
        }
        if(StringUtils.isNotBlank(catAlarmLogVO.getName())){
            criteria.andLike("name","%"+catAlarmLogVO.getName()+"%");
        }
        if(StringUtils.isNotBlank(catAlarmLogVO.getCheckType())){
            criteria.andEqualTo("checkType",catAlarmLogVO.getCheckType());
        }
        if(StringUtils.isNotBlank(catAlarmLogVO.getBizTypeCode())){
            criteria.andLike("bizTypeCode","%"+catAlarmLogVO.getBizTypeCode()+"%");
        }
        if(StringUtils.isNotBlank(catAlarmLogVO.getChannel())){
            criteria.andEqualTo("channel",catAlarmLogVO.getChannel());
        }
        example.setOrderByClause("id desc");
        PageHelper.startPage(page,size);
        List<CatAlarmLog> list = this.getMapper().selectByExample(example);
        PageInfo pageInfo = new PageInfo<>(list);
        return pageInfo;
    }
}
