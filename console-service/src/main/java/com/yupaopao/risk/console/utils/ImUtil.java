package com.yupaopao.risk.console.utils;

import org.apache.commons.lang3.StringUtils;

// IM详情工具类
public class ImUtil {
    private static String IM_MESSAGE = "im-message"; // IM单聊
    private static String PRIVATE_CHAT = "private-chat"; // 私聊
    private static String CHAT_ROOM = "chat-room"; // 聊天室
    private static String LIVE_ROOM = "live-room"; // 视频直播间、语音直播间公屏
    private static String CHAT_ROOM_SONA = "chat-room-sona";
    private static String IM_GROUP_MESSAGE = "im-group-message";

    public static String IM_USER_LOG_TAG = "IM USER SEARCH"; // IM用户日志标签
    public static String IM_CONTENT_LOG_TAG = "IM CONTENT SEARCH"; // IM内容日志标签


    // IM单聊
    public static boolean isImMessage(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && IM_MESSAGE.equals(eventCode)) {
            return true;
        }
        return false;
    }

    // 私聊
    public static boolean isPrivateChat(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && PRIVATE_CHAT.equals(eventCode)) {
            return true;
        }
        return false;
    }

    // 聊天室
    public static boolean isChatRoom(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && CHAT_ROOM.equals(eventCode)) {
            return true;
        }
        return false;
    }

    //公屏
    public static boolean isLiveRoom(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && LIVE_ROOM.contains(eventCode)) {
            return true;
        }
        return false;
    }

    //小游戏
    public static boolean isChatRoomSona(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && CHAT_ROOM_SONA.contains(eventCode)) {
            return true;
        }
        return false;
    }

    //小游戏
    public static boolean isImGroupMessage(String eventCode) {
        if (StringUtils.isNotBlank(eventCode) && IM_GROUP_MESSAGE.contains(eventCode)) {
            return true;
        }
        return false;
    }

    public static boolean isRiskHitLog(String eventCode){
        if (StringUtils.isNotBlank(eventCode) && (LIVE_ROOM.equals(eventCode) || CHAT_ROOM_SONA.equals(eventCode) || IM_GROUP_MESSAGE.equals(eventCode))) {
            return true;
        }
        return false;
    }
}
