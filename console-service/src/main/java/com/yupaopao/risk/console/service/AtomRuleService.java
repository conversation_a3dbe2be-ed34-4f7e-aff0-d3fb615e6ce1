package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.dto.RiskSubLabel;
import com.yupaopao.risk.common.enums.RuleStatus;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.bean.AtomRuleDTO;
import com.yupaopao.risk.console.bean.ConflictRulesCheckReq;

import java.util.List;
import java.util.Map;

public interface AtomRuleService extends BaseService<AtomRule> {

    List<AtomRuleDTO> relationFetch(List<AtomRule> list);

    List<AtomRule> selectByGroupId(Long groupId);

    List<AtomRule> selectByIds(List<Long> ids);

    /**
     * 根据ID改变规则状态
     * @param id
     * @param status
     * @return
     */
    boolean changeStatus(long id, RuleStatus status, User user);

    List<AtomRule> selectByDependent(String dependent);

    Map<String,Object> getConstFormatter(String dependent,Long id);

    Map<String,Object> getConstList(List<Event> events);

    List<AtomRule> getListRelatedScene();

    List<AtomRule> getListRelatedBizType();

    List<AtomRule> listByGroupIds(List<Long> groupIds);

    List<AtomRuleDTO> listByGroupIdsWithGroup(List<Long> groupIds);

    Map<Long,List<AtomRule>> mapByGroupIds(List<Long> groupIds);

    List<Map<String,Object>> conflictRulesCheck(ConflictRulesCheckReq req);

    Map<String, String> fetchReasons(Long ruleId);

    int countByBizTypeCode(String bizTypeCode);

    List<AtomRule> listByBizTypeCode(String bizTypeCode);

    Map<String, Object> simpleAll();

    List<Event> selectEventsByAtomRuleId(Long atomRuleId);

    List<RiskSubLabel> getSubLabels(AtomRule atomRule);
}
