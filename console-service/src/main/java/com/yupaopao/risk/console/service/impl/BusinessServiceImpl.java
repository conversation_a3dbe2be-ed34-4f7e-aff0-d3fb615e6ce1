package com.yupaopao.risk.console.service.impl;

import com.yupaopao.risk.common.mapper.BusinessMapper;
import com.yupaopao.risk.common.model.Business;
import com.yupaopao.risk.console.service.BusinessService;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class BusinessServiceImpl extends BaseServiceImpl<Business, BusinessMapper> implements BusinessService {

    @Override
    public Business getByCode(String code) {
        Business business = new Business();
        business.setCode(code);
        return get(business);
    }

}
