package com.yupaopao.risk.console.vo;

import com.yupaopao.payment.common.enums.App;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @time 2019/3/4 20:14
 */
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@ToString
@Builder
public class IncomeJournalVO implements Serializable {
    /**
     * 应用id
     */
    private App app;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 变动的大神收入
     */
    private BigDecimal amount;

    /**
     * 变动前的大神收入
     */
    private BigDecimal origin;
    
    /**
     * 流水类型
     * I 入
     * O 出
     */
    private String changeType;

    /**
     * 外部关联id
     */
    private String outId;

    /**
     * 备注
     */
    private String memo;

    /**
     * 流水类型
     */
    private String journalType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 来源用户id
     */
    private String fromUserId;

    /**
     * 来源用户id
     */
    @Deprecated
    private Long fromUid;

}
