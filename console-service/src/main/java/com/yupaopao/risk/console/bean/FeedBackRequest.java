package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Map;

public class FeedBackRequest implements Serializable {
    private static final long serialVersionUID = 1118619159448258671L;
    private String traceId; // 跟踪ID
    private String feedbackType; // 服务方
    private String eventCode; // 事件CODE
    private Map<String,Object> params; // 扩展参数，依据服务方决定
    private String resourceType; // 资源类型-文本、图片、音频、视频

    public FeedBackRequest() {
    }

    public FeedBackRequest(String traceId) {
        this.traceId = traceId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(String feedbackType) {
        this.feedbackType = feedbackType;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }
}
