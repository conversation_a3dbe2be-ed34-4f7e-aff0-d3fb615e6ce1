package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.MessageRecord;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/22 2:57 PM
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MessageRecordVO extends MessageRecord {

    private Date startTime;
    private Date endTime;

}
