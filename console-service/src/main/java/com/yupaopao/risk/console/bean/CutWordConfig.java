package com.yupaopao.risk.console.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CutWordConfig implements Serializable {

    /**
     * 提取top多少热词
     */
    private int size = 1000;

    /**
     * 是否是新词
     */
    private boolean newWordsOnly = true;

    /**
     * 提取的热词最大长度
     */
    private int max_word_len = 9;

    /**
     * 提取的热词最小频率
     */
    private float min_freq = 0.001f;

    /**
     * 提取的热词最小信息熵
     */
    private float min_entropy = 0.0f;

    /**
     * 提取的热词最小聚合度
     */
    private float min_aggregation = 6f;
}
