package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.AlarmRuleGroup;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2020/2/19 19:54
 */
public interface AlarmRuleGroupService extends BaseService<AlarmRuleGroup> {
    List<AlarmRuleGroupVO> listByConfigId(Long configId);
}
