package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.FirstTagNameSecondTag;
import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface TagService extends BaseService<Tag> {

//    List<Tag> batchSearch(List<Long> tagIds);

    List<Tag> batchSearch(List<Long> tagIds);

    List<Tag> findByWordId(Long wordId);

    /**
     * 根据toSystem,来获取相应的二级标签
     * @return
     */
    List<Tag> findSecTags();

    List<Tag> findAllSecTags();

    /**
     * 来获取所有的一级标签
     * @return
     */
    List<Tag> searchFirstTags();

    List<Tag> searchFirstTags(List<Long> tagIds);

    List<Tag> searchSecondTags(String firstTagName);

    List<FirstTagNameSecondTag> batchSearchSecondTagsByFirstTagNames(List<String> firstTagNames);

    PageInfo showInSel(Tag tag, Integer page, Integer size);

    Set<String> searchNotFirstTags(Set<String> firstTagNames);

    Set<String> searchNotSecondTags(Set<String> tagNames);

}
