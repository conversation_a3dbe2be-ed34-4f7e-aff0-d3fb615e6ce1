package com.yupaopao.risk.console.service;

import com.yupaopao.bixin.biggie.api.entity.BiggieInfoDTO;
import com.yupaopao.bixin.user.operation.api.dto.LoginDeviceRecordDTO;
import com.yupaopao.bixin.user.operation.api.dto.LoginDeviceRecordVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 解冻中心服务
 *
 * <AUTHOR>
 * @date 2021/9/22 10:25
 */
public interface UnfreezeCenterService {

    /**
     * 信息查询
     *
     * @param request
     * @return
     */
    UserInfoResponse getUserInfo(UserInfoRequest request);

    @Getter
    @Setter
    class UnfreezeRequest {
        private Long packageId;
        private Long uid;
        private String deviceId;
        private String roomId;
        private String proofImgUrl;
        private String internalReason;
        private String externalReason;
    }

    @Getter
    @Setter
    class UserInfoRequest {
        private String value;
        private String dimension;
        private String deviceId;
        private int page = 1;
        private Integer appId;
        private String nationCode;
    }

    @Getter
    @Setter
    class UserInfoResponse {
        /**
         * uid精度问题，前端只展示 定义为字符串即可
         */
        private String uid;
        private BiggieInfoDTO biggieInfo;
        private List<LoginDeviceRecordDTO> deviceInfos;
        private List<Map<String, Object>> freezeInfos;
        private int total = 0;
    }

}
