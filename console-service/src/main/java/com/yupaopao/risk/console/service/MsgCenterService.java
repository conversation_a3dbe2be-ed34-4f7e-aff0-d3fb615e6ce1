package com.yupaopao.risk.console.service;

import java.util.List;
import java.util.Map;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/10/20 17:34
 */
public interface MsgCenterService {
    /**
     * 钉钉群机器人，text消息类型
     * @param accessToken
     * @param content
     * @param toUsers
     */
    void dingTalkText(String accessToken, String content, List<String> toUsers);

    /**
     * 钉钉群机器人，markdown消息类型
     * @param accessToken
     * @param content
     * @param toUsers
     */
    void dingTalkMarkdown(String accessToken, String title,String text, List<String> toUsers);

    /**
     * 拨打电话
     * @param code
     * @param params
     * @param toUser
     * @param playTimes
     */
    void phone(String code, Map<String,String> params,String toUser,int playTimes);
}
