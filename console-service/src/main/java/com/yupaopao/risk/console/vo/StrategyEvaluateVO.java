package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.CheckTaskOperateLog;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.StrategyEvaluate;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class StrategyEvaluateVO extends StrategyEvaluate implements Serializable {

    /**
     * 创建人
     */
    private String creator;

    /**
     * 已标注数
     */
    private Integer markedCount;

    /**
     * 事件
     */
    private Event event;

    /**
     * 规则类型名
     */
    private String typeName;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 风控结果名
     */
    private String levelName;

    /**
     * 状态名
     */
    private String stateName;

    /**
     * 基础字段
     */
    private List<Map<String, String>> baseFields;

    /**
     * 操作人列表
     */
    private List<CheckTaskOperateLog> opLogs;

    public StrategyEvaluateVO() {
    }

    public StrategyEvaluateVO(StrategyEvaluate strategyEvaluate) {
        if (strategyEvaluate != null) {
            BeanUtils.copyProperties(strategyEvaluate,this);
        }
    }
}
