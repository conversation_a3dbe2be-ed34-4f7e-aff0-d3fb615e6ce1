package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.vo.GroupRuleVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 关系表
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface RuleRelationshipService {

    Map<String, List> search(String businessCode, String eventCode, Set<String> groupRuleId, Map map, User user);

    List<GroupRuleVO> getGroupRule(String businessCode, String eventCode, Set<String> groupRuleId, Map map);

    List<AtomRule> getAtomRule(List groupRuleId, Map map, User user);

}
