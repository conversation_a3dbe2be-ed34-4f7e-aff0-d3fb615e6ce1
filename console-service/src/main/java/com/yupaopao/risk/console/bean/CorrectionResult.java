package com.yupaopao.risk.console.bean;

import lombok.*;

@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class CorrectionResult {
    private boolean success;
    private String message;

    public static CorrectionResult error(String message) {
        CorrectionResult result = new CorrectionResult();
        result.message = message;
        return result;
    }

    public static CorrectionResult success() {
        CorrectionResult result = new CorrectionResult();
        result.success = true;
        return result;
    }
}
