package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.common.vo.AlarmVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 告警定时任务服务
 */
public interface AlarmJobService {

    /**
     * 告警
     * @param alarmVO
     * @param event
     * @param alarmConfig
     * @param hitGroups
     * @param jobTime
     */
    void alarm(AlarmVO alarmVO, Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups, Date jobTime);

    /**
     * 规则告警
     * @param alarmVO
     * @param atomRule
     * @param eventCode
     * @param alarmConfig
     * @param hitGroups
     * @param jobTime
     */
    void ruleAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups, Date jobTime);

    /**
     * 规则预熔断告警
     * @param alarmVO
     * @param atomRule
     * @param eventCode
     * @param alarmConfig
     * @param hitGroups
     * @param jobTime
     */
    Long ruleWillFuseAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups, Date jobTime);

    /**
     * 规则达成熔断条件，不熔断告警
     * @param alarmVO
     * @param atomRule
     * @param event
     * @param alarmConfig
     * @param hitGroups
     * @param jobTime
     * @return
     */
    void ruleNotFuseAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, AlarmConfig alarmConfig,
                          List<Map<String, String>> hitGroups, Date jobTime,String reason);
    /**
     * 规则已熔断告警
     * @param alarm
     * @param atomRule
     * @param alarmLog
     * @param alarmConfig
     * @param jobTime
     */
    void ruleAlreadyFuseAlarm(AtomRule atomRule,AlarmLog alarmLog,Date jobTime,String eventName);
    /**
     * 是否命中告警规则
     *
     * @param ruleAtom 规则
     * @param map      保存两个时间窗口的统计值
     * @param hitRules 保存当前规则组内命中的规则
     * @return
     */
    boolean hitRule(AlarmRuleAtom ruleAtom, Map<String, Integer> map, Map<String, String> hitRules);

    /**
     * 通用告警服务
     *
     * @param alarmContent
     * @return
     */
    boolean alarm(String alarmContent);

    /**
     * 时间范围检测
     * @param ruleGroups
     * @param timeNow
     * @return
     */
    List<AlarmRuleGroupVO> timeScopeCheck(Alarm alarm,AlarmConfig alarmConfig,List<AlarmRuleGroupVO> ruleGroups,Date timeNow);

    /**
     * 批量从缓存中获取值
     * @param keys
     * @return
     */
    List<Map<String,Integer>> batchGet(List<String> keys);
}
