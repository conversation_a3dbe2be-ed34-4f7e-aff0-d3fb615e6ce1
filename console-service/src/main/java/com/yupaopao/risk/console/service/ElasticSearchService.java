package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.OperationLog;
import com.yupaopao.risk.console.vo.LogSearchVO;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.search.aggregations.AggregationBuilder;

import java.util.Map;

/**
 * 风控系统ElasticSearch服务
 *
 * <AUTHOR>
 * @date 2019/2/25 4:11 PM
 */
public interface ElasticSearchService {

    // 获取Client
    RestHighLevelClient getClient();

    // 录入记录
    boolean indexOperationLog(OperationLog log);

    /**
     * 命中记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchHitLog(LogSearchVO.HitLogSearchVO vo);

    /**
     * 查询最近一条风控记录
     * @param vo
     * @return
     */
    Map<String,Object> searchFirstHitLog(LogSearchVO.HitLogSearchVO vo);

    /**
     * 命中记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchPunishLog(LogSearchVO.PunishLogSearchVO vo);

    /**
     * 命中记录搜索
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchHitLogByRequest(LogSearchVO.HitLogRequestSearchVO vo);

    /**
     * 业务历史命中记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchBizHitLog(LogSearchVO.HitBizLogSearchVO vo);

    /**
     * 命中记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchSingleHitLog(LogSearchVO.HitLogSearchVO vo);

    /**
     * 三方调用记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchThirdLog(LogSearchVO.ThirdLogSearchVO vo);

    /**
     * 送审记录查询
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchAuditLog(LogSearchVO.AuditLogSearchVO vo);

    /**
     * 送审记录查询
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchAuditNotifyLog(LogSearchVO.AuditNotifyLogSearchVO vo);

    /**
     * 业务通知记录查询
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchNotifyLog(LogSearchVO.NotifyLogSearchVO vo);


    /**
     * 风控记录查询
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchTraceLog(LogSearchVO.TraceSearchVO vo, User user);

    /**
     * 查询外部属性调用结果
     * @param vo
     * @return
     */
    PageInfo<Map<String,Object>> searchResult(LogSearchVO.TraceSearchVO vo);

    /**
     * 机人审记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchDiffLog(LogSearchVO.DiffLogSearchVO vo);

    /**
     * 操作日志记录搜索
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchOperationLog(LogSearchVO.OperationLogSearchVO vo);


    /**
     * 内容记录查询
     *
     * @param vo
     * @return
     */
    PageInfo<Map<String, Object>> searchContentLog(LogSearchVO.ContentLogSearchVO vo);

    /**
     * 通用汇聚查询
     * @param request
     * @param agg
     * @return
     */
    Map<String, Object> agg(SearchRequest request, AggregationBuilder agg);

    /**
     * 获取Cardinality值，注意只有一个Cardinality聚合
     * @param request
     * @param agg
     * @return
     */
    Long getCardinalityValue(SearchRequest request, AggregationBuilder agg);

}
