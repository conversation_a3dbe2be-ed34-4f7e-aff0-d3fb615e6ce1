package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

/**
 * 三方服务渠道
 *
 * <AUTHOR>
 * @date 2017年8月18日 下午3:11:17
 */
public enum DetectChannel {

    /**
     * 网易
     */
    NETEASE(1, "网易云易盾"),
    /**
     * 数美
     */
    SHUMEI(2, "数美");

    int value;
    String desc;

    DetectChannel(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(int value) {
        for (DetectChannel detectChannel : DetectChannel.values()) {
            if (value == detectChannel.getValue()) {
                return detectChannel.getDesc();
            }
        }
        return null;
    }

    public static DetectChannel nameOf(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (DetectChannel item : values()) {
                if (item.name().equalsIgnoreCase(name)) {
                    return item;
                }
            }
        }
        return null;
    }

}
