package com.yupaopao.risk.console.utils;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Sets;
import com.yupaopao.risk.access.utils.DateUtil;
import com.yupaopao.risk.console.bean.TraceType;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.cardinality.Cardinality;
import org.elasticsearch.search.sort.SortOrder;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

// 风控历史ES工具类
@Slf4j
public class RiskLogESUtil {
    public static final String HIT_LOG_INDEX = "risk_hit_log";
    public static final String PUNISH_LOG_INDEX = "risk_punish_log";
    public static final String HIT_IM_LOG_INDEX = "risk_hit_im_log";
    public static final String CONTENT_LOG_INDEX = "risk_content_log";
    public static final String THIRD_LOG_INDEX = "risk_third_log";
    public static final String AUDIT_LOG_INDEX = "risk_audit_log";
    public static final String AUDIT_NOTIFY_LOG_INDEX = "risk_audit_notify_log";
    public static final String NOTIFY_LOG_INDEX = "risk_notify_log";
    public static final String TRACE_LOG_INDEX = "risk_*_log";
    public static final String TRACE_LOG_INDEX_TEMPLATE = "risk_%s_log";
    public static final String OPERATION_LOG_INDEX = "risk_operation_log";
    public static final String DIFF_LOG_INDEX = "risk_diff_log";
    public static final String TYPE = "V1";
    public static final String DATE_FORMART = "yyyy-MM-dd\'T\'HH:mm:ss+08:00";
    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd'T'HH:mm:ss";
    public static final Pattern pattern = Pattern.compile("(?<=risk_).*(?=_log)");
    public static final String pattern_default = "yyyyMMdd";
    public static final String pattern_year_month = "yyyyMM";
    public static final String RANGE_START_APPEND = "_1";
    public static final String RANGE_END_APPEND = "_2";
    public static final String format_year_month_day = "yyyy-MM-dd";
    public static final String mobilePattern = "1[0-9]{10}";

    // 聚合结果解析
    public static Map<String, Object> parseAggregations(Aggregations aggregations, boolean nested) {
        Map<String, Object> data = new LinkedHashMap<>();
        if (aggregations != null) {
            List<Aggregation> list = aggregations.asList();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(item -> parseAggregations(item, data, nested));
            }
        }
        return data;
    }

    // 聚合结果解析
    public static void parseAggregations(Aggregation aggregation, Map<String, Object> data, boolean nested) {
        log.debug("聚合结果类型:{}", aggregation.getClass());
        List<? extends MultiBucketsAggregation.Bucket> buckets = new ArrayList<>();
        if (aggregation instanceof Terms) {
            Terms terms = (Terms) aggregation;
            buckets = terms.getBuckets();
        } else if (aggregation instanceof Histogram) {
            Histogram histogram = (Histogram) aggregation;
            buckets = histogram.getBuckets();
        }
        buckets.forEach(bucket -> {
            if (nested) {
                Aggregations aggregations = bucket.getAggregations();
                data.put(bucket.getKeyAsString(), parseAggregations(aggregations, false));
            } else {
                data.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
        });
    }

    // 获取Cardinality值，注意只有一个Cardinality聚合
    public static Long getCardinalityValue(Aggregations aggregations) {
        if (aggregations != null) {
            List<Aggregation> list = aggregations.asList();
            if (CollectionUtils.isNotEmpty(list)) {
                Aggregation aggregation = list.get(0);
                if(aggregation instanceof Cardinality){
                    Cardinality cardinality = (Cardinality) aggregation;
                    return cardinality.getValue();
                }
            }
        }
        return 0l;
    }

    // 处理分页参数
    public static PageInfo<Map<String, Object>> handlePageInfo(LogSearchVO vo) {
        log.debug("搜索请求:{}", vo);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(vo.getSize());
        pageInfo.setPageNum(vo.getPage());
        return pageInfo;
    }

    // 处理响应数据
    public static void handleResponse(SearchResponse response, PageInfo pageInfo) {
        handleResponse(response,pageInfo,false,false);
    }

    public static void handleResponse(SearchResponse response, PageInfo pageInfo,boolean origin){
        handleResponse(response,pageInfo,origin,false);
    }

    // 处理响应数据,封装index、id、type信息
    public static void handleResponse(SearchResponse response, PageInfo pageInfo,boolean origin,boolean rmMobile) {
        SearchHits hits = response.getHits();
        log.debug("命中数量:{}，耗时:{}", hits.getTotalHits(), response.getTook());
        pageInfo.setTotal(Math.min(hits.totalHits, 10000)); // ES默认最多返回1w条记录
        LinkedList<Map<String, Object>> list = new LinkedList<>();
        if(origin){
            hits.forEach(hit -> {
                Map<String,Object> map = new HashMap<>();
                map.put("hitIndex",hit.getIndex());
                map.put("hitId",hit.getId());
                map.put("hitType",hit.getType());
                map.put("hitData",getResponseDataRmMobile(hit.getSourceAsMap(),rmMobile));
                list.add(map);
            });
        }else{
            hits.forEach(hit -> list.add(getResponseDataRmMobile(hit.getSourceAsMap(),rmMobile)));
        }
        pageInfo.setList(list);
    }

    // 是否需要移除手机号
    protected static boolean removeMobile(String key){
        if(StringUtils.isNotBlank(key)) {
            if ((key.equalsIgnoreCase("mobileno") || key.equalsIgnoreCase("mobile"))) {
                return true;
            }
        }
        return false;
    }

    // 处理手机号信息,第一层及当前层的data数据进行了处理
    public static Map<String,Object> getResponseDataRmMobile(Map<String,Object> dataMap,boolean rmMobile){
        return getResponseDataRmMobile(dataMap,new HashMap<>(),rmMobile);
    }

    public static Map<String,Object> getResponseDataRmMobile(Map<String,Object> dataMap,Map<String,Object> currentMap,boolean rmMobile){
        if(!rmMobile){
            return dataMap;
        }else{
            if(MapUtils.isNotEmpty(dataMap)){
                for(Map.Entry<String,Object> entry : dataMap.entrySet()){
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if(value instanceof Map){
                        Map<String,Object> nextMap = new HashMap<>();
                        currentMap.put(key,nextMap);
                        getResponseDataRmMobile((Map<String, Object>) value,nextMap,true);
                        continue;
                    }else if(key.toLowerCase().contains("mobile") && Pattern.matches(mobilePattern,String.valueOf(value))){
                        continue;
                    }
                    currentMap.put(key,value);
                }
                return currentMap;
            }
            return dataMap;
        }
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequest(LogSearchVO vo, String index) {
        return buildSearchRequest(vo, index, pattern_default);
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequest(TraceType traceType, LogSearchVO vo, String index) {
        return buildSearchRequest(traceType, vo, index, pattern_default);
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequestOr(LogSearchVO vo, String index,Map<String,List<String>> orMap) {
        return buildSearchRequestOr(vo, index, pattern_default,orMap);
    }

    // 建立索引列表
    public static String[] buildSearchIndices(String index, Date start, Date end, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            pattern = pattern_default;
        }

        LinkedList<String> list = new LinkedList();
        end = end == null ? new Date() : end;
        if (start == null || start.after(end)) {
            list.add(index);
        } else {
            Calendar calendar = Calendar.getInstance();
            for (Date current = start; !current.after(end); ) {
                list.add(formatIndexName(index, current, pattern));
                calendar.setTime(current);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                current = calendar.getTime();
            }
        }
        return list.toArray(new String[0]);
    }

    // 建立搜索请求,当前给操作记录使用
    public static SearchRequest buildSearchRequestTime(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    if(value instanceof String){
                        query.filter(QueryBuilders.wildcardQuery(key, "*"+String.valueOf(value)+"*"));
                    }else{
                        query.filter(QueryBuilders.termQuery(key, value));
                    }
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createTime", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    /**
     *  建立搜索请求
     * @param vo
     * @param index
     * @param pattern
     * @param orMap
     * @return
     */
    public static SearchRequest buildRequestSearchRequestOr(LogSearchVO.HitLogRequestSearchVO vo, String index, String pattern,Map<String,List<String>> orMap){
        SearchRequest request = new SearchRequest(buildSearchIndices(index,vo.getStartTime(),vo.getEndTime(),pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if(StringUtils.isBlank(vo.getText())){
            query.must(QueryBuilders.matchAllQuery());
        }else{
            query.filter(QueryBuilders.wildcardQuery("full_text","*"+String.valueOf(vo.getText())+"*"));
        }
        if(vo.getStartTime()!=null||vo.getEndTime()!=null){
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            long now = System.currentTimeMillis();
            if(vo.getStartTime()!=null&&vo.getStartTime().getTime()>=now){
                builder.gte(DateFormatUtils.format(vo.getStartTime(),DATE_FORMART));
            }
            if(vo.getEndTime() != null && vo.getEndTime().getTime() <= now){
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if(vo.getQuery()!=null){
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    if(Objects.equals(key,"uids")){
                       query.mustNot(QueryBuilders.termsQuery("userId",vo.getQuery().getUids()));
                    }else if(Objects.equals(key,"mobileNos")){
                        query.mustNot(QueryBuilders.termsQuery("mobileNo",vo.getQuery().getMobileNos()));
                    }else if(Objects.equals(key,"deviceIds")){
                        query.must(QueryBuilders.termsQuery("deviceId",vo.getQuery().getDeviceIds()));
                    }else if(Objects.equals(key,"eventCodes")){
                        query.mustNot(QueryBuilders.termsQuery("eventCode",vo.getQuery().getEventCodes()));
                    }else{
                        query.filter(QueryBuilders.termQuery(key, value));
                    }

                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        if(MapUtils.isNotEmpty(orMap)){
            try {
                orMap.forEach((k,v) -> {
                    if (v == null || v.size() == 0) {
                        return;
                    }
                    BoolQueryBuilder orQuery = QueryBuilders.boolQuery();
                    for(String item : v){
                        orQuery.should(QueryBuilders.termQuery(k, item));
                    }
                    query.filter(orQuery);
                });
            }catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequestOr(LogSearchVO vo, String index, String pattern,Map<String,List<String>> orMap) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
            //query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            long now = System.currentTimeMillis();
            if (vo.getStartTime() != null && vo.getStartTime().getTime() >= now) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null && vo.getEndTime().getTime() <= now) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }
        if(MapUtils.isNotEmpty(orMap)){
            try {
                orMap.forEach((k,v) -> {
                    if (v == null || v.size() == 0) {
                        return;
                    }
                    BoolQueryBuilder orQuery = QueryBuilders.boolQuery();
                    for(String item : v){
                        orQuery.should(QueryBuilders.termQuery(k, item));
                    }
                    query.filter(orQuery);
                });
            }catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    /**
     *  建立搜索请求
     * @param vo
     * @param index
     * @param pattern
     * @return
     */
    public static SearchRequest buildRequestSearchRequest(LogSearchVO.HitLogRequestSearchVO vo, String index, String pattern){
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
            //query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getStartTime() != null && vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Long uid = vo.getQuery().getUid();
                vo.getQuery().setUid(null);
                if(null != uid){
                    query.filter(QueryBuilders.termQuery("userId", uid+""));
                }
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    if(Objects.equals(key,"uids")){
                        query.mustNot(QueryBuilders.termsQuery("userId",vo.getQuery().getUids()));
                    }else if(Objects.equals(key,"mobileNos")){
                        query.mustNot(QueryBuilders.termsQuery("mobileNo",vo.getQuery().getMobileNos()));
                    }else if(Objects.equals(key,"deviceIds")){
                        query.must(QueryBuilders.termsQuery("deviceId",vo.getQuery().getDeviceIds()));
                    }else if(Objects.equals(key,"eventCodes")){
                        query.mustNot(QueryBuilders.termsQuery("eventCode",vo.getQuery().getEventCodes()));
                    }else{
                        query.filter(QueryBuilders.termQuery(key, value));
                    }
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequest(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
            //query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            long now = System.currentTimeMillis();
            if (vo.getStartTime() != null && vo.getStartTime().getTime() >= now) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null && vo.getEndTime().getTime() <= now) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    // 建立搜索请求
    public static SearchRequest buildSearchRequest(TraceType traceType,LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
            //query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            long now = System.currentTimeMillis();
            if (vo.getStartTime() != null && vo.getStartTime().getTime() >= now) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null && vo.getEndTime().getTime() <= now) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        if(traceType.getCode()==TraceType.CONTENT.getCode()){
            query.filter(QueryBuilders.existsQuery("asyRuleId"));
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    public static SearchRequest buildWithBooleanSearchRequest(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    // 建立搜索请求
    public static SearchRequest buildBizSearchRequest(LogSearchVO vo, String index, String pattern,Map<String,List<String>> orMap) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = vo.getQuery() instanceof Map ? (Map<String, Object>) vo.getQuery() : PropertyUtils.describe(vo.getQuery());
                Set<String> rangeSetKeys = Sets.newHashSet();
                map.forEach((key, value) -> {
                    bulidQueryRequest(key,value,"",rangeSetKeys,map,query);
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }
        if(MapUtils.isNotEmpty(orMap)){
            try {
                orMap.forEach((k,v) -> {
                    if (v == null || v.size() == 0) {
                        return;
                    }
                    BoolQueryBuilder orQuery = QueryBuilders.boolQuery();
                    for(String item : v){
                        orQuery.should(QueryBuilders.termQuery(k, item));
                    }
                    query.filter(orQuery);
                });
            }catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }
    private static void bulidQueryRequest(String key,Object value,String parentKey,Set<String> rangeSetKeys,Map<String,Object> map,BoolQueryBuilder query){
        if (value == null || "class".equals(key)|| value instanceof Boolean) {
            return;
        }
        if(value instanceof Map){
             Map<String,Object> childMap = (Map<String, Object>) value;
             childMap.forEach((k,v)->{
                bulidQueryRequest(k,v,parentKey.concat(key).concat("."),rangeSetKeys,childMap,query);
            });
        }else{
            if(key.endsWith(RANGE_START_APPEND)){
                if(!rangeSetKeys.contains(parentKey.concat(key))){
                    rangeSetKeys.add(parentKey.concat(key));
                    String realKey = key.substring(0, key.indexOf(RANGE_START_APPEND));
                    RangeQueryBuilder builder = QueryBuilders.rangeQuery(parentKey.concat(realKey));
                    builder.gte(DateFormatUtils.format(DateUtil.parse(value.toString(),DATE_FORMAT_PATTERN), DATE_FORMART));
                    String endKey = realKey.concat(RANGE_END_APPEND);
                    if(map.containsKey(endKey)){
                        rangeSetKeys.add(parentKey.concat(endKey));
                        value = map.get(endKey);
                        if(null != value){
                            builder.lte(DateFormatUtils.format(DateUtil.parse(value.toString(),DATE_FORMAT_PATTERN), DATE_FORMART));
                        }
                    }
                    query.filter(builder);
                }
            }else if(key.endsWith(RANGE_END_APPEND)){
                if(!rangeSetKeys.contains(parentKey.concat(key))){
                    rangeSetKeys.add(parentKey.concat(key));
                    String realKey = key.substring(0, key.indexOf(RANGE_END_APPEND));
                    RangeQueryBuilder builder = QueryBuilders.rangeQuery(parentKey.concat(realKey));
                    builder.lte(DateFormatUtils.format(DateUtil.parse(value.toString(),DATE_FORMAT_PATTERN), DATE_FORMART));
                    String startKey = realKey.concat(RANGE_START_APPEND);
                    if(map.containsKey(startKey)){
                        rangeSetKeys.add(parentKey.concat(startKey));
                        value = map.get(startKey);
                        if(null != value){
                            builder.gte(DateFormatUtils.format(DateUtil.parse(value.toString(),DATE_FORMAT_PATTERN), DATE_FORMART));
                        }
                    }
                    query.filter(builder);
                }
            }else{
                if(value instanceof List){
                    query.filter(QueryBuilders.termsQuery(parentKey.concat(key), (List)value));
                }else{
                    query.filter(QueryBuilders.termQuery(parentKey.concat(key), value));
                }
            }
        }
    }

    // 建立人机搜索请求
    public static SearchRequest buildSearchRequestDiff(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.must(QueryBuilders.matchQuery("full_text", vo.getText()));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    // 建立IM搜索请求
    public static SearchRequest buildSearchRequestIm(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lte(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    /**
     * 构建搜索请求-时间区间半开闭，防止记录重复
     * @param vo
     * @param index
     * @param pattern
     * @return
     */
    public static SearchRequest buildSearch(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lt(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    /**
     * 升序
     * @param vo
     * @param index
     * @param pattern
     * @return
     */
    public static SearchRequest buildFirstSearch(LogSearchVO vo, String index, String pattern) {
        SearchRequest request = new SearchRequest(buildSearchIndices(index, vo.getStartTime(), vo.getEndTime(), pattern));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (StringUtils.isBlank(vo.getText())) {
            query.must(QueryBuilders.matchAllQuery());
        } else {
            query.filter(QueryBuilders.wildcardQuery("full_text", "*"+String.valueOf(vo.getText())+"*"));
        }
        if (vo.getStartTime() != null || vo.getEndTime() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (vo.getStartTime() != null) {
                builder.gte(DateFormatUtils.format(vo.getStartTime(), DATE_FORMART));
            }
            if (vo.getEndTime() != null) {
                builder.lt(DateFormatUtils.format(vo.getEndTime(), DATE_FORMART));
            }
            query.filter(builder);
        }
        if (vo.getQuery() != null) {
            try {
                Map<String, Object> map = PropertyUtils.describe(vo.getQuery());
                map.forEach((key, value) -> {
                    if (value == null || "class".equals(key) || value instanceof Map || value instanceof Boolean) {
                        return;
                    }
                    query.filter(QueryBuilders.termQuery(key, value));
                });
            } catch (Exception e) {
                log.error("解析搜索参数失败", e);
            }
        }

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.ASC);
        request.source().from((vo.getPage() - 1) * vo.getSize());
        request.source().size(vo.getSize());
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }


    // 格式化索引
    public static String formatIndexName(String index, Date date) {
        return formatIndexName(index, date, pattern_default);
    }

    // 格式化索引
    public static String formatIndexName(String index, Date date, String pattern) {
        if (StringUtils.isBlank(pattern)) {
            pattern = pattern_default;
        }
        return String.format("%s_%s", index, DateFormatUtils.format(date == null ? new Date() : date, pattern));
    }
}
