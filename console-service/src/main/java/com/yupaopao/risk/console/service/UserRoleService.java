package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Role;
import com.yupaopao.risk.common.model.UserRole;
import com.yupaopao.risk.common.service.BaseService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-27
 */
public interface UserRoleService extends BaseService<UserRole> {

    int batchDeleteByUserId(Long userId);

    int batchInsertUserRole(Long userId, List<Role> roles);

    List<Role> getRolesByUserId(Long userId);

    List<UserRole> getUserRolesByRoles(List<Role> roles);
}
