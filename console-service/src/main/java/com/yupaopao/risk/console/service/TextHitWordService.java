package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.clickhouse.model.HitWordCountInfo;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TextHitWordService {

    /**
     * 查询当天命中违禁词和白词条的总数
     * @param date 查询具体哪一天日期
     * @return
     */
    Integer countWordQuantity(Date date);

    /**
     * 查询当天命中违禁词的详情列表
     * @param date 查询具体哪一天日期
     * @param page 第几页
     * @param size 页面大小
     * @return
     */
    List<HitWordCountInfo> getWordList(Date date, Integer page, Integer size);

    /**
     * 查询指定日期之前的最近一个星期命中违禁词和白词条的详情列表
     * @param date 查询具体哪一天之前的近一个星期
     * @return
     */
    List<HitWordCountInfo> getWordListByWeek(Date date);

    /**
     * 查询指定日期之前的最近一个月命中违禁词和白词条的详情列表
     * @param date 查询具体哪一天之前的近一个月
     * @return
     */
    List<HitWordCountInfo> getWordListByMonth(Date date);

}
