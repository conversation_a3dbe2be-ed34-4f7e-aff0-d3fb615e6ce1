package com.yupaopao.risk.console.service.impl;

import com.google.common.collect.Lists;
import com.yupaopao.risk.common.enums.StateEnum;
import com.yupaopao.risk.common.mapper.BizChannelMapper;
import com.yupaopao.risk.common.model.BizChannel;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.service.BizChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * author: liji<PERSON>jun
 * date: 2021/3/25 14:42
 */
@Slf4j
@Service
public class BizChannelServiceImpl extends BaseServiceImpl<BizChannel, BizChannelMapper> implements BizChannelService {

    @Override
    public int countByThirdChannelId(Long thirdChannelId) {
        Example example = new Example(BizChannel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("thirdChannelId",thirdChannelId);
        criteria.andIn("state",Lists.newArrayList(StateEnum.ENABLE.getType(),StateEnum.DISABLE.getType()));
        return this.getMapper().selectCountByExample(example);
    }

    @Override
    public List<BizChannel> listByBizTypeIds(List<Long> bizTypeIdList) {
        Example example = new Example(BizChannel.class);
        example.setOrderByClause("biz_type_id,priority");
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("bizTypeId",bizTypeIdList);
        criteria.andIn("state",Lists.newArrayList(StateEnum.ENABLE.getType(),StateEnum.DISABLE.getType()));
        return this.getMapper().selectByExample(example);
    }

    @Override
    public List<BizChannel> listByThirdChannelIds(List<Long> thirdChannelIdList) {
        Example example = new Example(BizChannel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("thirdChannelId",thirdChannelIdList);
        criteria.andIn("state",Lists.newArrayList(StateEnum.ENABLE.getType(),StateEnum.DISABLE.getType()));
        return this.getMapper().selectByExample(example);
    }

    @Override
    public void updateState(BizChannel bizChannel,String modifyer) {
        Example example = new Example(BizChannel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bizTypeId",bizChannel.getBizTypeId());
        criteria.andEqualTo("thirdChannelId",bizChannel.getThirdChannelId());
        criteria.andIn("state",Lists.newArrayList(StateEnum.ENABLE.getType(),StateEnum.DISABLE.getType()));
        BizChannel upBizChannel = new BizChannel();
        upBizChannel.setState(bizChannel.getState());
        upBizChannel.setModifier(modifyer);
        this.getMapper().updateByExampleSelective(upBizChannel,example);
    }

    @Override
    public void deleteByBizTypeId(Long bizTypeId,String modifyer) {
        Example example = new Example(BizChannel.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bizTypeId",bizTypeId);
        criteria.andIn("state",Lists.newArrayList(StateEnum.ENABLE.getType(),StateEnum.DISABLE.getType()));
        BizChannel bizChannel = new BizChannel();
        bizChannel.setState(StateEnum.DELETE.getType());
        bizChannel.setModifier(modifyer);
        this.getMapper().updateByExampleSelective(bizChannel,example);
    }
}
