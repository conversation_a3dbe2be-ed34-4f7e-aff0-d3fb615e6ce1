package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.*;

import java.util.List;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/8/19 16:53
 */
public class RiskLogReq {

    private Event event;
    private List<GroupRule> ruleGroups;
    private List<AtomRule> ruleAtoms;
    private List<Attribute> localAttrs;
    private List<Attribute> remoteAttrs;
    private List<Factor> factors;

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public List<GroupRule> getRuleGroups() {
        return ruleGroups;
    }

    public void setRuleGroups(List<GroupRule> ruleGroups) {
        this.ruleGroups = ruleGroups;
    }

    public List<AtomRule> getRuleAtoms() {
        return ruleAtoms;
    }

    public void setRuleAtoms(List<AtomRule> ruleAtoms) {
        this.ruleAtoms = ruleAtoms;
    }

    public List<Attribute> getLocalAttrs() {
        return localAttrs;
    }

    public void setLocalAttrs(List<Attribute> localAttrs) {
        this.localAttrs = localAttrs;
    }

    public List<Attribute> getRemoteAttrs() {
        return remoteAttrs;
    }

    public void setRemoteAttrs(List<Attribute> remoteAttrs) {
        this.remoteAttrs = remoteAttrs;
    }

    public List<Factor> getFactors() {
        return factors;
    }

    public void setFactors(List<Factor> factors) {
        this.factors = factors;
    }
}
