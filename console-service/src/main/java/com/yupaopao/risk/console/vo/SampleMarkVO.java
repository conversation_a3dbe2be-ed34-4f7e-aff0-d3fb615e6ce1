package com.yupaopao.risk.console.vo;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.common.model.SampleMark;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/6/23 19:12
 */
@Data
public class SampleMarkVO extends SampleMark implements Serializable{

    private static final long serialVersionUID = -8549262164718868712L;

    /**
     * 标注状态
     */
    private Integer markStatus;

    /**
     * 样本详情
     */
    private JSONObject infoJson;

    public SampleMarkVO() {
    }

    public SampleMarkVO(SampleMark sampleMark) {
        if (sampleMark != null) {
            BeanUtils.copyProperties(sampleMark,this);
        }
    }
}
