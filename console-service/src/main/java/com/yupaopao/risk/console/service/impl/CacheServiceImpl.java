package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.console.bean.CacheOperation;
import com.yupaopao.risk.console.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
@Slf4j
public class CacheServiceImpl implements CacheService {

    private final static String CACHE_HINTS_KEY = "cache.hints";

    private List<Hint> hintList;

    @Autowired
    private RedisService redisService;

    @ApolloConfig
    private Config config;

    @PostConstruct
    public void init() {
        hintList = JSON.parseArray(config.getProperty(CACHE_HINTS_KEY, "[]"), Hint.class);
        config.addChangeListener((event) -> {
            ConfigChange configChange = event.getChange(CACHE_HINTS_KEY);
            if (configChange != null) {
                hintList = JSON.parseArray(configChange.getNewValue(), Hint.class);
            }
        });
    }

    @Override
    public Object execute(CacheOperation cacheOperation) {
        Object result = null;
        CacheOperation.Operation operation = cacheOperation.getOperation();
        CacheOperation.Type type = cacheOperation.getType();
        switch (operation) {
            case GET:
                switch (type) {
                    case HASH:
                        result = redisService.hget(cacheOperation.getKey(), cacheOperation.getField());
                        break;
                    case STRING:
                        result = redisService.get(cacheOperation.getKey());
                        break;
                    case ZSET:
                        break;
                    case SET:
                        break;
                    case LIST:
                        break;
                }
                break;
            case SET:
                switch (type) {
                    case HASH:
                        result = redisService.hset(cacheOperation.getKey(), cacheOperation.getField(), cacheOperation.getValue());
                        break;
                    case STRING:
                        result = redisService.set(cacheOperation.getKey(), cacheOperation.getValue());
                        break;
                    case ZSET:
                        break;
                    case SET:
                        break;
                    case LIST:
                        break;
                }
                break;
            case DEL:
                result = redisService.del(cacheOperation.getKey());
                break;
        }
        return result;
    }

    @Override
    public List<Hint> hints() {
        return hintList;
    }
}
