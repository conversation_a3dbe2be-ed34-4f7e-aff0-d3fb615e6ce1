package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.vo.AtomRuleVO;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.common.vo.GroupRuleVO;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/10/23 14:56
 */

@Getter
@Setter
public class ConflictRulesCheckReq implements Serializable {
    private static final long serialVersionUID = -4912498065068758901L;

    /**
     * 规则
     */
    private AtomRuleVO atomRuleVO;
    /**
     * 规则组
     */
    private GroupRuleVO groupRuleVO;
    /**
     * 事件
     */
    private EventVO eventVO;
    /**
     * 检测类型
     */
    private Integer type;

    @Getter
    public enum CheckType{

        ATOM_RULE(1),
        GROUP_RULE(2),
        EVENT(3),
        BUILD_TREE(4);

        private int type;

        CheckType(int type) {
            this.type = type;
        }
    }
}
