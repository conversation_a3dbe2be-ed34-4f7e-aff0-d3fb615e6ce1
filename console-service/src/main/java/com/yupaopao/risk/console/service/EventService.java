package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.EventVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface EventService extends BaseService<Event> {

    List<String> relateAttributes(String eventCode);

    List<EventVO> relationFetch(List<Event> list);

    List<AtomRule> relateAtomRule(List<Event> list);

    Event getByCode(String code);

    String getEventName(String code);

    EventVO getVOByCode(String code);

    Map<String, Object> simpleAll();

    Event getByName(String name);

    Map<String,Object> getEventsByCodes(Set<String> codes);

    List<Event> getEventsByRuleGroupId(Long ruleGroupId);

    List<Event> getEventsByRuleId(Long ruleId);

    /**
     * 绑定入参检测规则
     *
     * @param record
     * @param user
     */
    void bindParamValidators(EventVO record, User user);

    Map<String,Event> map(List<String> codes);

    List<Event> list(List<String> codes);

    boolean updateSaveRuleResult(EventVO record, User user);

    boolean updateDefaultLevel(EventVO record, User user);

    Map<String,Object> getDetailByCode(String code);

    List<String> getAllBizTypeCode(String code);

}
