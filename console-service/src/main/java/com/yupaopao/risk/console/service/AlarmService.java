package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * author: <PERSON><PERSON><PERSON>jun
 * date: 2020/2/18 14:50
 */
public interface AlarmService extends BaseService<Alarm> {

    Alarm getByConfigId(Long configId);

    boolean saveAlarm(AlarmVO vo);

    boolean saveFuseAlarm(AlarmVO vo);

    PageInfo<AlarmVO> searchAlarm(AlarmVO vo, int page, int size);

    boolean updateStatus(AlarmVO vo);

    boolean generateTemplate(AlarmVO vo);

    AlarmVO getDetailById(Long id);

    boolean updateAlarm(AlarmVO vo);

    boolean updateFuseAlarm(AlarmVO vo);

    List<Map<String, Object>> simpleAll(AlarmVO vo);

    Alarm getByName(String name);

    List<AlarmVO> searchAlarm(Integer type,Integer countType,Integer state);

}
