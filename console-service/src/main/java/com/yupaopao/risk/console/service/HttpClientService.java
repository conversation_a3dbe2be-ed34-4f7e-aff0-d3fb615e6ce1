package com.yupaopao.risk.console.service;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017年8月17日 上午11:36:35
 */
@Slf4j
@Service
public class HttpClientService {

    @Autowired
    private OkHttpClient httpClient;
    MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    public String doGet(String url) {
        Request request = new Request.Builder().url(url).get().build();
        try (Response response = httpClient.newCall(request).execute()) {
            return response == null ? StringUtils.EMPTY : response.body().string();
        } catch (Exception e) {
            log.error("GET请求出错：{}", url, e);
        }
        return StringUtils.EMPTY;
    }

    public String doPostMap(String url, Map<String, String> params) {
        FormBody.Builder builder = new FormBody.Builder();
        if (MapUtils.isNotEmpty(params)) {
            params.entrySet().forEach(entry -> builder.add(entry.getKey(), entry.getValue()));
        }
        Request request = new Request.Builder().url(url).post(builder.build()).build();
        try (Response response = httpClient.newCall(request).execute()) {
            return response == null ? StringUtils.EMPTY : response.body().string();
        } catch (Exception e) {
            log.error("POST请求出错：{}", url, e);
        }
        return StringUtils.EMPTY;
    }

    public String doPostJson(String url, String data) {
        data = StringUtils.trimToEmpty(data);
        RequestBody body = RequestBody.create(JSON_TYPE, data);
        Request request = new Request.Builder().url(url).post(body).build();
        try (Response response = httpClient.newCall(request).execute()) {
            return response == null ? StringUtils.EMPTY : response.body().string();
        } catch (Exception e) {
            log.error("POST请求出错：{}", url, e);
        }
        return StringUtils.EMPTY;
    }

    public void setHttpClient(OkHttpClient httpClient) {
        this.httpClient = httpClient;
    }
}
