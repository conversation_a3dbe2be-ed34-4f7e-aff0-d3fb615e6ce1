package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

/**
 * 纠错反馈状态
 *
 * <AUTHOR>
 */
public enum FeedbackStatus {

    UNFEEDBACK(1, "未反馈"), FEEDBACK(2, "已反馈");

    int value;
    String desc;

    FeedbackStatus(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(int value) {
        for (FeedbackStatus em : FeedbackStatus.values()) {
            if (value == em.getValue()) {
                return em.getDesc();
            }
        }
        return null;
    }

    public static FeedbackStatus nameOf(String name) {
        if (StringUtils.isNotBlank(name)) {
            for (FeedbackStatus em : values()) {
                if (em.name().equalsIgnoreCase(name)) {
                    return em;
                }
            }
        }
        return null;
    }

    public static FeedbackStatus get(Integer val) {
        if(val!=null){
            for (FeedbackStatus obj : values()) {
                if (obj.getValue() == val.intValue()) {
                    return obj;
                }
            }
        }
        return null;
    }

}
