package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.platform.common.constant.APP;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.device.dto.DeviceInfoDTO;
import com.yupaopao.platform.device.enums.ProductTypeEnum;
import com.yupaopao.platform.device.service.DeviceInfoService;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.PatrolRecord;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.PunishPackageResult;
import com.yupaopao.risk.console.bean.PunishPkgVO;
import com.yupaopao.risk.console.bean.PunishResponse;
import com.yupaopao.risk.console.enums.PunishObjectType;
import com.yupaopao.risk.console.service.AllPunishService;
import com.yupaopao.risk.console.service.PatrolRecordService;
import com.yupaopao.risk.console.utils.ThreadPoolUtils;
import com.yupaopao.risk.punish.api.RiskPunishConfigService;
import com.yupaopao.risk.punish.api.RiskPunishService;
import com.yupaopao.risk.punish.bean.KVDModel;
import com.yupaopao.risk.punish.bean.PunishAbilityInfo;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import com.yupaopao.risk.punish.request.SearchPunishPkgRequest;
import com.yupaopao.risk.punish.result.BatchPunishResult;
import com.yupaopao.risk.punish.result.PunishDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class AllPunishServiceImpl implements AllPunishService {

    @ApolloConfig
    private Config apolloConfig;
    @Autowired
    private ThreadPoolUtils threadPoolUtils;
    private Map<String,Integer> quantity = new ConcurrentHashMap<>();
    @DubboReference
    private RiskPunishConfigService riskPunishConfigService;
    @DubboReference
    private RiskPunishService riskPunishService;
    @Autowired
    private PatrolRecordService patrolRecordService;
    @DubboReference
    private DeviceInfoService deviceInfoService;

    private final static String PUNISH_CHANNEL = "REALTIME_RISK_PUNISH";

    @PostConstruct
    public void init(){
        this.parsePunishConfig();

        apolloConfig.addChangeListener(event->{
            if(event.isChanged("quantity.one.time.punish")){
                this.parsePunishConfig();
            }
        });
    }

    private void parsePunishConfig(){
        String quantityConfig = this.apolloConfig.getProperty("quantity.one.time.punish","");
        Map<String,Integer> quantity = new ConcurrentHashMap<>();
        if(StringUtils.isNotBlank(quantityConfig)){
            JSONObject jsonObject =JSON.parseObject(quantityConfig);
            for(String punishType:jsonObject.keySet()){
                quantity.put(punishType,Integer.valueOf(jsonObject.get(punishType).toString()));
            }
            this.quantity = quantity;
        }
    }

    @Override
    public Map<String,Object> getPackagesAndObjectTypes(GetPunishPackagesRequest request){
        Map<String,Object> rtnMap = new HashMap<>();
        try{
            List<Map<String, Object>> pkgs = new ArrayList<>();
            String channel = PUNISH_CHANNEL;
            if(request != null && StringUtils.isNotBlank(request.getChannel())){
                channel = request.getChannel();
            }
            List<Map<String, Object>>  punishPkgs = getPackageMapByChannel(channel);
            pkgs.addAll(punishPkgs);

            rtnMap.put("packages", pkgs);
            rtnMap.put("objectTypes", PunishObjectType.getAllTypes());
        }catch (Exception exp){
            log.error("获取惩罚包发生异常", exp);
        }
        return rtnMap;
    }

    private List<Map<String, Object>> getPackageMapByChannel(String channel){
        List<Map<String,Object>> list = Lists.newArrayList();

        SearchPunishPkgRequest request = new SearchPunishPkgRequest();
        request.setChannel(channel);
        Response<List<KVDModel>> response = riskPunishConfigService.searchPunishPackagesSingleLayer(request);
        if(response!=null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())){
            List<Map<String, Object>> pkgs = new ArrayList<>();
            response.getResult().stream().forEach(item -> {
                if(item.getValue() != null && StringUtils.isNotBlank(item.getDesc())){
                    Map<String, Object> pkg = new HashMap<>();
                    pkg.put("code", item.getValue());
                    pkg.put("msg", item.getDesc());
                    pkgs.add(pkg);
                }
            });
            list.addAll(pkgs);
        }

        return list;
    }

    @Override
    public List<PunishPkgVO> listPackages() {
        List<PunishPkgVO> list = Lists.newArrayList();
        try{
            SearchPunishPkgRequest request = new SearchPunishPkgRequest();
            request.setChannel(PUNISH_CHANNEL);
            Response<List<KVDModel>> response = riskPunishConfigService.searchPunishPackagesSingleLayer(request);
            if(response!=null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())){
                response.getResult().stream().forEach(item -> {
                    if(item.getValue() != null && StringUtils.isNotBlank(item.getDesc())){
                        PunishPkgVO punishPkgVO = new PunishPkgVO();
                        punishPkgVO.setCode(Long.valueOf(String.valueOf(item.getValue())));
                        punishPkgVO.setMsg(item.getDesc());
                        list.add(punishPkgVO);
                    }
                });
            }
        }catch (Exception exp){
            log.error("获取惩罚包发生异常", exp);
        }
        return list;
    }

    @Override
    public List<PunishPkgVO> listPackagesByIds(List<Long> ids) {
        List<PunishPkgVO> list = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(ids)){
            try{
                SearchPunishPkgRequest request = new SearchPunishPkgRequest();
                request.setChannel(PUNISH_CHANNEL);
                request.setPackageIds(ids);
                Response<List<KVDModel>> response = riskPunishConfigService.searchPunishPackagesSingleLayer(request);
                if(response!=null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())){
                    response.getResult().stream().forEach(item -> {
                        if(item.getValue() != null && StringUtils.isNotBlank(item.getDesc())){
                            PunishPkgVO punishPkgVO = new PunishPkgVO();
                            punishPkgVO.setCode(Long.valueOf(String.valueOf(item.getValue())));
                            punishPkgVO.setMsg(item.getDesc());
                            list.add(punishPkgVO);
                        }
                    });
                }
            }catch (Exception exp){
                log.error("获取惩罚包发生异常", exp);
            }
        }
        return list;
    }

    private Integer getMaxQuantity(String punishPackageId){
        Integer rtn = 100;
        try{
            if(StringUtils.isNumeric(punishPackageId)){
                Response<List<PunishAbilityInfo>> response = riskPunishConfigService.searchAbilitiesInfoByPkgId(Long.parseLong(punishPackageId));
                if(response != null && response.isSuccess() && CollectionUtils.isNotEmpty(response.getResult())){
                    for(PunishAbilityInfo item : response.getResult()){
                        Integer k = quantity.get(item.getCode());
                        if(k != null && k < rtn){
                            rtn = k;
                        }
                    }
                }
            }
        }catch (Exception exp){
            log.error("获取批量处理最大数量时产生异常 punishPackageId:{}", punishPackageId, exp);
        }
        return rtn;
    }

    @Override
    public PunishResponse doPunish(User user, Map<String, Object> parameters) {
        log.info("需要做如下惩罚 user:{} parameters:{}", user, parameters);
        try{
            if(MapUtils.isEmpty(parameters)){
                return new PunishResponse(false,"入参不能为空",null);
            }

            String punishPackageIdStr = MapUtils.getString(parameters,"packageId");
            if(StringUtils.isBlank(punishPackageIdStr)){
                return new PunishResponse(false,"请选择惩罚包",null);
            }

            String objectType = MapUtils.getString(parameters,"objectType");
            if(StringUtils.isBlank(objectType)){
                return new PunishResponse(false,"请选择惩罚对象类型",null);
            }

            String objectIds = MapUtils.getString(parameters,"objectIds");
            if(StringUtils.isBlank(objectIds)){
                return new PunishResponse(false,"惩罚对象不能为空",null);
            }

            String proofImgUrl = MapUtils.getString(parameters,"proofImgUrl"); // 图片凭证
            if (StringUtils.isBlank(proofImgUrl)) {
                return new PunishResponse(false, "图片凭证非法", null);
            }

            Integer quantity = getMaxQuantity(punishPackageIdStr);
            List<Object> objectIdList = new ArrayList<>();

            if(Objects.equals(objectType, PunishObjectType.UID.name())){
                for(String objectIdStr: objectIds.split("\n")){
                    if(StringUtils.isNumeric(objectIdStr)){
                        objectIdList.add(Long.parseLong(objectIdStr));
                    }else{
                        return new PunishResponse(false,"用户uid非法",null);
                    }
                }
            }else{
                for(String objectIdStr: objectIds.split("\n")){
                    if(StringUtils.isNotBlank(objectIdStr)){
                        objectIdList.add(objectIdStr);
                    }
                }
            }

            if (objectIdList.size() > quantity) {
                return new PunishResponse(false,"惩罚对象总数不能超过"+quantity+"个",null);
            }

            List<Object> partialSuccessObjectIds = new ArrayList<>(); // 部分成功对象id
            List<Object> failureObjectIds = new ArrayList<>(); // 失败对象id
            List<Object> timeoutObjectIds = new ArrayList<>(); // 超时对象id
            List<Object> exceptionObjectIds = new ArrayList<>(); // 异常对象id

            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger partialSuccessCount = new AtomicInteger(0);
            AtomicInteger failureCount = new AtomicInteger(0);
            AtomicInteger timeoutCount = new AtomicInteger(0);
            AtomicInteger exceptionCount = new AtomicInteger(0);


            List<Future<PunishPackageResult<BatchPunishRequest>>> futureList = new ArrayList<>();
            objectIdList.forEach(objectId->{
                BatchPunishRequest request = new BatchPunishRequest();
                if(Objects.equals(objectType, PunishObjectType.UID.name())){
                    request.setUid(Long.parseLong(String.valueOf(objectId)));
                }else if(Objects.equals(objectType,PunishObjectType.DEVICEID.name())){
                    request.setDeviceId(String.valueOf(objectId));
                }else if(Objects.equals(objectType,PunishObjectType.MOBILE.name())){
                    request.setMobile(String.valueOf(objectId));
                }else if(Objects.equals(objectType,PunishObjectType.CHATROOMID.name())){
                    request.setChatRoomId(String.valueOf(objectId));
                }else if(Objects.equals(objectType,PunishObjectType.LIVEROOMID.name())){
                    request.setLiveRoomId(String.valueOf(objectId));
                }else if(Objects.equals(objectType,PunishObjectType.IMGROUPID.name())){
                    request.setGroupId(String.valueOf(objectId));
                }

                String channel = MapUtils.getString(parameters,"channel");
                if(StringUtils.isBlank(channel)){
                    channel = PUNISH_CHANNEL;
                }
                request.setChannel(channel);
                Long packageId = Long.parseLong(punishPackageIdStr);
                request.setPackageId(packageId);
                request.setOperator(user.getName());
                request.setBizId("consolePunishCenter");
                request.setImages(Lists.newArrayList(proofImgUrl));
                request.setInternalReason(MapUtils.getString(parameters,"internalReason"));
                request.setExternalReason(MapUtils.getString(parameters,"externalReason"));

                String deviceId = MapUtils.getString(parameters,"deviceId");
                if(StringUtils.isNotEmpty(deviceId)){
                    request.setDeviceId(deviceId);
                }

                String appId = MapUtils.getString(parameters,"appId");
                if(StringUtils.isNotEmpty(appId)){
                    request.setAppId(Integer.parseInt(appId));
                }

                PunishServiceCallable callable = new PunishServiceCallable(riskPunishService, PunishObjectType.valueOf(objectType), request);
                Future<PunishPackageResult<BatchPunishRequest>> future = threadPoolUtils.submit(callable);
                futureList.add(future);
            });

            for(Future<PunishPackageResult<BatchPunishRequest>> future : futureList){
                PunishPackageResult<BatchPunishRequest> result = future.get();

                if(Objects.equals(result.getCode(),1)){
                    successCount.incrementAndGet();
                }else if(Objects.equals(result.getCode(),3)){
                    partialSuccessCount.incrementAndGet();
                    Object objectId = extractObjectId(result);
                    if(objectId != null){
                        partialSuccessObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),2)){
                    failureCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        failureObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),4)){
                    timeoutCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        timeoutObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),5)){
                    exceptionCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        exceptionObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }
            }

            Map<String,Object> map = new HashMap<>();
            map.put("successCount", successCount);
            map.put("partialSuccessCount", partialSuccessCount);
            map.put("failureCount", failureCount);
            map.put("timeoutCount", timeoutCount);
            map.put("exceptionCount", exceptionCount);

            map.put("partialSuccessObjectIds", printObjectIdList(partialSuccessObjectIds));
            map.put("failureObjectIds", printObjectIdList(failureObjectIds));
            map.put("timeoutObjectIds", printObjectIdList(timeoutObjectIds));
            map.put("exceptionObjectIds", printObjectIdList(exceptionObjectIds));

            return PunishResponse.success(map);
        } catch (Exception e){
            log.error("惩罚异常 user:{} parameters:{}", user, parameters, e);
            return PunishResponse.failure(e.getMessage());
        }
    }

    @Override
    public PunishResponse executePunish(User user, Map<String,Object> parameters){
        log.info("需要做如下惩罚 user:{} parameters:{}", user, parameters);
        try{
            if(MapUtils.isEmpty(parameters)){
                return new PunishResponse(false,"入参不能为空",null);
            }

            String punishPackageIdStr = MapUtils.getString(parameters,"packageId");
            if(StringUtils.isBlank(punishPackageIdStr)){
                return new PunishResponse(false,"请选择惩罚包",null);
            }

            String objectIds = MapUtils.getString(parameters,"objectIds");
            if(StringUtils.isBlank(objectIds)){
                return new PunishResponse(false,"惩罚对象不能为空",null);
            }

            String proofImgUrl = MapUtils.getString(parameters,"proofImgUrl"); // 图片凭证

            Integer quantity = getMaxQuantity(punishPackageIdStr);

            List<Long> recordIdList = new ArrayList<>();
            for(String objectIdStr: objectIds.split("\n")){
                if(StringUtils.isNotBlank(objectIdStr)){
                    recordIdList.add(Long.parseLong(objectIdStr));
                }
            }

            if (recordIdList.size() > quantity) {
                return new PunishResponse(false,"惩罚对象总数不能超过"+quantity+"个",null);
            }

            List<Object> partialSuccessObjectIds = new ArrayList<>(); // 部分成功对象id
            List<Object> failureObjectIds = new ArrayList<>(); // 失败对象id
            List<Object> timeoutObjectIds = new ArrayList<>(); // 超时对象id
            List<Object> exceptionObjectIds = new ArrayList<>(); // 异常对象id

            AtomicInteger successCount = new AtomicInteger(0);
            AtomicInteger partialSuccessCount = new AtomicInteger(0);
            AtomicInteger failureCount = new AtomicInteger(0);
            AtomicInteger timeoutCount = new AtomicInteger(0);
            AtomicInteger exceptionCount = new AtomicInteger(0);


            List<Future<PunishPackageResult<BatchPunishRequest>>> futureList = new ArrayList<>();
            List<PatrolRecord> patrolRecords = patrolRecordService.batchSearchByIds(recordIdList);
            if(CollectionUtils.isEmpty(patrolRecords)){
                return new PunishResponse(false,"没有有效的惩罚对象",null);
            }

            patrolRecords.forEach(patrolRecord->{
                BatchPunishRequest request = new BatchPunishRequest();

                if(patrolRecord.getUid()!=null){
                    request.setUid(patrolRecord.getUid());
                }

                if(StringUtils.isNotBlank(patrolRecord.getDeviceId())){
                    request.setDeviceId(patrolRecord.getDeviceId());
                }

                String channel = MapUtils.getString(parameters,"channel");
                if(StringUtils.isBlank(channel)){
                    channel = PUNISH_CHANNEL;
                }
                request.setChannel(channel);
                Long packageId = Long.parseLong(punishPackageIdStr);
                request.setPackageId(packageId);
                request.setOperator(user.getName());
                request.setBizId("consolePunishCenter");
                request.setImages(Lists.newArrayList(proofImgUrl));
                request.setInternalReason(MapUtils.getString(parameters,"internalReason"));
                request.setExternalReason(MapUtils.getString(parameters,"externalReason"));

                PunishServiceCallable callable = new PunishServiceCallable(riskPunishService, PunishObjectType.UNKNOW, request);
                Future<PunishPackageResult<BatchPunishRequest>> future = threadPoolUtils.submit(callable);
                futureList.add(future);
            });

            for(Future<PunishPackageResult<BatchPunishRequest>> future : futureList){
                PunishPackageResult<BatchPunishRequest> result = future.get();

                if(Objects.equals(result.getCode(),1)){
                    successCount.incrementAndGet();
                }else if(Objects.equals(result.getCode(),3)){
                    partialSuccessCount.incrementAndGet();
                    Object objectId = extractObjectId(result);
                    if(objectId != null){
                        partialSuccessObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),2)){
                    failureCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        failureObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),4)){
                    timeoutCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        timeoutObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }else if(Objects.equals(result.getCode(),5)){
                    exceptionCount.incrementAndGet();

                    Object objectId = extractObjectId(result);
                    if(objectId!=null){
                        exceptionObjectIds.add(extractUnsuccessPunishItem(objectId, result));
                    }
                }
            }

            Map<String,Object> map = new HashMap<>();
            map.put("successCount", successCount);
            map.put("partialSuccessCount", partialSuccessCount);
            map.put("failureCount", failureCount);
            map.put("timeoutCount", timeoutCount);
            map.put("exceptionCount", exceptionCount);

            map.put("partialSuccessObjectIds", printObjectIdList(partialSuccessObjectIds));
            map.put("failureObjectIds", printObjectIdList(failureObjectIds));
            map.put("timeoutObjectIds", printObjectIdList(timeoutObjectIds));
            map.put("exceptionObjectIds", printObjectIdList(exceptionObjectIds));

            return PunishResponse.success(map);
        } catch (Exception e){
            log.error("惩罚异常 user:{} parameters:{}", user, parameters, e);
            return PunishResponse.failure(e.getMessage());
        }
    }


    protected String printObjectIdList(List<Object> objectIds){
        StringBuilder result = new StringBuilder();

        if(CollectionUtils.isNotEmpty(objectIds)){
            for(Object objectId:objectIds){
                result.append(objectId.toString()).append("\n");
            }
        }

        return result.toString();
    }

    private Object extractObjectId(PunishPackageResult<BatchPunishRequest> result){
        if(Objects.equals(result.getPunishObjectType(),PunishObjectType.UID)){
            return result.getReq().getUid();
        }else if(Objects.equals(result.getPunishObjectType(),PunishObjectType.DEVICEID)){
            return result.getReq().getDeviceId();
        }else if(Objects.equals(result.getPunishObjectType(),PunishObjectType.MOBILE)){
            return result.getReq().getMobile();
        }else if(Objects.equals(result.getPunishObjectType(),PunishObjectType.LIVEROOMID)){
            return result.getReq().getLiveRoomId();
        }else if(Objects.equals(result.getPunishObjectType(),PunishObjectType.CHATROOMID)){
            return result.getReq().getChatRoomId();
        }else if(Objects.equals(result.getPunishObjectType(),PunishObjectType.IMGROUPID)){
            return result.getReq().getGroupId();
        }else{
            return null;
        }
    }

    private String extractUnsuccessPunishItem(Object objectId,PunishPackageResult<BatchPunishRequest> response){
        StringBuilder result = new StringBuilder(objectId.toString()+":");

        List<PunishDetail> details = response.getDetail();
        if(CollectionUtils.isNotEmpty(details)){
            for(PunishDetail detail : details){
                if(detail.getResult() != null && detail.getResult() != 1){
                    result.append(detail.getPunishName()).append("惩罚失败，").append(detail.getResultDesc()).append(";");
                }
            }
        }
        return result.toString();
    }

    @Override
    public List<Map<String, Object>> getRecentLoginDevice(Map<String, Object> parameters) {

        List<Map<String,Object>> devices = Lists.newArrayList();

        if(parameters.containsKey("uid")){
          String uidStr = parameters.get("uid").toString();
          if(StringUtils.isNumeric(uidStr)){
             Long uid = Long.parseLong(uidStr);
             for(APP app:APP.values()){
                 List<Long> uidList = Lists.newArrayList();
                 uidList.add(uid);

                 Response<List<DeviceInfoDTO>> response = deviceInfoService.getLastLoginDevice(uidList,app,ProductTypeEnum.APP_All);
                 if((response!=null)&&CollectionUtils.isNotEmpty(response.getResult())){
                     for(int index=0;index<response.getResult().size()&&index<10;index++){
                         DeviceInfoDTO deviceInfoDTO = response.getResult().get(index);

                         Map<String,Object> deviceMap = Maps.newHashMap();

                         //APPID
                         deviceMap.put("APPID",deviceInfoDTO.getAppId());

                         //设备id
                         deviceMap.put("deviceId",deviceInfoDTO.getUdid());

                         devices.add(deviceMap);
                     }
                 }
             }
          }
        }

        return devices;
    }
}

@Slf4j
class PunishServiceCallable implements Callable<PunishPackageResult<BatchPunishRequest>>{
    private RiskPunishService riskPunishService;
    private PunishObjectType punishObjectType;
    private BatchPunishRequest request;

    public PunishServiceCallable(RiskPunishService riskPunishService, PunishObjectType punishObjectType, BatchPunishRequest request){
        this.riskPunishService = riskPunishService;
        this.punishObjectType = punishObjectType;
        this.request = request;
    }

    @Override
    public PunishPackageResult<BatchPunishRequest> call() throws RiskException {
        try{
            Response<BatchPunishResult> response = riskPunishService.batchPunish(request); // result 1:成功 2:部分成功 0:失败
            if(response != null){
                if(StringUtils.isNotBlank(response.getCode()) && response.getCode().startsWith("82")){
                    throw new RiskException(response.getMsg());
                }

                // 打包惩罚结果 1:成功 3:部分成功  2:失败
                if(response.isSuccess() && Objects.equals(response.getResult().getResult(),1)){
                    return new PunishPackageResult<>(request, punishObjectType, 1, response.getResult().getDetails());
                }else if(response.isSuccess() && Objects.equals(response.getResult().getResult(),2)){ //打包惩罚结果
                    return new PunishPackageResult<>(request, punishObjectType, 3, response.getResult().getDetails());
                }else if(response.isSuccess() && Objects.equals(response.getResult().getResult(),0)){ // 打包惩罚结果
                    return new PunishPackageResult<>(request, punishObjectType, 2, response.getResult().getDetails());
                }else{
                    return new PunishPackageResult<>(request, punishObjectType,response.getResult().getResult(), response.getResult().getDetails());
                }
            }else{
                log.error("调用惩罚包接口惩罚对象返回结果为null,惩罚请求:{}",JSONObject.toJSONString(this.request));
                List<PunishDetail> punishDetails = Lists.newArrayList();

                PunishDetail punishDetail = new PunishDetail();
                punishDetail.setResultDesc("调用超时");
                return new PunishPackageResult<>(request, punishObjectType,4, punishDetails);
            }
        }catch (RiskException exp){
            log.error("调用惩罚包接口惩罚对象发生异常:{},惩罚请求:{}",exp,JSONObject.toJSONString(this.request));
            throw exp;
        }catch (Exception exp){
            log.error("调用惩罚包接口惩罚对象发生异常:{},惩罚请求:{}",exp,JSONObject.toJSONString(this.request));

            List<PunishDetail> punishDetails = Lists.newArrayList();

            PunishDetail punishDetail = new PunishDetail();
            punishDetail.setResultDesc("调用发生异常");
            return new PunishPackageResult<>(request, punishObjectType,5, punishDetails);
        }
    }
}

