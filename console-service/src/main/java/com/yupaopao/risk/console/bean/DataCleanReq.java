package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.GrayList;

import java.io.Serializable;
import java.util.List;

public class DataCleanReq implements Serializable {

    private Boolean shumei;
    private Boolean portrait;
    private Boolean blackGray;
    private List<GrayList>  blackGrayList;
    private Boolean factor;
    private String factorIds;
    private String comment;

    public Boolean getShumei() {
        return shumei;
    }

    public void setShumei(Boolean shumei) {
        this.shumei = shumei;
    }

    public Boolean getPortrait() {
        return portrait;
    }

    public void setPortrait(Boolean portrait) {
        this.portrait = portrait;
    }

    public Boolean getBlackGray() {
        return blackGray;
    }

    public void setBlackGray(Boolean blackGray) {
        this.blackGray = blackGray;
    }

    public List<GrayList> getBlackGrayList() {
        return blackGrayList;
    }

    public void setBlackGrayList(List<GrayList> blackGrayList) {
        this.blackGrayList = blackGrayList;
    }

    public Boolean getFactor() {
        return factor;
    }

    public void setFactor(Boolean factor) {
        this.factor = factor;
    }

    public String getFactorIds() {
        return factorIds;
    }

    public void setFactorIds(String factorIds) {
        this.factorIds = factorIds;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
