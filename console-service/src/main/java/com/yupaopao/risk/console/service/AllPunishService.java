package com.yupaopao.risk.console.service;


import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.PunishPkgVO;
import com.yupaopao.risk.console.bean.PunishResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

public interface AllPunishService{

    Map<String,Object> getPackagesAndObjectTypes(GetPunishPackagesRequest request);

    List<PunishPkgVO> listPackages();

    List<PunishPkgVO> listPackagesByIds(List<Long> ids);

    PunishResponse doPunish(User user, Map<String,Object> parameters);

    PunishResponse executePunish(User user, Map<String,Object> parameters);

    List<Map<String,Object>> getRecentLoginDevice(Map<String,Object> parameters);

    @Setter
    @Getter
    @ToString
    class GetPunishPackagesRequest {
        private String channel; // 渠道
    }

}
