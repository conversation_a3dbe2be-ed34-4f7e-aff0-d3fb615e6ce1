package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.mapper.AlarmLogMapper;
import com.yupaopao.risk.common.model.AlarmLog;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AlarmLogVO;
import com.yupaopao.risk.console.service.AlarmLogService;
import com.yupaopao.risk.console.service.AtomRuleService;
import com.yupaopao.risk.console.service.EventService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * author: lijianjun
 * date: 2020/2/18 14:51
 */
@Service
public class AlarmLogServiceImpl extends BaseServiceImpl<AlarmLog, AlarmLogMapper> implements AlarmLogService {

    @Autowired
    private EventService eventService;

    @Autowired
    private AtomRuleService atomRuleService;

    @Override
    public PageInfo<AlarmLogVO> searchLog(AlarmLogVO alarmLogVO, Integer page, Integer size) {
        Example example = new Example(AlarmLog.class);
        Example.Criteria criteria = example.createCriteria();
        if(alarmLogVO.getStartTime() != null){
            criteria.andGreaterThanOrEqualTo("createTime",alarmLogVO.getStartTime());
        }
        if(alarmLogVO.getEndTime() != null){
            criteria.andLessThanOrEqualTo("createTime",alarmLogVO.getEndTime());
        }
        if(null != alarmLogVO.getId()){
            criteria.andEqualTo("id",alarmLogVO.getId());
        }
        if(StringUtils.isNotBlank(alarmLogVO.getEventCode())){
            criteria.andEqualTo("eventCode",alarmLogVO.getEventCode());
        }
        if(alarmLogVO.getRuleId()!=null){
            criteria.andEqualTo("ruleId",alarmLogVO.getRuleId());
        }
        if(null != alarmLogVO.getAlarmId()){
            criteria.andEqualTo("alarmId",alarmLogVO.getAlarmId());
        }
        if(null != alarmLogVO.getType()){
            criteria.andEqualTo("type",alarmLogVO.getType());
        }
        if(null != alarmLogVO.getRuleType()){
            criteria.andEqualTo("ruleType",alarmLogVO.getRuleType());
        }
        if(null != alarmLogVO.getActionType()){
            criteria.andEqualTo("actionType",alarmLogVO.getActionType());
        }
        if(null != alarmLogVO.getHandleState()){
            criteria.andEqualTo("handleState",alarmLogVO.getHandleState());
        }
        if(null != alarmLogVO.getFuseState()){
            criteria.andEqualTo("fuseState",alarmLogVO.getFuseState());
        }
        example.setOrderByClause("id desc");
        PageHelper.startPage(page,size);
        List<AlarmLog> list = this.getMapper().selectByExample(example);
        if(CollectionUtils.isNotEmpty(list)){
            PageInfo pageInfo = new PageInfo<>(list);
            Set<String> eventCodes = list.stream().map(log -> log.getEventCode()).collect(Collectors.toSet());
            Map<String,Object> eventMap = eventService.getEventsByCodes(eventCodes);
            Map<String,String> codeAndNameMap = (Map<String, String>) eventMap.get("data");

            Map<Long,String> ruleIdAndNameMap = Maps.newHashMap();
            Set<Long> ruleIds = list.stream().map(log->log.getRuleId()).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(ruleIds)){
                List<AtomRule> atomRules = this.atomRuleService.selectByIds(new ArrayList<>(ruleIds));
                if(CollectionUtils.isNotEmpty(atomRules)){
                    ruleIdAndNameMap = atomRules.stream().collect(Collectors.toMap(AtomRule::getId,AtomRule::getName));
                }
            }
            Map<Long, String> finalRuleIdAndNameMap = ruleIdAndNameMap;
            List<AlarmLogVO> voList = list.stream().map(alarmLog -> {
                AlarmLogVO vo = new AlarmLogVO(alarmLog);
                vo.setAlarmType(AlarmType.getByCode(alarmLog.getType()).getName());
                vo.setEventName(codeAndNameMap.get(alarmLog.getEventCode()));
                vo.setRuleName(finalRuleIdAndNameMap.get(alarmLog.getRuleId()));
                RuleType ruleType = RuleType.getByCode(vo.getRuleType());
                vo.setRuleTypeName(ruleType == null ? "" : (ruleType.getCode() == 0 ? "全类型" : ruleType.getName()));
                AlarmHandState handState = AlarmHandState.getByCode(alarmLog.getHandleState());
                vo.setHandleStateName(handState == null ? "" : handState.getName());
                AlarmFuseState fuseState = AlarmFuseState.getByCode(alarmLog.getFuseState());
                vo.setFuseStateName(fuseState == null ? "" : fuseState.getName());
                return vo;
            }).collect(Collectors.toList());
            pageInfo.setList(voList);
            return pageInfo;
        }else{
            return new PageInfo<>();
        }
    }

    @Override
    public boolean updateHandleState(AlarmLogVO vo) {
        AlarmLog upAlarmLog = new AlarmLog();
        upAlarmLog.setId(vo.getId());
        upAlarmLog.setHandleState(vo.getHandleState());
        upAlarmLog.setHandleResult(vo.getHandleResult());
        return this.updateSelectiveById(upAlarmLog);
    }

    @Transactional
    @Override
    public boolean updateForFuse(AtomRule atomRule, AlarmLog alarmLog) {
        AtomRule upAtomRule = new AtomRule();
        upAtomRule.setId(atomRule.getId());
        upAtomRule.setStatus(RuleStatus.TEST.name());
        atomRuleService.updateSelectiveById(upAtomRule);
        AlarmLog upAlarmLog = new AlarmLog();
        upAlarmLog.setId(alarmLog.getId());
        upAlarmLog.setFuseState(AlarmFuseState.ALREADY.getCode());
        this.updateSelectiveById(upAlarmLog);
        return true;
    }
}
