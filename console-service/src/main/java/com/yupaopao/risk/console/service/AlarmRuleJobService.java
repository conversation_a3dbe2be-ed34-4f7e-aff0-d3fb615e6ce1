package com.yupaopao.risk.console.service;


import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.common.vo.AlarmVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AlarmRuleJobService {

    Map<Integer,List<AtomRule>> getAtomRules();

    void doAlarm(AlarmVO alarmVO, AtomRule atomRule, Event event, Date jobTime);

}
