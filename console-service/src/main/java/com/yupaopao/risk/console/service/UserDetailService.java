package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.bixin.biggie.api.entity.BiggieInfoDTO;
import com.yupaopao.live.dto.LiveDTO;
import com.yupaopao.platform.passport.response.*;
import com.yupaopao.platform.passport.sdk.enums.SourceTypeEnum;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.platform.user.growth.api.entity.UserExperienceDTO;
import com.yupaopao.risk.common.model.DataClean;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.DataCleanReq;
import com.yupaopao.risk.console.vo.UserDataAssociate;
import com.yupaopao.yuer.chatroom.dto.ChatroomInfoDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface UserDetailService {

    /**
     * 获取用户电话号码的区号和尾串
     *
     * @param mobileNo
     * @return
     */
    Map<String,String> getNationCodeAndContent(String mobileNo);

    /**
     * 获取原始用户信息
     *
     * @param userQuery
     * @return
     */
    UserInfoDTO getUserInfo(UserQuery userQuery);

    /**
     * 根据UId获取原始用户信息
     *
     * @param uid
     * @return
     */
    UserInfoDTO getUserInfoByUID(String uid,Integer appId);

    /**
     * 根据UId,appId获取原始用户信息
     *
     * @param uid
     * @return
     */
    UserInfoDTO getUserInfoByUID(Long uid,Integer appId);

    /**
     * 根据鱼泡泡No获取原始用户信息
     *
     * @param yppNo
     * @return
     */
    UserInfoDTO getUserInfoByYppNo(String yppNo,Integer appId);

    /**
     * 根据鱼泡泡No,appId获取原始用户信息
     *
     * @param yppNo
     * @return
     */
    UserInfoDTO getUserInfoByYppNo(Long showNo,Integer appId);


    /**
     * 根据用户手机号码获取用户信息
     * @param mobileNo
     * @return
     */
    AccountDto getAccountByMobileNo(String mobileNo,Integer appId);

    List<AccountDto> getAccountsByMobileNo(String mobileNo);

    /**
     * 根据uid获取帐号信息，其createTime是注册时间
     * @param uid
     * @return
     */
    AccountDto getAccountByUid(Long uid);

    /**
     * 根据userId获取获取所有类型经验值
     *
     * @param uid
     * @return
     */
    List<UserExperienceDTO> getAllExpsByUid(Long uid);

    /**
     * 获得个人资料卡
     * @param uid
     * @return
     */
    Map<String,Object> getDataCard(Long uid);

    /**
     * 根据userId、event获取风控历史记录
     *
     * @param userId
     * @param days
     * @return
     */
    List<Map<String, Object>> getHitLog(String userId, int days);

    /**
     * 根据UserId聚合给定时间段内关联的设备
     *
     * @param userId
     * @param days
     * @return
     */
    Map<String, Object> getRelationDevice(String userId, Date startTime,Date endTime);

    /**
     * 根据UserId聚合给定时间段内关联的手机号码
     *
     * @param userId
     * @param days
     * @return
     */
    Map<String, Object> getRelationMobile(String userId, Date startTime,Date endTime);

    /**
     * 获得来源
     * @param uid
     * @return
     */
    SourceTypeEnum getSource(Long uid);

   /**
     * 根据UserId聚合给定时间段内关联的IP
     *
     * @param userId
     * @param days
     * @return
     */
    Map<String, Object> getRelationIP(String userId, Date startTime,Date endTime);

    /**
     * 根据UserId聚合给定时间段内风险分布
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    Map<String, Object> getRiskSummary(String userId, Date startTime,Date endTime);

    /**
     * 根据userId获取手机信息
     *
     * @param userId
     * @return
     */
    MobileInfo getMobileInfoByUserId(String userId);

    /**
     * 根据uid获取微信、QQ绑定标识
     *
     * @param uid
     * @return
     */
    UserDataAssociate getAssociateInfoByUid(Long uid,Integer accountType);

    // 获取用户事件等级聚合信息
    Map<String, Object> getRelationEvent(String userId, Date startTime,Date endTime);

    /**
     * 清洗
     * @param uid
     * @return
     */
    DataClean cleanData(User user, String uid, DataCleanReq request);

    /**
     * 根据roomId批量获取聊天室房间信息
     * @param appId: appId
     * @param roomIds: roomIds
     * @return list: chatroomInfoDTO list
     */
    List<ChatroomInfoDTO> getChatroomInfoByRoomIds(int appId, List<String> roomIds);

    /**
     * 根据roomNos批量获取聊天室房间信息
     * @param appId: appId
     * @param roomNos: roomNos
     * @return list
     */
    List<ChatroomInfoDTO> getChatroomInfoByRoomNos(int appId, List<Integer> roomNos);

    BiggieInfoDTO queryBiggieInfo(Long uid);

    List<AccountAPPFreezeInfo> getAccountFreezeInfo(Long uid);

    List<Map<String,Object>> getAccountFreezeInfos(Long uid,Integer accountType);

    List<GrayList> getBlackGray(Long uid);

    Map<String,Object> getUserRiskPortraitData(String uid,Date startTime,Date endTime);

    Boolean isChatroomHost(Long uid); // 是否聊天室主持人

    Map<String,Boolean> getChatroomRoomTag(Long uid); // 获取用户是否有聊天室官方房间、个人房间标签

    Boolean isAnchor(Long uid); // 是否主播

    JSONObject deviceFinger(String deviceId); // 获取设备指纹数据

    // 根据roomId获取直播间信息
    JSONObject getLiveInfoByRoomId(Long roomId);

    // 根据liveId获取直播间信息
    JSONObject getLiveInfoByLiveId(Long liveId);

    // 根据主播uid获取直播间信息
    JSONObject getLiveInfoByUid(Long uid);

    boolean deviceCleaning(String deviceId, User user);

    /**
     * 是否直播间机器人
     * @param uid
     * @return
     */
    boolean isLiveRobot(Long uid);

    /**
     * 是否聊天室机器人
     * @param uid
     * @return
     */
    boolean isYuerRobot(Long uid);

    @Setter
    @Getter
    class UserQuery {
        private UserDimension dimension;
        private String value;
        private UserTime time;
        private Integer accountType;
    }

    @Setter
    @Getter
    class UserRequest {
        private UserDimension dimension;
        private String value;
        private Date startTime;
        private Date endTime;
    }

    enum UserDimension {
        USERID,
        UID,
        MOBILENO,
        YPPNO;
    }

    enum UserTime {
        DAY(0),
        WEEK(6),
        HALFMONTH(14),
        MONTH(29);

        private int days;

        UserTime(int days) {
            this.days = days;
        }

        public int getDays() {
            return days;
        }
    }

    @Setter
    @Getter
    class UserHitQuery {
        private String userId;
        private UserTime time;
    }

    @Setter
    @Getter
    class UserHitRequest {
        private String userId;
        private Date startTime;
        private Date endTime;
    }

    @Setter
    @Getter
    class PhoneDetailRequest {
        private String userId;
        private String phone;
        private Object activeDegree;
        private Date startTime;
        private Date endTime;
    }

    @Setter
    @Getter
    class ChatroomQuery {
        private int appId;
        private List<String> roomIds;
        private List<Integer> roomNos;
    }
}




