package com.yupaopao.risk.console.utils;

import org.apache.commons.lang3.StringUtils;

public class AnalysisUtils {
    private static final Integer DEFAULT_MIN_DAYS = 7;
    private static final String WEEK = "WEEK";
    private static final String HALF_MONTH = "HALFMONTH";
    private static final String MONTH = "MONTH";
    private static final Integer DEFAULT_HALF_MONTH = 15;
    private static final Integer DEFAULT_MAX_DAYS = 29;

    public static Integer getQueryDate(String type) {
        if(StringUtils.isEmpty(type)){
            return DEFAULT_MIN_DAYS;
        }
        if (HALF_MONTH.equals(type)){
            return DEFAULT_HALF_MONTH;
        }
        if (MONTH.equals(type)){
            return DEFAULT_MAX_DAYS;
        }
        return DEFAULT_MIN_DAYS;
    }


}
