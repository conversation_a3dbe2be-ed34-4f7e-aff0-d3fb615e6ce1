package com.yupaopao.risk.console.service;

import com.yupaopao.risk.console.bean.EventHitQuery;
import com.yupaopao.risk.console.bean.RiskHitLogQuery;
import com.yupaopao.risk.console.vo.RiskHitLogVO;

import java.util.List;
import java.util.Map;

public interface RiskHitLogService {

    Map<String,Object> queryPassRiskResult(RiskHitLogVO record);

    Map<String,Object> queryReviewRejectRiskResult(RiskHitLogVO record);

    List<Map<String, Object>> queryActiveByDate(RiskHitLogVO record);

    List<Map<String,Object>> queryActiveSceneTop(RiskHitLogVO record);

    List<Map<String,Object>> queryViolationSceneTop(RiskHitLogVO record);

    List<Map<String,Object>> queryViolationTextTop(RiskHitLogVO record);

    List<Map<String,Object>> queryViolationImageTop(RiskHitLogVO record);


    /**
     * 查询该事件每一天的量
     * @param eventHitQuery
     * @return
     */
    List<Map<String,Object>> getRiskData(EventHitQuery eventHitQuery);

    /**
     * 分页查询指定事件指定时间段内的发送人和内容
     * @param query
     * @return
     */
    List<Map<String,Object>> queryRiskHitLog(RiskHitLogQuery query,String sqlFormat);

    /**
     * 查询指定时间段内指定事件的消息总量
     * @param query
     * @return
     */
    Integer queryCount(RiskHitLogQuery query);

}
