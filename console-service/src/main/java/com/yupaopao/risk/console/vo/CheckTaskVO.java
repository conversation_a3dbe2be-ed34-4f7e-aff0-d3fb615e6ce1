package com.yupaopao.risk.console.vo;

import com.alibaba.fastjson.JSONArray;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.common.model.CheckTaskOperateLog;
import com.yupaopao.risk.common.model.Event;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/6/23 17:32
 */
@Data
public class CheckTaskVO extends CheckTask implements Serializable {

    private static final long serialVersionUID = -5505331254976280240L;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 已标注数
     */
    private Integer markedCount;

    /**
     * 检测渠道名称
     */
    private String channelName;

    /**
     * 事件
     */
    private Event event;

    /**
     * 样本类型名
     */
    private String sampleTypeName;

    /**
     * 状态名
     */
    private String stateName;

    /**
     * 基础字段
     */
    private List<Map<String, String>> baseFields;

    /**
     * 操作人列表
     */
    private List<CheckTaskOperateLog> opLogs;

    public CheckTaskVO() {
    }

    public CheckTaskVO(CheckTask checkTask) {
        if (checkTask != null) {
            BeanUtils.copyProperties(checkTask,this);
        }
    }
}
