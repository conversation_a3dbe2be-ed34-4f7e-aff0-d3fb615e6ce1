package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.model.ThirdChannel;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.CheckType;
import com.yupaopao.risk.console.bean.ThirdChannelVO;

import java.util.List;
import java.util.Set;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/3/25 14:41
 */
public interface ThirdChannelService extends BaseService<ThirdChannel> {
    /**
     * 获得所有检测类型
     * @return
     */
    List<CheckType> getCheckTypes();

    /**
     * 关联查询
     * @param list
     * @return
     */
    List<ThirdChannelVO> relationFetch(List<ThirdChannel> list);

    /**
     * 根据id查询三方通道列表
     * @param ids
     * @return
     */
    List<ThirdChannel> listByIds(Set<Long> ids);

    /**
     * 通道参数中含有场景配置的三方通道
     * @param key 关键字
     * @return
     */
    List<ThirdChannel> hasConfig(String key);

    /**
     * 根据code获得检测类型
     * @param code
     * @return
     */
    CheckType getCheckTypeByCode(String code);

    /**
     * 查询三方通道列表
     * @param thirdChannelVO
     * @return
     */
    List<ThirdChannel> search(ThirdChannelVO thirdChannelVO);

    ThirdChannel getByName(String name);
}
