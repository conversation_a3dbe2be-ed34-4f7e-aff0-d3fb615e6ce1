package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Permission;
import com.yupaopao.risk.common.model.Role;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-6-14
 */
public interface PermissionService extends BaseService<Permission> {
    /**
     * 逻辑删除
     * @param id
     * @return -1: 存在子权限，父权限无法删除； 1：逻辑删除成功; 0: 删除失败
     */
    int deleteLogicalById(Long id);

    boolean updateByPrimaryKey(Permission permission);

    /**
     * 查询包含子权限的权限列表
     * @param userId 用户id
     * @return 含子权限的权限列表
     */
    List<Permission> getPermissionTreeList(Long userId);
    /**
     * 查询包含子权限的权限列表
     * @param roleList 角色列表
     * @return 含子权限的权限列表
     */
    List<Permission> getPermissionTreeList(List<Role> roleList);
}
