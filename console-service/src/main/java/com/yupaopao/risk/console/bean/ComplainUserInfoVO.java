package com.yupaopao.risk.console.bean;

import com.yupaopao.platform.common.annotation.Description;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ComplainUserInfoVO implements Serializable {
    private static final long serialVersionUID = 8058551090113711075L;

    @Description("被举报人uid")
    private String toUid;

    @Description("举报人uid")
    private String fromUid;

    @Description("举报时间")
    private Date complainTime;

    @Description("举报来源code")
    private String sourceCode;

    @Description("举报来源描述")
    private String sourceDesc;

    @Description("举报原因code")
    private String reasonCode;

    @Description("举报原因描述")
    private String reasonDesc;

    @Description("补充说明")
    private String briefDesc;

    @Description("补充材料")
    private List picUrls;

    @Description("处理状态:0=未处理 1=已处理 2=不处理")
    private String status;

    @Description("审核/处理时间")
    private Date auditTime;

    @Description("处理原因code")
    private String handleReasonCode;

    @Description("处理原因描述")
    private String handleReasonDesc;

    @Description("处理结果")
    private String result;
}
