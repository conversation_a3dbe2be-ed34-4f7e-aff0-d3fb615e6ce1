package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.MessageRecord;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2019/1/22 2:38 PM
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AuditNotifyResultVO extends MessageRecord {

    private String auditResult;
    private String eventType;
    private String uid;

}
