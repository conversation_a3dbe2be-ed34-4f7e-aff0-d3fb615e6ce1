package com.yupaopao.risk.console.bean;

public enum TraceType {

    UNKNOW(0),
    THIRD(10),
    HIT(20),
    AUDIT(30),
    AUDIT_NOTIFY(40),
    NOTIFY(50),
    CONTENT(60);

    private int code;

    TraceType(int code) {
        this.code = code;
    }

    public static TraceType nameOf(String name) {
        for (TraceType traceType : values()) {
            if (traceType.name().equalsIgnoreCase(name)) {
                return traceType;
            }
        }
        return UNKNOW;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
