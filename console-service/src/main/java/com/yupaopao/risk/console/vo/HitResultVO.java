package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.HitResult;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/1/22 2:38 PM
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HitResultVO extends HitResult {

    private String text;
    private Date startTime;
    private Date endTime;

}
