package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.AlarmRuleAtom;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2020/2/19 19:54
 */
public interface AlarmRuleAtomService extends BaseService<AlarmRuleAtom> {
    List<AlarmRuleAtomVO> listByRuleGroupId(Long ruleGroupId);
}
