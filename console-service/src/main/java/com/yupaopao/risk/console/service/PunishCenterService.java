package com.yupaopao.risk.console.service;


import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.PunishResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 惩罚中心服务
 * wangxinqi
 * 2021-03-18 16:12
 */
public interface PunishCenterService {
    Map<String, Object> getPunishPackages();

    PunishResponse batchPunish(User user, PunishCenterRequest punishCenterRequest);

    ShowNo2UidResponse showNo2Uid(ShowNo2UidRequest request);

    @Setter
    @Getter
    @ToString
    class PunishCenterRequest {
        private Long packageId; // 惩罚包ID
        private String objectTypeA; // 惩罚对象类型A
        private List<String> objectIdsA; // 惩罚对象ID集合A
        private String objectTypeB; // 惩罚对象类型B
        private List<String> objectIdsB; // 惩罚对象ID集合B
        private String proofImgUrl; // 图片凭证
        private String internalReason;
        private String externalReason;
        private Integer appId;
    }

    @Setter
    @Getter
    @ToString
    class ShowNo2UidRequest {
        private String showNo;
        private String accountType;
    }

    @Setter
    @Getter
    @ToString
    class ShowNo2UidResponse {
        private String showNos;
        private String uids;
    }
}
