package com.yupaopao.risk.console.bean;

public enum CorrectionType {
    ERROR("error", "误杀"),
    MISS("miss", "漏杀");

    private String code; // 类型
    private String desc; // 描述

    CorrectionType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }
}
