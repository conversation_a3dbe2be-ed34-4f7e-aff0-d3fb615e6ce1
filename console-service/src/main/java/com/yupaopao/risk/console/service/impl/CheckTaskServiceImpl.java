package com.yupaopao.risk.console.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.mapper.CheckTaskMapper;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.common.model.CheckTaskOperateLog;
import com.yupaopao.risk.common.model.SampleMark;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.bean.ValidResult;
import com.yupaopao.risk.console.service.CheckTaskOpLogService;
import com.yupaopao.risk.console.service.CheckTaskService;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.service.SampleMarkService;
import com.yupaopao.risk.console.vo.CheckTaskVO;
import com.yupaopao.risk.data.api.RiskDataService;
import com.yupaopao.risk.data.bean.RequestData;
import com.yupaopao.risk.data.bean.ResponseData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * author: lijianjun
 * date: 2021/6/23 16:42
 */
@Slf4j
@Service
public class CheckTaskServiceImpl extends BaseServiceImpl<CheckTask, CheckTaskMapper> implements CheckTaskService {

    private final static String CHECK_TASK_SAMPLE_TYPE_INFO = "check.task.sample.type.info";
//    private final static String LEGAL_SEARCH_SQL = "select(.*)from(.*)(where.*)?(group by.*)?(order by.*)?(limit.*)?";
    private final static String LEGAL_SEARCH_SQL = "select(.*)from(.*)(where.*)?(limit.*)?(((group by.*)?(order by.*)?)|((order by.*)?(group by.*)?))";
    private final static Pattern CORRECT_SEARCH_SQL_PATTERN = Pattern.compile(LEGAL_SEARCH_SQL);
    private final static Pattern SQL_LIMIT_PATTERN = Pattern.compile("(limit )([0-9]+)(;)?");

    @Reference(check = false,timeout = 60000)
    public RiskDataService riskDataService;

    @Autowired
    private SampleMarkService sampleMarkService;

    @Autowired
    private EventService eventService;

    @ApolloConfig
    private Config config;

    @Autowired
    private CheckTaskOpLogService checkTaskOpLogService;

    @Value("${search.click.house.data.max.quantity:5000}")
    private Integer sqlSearchMaxQuantity;

    @Value("${sample.mark.un.base.field.max.len:200}")
    private Integer unBaseFieldMaxLen;

    private Map<String, SampleTypeInfo> sampleTypeInfoMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {

        initSampleTypeInfoMap(config.getProperty(CHECK_TASK_SAMPLE_TYPE_INFO, ""));

        config.addChangeListener(event -> {
            if (event.isChanged(CHECK_TASK_SAMPLE_TYPE_INFO)) {
                String value = event.getChange(CHECK_TASK_SAMPLE_TYPE_INFO).getNewValue();
                initSampleTypeInfoMap(value);
            }
        });
    }

    private void initSampleTypeInfoMap(String info) {
        Map<String, SampleTypeInfo> sampleTypeInfoMap = new ConcurrentHashMap<>();

        List<SampleTypeInfo> sampleTypeInfos = JSONArray.parseArray(info, SampleTypeInfo.class);
        if (CollectionUtils.isNotEmpty(sampleTypeInfos)) {
            sampleTypeInfos.forEach(item -> {
                String typeName = item.getType();
                sampleTypeInfoMap.put(typeName, item);
            });
        }

        this.sampleTypeInfoMap = sampleTypeInfoMap;
    }

    @Override
    public PageInfo<CheckTask> search(User user, CheckTask record, Integer page, Integer size) {

        if (record == null) {
            return null;
        }

        PageHelper.startPage(page, size, "state asc,update_time desc");
        Example example = new Example(CheckTask.class, false);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(record.getName())) {
            criteria.andLike("name","%"+record.getName()+"%");
        }
        if (StringUtils.isNotBlank(record.getDescription())) {
            criteria.andLike("description", "%"+record.getDescription()+"%");
        }
        if (record.getState() != null) {
            criteria.andEqualTo("state", record.getState());
        }
        if (record.getSampleType() != null) {
            criteria.andEqualTo("sampleType", record.getSampleType());
        }
        if (StringUtils.isNotBlank(record.getScene())) {
            criteria.andEqualTo("scene", record.getScene());
        }
        if (record.getChannel() != null) {
            criteria.andEqualTo("channel", record.getChannel());
        }

        return new PageInfo<>(getMapper().selectByExample(example));
    }


    @Override
    public ValidResult validSearchSql(String sql,String sampleType) {
        String newSql = sql.replace("\n", " ");
        if(!newSql.endsWith(";")){
            return ValidResult.error("8006", "查询语句必须以;结束");
        }

        newSql = newSql.substring(0,sql.indexOf(";"));

        newSql = newSql.replace("from(","from (");
        Matcher matcher = CORRECT_SEARCH_SQL_PATTERN.matcher(newSql);
        if (!matcher.matches()) {
            return ValidResult.error("8001", "查询语句语法错误！");
        }

        // 校验查询语句是否包含了基础字段
        String baseFieldStr = matcher.group(1);
        SampleTypeInfo sampleTypeInfo = this.sampleTypeInfoMap.get(sampleType);
        if(sampleTypeInfo==null){
            return ValidResult.error("8007","缺少相应的配置");
        }

        for(Map<String, String> baseField:sampleTypeInfo.getBaseFields()){
            String fieldCode = baseField.get("code");
            if(!baseFieldStr.contains(fieldCode)){
                return ValidResult.error("8004","查询语句缺少必要的基础字段"+fieldCode);
            }
        }

        //是否含有limit 10;
        Matcher matcher1 = SQL_LIMIT_PATTERN.matcher(newSql);
        if(matcher1.find()){
            if(newSql.endsWith(matcher1.group(0))){
                Integer count = Integer.parseInt(matcher1.group(2));
                if(count>sqlSearchMaxQuantity){
                    return ValidResult.error("8003", "查询语句查询数据量超过最大限制" + sqlSearchMaxQuantity);
                }else{
                    return ValidResult.success();
                }
            }
        }

        //不含有limit 10。 需要去拼count(*)
//        String targetStr = newSql.substring(newSql.indexOf("select ")+7,newSql.indexOf(" from "));
//        // 校验查询语句执行获取的数据量
//        newSql = newSql.replace(targetStr, " count(*) as count ");
        newSql = String.format("select count(*) as count from (%s);",newSql);

        RequestData requestData = new RequestData().withSystemCode("risk-console").withApproachCode("clickhouseQuery");
        requestData.withParam("sql", newSql);

        try {
            ResponseData response = riskDataService.getRiskData(requestData);
            if (response == null) {
                Thread.sleep(10);
                response = riskDataService.getRiskData(requestData);
                if (response == null) {
                    response = riskDataService.getRiskData(requestData);
                    if (response == null) {
                        return ValidResult.error("8002", "调用clickhouse查询数据接口返回值为null！");
                    }
                }
            }

            log.info("response: {}", JSON.toJSONString(response));
            if (!"000".equals(response.getCode())) {
                return ValidResult.error(response.getCode(), response.getMessage());
            }

            if (!MapUtils.isEmpty(response.getData())) {
                Map queryRes = (Map) response.getData().get("clickhouseQueryService");
                if (!MapUtils.isEmpty(queryRes)) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) queryRes.get("sqlResult");
                    if (CollectionUtils.isNotEmpty(list)) {
                        if (list.get(0).get("count") != null && StringUtils.isNotBlank(list.get(0).get("count").toString())) {
                            Integer count = Integer.parseInt(list.get(0).get("count").toString());
                            if (count > sqlSearchMaxQuantity) {
                                return ValidResult.error("8003", "查询语句查询数据量超过最大限制" + sqlSearchMaxQuantity);
                            }
                        }else{
                            return ValidResult.error("8005","返回值中不包含count字段值");
                        }
                    }else{
                        return ValidResult.error("8005","返回值中不包含count字段值");
                    }
                }
            }

        } catch (Exception exp) {
            log.error("校验查询语句，调用离线风控接口发生异常", exp);
            return ValidResult.error("8005","校验查询语句，调用离线风控接口发生异常");
        }

        return ValidResult.success();
    }


    @Override
    public Boolean add(CheckTask record, User user) {

        // 新增任务
        record.setState(CheckTaskState.CREATING.getCode()); //"生成中"
        record.setOperator(user.getName());
        record.setEvaluateResult("");
        Date date = new Date();
        record.setCreateTime(date);
        record.setUpdateTime(date);
        record.setSampleCount(0);
        record.setRejectCount(0);
        record.setPassCount(0);

        long count = this.getMapper().save(record);
        if (count <= 0) {
            return false;
        }

        //新增一条任务操作记录
        CheckTaskOperateLog opLog = new CheckTaskOperateLog();
        opLog.setTaskId(record.getId());
        opLog.setOperator(user.getName());
        opLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
        opLog.setType(CheckTaskOpType.CREATE.getCode());

        boolean success = checkTaskOpLogService.insertSelective(opLog);

        if (!success) {
            return false;
        }

        //执行任务
        this.execute(record);

        return true;
    }

    @Override
    @Async
    public void execute(CheckTask record) {

        RequestData requestData = new RequestData().withSystemCode("risk-console").withApproachCode("clickhouseQuery");
        requestData.withParam("sql", record.getSearchSql());

        ResponseData response = null;
        try {
            response = riskDataService.getRiskData(requestData);
            if (response == null) {
                response = riskDataService.getRiskData(requestData);
                if (response == null) {
                    response = riskDataService.getRiskData(requestData);
                    if (response == null) {
                        log.error("执行任务" + record.getName() + "调用clickhouse查询数据接口返回值为null！");
                        return;
                    }
                }
            }

            log.info("请求入参:{},调用离线风控接口返回值: {}", JSONObject.toJSONString(record),JSON.toJSONString(response));
            if (!"000".equals(response.getCode())) {
                log.error("执行任务" + record.getName() + "调用clickhouse查询数据接口报异常,code:" + response.getCode() + ",msg:" + response.getMessage());
                return;
            }
        } catch (Exception exp) {
            log.error("校验查询语句，调用离线风控接口发生异常", exp);
            return;
        }

        try{
            List<SampleMark> sampleMarks = Lists.newArrayList();

            if (MapUtils.isNotEmpty(response.getData())) {
                Map queryRes = (Map) response.getData().get("clickhouseQueryService");
                if (MapUtils.isNotEmpty(queryRes)) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) queryRes.get("sqlResult");
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (Map<String, Object> item : list) {
                            SampleMark sampleMark = create(item,record.getSampleType(),record.getId());
                            sampleMarks.add(sampleMark);
                        }
                    }else{
                        log.error("执行任务" + record.getName() + "调用clickhouse查询数据接口返回结果为空");
                    }
                }
            }

            //执行批量插入样本数据
            if(CollectionUtils.isNotEmpty(sampleMarks))
            {
                this.sampleMarkService.batchInsert(sampleMarks);
            }

            //修改任务状态为"待标注"
            record.setState(CheckTaskState.TOMARK.getCode());
            record.setSampleCount(sampleMarks.size());

            this.updateSelectiveById(record);
        }catch(Exception exception){
            log.error("解析离线风控接口返回值，生成样本数据并保存时发生异常", exception);
        }

    }

    @Override
    public SampleMark create(Map<String, Object> record, String sampleType, Long taskId) {

        SampleTypeInfo sampleTypeInfo = this.sampleTypeInfoMap.get(sampleType);
        if (sampleTypeInfo == null||CollectionUtils.isEmpty(sampleTypeInfo.getBaseFields())) {
            return null;
        }

        SampleMark sampleMark = new SampleMark();

        // 基础字段
        for (Map<String, String> baseField : sampleTypeInfo.getBaseFields()) {
            String code = baseField.get("code");
            String msg = record.get(code)==null?"":record.get(code).toString();

            if (SampleBaseField.SAMPLE.getName().equals(code)) {
                sampleMark.setSample(msg.length() < 512 ? msg : msg.substring(0, 512));
            } else if (SampleBaseField.LEVEL.getName().equals(code)) {
                sampleMark.setLevel(msg);
            } else if (SampleBaseField.WORD.getName().equals(code)) {
                sampleMark.setWord(msg.length() < 32 ? msg : msg.substring(0,32));
            } else if (SampleBaseField.RISKLABELS.getName().equals(code)) {
                sampleMark.setRiskLabels(msg);
            } else if (SampleBaseField.RISKSUBLABELS.getName().equals(code)) {
                sampleMark.setRiskSubLabels(msg);
            }
        }

        if(sampleMark.getSample()==null){
            sampleMark.setSample("");
        }

        if(sampleMark.getLevel()==null){
            sampleMark.setLevel("");
        }

        if(sampleMark.getWord()==null){
            sampleMark.setWord("");
        }

        if(sampleMark.getRiskLabels()==null){
            sampleMark.setRiskLabels("");
        }

        if(sampleMark.getRiskSubLabels()==null){
            sampleMark.setRiskSubLabels("");
        }

        // 非基础字段
        sampleMark.setResult(SampleMarkResult.UNDONE.getCode());
        sampleMark.setTaskId(taskId);
        sampleMark.setType(sampleType);
        sampleMark.setInfo(createInfo(record,sampleTypeInfo));
        sampleMark.setExecutor("");
        Date date= new Date();
        sampleMark.setCreateTime(date);
        sampleMark.setUpdateTime(date);

        return sampleMark;
    }

    private String createInfo(Map<String, Object> record,SampleTypeInfo sampleTypeInfo){

        List<String> baseFields = Lists.newArrayList();
        for (Map<String, String> baseField : sampleTypeInfo.getBaseFields()) {
            String code = baseField.get("code");
            baseFields.add(code);
        }

        if(MapUtils.isNotEmpty(record)){
            for(Map.Entry<String,Object> entry:record.entrySet()){
                if(!baseFields.contains(entry.getKey())){
                    if(entry.getValue()!=null){
                        String value = entry.getValue().toString();
                        value = value.length() < unBaseFieldMaxLen? value: value.substring(0,unBaseFieldMaxLen);
                        record.put(entry.getKey(),value);
                    }
                }
            }
        }

        return JSONObject.toJSONString(record);
    }


    @Override
    public CheckTaskVO detail(Long taskId) {

        CheckTask checkTask = this.get(taskId);
        if(null != checkTask){
            CheckTaskVO checkTaskVO = new CheckTaskVO(checkTask);
            checkTaskVO.setChannelName(CheckDetectChannel.getEnumByCode(checkTaskVO.getChannel()).getMsg());
            checkTaskVO.setEvent(eventService.getByCode(checkTaskVO.getScene()));
            checkTaskVO.setSampleTypeName(SampleType.getEnumByName(checkTaskVO.getSampleType()).getMsg());
            checkTaskVO.setStateName(CheckTaskState.getEnumByCode(checkTaskVO.getState()).getMsg());
            checkTaskVO.setEvent(this.eventService.getByCode(checkTaskVO.getScene()));
            SampleTypeInfo sampleTypeInfo = sampleTypeInfoMap.get(checkTaskVO.getSampleType());
            if(sampleTypeInfo==null){
                checkTaskVO.setBaseFields(Lists.newArrayList());
            }else{
                List<Map<String, String>> maps = sampleTypeInfo.getBaseFields();
                checkTaskVO.setBaseFields(maps);
            }

            CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
            operateLog.setTaskId(checkTask.getId());
            List<CheckTaskOperateLog> operateLogs = this.checkTaskOpLogService.select(operateLog);
            if(CollectionUtils.isNotEmpty(operateLogs)){
                checkTaskVO.setOpLogs(operateLogs);
                operateLogs.forEach(log->{
                    log.setTypeName(CheckTaskOpType.getEnumByCode(log.getType()).getMsg());
                    if(Objects.equals(log.getType(),CheckTaskOpType.CREATE.getCode())){
                        checkTaskVO.setCreator(log.getOperator());
                    }
                });
            }

            //不是"完成"和"取消"状态的时候，需要实时统计
            if(!Objects.equals(checkTask.getState(),CheckTaskState.COMPLETED.getCode())||!Objects.equals(checkTask.getState(),CheckTaskState.CANCEL.getCode())){
                int sampleCount = this.sampleMarkService.countByTaskId(taskId,null);
                int passCount = this.sampleMarkService.countByTaskId(taskId, com.google.common.collect.Lists.newArrayList(SampleMarkResult.PASS.getCode()));
                int rejectCount = this.sampleMarkService.countByTaskId(taskId, com.google.common.collect.Lists.newArrayList(SampleMarkResult.REJECT.getCode()));

                checkTaskVO.setSampleCount(sampleCount);
                checkTaskVO.setPassCount(passCount);
                checkTaskVO.setRejectCount(rejectCount);
            }

            checkTaskVO.setMarkedCount(checkTaskVO.getPassCount()+checkTaskVO.getRejectCount());

            return checkTaskVO;
        }

        return null;
    }

    @Override
    public Boolean evaluate(CheckTask record, User user) {

        //  将状态改为"已完成"，并保存评估结果
        record.setState(CheckTaskState.COMPLETED.getCode());
        record.setOperator(user.getName());

        this.updateSelectiveById(record);

        //增加一条"完成评估"操作记录
        CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
        operateLog.setTaskId(record.getId());
        operateLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
        operateLog.setType(CheckTaskOpType.EVALUATED.getCode());
        operateLog.setOperator(user.getName());

        return this.checkTaskOpLogService.insertSelective(operateLog);
    }


    @Override
    public Boolean cancel(Long taskId,User user) {
        CheckTask  record = this.get(taskId);

        //  将状态改为"已取消"，并保存评估结果
        record.setState(CheckTaskState.CANCEL.getCode());
        record.setOperator(user.getName());
        this.updateSelectiveById(record);

        //增加一条"取消任务"操作记录
        CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
        operateLog.setTaskId(record.getId());
        operateLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
        operateLog.setType(CheckTaskOpType.CANCEL.getCode());
        operateLog.setOperator(user.getName());

        return this.checkTaskOpLogService.insertSelective(operateLog);
    }

    @Override
    public Boolean toMark(CheckTask record, User user) {
        Long taskId = record.getId();
        record = this.get(taskId);

        //该任务的状态是"标注中"
        if(Objects.equals(record.getState(),CheckTaskState.MARKING.getCode())){
            return true;
        }else{
            //去更新该任务的状态为"开始标注"
            record.setState(CheckTaskState.MARKING.getCode());
            record.setOperator(user.getName());

            Example example = new Example(CheckTask.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("state",CheckTaskState.TOMARK.getCode());
            criteria.andEqualTo("id",record.getId());

            int count = getMapper().updateByExampleSelective(record,example);
            if(count>=1){
                //插入一条"开始标记"的操作记录
                CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
                operateLog.setTaskId(record.getId());
                operateLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
                operateLog.setType(CheckTaskOpType.STARTMARK.getCode());
                operateLog.setOperator(user.getName());

                return this.checkTaskOpLogService.insertSelective(operateLog);
            }

            return false;
        }

    }

    @Override
    public CheckTaskVO getCheckTask(Long taskId) {
        CheckTask checkTask = this.get(taskId);
        if(null != checkTask){
            CheckTaskVO checkTaskVO = new CheckTaskVO(checkTask);
            checkTaskVO.setChannelName(CheckDetectChannel.getEnumByCode(checkTaskVO.getChannel()).getMsg());
            checkTaskVO.setEvent(eventService.getByCode(checkTaskVO.getScene()));
            checkTaskVO.setSampleTypeName(SampleType.getEnumByName(checkTaskVO.getSampleType()).getMsg());

            SampleTypeInfo sampleTypeInfo = sampleTypeInfoMap.get(checkTaskVO.getSampleType());
            if(sampleTypeInfo==null){
                checkTaskVO.setBaseFields(Lists.newArrayList());
            }else{
                List<Map<String, String>> maps = sampleTypeInfo.getBaseFields();
                checkTaskVO.setBaseFields(maps);
            }

            int sampleCount = this.sampleMarkService.countByTaskId(taskId,null);
            int passCount = this.sampleMarkService.countByTaskId(taskId, com.google.common.collect.Lists.newArrayList(SampleMarkResult.PASS.getCode()));
            int rejectCount = this.sampleMarkService.countByTaskId(taskId, com.google.common.collect.Lists.newArrayList(SampleMarkResult.REJECT.getCode()));

            checkTaskVO.setSampleCount(sampleCount);
            checkTaskVO.setMarkedCount(passCount+rejectCount);
            checkTaskVO.setPassCount(passCount);
            checkTaskVO.setRejectCount(rejectCount);

            return checkTaskVO;
        }

        return null;
    }

    @Transactional
    @Override
    public boolean batchUpdateResult(List<SampleMark> sampleMarks, User user) {
        Long taskId = sampleMarks.get(0).getTaskId();
        CheckTask checkTask = this.get(taskId);
        if(Objects.equals(checkTask.getState(),CheckTaskState.COMPLETED.getCode())
        || Objects.equals(checkTask.getState(),CheckTaskState.CANCEL.getCode())){
            throw new RiskException("该任务已不在可标注状态！");
        }
        for(SampleMark sampleMark : sampleMarks){
            SampleMark upSampleMark = new SampleMark();
            upSampleMark.setId(sampleMark.getId());
            upSampleMark.setResult(sampleMark.getResult());
            upSampleMark.setExecutor(user.getName());
            sampleMarkService.updateSelectiveById(upSampleMark);
        }
        if(!Objects.equals(checkTask.getState(),CheckTaskState.TOEVALUATE.getCode())){
            int count  = sampleMarkService.countByTaskId(taskId,Lists.newArrayList(SampleMarkResult.UNDONE.getCode()));
            if(count == 0){
                CheckTask upCheckTask = new CheckTask();
                upCheckTask.setId(taskId);
                upCheckTask.setState(CheckTaskState.TOEVALUATE.getCode());
                upCheckTask.setOperator(user.getName());
                this.updateSelectiveById(upCheckTask);

                CheckTaskOperateLog operateLog = new CheckTaskOperateLog();
                operateLog.setTaskId(taskId);
                operateLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
                operateLog.setType(CheckTaskOpType.MARKED.getCode());
                operateLog.setOperator(user.getName());
                operateLog.setTaskType(TaskOpType.CONTENT_CHECK.getCode());
                checkTaskOpLogService.insertSelective(operateLog);
            }
        }
        return true;
    }
}

    @Data
    class SampleTypeInfo implements Serializable {
        private String type;
        private List<Map<String, String>> baseFields;
    }