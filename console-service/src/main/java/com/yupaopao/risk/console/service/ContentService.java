package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.console.vo.LogSearchVO;

import java.util.Map;

public interface ContentService {

    Map<String, Object> summary(LogSearchVO vo);

    Map<String, Object> trend(LogSearchVO vo);

    PageInfo event(LogSearchVO vo);

    Map<String, Object> reject(LogSearchVO vo);

    Map<String, Object> review(LogSearchVO vo);

    PageInfo hotProhibitedWords(LogSearchVO logSearchVO);

}
