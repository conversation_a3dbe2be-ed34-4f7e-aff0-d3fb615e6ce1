package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.aries.client.AriesClient;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.access.api.RiskService;
import com.yupaopao.risk.access.bean.RiskAction;
import com.yupaopao.risk.access.bean.RiskLevel;
import com.yupaopao.risk.access.bean.RiskResult;
import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.vo.AlarmConfigVO;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.job.RuleFuseJob;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.utils.DateUtils;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AlarmRuleJobServiceImpl implements AlarmRuleJobService {

    @RedisAutowired("middleware.redis.risk-magic")
    private RedisService redisService;
    @Autowired
    private AtomRuleService atomRuleService;
    @Autowired
    private AlarmJobService alarmJobService;
    @Autowired
    private AriesClient ariesClient;
    @Autowired
    private RuleFuseJob ruleFuseJob;
    @Autowired
    private ElasticSearchService elasticSearchService;
    @Autowired
    private AttributeService attributeService;
    @DubboReference(timeout = 60000)
    private RiskService riskService;
    /**
     * 熔断任务延迟时间
     */
    @Value("${fuse.job.delay.time:180}")
    private Integer fuseJobDelayTime;
    @Value("${risk.timeout.tax:15}")
    private Long taxTime;

    @Override
    public Map<Integer,List<AtomRule>> getAtomRules() {
        Map<Integer,List<AtomRule>> resultMap = Maps.newHashMap();
        AtomRule record = new AtomRule();
        record.setStatus(RuleStatus.ENABLE.name());
        List<AtomRule> list = atomRuleService.select(record);
        List<AtomRule> cheatList = Lists.newArrayList(),spamList = Lists.newArrayList();
        resultMap.put(RuleType.DEFAULT.getCode(),list);
        resultMap.put(RuleType.CHEAT.getCode(),cheatList);
        resultMap.put(RuleType.SPAM.getCode(),spamList);
        if(CollectionUtils.isNotEmpty(list)){
            for(AtomRule atomRule : list){
                if(atomRule.getType() == RuleType.CHEAT.getCode()){
                    cheatList.add(atomRule);
                }else if(atomRule.getType() == RuleType.SPAM.getCode()){
                    spamList.add(atomRule);
                }
            }
        }
        return resultMap;
    }

    @Override
    public void doAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, Date jobTime) {
        //查询告警配置
        AlarmConfigVO alarmConfigVO = alarmVO.getConfig();
        if (alarmConfigVO == null) {
            return;
        }
        //如果是熔断告警，但规则不可以熔断，直接返回
        if(alarmVO.getActionType() == AlarmActionType.FUSE.getCode()
                && !canFuse(atomRule)){
            return;
        }
        List<AlarmRuleGroupVO> groupVOS = alarmConfigVO.getRuleGroup();
        if(CollectionUtils.isEmpty(groupVOS)){
            return;
        }
        //前置时间范围检测，如果不符合时间范围，就无需从缓存获取数据，降低qps
        groupVOS = alarmJobService.timeScopeCheck(alarmVO,alarmConfigVO,groupVOS,DateUtils.addMinutes(jobTime, -1));
        if (CollectionUtils.isEmpty(groupVOS)) {
            return;
        }
        //命中规则组内容
        List<Map<String, String>> hitGroups = new ArrayList<>();
        Map<String,Integer> countMap = getCount(alarmConfigVO.getCountType(),jobTime,atomRule.getId(),event.getCode(),alarmVO.getRiskResult(),alarmVO.getActionType());
        log.info("告警Job统计 id:{}，name:{}，map:{}",alarmVO.getId(),alarmVO.getName(), countMap);
        groupVOS.forEach(ruleGroup -> {
            List<AlarmRuleAtomVO> alarmRules = ruleGroup.getRules();
            //当前规则组中命中规则
            Map<String, String> hitRules = new HashMap<>();
            for(AlarmRuleAtomVO alarmRule : alarmRules){
                //时间范围已经前置检测过
                if (alarmRule.getType() == AlarmRuleType.ALARM_TIME_RANGE.getCode()) {
                    continue;
                }
                //根据规则获取统计并判断是否告警，组内规则需全部命中
                if (!alarmJobService.hitRule(alarmRule, countMap, hitRules)) {
                    return;
                }
            }
            if (hitRules.size() > 0) {
                hitGroups.add(hitRules);
            }
        });
        //未命中规则组规则，不进行告警
        if (hitGroups.size() <= 0) {
            return;
        }
        //调用告警服务告警
        if(alarmVO.getActionType() == AlarmActionType.ALARM.getCode()){
            alarmJobService.ruleAlarm(alarmVO,atomRule,event,alarmVO.getConfig(),hitGroups,jobTime);
        }else{
            if(canFuse(event,jobTime)){
                Long logId = alarmJobService.ruleWillFuseAlarm(alarmVO,atomRule,event,alarmVO.getConfig(),hitGroups,jobTime);
                Map<String,Object> jobParams = Maps.newHashMap();
                jobParams.put("ruleId",atomRule.getId());
                jobParams.put("alarmLogId",logId);
                jobParams.put("eventName",event.getName());
                ariesClient.createDefaultDelayJob(ruleFuseJob,fuseJobDelayTime,JSON.toJSONString(jobParams));
            }else{
                alarmJobService.ruleNotFuseAlarm(alarmVO,atomRule,event,alarmVO.getConfig(),hitGroups,jobTime,"自动风控检测后不熔断");
            }
        }
    }

    private boolean canFuse(AtomRule atomRule){
        if(RuleStatus.ENABLE.name().equals(atomRule.getStatus())
                && atomRule.getCanFuse()){
            return true;
        }
        return false;
    }

    private Map<String,Integer> getCount(Integer countType, Date jobTime, Long ruleId,String eventCode, String level,Integer actionType) {
        Map<String,Integer> countMap = Maps.newHashMap();
        Map<String,Integer> behindCountMap = null,frontCountMap = null;
        if (countType == AlarmConfigCountType.MINUTE.getCode()) {
            Date endTime = DateUtils.addMinutes(jobTime, -1);
            behindCountMap = getMinCount(endTime,ruleId,eventCode,level,actionType);
            endTime = DateUtils.addMinutes(endTime, -1);
            frontCountMap = getMinCount(endTime,ruleId,eventCode,level,actionType);
        } else if (countType == AlarmConfigCountType.HOUR.getCode()) {
            Date endTime = DateUtils.addMinutes(jobTime, -1);
            behindCountMap = getHourCount(endTime,ruleId,eventCode,level,actionType);
            endTime = DateUtils.addMinutes(endTime, -60);
            frontCountMap = getHourCount(endTime,ruleId,eventCode,level,actionType);
        } else if (countType == AlarmConfigCountType.DAY.getCode()) {
            Date endTime = DateUtils.addHours(jobTime,-1);
            behindCountMap = getDayCount(endTime,ruleId,eventCode,level,actionType);
            endTime = DateUtils.addHours(jobTime,-24);
            frontCountMap = getDayCount(endTime,ruleId,eventCode,level,actionType);
        } else if (countType == AlarmConfigCountType.WEEK.getCode()) {
            Date endTime = DateUtils.addDays(jobTime,-1);
            behindCountMap = getWeekCount(endTime,ruleId,eventCode,level,actionType);
            endTime = DateUtils.addDays(jobTime,-7);
            frontCountMap = getWeekCount(endTime,ruleId,eventCode,level,actionType);
        }
        countMap.put(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT,MapUtils.getInteger(behindCountMap,ConsoleConstants.GIVEN_RESULT_COUNT,0));
        countMap.put(ConsoleConstants.BEHIND_ALL_RESULT_COUNT,MapUtils.getInteger(behindCountMap,ConsoleConstants.ALL_RESULT_COUNT,0));
        countMap.put(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT,MapUtils.getInteger(frontCountMap,ConsoleConstants.GIVEN_RESULT_COUNT,0));
        countMap.put(ConsoleConstants.FRONT_ALL_RESULT_COUNT,MapUtils.getInteger(frontCountMap,ConsoleConstants.ALL_RESULT_COUNT,0));
        return countMap;
    }

    private Map<String,Integer> getMinCount(Date endTime, Long ruleId, String eventCode, String level, Integer actionType){
        String timeKey = DateUtils.formatDate(endTime,DateUtils.YYYYMMDDHHMM);
        String cacheKey = String.format(Constants.RULE_COUNT_REDIS_KEY,ruleId,timeKey);
        try {
            Map cacheMap = redisService.hmget(cacheKey);
            return collectCount(Lists.newArrayList(cacheMap),eventCode,level,actionType);
        }catch (Exception e){
            log.error("从redis中获取规则命中统计值异常，key："+cacheKey+"，eventCode："+eventCode+"，level"+level, e);
        }
        return Maps.newHashMap();
    }

    private Map<String,Integer> getHourCount(Date endTime,Long ruleId,String eventCode, String level,Integer actionType){
        List<String> keys = Lists.newArrayList();
        for (int times = 0; times < 60; times++) {
            String timeKey = DateUtils.formatDate(endTime,DateUtils.YYYYMMDDHHMM);
            String cacheKey = String.format(Constants.RULE_COUNT_REDIS_KEY,ruleId,timeKey);
            keys.add(cacheKey);
            endTime = DateUtils.addMinutes(endTime, -1);
        }
        return getCacheCount(keys,eventCode,level,actionType);
    }

    private Map<String,Integer> getDayCount(Date endTime,Long ruleId,String eventCode, String level,Integer actionType){
        List<String> keys = Lists.newArrayList();
        for (int times = 0; times < 24; times++) {
            String timeKey = DateUtils.formatDate(endTime,DateUtils.YYYYMMDDHH);
            String cacheKey = String.format(Constants.RULE_COUNT_REDIS_KEY,ruleId,timeKey);
            keys.add(cacheKey);
            endTime = DateUtils.addHours(endTime, -1);
        }
        return getCacheCount(keys,eventCode,level,actionType);
    }

    private Map<String,Integer> getWeekCount(Date endTime,Long ruleId,String eventCode, String level,Integer actionType){
        List<String> keys = Lists.newArrayList();
        for (int times = 0; times < 7; times++) {
            String timeKey = DateUtils.formatDate(endTime,DateUtils.YYYYMMDD);
            String cacheKey = String.format(Constants.RULE_COUNT_REDIS_KEY,ruleId,timeKey);
            keys.add(cacheKey);
            endTime = DateUtils.addDays(endTime, -1);
        }
        return getCacheCount(keys,eventCode,level,actionType);
    }

    private Map<String,Integer> getCacheCount(List<String> keys, String eventCode, String level,Integer actionType) {
        List<Map<String,Integer>> countList = alarmJobService.batchGet(keys);
        return collectCount(countList,eventCode,level,actionType);
    }

    private Map<String,Integer> collectCount(List<Map<String,Integer>> countList, String eventCode, String level,Integer actionType) {
        int count = 0,total = 0;
        for (Map<String,Integer> itemMap : countList){
            if(MapUtils.isNotEmpty(itemMap)){
                for(String subKey : itemMap.keySet()){
                    if(subKey.startsWith(eventCode+":")){
                        int curCount = MapUtils.getInteger(itemMap,subKey,0);
                        if(Constants.ALARM_RISK_RESULT_ALL.equals(level)) {
                            if(actionType == AlarmActionType.ALARM.getCode()){
                                count += curCount;
                            }else if(actionType == AlarmActionType.FUSE.getCode()){
                                if(subKey.endsWith(":"+ RiskLevel.REVIEW.name())
                                        || subKey.endsWith(":"+ RiskLevel.REJECT.name())){
                                    count += curCount;
                                }
                            }
                        }else if(subKey.endsWith(":"+level)){
                            count += curCount;
                        }
                        total += curCount;
                    }
                }
            }
        }
        Map<String,Integer> countMap = Maps.newHashMap();
        countMap.put(ConsoleConstants.GIVEN_RESULT_COUNT,count);
        countMap.put(ConsoleConstants.ALL_RESULT_COUNT,total);
        return countMap;
    }

    private boolean canFuse(Event event,Date jobTime){
        try{
            LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
            HitResult hitResult = new HitResult();
            hitResult.setEventCode(event.getCode());
            hitResult.setLevel(RiskLevel.PASS.name());
            vo.setQuery(hitResult);
            vo.setStartTime(DateUtils.addHours(jobTime,-1));
            vo.setEndTime(jobTime);
            Map<String,Object> resultMap = elasticSearchService.searchFirstHitLog(vo);
            if(null == resultMap){
                return true;
            }
            Map<String,Object> dataMap = (Map<String, Object>) resultMap.get("data");
            if(null == dataMap){
                return true;
            }
            dataMap.remove("canRetFlag");
            dataMap.remove("riskRequestTime");
            dataMap.remove("TraceId");
            dataMap.remove("profiles");
            dataMap.remove("invoker");
            Long timeOut = MapUtils.getLong(dataMap,"Timeout",1000L);
            List<String> attrNames = attributeService.listAllNames();
            for(String attrName : attrNames){
                dataMap.remove(attrName);
            }
            RiskAction riskAction = RiskAction.create(event.getCode());
            dataMap.forEach((k,v) -> {
                riskAction.getData().put(k,v);
            });
            riskAction.setTimeout(timeOut+taxTime).put(ConsoleConstants.TEST_FLAG,"true")
                    .setAsync(false);
            RiskResult result = riskService.detect(riskAction);
            log.info("熔断风控检测，参数："+JSON.toJSONString(riskAction.getData())+",结果："+JSON.toJSONString(result));
            if(result != null && result.isSuccess() &&
                    result.getLevel().getLevel() != RiskLevel.PASS.getLevel()){
                return true;
            }
        }catch (Exception e){
            log.error("是否可熔断风控检测出错",e);
        }
        return false;
    }

}
