package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPunishService {

    private final static Logger LOGGER = LoggerFactory.getLogger(AbstractPunishService.class);

    protected PunishApolloConfig punishApolloConfig;

    /**
     * 获取匹配服务的名字
     *
     * @return
     */
    public abstract String getName();

    /**
     * 获取惩罚的apollo配置
     * @return
     */
    public PunishApolloConfig getPunishApolloConfig() {
        return punishApolloConfig;
    }

    public void setPunishApolloConfig(PunishApolloConfig punishApolloConfig) {
        this.punishApolloConfig = punishApolloConfig;
    }

    /**
     * 获取入参
     *
     * @param
     * @return
     */
    public List<Map<String,Object>> getPageParameter(){
        List<Map<String,Object>> parameterList = Lists.newArrayList();

        if((this.punishApolloConfig!=null)&&(this.punishApolloConfig.getInputFields()!=null)){
            List<PageInputFields> fieldConfigs = punishApolloConfig.getInputFields().getPageInputFields();
            if(CollectionUtils.isNotEmpty(fieldConfigs)){
                for(PageInputFields fieldConfig:fieldConfigs){
                    Map<String,Object> fieldMap = new HashMap<>(7);
                    fieldMap.put("property",fieldConfig.getProperty());
                    fieldMap.put("desc",fieldConfig.getDesc());
                    fieldMap.put("tip",fieldConfig.getTip());
                    fieldMap.put("required",fieldConfig.getRequired());
                    fieldMap.put("type",fieldConfig.getType());
                    Object  initData = getFieldInitData(fieldConfig);
                    if(initData!=null){
                        fieldMap.put("initData",initData);
                    }

                    fieldMap.put("defaultValue",fieldConfig.getDefaultValue()!=null?fieldConfig.getDefaultValue():"");

                    parameterList.add(fieldMap);
                }
            }
        }

        return parameterList;
    }

    /**
     * 校验入参
     *
     * @param context
     * @return
     */
    protected abstract List<Long> validateParameter(Map<String,Object> context);

    protected void convertPageInputFields(Map<String,Object> param){
        if(this.punishApolloConfig==null||this.punishApolloConfig.getInputFields()==null||CollectionUtils.isEmpty(this.punishApolloConfig.getInputFields().getPageInputFields())){
            return;
        }

        List<PageInputFields> pageInputFields = this.punishApolloConfig.getInputFields().getPageInputFields();
        if(MapUtils.isNotEmpty(param)){
            for(Map.Entry<String,Object> entry:param.entrySet()){
                for(PageInputFields pageInputField:pageInputFields){
                    if(Objects.equals(entry.getKey(),pageInputField.getProperty())&&Objects.equals(pageInputField.getType(),"select")&&Objects.equals(pageInputField.getInitData().getDecideField(),"msg")){
                        for(Value value:pageInputField.getInitData().getValue()){
                            if(Objects.equals(entry.getValue(),value.getCode())){
                                if(Objects.equals(value.getMsg(),"输入其他")){
                                    Map<String,String> additional = (Map<String,String>)param.get("additional");
                                    if(MapUtils.isNotEmpty(additional)){
                                        String result = additional.get(entry.getKey());
                                        if(StringUtils.isNotBlank(result)){
                                            param.put(entry.getKey(),result);
                                        }else{
                                            throw new RiskException(pageInputField.getDesc()+"没有输入其他！");
                                        }
                                    }else{
                                        throw new RiskException(pageInputField.getDesc()+"没有输入其他！");
                                    }
                                }else{
                                    param.put(entry.getKey(),value.getMsg());
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    protected void mergeDefaultInputFields(User user,Map<String,Object> param){

        if((this.punishApolloConfig.getInputFields()!=null)&&CollectionUtils.isNotEmpty(this.punishApolloConfig.getInputFields().getDefaultInputFields())){
            List<DefaultInputFields> defaultInputFields = this.punishApolloConfig.getInputFields().getDefaultInputFields();
            for(DefaultInputFields defaultInputField:defaultInputFields){
                String key = defaultInputField.getProperty();
                param.put(key,getDefaultValue(user,defaultInputField));
            }
        }

    }

    private Object getDefaultValue(User user,DefaultInputFields defaultInputField){
        if(defaultInputField.getType().equalsIgnoreCase("text")){
            if(defaultInputField.getDefaultValue().equalsIgnoreCase("currentUser")){
                return user.getName();
            }

            return defaultInputField.getDefaultValue();
        }

        return "";
    }

    protected String printUidList(List<Long> uids){
        StringBuilder result = new StringBuilder("");

        if(CollectionUtils.isNotEmpty(uids)){
            for(Long uid:uids){
                result.append(uid.toString()).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 执行匹配
     *
     * @param param
     * @return
     */
    public abstract PunishResponse punish(User user, Map<String,Object> param);

    /**
     * 获取字段初始值
     * @param fieldConfig
     * @return
     */
    protected Object getFieldInitData(PageInputFields fieldConfig){

        if(fieldConfig.getType().equalsIgnoreCase("input")){
            return fieldConfig.getDefaultValue();
        }else if(fieldConfig.getType().equalsIgnoreCase("select")){
            Map<String,Object> result = new HashMap<>();

            result.put("decideField",fieldConfig.getInitData().getDecideField());

            List<Map<String,Object>> values = Lists.newArrayList();
            if((fieldConfig.getInitData()!=null)&&CollectionUtils.isNotEmpty(fieldConfig.getInitData().getValue())){
                for(Value item:fieldConfig.getInitData().getValue()){
                    Map<String,Object> value = new HashMap<>();

                    value.put("code",item.getCode());
                    value.put("msg",item.getMsg());
                    values.add(value);
                }
            }

            result.put("value",values);
            return result;
        }

        return null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class ValidateResult{

        private Boolean success;
        private String messsage;

    }

}
