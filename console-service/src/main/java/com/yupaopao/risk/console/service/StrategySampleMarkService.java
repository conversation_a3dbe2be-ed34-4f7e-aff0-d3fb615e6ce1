package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.StrategySampleMark;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.vo.StrategySampleMarkVO;

import java.util.List;

public interface StrategySampleMarkService extends BaseService<StrategySampleMark> {

    int countByTaskId(Long taskId, List<Integer> results);

    PageInfo<StrategySampleMarkVO> searchVO(StrategySampleMarkVO stgySampleMarkVO, Integer page, Integer size);

    int batchInsert(List<StrategySampleMark> list);

}
