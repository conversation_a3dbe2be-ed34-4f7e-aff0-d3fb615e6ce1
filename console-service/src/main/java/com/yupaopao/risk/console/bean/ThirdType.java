package com.yupaopao.risk.console.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

public enum ThirdType {
    SHUMEI("shumei","数美"),
    NETEASE("netease","易顿");

    private String code; // 编号
    private String desc; // 描述

    private static final Map<String, ThirdType> map = new TreeMap<>();

    static {
        for (ThirdType enum1 : ThirdType.values()) {
            map.put(enum1.getCode(), enum1);
        }
    }

    ThirdType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据编号获取描述
     * @param code
     * @return
     */
    public static String getDesc(String code) {
        if (StringUtils.isNotBlank(code)){
            for (ThirdType enum1 : ThirdType.values()) {
                if (code.equals(enum1.getCode())) {
                    return enum1.desc;
                }
            }
        }
        return null;
    }

    /**
     * 存在
     * @param code
     * @return
     */
    public static boolean exists(String code){
        return StringUtils.isNotBlank(code)&&map.containsKey(code);
    }
}
