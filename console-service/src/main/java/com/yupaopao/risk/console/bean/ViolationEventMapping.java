package com.yupaopao.risk.console.bean;


import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViolationEventMapping implements Serializable {

    private String eventCode;
    private List<ViolationEventType> eventTypes;

    public static ViolationEventMapping parse(String json){
        return StringUtils.isNotBlank(json) ? JSONObject.parseObject(json,ViolationEventMapping.class) : null;
    }

}
