package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.StateEnum;
import com.yupaopao.risk.common.mapper.BizTypeMapper;
import com.yupaopao.risk.common.model.BizChannel;
import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.model.ThirdChannel;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.bean.BizTypeVO;
import com.yupaopao.risk.console.bean.CheckType;
import com.yupaopao.risk.console.bean.ThirdChannelVO;
import com.yupaopao.risk.console.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * author: lijianjun
 * date: 2021/3/25 14:42
 */
@Slf4j
@Service
public class BizTypeServiceImpl extends BaseServiceImpl<BizType, BizTypeMapper> implements BizTypeService {

    @Autowired
    private BizChannelService bizChannelService;
    @Autowired
    private ThirdChannelService thirdChannelService;
    @Autowired
    private EventService eventService;
    @Autowired
    private AtomRuleService atomRuleService;
    @Autowired
    private PatrolRuleService patrolRuleService;

    @Override
    public PageInfo<BizTypeVO> search(BizTypeVO bizTypeVO, int page, int size) {
        PageHelper.startPage(page, size);
        Example example = new Example(BizType.class, false);
        example.setOrderByClause("id");
        Example.Criteria criteria = example.createCriteria();
        if(null != bizTypeVO.getId()){
            criteria.andEqualTo("id",bizTypeVO.getId());
        }
        if(StringUtils.isNotBlank(bizTypeVO.getCheckType())){
            criteria.andEqualTo("checkType",bizTypeVO.getCheckType());
        }
        if(StringUtils.isNotBlank(bizTypeVO.getName())){
            criteria.andLike("name","%" + bizTypeVO.getName() + "%");
        }
        if(StringUtils.isNotBlank(bizTypeVO.getCode())){
            criteria.andLike("code","%" + bizTypeVO.getCode() + "%");
        }
        if(null != bizTypeVO.getEvent()){
            List<String> bizTypeCodeList = eventService.getAllBizTypeCode(bizTypeVO.getEvent().getCode());
            if(CollectionUtils.isNotEmpty(bizTypeCodeList)){
                criteria.andIn("code",bizTypeCodeList);
            }else{
                return new PageInfo(Collections.EMPTY_LIST);
            }
        }
        criteria.andEqualTo("state",StateEnum.ENABLE.getType());
        List<BizType> list = getMapper().selectByExample(example);
        PageInfo pageInfo = new PageInfo(list);
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> bizTypeIdList = list.stream().map(b -> b.getId()).collect(Collectors.toList());
            List<BizChannel> bizChannelList = bizChannelService.listByBizTypeIds(bizTypeIdList);
            Set<Long> thirdChannelIdList = bizChannelList.stream().map(b -> b.getThirdChannelId()).collect(Collectors.toSet());
            List<ThirdChannel> thirdChannelList = thirdChannelService.listByIds(thirdChannelIdList);
            Map<Long,ThirdChannel> thirdChannelMap = thirdChannelList.stream().collect(Collectors.toMap(ThirdChannel::getId,t -> t,(k1,k2) -> k1));
            Map<Long,List<BizChannel>> bizChannelMap = bizChannelList.stream().collect(Collectors.groupingBy(BizChannel::getBizTypeId));
            List<BizTypeVO> bizTypeVOList = Lists.newArrayList();
            for(BizType bizType : list){
                BizTypeVO vo = new BizTypeVO(bizType);
                List<ThirdChannelVO> thirdChannelVOList = Lists.newArrayList();
                vo.setThirdChannelVOList(thirdChannelVOList);
                for(BizChannel bizChannel : bizChannelMap.get(bizType.getId())){
                    ThirdChannel thirdChannel = thirdChannelMap.get(bizChannel.getThirdChannelId());
                    ThirdChannelVO thirdChannelVO = new ThirdChannelVO(thirdChannel);
                    thirdChannelVO.setRelationState(bizChannel.getState());
                    thirdChannelVO.setPriority(bizChannel.getPriority());
                    thirdChannelVO.setCheckTypeDto(thirdChannelService.getCheckTypeByCode(thirdChannel.getCheckType()));
                    thirdChannelVO.setBizTypeId(bizChannel.getBizTypeId());
                    thirdChannelVOList.add(thirdChannelVO);
                }
                CheckType checkType = thirdChannelService.getCheckTypeByCode(bizType.getCheckType());
                vo.setCheckTypeName(null == checkType ? "" : checkType.getName());
                bizTypeVOList.add(vo);
            }
            pageInfo.setList(bizTypeVOList);
        }
        return pageInfo;
    }

    @Transactional
    @Override
    public boolean insert(BizTypeVO bizTypeVO) {
        if(super.insertSelective(bizTypeVO)){
            addRelation(bizTypeVO,bizTypeVO.getThirdChannelVOList().stream().map(v -> v.getId()).collect(Collectors.toList()));
            return true;
        };
        return false;
    }

    @Override
    public BizType getByName(String name) {
        BizType bizType = new BizType();
        bizType.setName(name);
        bizType.setState(StateEnum.ENABLE.getType());
        return super.get(bizType);
    }

    @Override
    public BizType getByCode(String code) {
        BizType bizType = new BizType();
        bizType.setCode(code);
        bizType.setState(StateEnum.ENABLE.getType());
        return super.get(bizType);
    }

    @Transactional
    @Override
    public boolean update(BizTypeVO bizTypeVO) {
        if(super.updateSelectiveById(bizTypeVO)){
            updateRelation(bizTypeVO,bizTypeVO.getThirdChannelVOList().stream().map(v -> v.getId()).collect(Collectors.toList()));
            return true;
        };
        return false;
    }

    @Transactional
    @Override
    public boolean switchThirdChannel(BizChannel record,String modifier) {
        BizType upBizType = new BizType();
        upBizType.setId(record.getBizTypeId());
        upBizType.setModifier(modifier);
        this.getMapper().updateByPrimaryKeySelective(upBizType);
        bizChannelService.updateState(record,modifier);
        return true;
    }

    @Transactional
    @Override
    public boolean delete(Long id, String modifier) {
        BizType bizType = this.get(id);
        if(atomRuleService.countByBizTypeCode(bizType.getCode())>0){
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "该业务类型正在被使用中，不能删除");
        }
        BizType upBizType = new BizType();
        upBizType.setId(id);
        upBizType.setModifier(modifier);
        upBizType.setState(StateEnum.DELETE.getType());
        this.getMapper().updateByPrimaryKeySelective(upBizType);
        bizChannelService.deleteByBizTypeId(id,modifier);
        return true;
    }

    @Transactional
    @Override
    public boolean resetPriority(BizTypeVO bizTypeVO, String modifier) {
        List<BizChannel> bizChannelList = bizChannelService.listByBizTypeIds(Lists.newArrayList(bizTypeVO.getId()));
        if(CollectionUtils.isEmpty(bizChannelList) || bizChannelList.size() != bizTypeVO.getThirdChannelVOList().size()){
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "该业务类型绑定的三方通道有变化，请刷新页面");
        }
        Map<Long,BizChannel> bizChannelMap = bizChannelList.stream().collect(Collectors.toMap(BizChannel::getThirdChannelId,bz -> bz));
        int i=0;
        for(ThirdChannelVO thirdChannelVO : bizTypeVO.getThirdChannelVOList()){
            BizChannel bizChannel = bizChannelMap.get(thirdChannelVO.getId());
            if(null == bizChannel){
                throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "该业务类型绑定的三方通道有变化，请刷新页面");
            }
            BizChannel upBizChannel = new BizChannel();
            upBizChannel.setId(bizChannel.getId());
            upBizChannel.setPriority(i++);
            upBizChannel.setModifier(modifier);
            bizChannelService.updateSelectiveById(upBizChannel);
        }
        BizType upBizType = new BizType();
        upBizType.setId(bizTypeVO.getId());
        upBizType.setModifier(modifier);
        this.getMapper().updateByPrimaryKeySelective(upBizType);
        return true;
    }

    @Override
    public List<BizType> listByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.EMPTY_LIST;
        }
        Example example = new Example(BizType.class, false);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id",ids);
        return this.getMapper().selectByExample(example);
    }

    @Override
    public BizTypeVO getRelateRules(BizTypeVO bizTypeVO) {
        bizTypeVO.setAtomRuleList(atomRuleService.listByBizTypeCode(bizTypeVO.getCode()));
        bizTypeVO.setPatrolRuleList(patrolRuleService.listByBizTypeCode(bizTypeVO.getCode()));
        return bizTypeVO;
    }

    private void addRelation(BizTypeVO bizTypeVO, List<Long> thirdChannelIds) {
        if (bizTypeVO == null || bizTypeVO.getId() == null) {
            return;
        }
        BizChannel bizChannel = new BizChannel();
        bizChannel.setBizTypeId(bizTypeVO.getId());
        bizChannel.setState(StateEnum.DISABLE.getType());
        bizChannel.setModifier(bizTypeVO.getModifier());
        bizChannel.setAuthor(bizTypeVO.getAuthor());
        int i = 0;
        for(Long thirdChannelId : thirdChannelIds){
            bizChannel.setId(null);
            bizChannel.setThirdChannelId(thirdChannelId);
            bizChannel.setPriority(i++);
            bizChannelService.insertSelective(bizChannel);
        }
    }

    private void updateRelation(BizTypeVO bizTypeVO, List<Long> thirdChannelIds) {
        if (bizTypeVO == null || bizTypeVO.getId() == null) {
            return;
        }
        List<BizChannel> existList = bizChannelService.listByBizTypeIds(Lists.newArrayList(bizTypeVO.getId()));
        List<Long> existThirdChannelIds = existList.stream().map(b -> b.getThirdChannelId()).collect(Collectors.toList());
        Map<Long,BizChannel> bizChannelMap = existList.stream().collect(Collectors.toMap(BizChannel::getThirdChannelId,bz -> bz));
        for(BizChannel bizChannel : existList){
            if(!thirdChannelIds.contains(bizChannel.getThirdChannelId())){
                BizChannel upBizChannel = new BizChannel();
                upBizChannel.setId(bizChannel.getId());
                upBizChannel.setModifier(bizTypeVO.getModifier());
                upBizChannel.setState(StateEnum.DELETE.getType());
                bizChannelService.updateSelectiveById(upBizChannel);
            }
        }
        int i = 0;
        for(Long thirdChannelId : thirdChannelIds){
            if(!existThirdChannelIds.contains(thirdChannelId)){
                BizChannel bizChannel = new BizChannel();
                bizChannel.setBizTypeId(bizTypeVO.getId());
                bizChannel.setState(StateEnum.DISABLE.getType());
                bizChannel.setModifier(bizTypeVO.getModifier());
                bizChannel.setAuthor(bizTypeVO.getAuthor());
                bizChannel.setId(null);
                bizChannel.setThirdChannelId(thirdChannelId);
                bizChannel.setPriority(i++);
                bizChannelService.insertSelective(bizChannel);
            }else{
                BizChannel bizChannel = bizChannelMap.get(thirdChannelId);
                BizChannel upBizChannel = new BizChannel();
                upBizChannel.setId(bizChannel.getId());
                upBizChannel.setPriority(i++);
                upBizChannel.setModifier(bizTypeVO.getModifier());
                bizChannelService.updateSelectiveById(upBizChannel);
            }
        }
    }
}


