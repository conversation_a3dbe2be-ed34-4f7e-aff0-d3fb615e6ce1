package com.yupaopao.risk.console.service.impl;

import com.yupaopao.risk.common.mapper.AdvertisementMapper;
import com.yupaopao.risk.common.model.Advertisement;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.console.service.AdService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional
public class AdServiceImpl extends BaseServiceImpl<Advertisement, AdvertisementMapper> implements AdService {

    @Override
    public boolean insertSelective(Advertisement record) {
        if (StringUtils.isNotBlank(record.getContent())) {
            String[] dimValues = record.getContent().trim().split("\n");
            String name = record.getName();
            if (dimValues.length > 1) {
                for (String str : dimValues) {
                    if (StringUtils.isNotBlank(str)) {
                        Advertisement ad = new Advertisement();
                        String se = String.valueOf(System.currentTimeMillis());
                        String b = se.substring(se.length() - 4);
                        String te = name + b;
                        ad.setName(te);
                        ad.setContent(filterAdContent(str));
                        ad.setCreateTime(new Date());
                        ad.setLastUpdateTime(new Date());
                        super.insertSelective(ad);
                    }
                }
                return true;
            } else if (dimValues.length == 1) {
                filterAdContent(record.getContent());
                return super.insertSelective(record);
            }
        }
        return false;
    }

    private static String filterAdContent(String origin) {
        return origin.replaceAll("[^\\u4E00-\\u9FA5a-zA-Z0-9]", "");
    }

}
