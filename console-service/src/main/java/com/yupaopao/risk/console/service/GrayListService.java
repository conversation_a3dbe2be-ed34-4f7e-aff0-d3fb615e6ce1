package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.enums.Dimensions;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.RiskIsGrayRequest;

import java.util.List;

/**
 * (黑白)名单服务
 *
 * <AUTHOR>
 * @date 2018/8/24 上午11:24
 */
public interface GrayListService extends BaseService<GrayList> {

    /**
     * 删除过期数据
     *
     * @return
     */
    int deleteExpired();

    /**
     * 删除过期一周数据
     * @return
     */
    int deleteExpiredByJob();

    /**
     * 保存数据,支持批量模式
     *
     * @param record
     * @return
     */
    int save(GrayList record);

    /**
     * 保存或更新
     *
     * @param record
     * @return
     */
    boolean saveOrUpdate(GrayList record);

    /**
     * 批量删除
     *
     * @param record
     * @return
     */
    int batchDelete(GrayList record);

    /**
     * 批量删除指定的名单列表
     *
     * @param list
     * @return
     */
    int batchDelete(List<GrayList> list);

    /**
     * 获取风控名单列表
     * RiskGrayService dubbo 服务使用
     *
     * @param record
     * @param valid  是否有效 true:有效 其它:不处理
     * @return
     */
    List<GrayList> searchRiskGray(GrayList record, Boolean valid);

    /**
     * 指定名单分组下是否为空
     *
     * @param groupId
     * @return
     */
    boolean isEmpty(Long groupId);


    PageInfo<GrayList> search(List<GrayList> records, int page, int size);

    /**
     * 根据值，类型查询名单列表
     *
     * @param value
     * @param dimensions
     * @return
     */
    List<GrayList> searchByValueAndDimensions(String value, Dimensions dimensions);

    /**
     * 根据值列表，类型列表查询名单列表
     *
     * @param value
     * @param dimensions
     * @return
     */
    List<GrayList> searchByValueAndDimensions(List<String> values, List<Dimensions> dimensions);

    List<GrayList> searchByOffsetLimit(int offset,int limit);

    // 获取命中黑白名单情况
    Boolean isGray(RiskIsGrayRequest request);

}
