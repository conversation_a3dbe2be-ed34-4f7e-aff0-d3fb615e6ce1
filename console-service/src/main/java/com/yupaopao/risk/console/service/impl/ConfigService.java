package com.yupaopao.risk.console.service.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Component
public class ConfigService {

    @ApolloConfig
    private Config config;

    @Autowired
    RedisService redisService;

    public Integer getDefaultExpiredTime() {
        return config.getIntProperty("list.expireTime", 864000); // 黑名单的默认失效时间
    }

    public List<String> getLocalFunctionList() {
        return Arrays.asList(config.getArrayProperty("function.local", ",", new String[0]));
    }

    public List<String> getRemoteFunctionList() {
        return Arrays.asList(config.getArrayProperty("function.remote", ",", new String[0]));
    }

    public Long getLoginListGroupId() {
        return config.getLongProperty("list.login", null);
    }

    public Long getGlobalListGroupId() {
        return config.getLongProperty("list.global", null);
    }
}
