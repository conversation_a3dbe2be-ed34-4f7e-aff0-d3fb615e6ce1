package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yupaopao.risk.common.enums.ToSystemType;
import com.yupaopao.risk.common.enums.WordLocationType;
import com.yupaopao.risk.common.mapper.AuditTextStatisticsMapper;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AuditTextStatisticsVO;
import com.yupaopao.risk.console.bean.ExportedAuditTextStatistic;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuditTextStatisticsServiceImpl extends BaseServiceImpl<AuditTextStatistics, AuditTextStatisticsMapper> implements AuditTextStatisticsService {

    @Autowired
    private WordService wordService;

    @Autowired
    private WordTagService wordTagService;

    @Autowired
    private WordSceneService wordSceneService;

    @Autowired
    private TagService tagService;

    @Autowired
    private SceneService sceneService;

    /**
     * 获取该批词条的自有属性
     *
     * @param
     * @return
     */
    public List<AuditTextStatisticsVO> searchWordProp(List<AuditTextStatisticsVO> voList, List<Word> words) {
        List<AuditTextStatisticsVO> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(words)) {
            return result;
        }

        List<Long> wordIds = words.stream().map(Word::getId).collect(Collectors.toList());
        List<WordTag> wordTags = this.wordTagService.batchSearch(wordIds);
        List<WordScene> wordScenes = this.wordSceneService.batchSearch(wordIds);

        Scene searchScene = new Scene();
        searchScene.setToSystem(ToSystemType.AUDIT.getCode());
        List<Scene> auditScenes = this.sceneService.select(searchScene);
        List<Tag> tags = this.tagService.selectAll();

        Map<Long, Scene> sceneMap = new HashMap<>(auditScenes.size());
        auditScenes.forEach(scene -> sceneMap.put(scene.getId(), scene));

        Map<Long, List<Scene>> wordSceneMap = new HashMap<>(words.size());
        wordScenes.forEach(wordScene -> {
            if (sceneMap.containsKey(wordScene.getSceneId())) {
                if (!wordSceneMap.containsKey(wordScene.getWordId())) {
                    wordSceneMap.put(wordScene.getWordId(), Lists.newArrayList());
                }
                wordSceneMap.get(wordScene.getWordId()).add(sceneMap.get(wordScene.getSceneId()));
            }
        });

        Map<Long, Tag> tagMap = new HashMap<>(tags.size());
        tags.forEach(tag -> tagMap.put(tag.getId(), tag));
        Map<Long, List<Tag>> wordTagMap = new HashMap<>(words.size());
        Map<Long, List<Tag>> wordFirstTagMap = new HashMap<>(words.size());
        wordTags.forEach(wordTag -> {
            if (tagMap.containsKey(wordTag.getTagId())) {
                if (!wordTagMap.containsKey(wordTag.getWordId())) {
                    wordTagMap.put(wordTag.getWordId(), Lists.newArrayList());
                }
                wordTagMap.get(wordTag.getWordId()).add(tagMap.get(wordTag.getTagId()));

                if (!wordFirstTagMap.containsKey(wordTag.getWordId())) {
                    wordFirstTagMap.put(wordTag.getWordId(), Lists.newArrayList());
                }
                Long pId = tagMap.get(wordTag.getTagId()).getParentId();
                wordFirstTagMap.get(wordTag.getWordId()).add(tagMap.get(pId));
            }
        });

        try {
            Map<String, Word> wordMap = words.stream().collect(Collectors.toMap(Word::getContent, word -> word));

            for (AuditTextStatisticsVO auditTextStatisticsVO : voList) {
                AuditTextStatisticsVO vo = new AuditTextStatisticsVO();

                BeanUtils.copyProperties(vo, auditTextStatisticsVO);

                Word word = wordMap.get(vo.getContent());
                if(word!=null&&word.getId()!=null){
                    vo.setFirstTags(wordFirstTagMap.get(word.getId()));
                    vo.setTags(wordTagMap.get(word.getId()));
                    vo.setScenes(wordSceneMap.get(word.getId()));
                    vo.setWordLocationType(WordLocationType.codeOf(word.getWordLocationType()).getMsg());
                    result.add(vo);
                }

            }
        } catch (Exception exp) {
            log.error("给AuditTextStatisticsVO赋值出错", exp);
        }

        return result;
    }

    private List<AuditTextStatisticsVO> filterByRate(List<AuditTextStatisticsVO> list,AuditTextStatisticsVO record){
        List<AuditTextStatisticsVO> result = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(list)){
            for(AuditTextStatisticsVO item:list){
                if(item.getEffectiveRate()==null||item.getRejectRate()==null){
                    continue;
                }

                if((record.getMinAvailableRate()!=null)&&(item.getEffectiveRate()<record.getMinAvailableRate())){
                    continue;
                }

                if(record.getMaxAvailableRate()!=null&&item.getEffectiveRate()>record.getMaxAvailableRate()){
                    continue;
                }

                if((record.getMinRejectRate()!=null)&&(item.getRejectRate()<record.getMinRejectRate())){
                    continue;
                }

                if(record.getMaxRejectRate()!=null&&item.getRejectRate()>record.getMaxRejectRate()){
                    continue;
                }

                item.setEffectiveRateStr(String.format("%.2f",item.getEffectiveRate()*100));
                item.setRejectRateStr(String.format("%.2f",item.getRejectRate()*100));
                result.add(item);
            }
        }

        return result;
    }

    @Override
    public PageInfo<AuditTextStatisticsVO> complexSearch(AuditTextStatisticsVO record, Integer page, Integer size) {
        PageInfo<AuditTextStatisticsVO> pageInfo = new PageInfo<>();

        try {
            AuditTextStatistics auditTextStatistics = new AuditTextStatistics();
            BeanUtils.copyProperties(auditTextStatistics, record);

            if (CollectionUtils.isEmpty(record.getFirstTags()) && CollectionUtils.isEmpty(record.getTags())
                    && (record.getAuditChannel() == null) && StringUtils.isBlank(record.getContent())) {
                List<AuditTextStatisticsVO> list = this.searchSummaryResult(auditTextStatistics, page, size);
                List<AuditTextStatisticsVO> availableList = filterByRate(list,record);
                pageInfo.setList(availableList);
                List<AuditTextStatisticsVO> voList = pageInfo.getList();

                if (CollectionUtils.isNotEmpty(voList)) {
                    List<String> contents =
                            voList.stream().map(AuditTextStatisticsVO::getContent).collect(Collectors.toList());
                    List<Word> words = this.wordService.batchSearch(contents, ToSystemType.AUDIT.getCode(),StringUtils.isNotBlank(record.getWordLocationType())?Integer.parseInt(record.getWordLocationType()):null);

                    //获取该批词条的自有属性
                    pageInfo.setList(searchWordProp(voList, words));
                }

                pageInfo.setPageNum(page);
                pageInfo.setSize(size);
                pageInfo.setSize(0);
                pageInfo.setTotal(0);
                pageInfo.setPages(0);

            } else {
                List<Scene> auditScenes = this.sceneService.findByToSystem(ToSystemType.AUDIT.getCode());
                List<Word> wordList = wordService.search(record.getFirstTags(), record.getTags(), auditScenes,
                        record.getContent());

                if (CollectionUtils.isEmpty(wordList)) {
                    pageInfo.setPageNum(page);
                    pageInfo.setPageSize(size);
                    pageInfo.setSize(0);
                    pageInfo.setTotal(0);
                    pageInfo.setPages(0);

                } else {
                    if(auditTextStatistics.getCreateTime()!=null){
                        String minMsgDate = DateUtils.formatDate(auditTextStatistics.getCreateTime(),DateUtils.YYYY_MM_DD);
                        auditTextStatistics.setMinMsgDate(minMsgDate);
                    }

                    if(auditTextStatistics.getUpdateTime()!=null){
                        String maxMsgDate = DateUtils.formatDate(DateUtils.addSeconds(auditTextStatistics.getUpdateTime(),1),DateUtils.YYYY_MM_DD);
                        auditTextStatistics.setMaxMsgDate(maxMsgDate);
                    }
                    List<AuditTextStatisticsVO> list = this.getMapper().searchSummaryResult(auditTextStatistics);
                    List<AuditTextStatisticsVO> voList = filterByRate(list,record);

                    List<String> contents = wordList.stream().map(Word::getContent).collect(Collectors.toList());

                    List<Word> words = this.wordService.batchSearch(contents, ToSystemType.AUDIT.getCode(),StringUtils.isNotBlank(record.getWordLocationType())?Integer.parseInt(record.getWordLocationType()):null);

                    List<AuditTextStatisticsVO> statisticsVOS = Lists.newArrayList();

                    if (CollectionUtils.isNotEmpty(voList)) {
                        statisticsVOS = searchWordProp(voList, words);
                    }

                    pageInfo.setPageNum(page);
                    pageInfo.setPageSize(size);

                    if (CollectionUtils.isEmpty(statisticsVOS)) {
                        pageInfo.setSize(0);
                        pageInfo.setTotal(0);
                        pageInfo.setPages(0);
                    } else {
                        int pages = statisticsVOS.size() % size == 0 ? (statisticsVOS.size() / size) :
                                (statisticsVOS.size() / size + 1);
                        int currentPageSize = (statisticsVOS.size() - page * size >= 0) ? size :
                                (statisticsVOS.size() - ((page - 1) * size));

                        pageInfo.setSize(currentPageSize);
                        pageInfo.setTotal(statisticsVOS.size());
                        pageInfo.setPages(pages);
                        pageInfo.setList(statisticsVOS);

                    }

                }

            }
            return pageInfo;

        } catch (Exception exp) {
            log.error("查询审核文本命中统计出现异常", exp);
        }

        return pageInfo;
    }

    public List<AuditTextStatisticsVO> searchSummaryResult(AuditTextStatistics auditTextStatistics, Integer page,
                                                               Integer size) {

        try {
            if(auditTextStatistics.getCreateTime()!=null){
                String minMsgDate = DateUtils.formatDate(auditTextStatistics.getCreateTime(),DateUtils.YYYY_MM_DD);
                auditTextStatistics.setMinMsgDate(minMsgDate);
            }

            if(auditTextStatistics.getUpdateTime()!=null){
                String maxMsgDate = DateUtils.formatDate(DateUtils.addSeconds(auditTextStatistics.getUpdateTime(),1),DateUtils.YYYY_MM_DD);
                auditTextStatistics.setMaxMsgDate(maxMsgDate);
            }

            return this.getMapper().searchSummaryResult(auditTextStatistics);
        } catch (Exception exp) {
            log.error("查询审核文本命中汇总结果出现异常", exp);
            return Lists.newArrayList();
        }

    }

    @Override
    public List<ExportedAuditTextStatistic> export(User user, AuditTextStatisticsVO record) {
        //本导出最多只能导出10000条
        Integer page = 1;
        Integer size = 10000;

        PageInfo<AuditTextStatisticsVO> wordPageInfo = this.complexSearch(record, page, size);
        List<AuditTextStatisticsVO> wordList = wordPageInfo.getList();

        List<ExportedAuditTextStatistic> exportedWordList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(wordList)) {
            for (AuditTextStatisticsVO word : wordList) {

                ExportedAuditTextStatistic exportedWord = new ExportedAuditTextStatistic();

                exportedWord.setContent(word.getContent());
                exportedWord.setFirstTagNames(printTagNames(word.getFirstTags()));
                exportedWord.setTagNames(printTagNames(word.getTags()));
                exportedWord.setSceneNames(printSceneNames(word.getScenes()));
                exportedWord.setChannelName(word.getChannelName());
                exportedWord.setWordLocationTypeName(WordLocationType.codeOf(word.getWordLocationType()).getMsg());
                exportedWord.setHitCount(word.getHitCount());
                exportedWord.setRejectCount(word.getRejectCount());
                exportedWord.setPassCount(word.getPassCount());
                exportedWord.setEffectiveRate(word.getEffectiveRate());
                exportedWord.setRejectRate(word.getRejectRate());

                exportedWordList.add(exportedWord);

            }
        }

        return exportedWordList;
    }

    private String printTagNames(List<Tag> tags) {
        StringBuilder result = new StringBuilder();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                result.append(tag.getName()).append("/");
            }
            return result.substring(0, result.length() - 1);
        }

        return result.toString();
    }

    private String printSceneNames(List<Scene> scenes) {
        StringBuilder result = new StringBuilder();
        if (CollectionUtils.isNotEmpty(scenes)) {
            for (Scene scene : scenes) {
                result.append(scene.getName()).append("/");
            }
            return result.substring(0, result.length() - 1);
        }

        return result.toString();
    }

}
