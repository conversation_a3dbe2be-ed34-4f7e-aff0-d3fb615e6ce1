package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.model.Word;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.WordVO;
import com.yupaopao.risk.console.bean.ExportedWord;
import com.yupaopao.risk.console.bean.TemplateWord;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface WordService extends BaseService<Word> {

    List<Word> selectByWordIds(List<Long> wordIds);
    
    List<Word> batchSearch(List<String> contents);

    List<Word> batchSearch(List<String> contents,Integer toSystem,Integer wordLocationType);

    PageInfo<Word> commonSearch(User user, WordVO record, Integer page, Integer size);

    List<Word> search(List<Tag> firstTags,List<Tag> secondTags,List<Scene> scenes,String content);

    List<Word> searchWordContent(Word record, String order);

    Map<String,Object> insert(User user,Word record);

    List<WordVO> checkRepeatWords(User user,WordVO record);

    List<Float> getWeights();

    boolean updateSelectiveByIdAndToSystem(Word oldWord,Word record);

    boolean existName(WordVO record);

    boolean update(long id, WordVO record,User user);

    boolean updateByAddUsed(Word record);

    boolean updateByAddUsed(Word record,List<Tag> tags,List<Scene> scenes);

    boolean updateByCondition(Word record);

    boolean insertBeanAddRelation(Word record);

    boolean insertBeanAddRelation(Word record, List<Tag> tags, List<Scene> scenes);

    String batchUpdate(User user,WordVO record);

    boolean deleteByIdAndToSystem(Long id,Integer toSystem);

    boolean delete(long id, User user);

    List<ExportedWord> export(User user, WordVO record);

    List<TemplateWord> downloadTemplate(User user);

    Set<String> searchNotIn(Set<String> words,Integer toSystem);

    int batchInsertInto(List<Word> words);

    int batchUpdate(List<Word> words,List<Word> failWords);

}
