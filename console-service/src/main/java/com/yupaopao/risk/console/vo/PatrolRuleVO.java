package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.PatrolRule;

import java.util.List;


public class PatrolRuleVO extends PatrolRule {

    private List<Event> relateEvents;

    // 被举报人
    private String targetId;

    // 举报人
    private String fromUid;

    // 举报描述
    private String briefDesc;

    // 业务标识BizId
    private String bizId;

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getBriefDesc() {
        return briefDesc;
    }

    public void setBriefDesc(String briefDesc) {
        this.briefDesc = briefDesc;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public List<Event> getRelateEvents() {
        return relateEvents;
    }

    public void setRelateEvents(List<Event> relateEvents) {
        this.relateEvents = relateEvents;
    }
}
