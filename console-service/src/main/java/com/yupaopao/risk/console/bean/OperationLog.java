package com.yupaopao.risk.console.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志
 * <AUTHOR>
 * @date 2019-4-30
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OperationLog implements Serializable {

    private String userName;
    private String url;
    private String method;
    private String param;
    private Date createTime;
    private String createdAt;
    private String response;
    private String requestUrl;
    //请求ip链路
    private String ipChain;
    private String userAgent;

}
