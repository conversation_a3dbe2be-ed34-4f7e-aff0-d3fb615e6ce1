package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.ViolationOperation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ViolationOperationVO extends ViolationOperation implements Serializable {

    private Date msgStartTime;
    private Date msgEndTime;

}
