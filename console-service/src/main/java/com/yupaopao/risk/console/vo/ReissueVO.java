package com.yupaopao.risk.console.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ReissueVO {

    /**
     * risk_hit_log日志记录
     */
    private String log;
    /**
     * 数据补偿kafka topic
     */
    private String topic;
    /**
     * 业务通知类型: 1-机审；2-人审
     */
    private int reissueType;

    public ReissueVO(String log, String topic) {
        this.log = log;
        this.topic = topic;
    }

}
