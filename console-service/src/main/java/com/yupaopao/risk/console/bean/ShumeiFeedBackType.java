package com.yupaopao.risk.console.bean;

/**
 * 数美纠结反馈服务类型
 */
public enum ShumeiFeedBackType {

    TEXT("TEXT", "POST_TEXT"), IMAGE("IMAGE", "POST_IMG"), ARTICLE("ARTICLE", "POST_ARTICLE"), AUDIO("AUDIO", "POST_AUDIO")
    , LOGIN("LOGIN", "ACCOUNT_LOGIN");

    private String code;
    private String type;

    ShumeiFeedBackType(String code, String type) {
        this.code = code;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public static ShumeiFeedBackType get(String code) {
        for (ShumeiFeedBackType obj : values()) {
            if (obj.getCode().equalsIgnoreCase(code)) {
                return obj;
            }
        }
        return null;
    }
}
