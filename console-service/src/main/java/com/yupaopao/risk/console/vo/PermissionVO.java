package com.yupaopao.risk.console.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.common.model.Permission;
import lombok.*;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/15 00:56 PM
 */
@Data
public class PermissionVO  {
    private long id;
    @JsonProperty("pId")
    private long pId;
    private String name;
    private boolean checked;

    @JsonProperty("pId")
    public long getPId() {
        return pId;
    }

    @JsonProperty("pId")
    public void setPId(long pId) {
        this.pId = pId;
    }
}
