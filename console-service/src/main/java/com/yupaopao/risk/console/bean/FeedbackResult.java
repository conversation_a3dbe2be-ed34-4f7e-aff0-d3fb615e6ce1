package com.yupaopao.risk.console.bean;

import lombok.*;

import java.util.HashMap;
import java.util.Map;

@ToString
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackResult {
    private boolean success;
    private String message;
    private Map<String,Object> data;

    public static FeedbackResult error(String message) {
        FeedbackResult result = new FeedbackResult();
        result.message = message;
        return result;
    }

    public static FeedbackResult success() {
        FeedbackResult result = new FeedbackResult();
        result.success = true;
        return result;
    }

    public static FeedbackResult error(String message, Object returnMap) {
        FeedbackResult result = new FeedbackResult();
        result.message = message;
        Map<String,Object> data = new HashMap<>();
        data.put("returnMap",returnMap);
        result.data = data;
        return result;
    }
}
