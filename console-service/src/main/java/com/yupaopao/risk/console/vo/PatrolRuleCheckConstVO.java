package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.Event;

import java.util.List;

public class PatrolRuleCheckConstVO {

    private List<Event> relateEvents;
    private String riskConst;

    public List<Event> getRelateEvents() {
        return relateEvents;
    }

    public void setRelateEvents(List<Event> relateEvents) {
        this.relateEvents = relateEvents;
    }

    public String getRiskConst() {
        return riskConst;
    }

    public void setRiskConst(String riskConst) {
        this.riskConst = riskConst;
    }
}
