package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.model.GrayList;
import org.apache.commons.beanutils.BeanUtils;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2019/12/10 20:16
 */
public class GrayListVo extends GrayList {

    /**
     * 名单组
     */
    private GrayGroup grayGroup;

    /**
     * 用于客服支持-名单查询页中 该名单行是否显示删除按钮
     */
    private boolean show = false;

    public GrayListVo() {
    }

    public GrayListVo(GrayList grayList) {
        if (grayList != null) {
            try {
                BeanUtils.copyProperties(this, grayList);
            } catch (Exception e) {
                throw new RiskException(ErrorMessage.SYSTEM_ERROR);
            }
        }
    }

    public GrayGroup getGrayGroup() {
        return grayGroup;
    }

    public void setGrayGroup(GrayGroup grayGroup) {
        this.grayGroup = grayGroup;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }
}
