package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.GrayList;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/1/4 19:40
 */
public class SyncRiskGrayData extends GrayList {

    /**
     * 名单组名称
     */
    private String groupName;
    /**
     * 变更类型
     */
    private String changeType;

    public SyncRiskGrayData() {
    }

    public SyncRiskGrayData(GrayList grayList) {
        if(null != grayList){
            BeanUtils.copyProperties(grayList,this);
        }
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }
}
