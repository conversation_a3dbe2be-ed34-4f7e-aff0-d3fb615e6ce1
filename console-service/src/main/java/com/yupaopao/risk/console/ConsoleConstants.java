package com.yupaopao.risk.console;

public class ConsoleConstants {

    public final static String USER_CACHE_KEY = "USER-CACHE-KEY";

    // redis locker
    public static final String PREFIX = "console:";
    public final static String REDIS_LOCKER_PREFIX = PREFIX + "generate:complain:" + "%s_%s_%s";
    public static final String KEY_TRY_DO_IN_REDIS = "retry_do_jobNo_set";

    //alarm
    public static final String ALARM_COUNT_PREFIX = "alarm:count:";
    public static final String WARNING_COUNT_KEY_ALL = "ALL";
    public static final String BEHIND_GIVEN_RESULT_COUNT = "behind";
    public static final String FRONT_GIVEN_RESULT_COUNT = "front";
    public static final String BEHIND_ALL_RESULT_COUNT = "behind:all";
    public static final String FRONT_ALL_RESULT_COUNT = "front:all";
    public static final String GIVEN_RESULT_COUNT = "giveResult";
    public static final String ALL_RESULT_COUNT = "allResult";


    // redis cache
    public final static String REDIS_CACHE_FIRST_TAG_LIST_PREFIX = PREFIX +"first:tag:list";   // 一级标签

    public final static String REDIS_CACHE_SECOND_TAG_LIST_PREFIX = PREFIX + "first:tag:" + "%s" + ":to:second:tag:list";  // 二级标签

    public final static String REDIS_CACHE_SCENE_LIST_PREFIX = PREFIX + "scene:list:toSystem:" + "%s"; // 场景

    public final static String REDIS_CACHE_WORD_PREFIX = PREFIX + "word:toSystem:" + "%S";

    public final static String REDIS_CACHE_SECOND_TAG_PREFIX = PREFIX +"second:tag:" + "%s";

    // redis expire time

    public final static Integer REDIS_EXPIRE_TIME = 24 * 60 * 60;


    // error info
    public final static String RISK_GRAY_LIST_REQUEST_NULL = "名单请求不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_NULL_AUTHOR = "名单请求作者不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_NULL_DIMENSION = "名单请求维度不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_NULL_GROUP_IDENTITY = "名单请求组标识不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_INVALID_GROUP_IDENTITY = "无效的名单组标识";
    public final static String RISK_GRAY_LIST_REQUEST_NO_PERMISSION = "该用户没有操作该类型名单组的权限";

    public final static String RISK_GRAY_LIST_REQUEST_SAVE_EXCEPTION = "保存名单异常";
    public final static String RISK_GRAY_LIST_REQUEST_SAVE_ERROR = "保存名单失败";
    public final static String RISK_GRAY_LIST_REQUEST_DELETE_EXCEPTION = "删除名单异常";

    public final static String RISK_GRAY_LIST_REQUEST_DELETE__ERROR_MESSAGE1 = "业务场景标识的映射关系不存在";
    public final static String RISK_GRAY_LIST_REQUEST_DELETE__ERROR_MESSAGE2 = "未在数据库中找到该名单,认为删除名单成功！";

    public final static String RISK_GRAY_LIST_REQUEST_NULL_EXPIRE_TIME = "失效时间不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_NULL_TYPE = "名单类型不能为空";
    public final static String RISK_GRAY_LIST_REQUEST_INVALID_TYPE = "名单类型取值为BLACK或WHITE";
    public final static String RISK_GRAY_LIST_REQUEST_NULL_VALUE = "维度值不能为空";

    // redis 缓存
    // 统计im场景下热词最近一次时间
   public final static String IM_LAST_TIME_STATISTIC_HOT_WORD = "last.time.statistic.hot.word";
    // 统计live-room场景下热词最近一次时间
   public final static String LIVE_ROOM_LAST_TIME_STATISTIC_HOT_WORD = "live.room.last.time.statistic.hot.word";
    // 统计chat-room场景下热词最近一次时间
    public final static String CHAT_ROOM_LAST_TIME_STATISTIC_HOT_WORD = "chat.room.last.time.statistic.hot.word";

    public static final String PUNISH_CHANNEL = "REALTIME_RISK_PUNISH";

    public static final String TEST_FLAG = "testFlag";

}
