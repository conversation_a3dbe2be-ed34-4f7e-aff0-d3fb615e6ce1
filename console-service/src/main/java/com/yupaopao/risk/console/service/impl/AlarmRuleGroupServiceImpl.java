package com.yupaopao.risk.console.service.impl;

import com.google.common.collect.Lists;
import com.yupaopao.risk.common.mapper.AlarmRuleGroupMapper;
import com.yupaopao.risk.common.model.AlarmRuleGroup;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.console.service.AlarmRuleAtomService;
import com.yupaopao.risk.console.service.AlarmRuleGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * author: lijianjun
 * date: 2020/2/19 19:55
 */
@Service
public class AlarmRuleGroupServiceImpl extends BaseServiceImpl<AlarmRuleGroup, AlarmRuleGroupMapper> implements AlarmRuleGroupService {

    @Autowired
    private AlarmRuleAtomService alarmRuleAtomService;
    @Override
    public List<AlarmRuleGroupVO> listByConfigId(Long configId) {
        Example example = new Example(AlarmRuleGroup.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("configId", configId);
        example.setOrderByClause("id");
        List<AlarmRuleGroup> list = getMapper().selectByExample(example);
        List<AlarmRuleGroupVO> voList = Lists.newArrayListWithExpectedSize(list.size());
        for (AlarmRuleGroup ruleGroup:list){
            AlarmRuleGroupVO vo = new AlarmRuleGroupVO(ruleGroup);
            vo.setRules(alarmRuleAtomService.listByRuleGroupId(ruleGroup.getId()));
            voList.add(vo);
        }
        return voList;
    }
}
