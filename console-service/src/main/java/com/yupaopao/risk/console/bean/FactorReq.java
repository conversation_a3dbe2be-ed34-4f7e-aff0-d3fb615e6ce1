package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.model.Attribute;

import java.util.List;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/8/13 15:54
 */
public class FactorReq {
    /**
     * 规则组
     */
    private String ruleGroupId;
    /**
     * 属性列表
     */
    private List<Attribute> attributes;

    public String getRuleGroupId() {
        return ruleGroupId;
    }

    public void setRuleGroupId(String ruleGroupId) {
        this.ruleGroupId = ruleGroupId;
    }

    public List<Attribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes) {
        this.attributes = attributes;
    }
}
