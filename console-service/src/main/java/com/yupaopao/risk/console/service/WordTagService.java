package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.model.WordScene;
import com.yupaopao.risk.common.model.WordTag;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface WordTagService extends BaseService<WordTag> {

    /**
     * 根据wordIds去查询wordTag之间的关系
     * @param wordIds
     * @return
     */
    List<WordTag> batchSearch(List<Long> wordIds);

    /**
     * 根据wordTags（不含有id）去库中查询已存在的关系
     * @param wordTags
     * @return
     */
    List<WordTag> search(List<WordTag> wordTags);

    /**
     * 更新word与tag关系
     *
     * @param wordId
     * @param tags
     * @return
     */
    public boolean update(long wordId, List<Tag> tags);

    /**
     * 更新风控系统或审核系统中word与tag关系
     * @param wordId
     * @param tags
     * @param toSystem
     * @return
     */
    public boolean updateByToSystem(long wordId, List<Tag> tags, Integer toSystem);

    public boolean add(long wordId, List<Tag> tags);

    /**
     * 根据标签ID查询关系
     *
     * @param tagId
     * @return
     */
    public List<WordTag> selectByTagId(long tagId);

    /**
     * 根据词条ID查询关系
     *
     * @param wordId
     * @return
     */
    List<WordTag> selectByWordId(long wordId);

    /**
     * 更新word与tag关系,保留原关系
     *
     * @param wordId
     * @param tags
     * @return
     */
    public boolean updatePart(long wordId, List<Tag> tags);

    /**
     * 根据wordId与toSystem来删除相应的关系
     *
     * @param wordId
     * @return
     */
    boolean deleteByWordId(Long wordId);

    /**
     * 批量删除
     *
     * @param wordTags
     * @return
     */
    int batchDelete(List<WordTag> wordTags);

    /**
     * 批量插入
     *
     * @param wordTags
     * @return
     */
    int batchInsertList(List<WordTag> wordTags);

}
