package com.yupaopao.risk.console.suppport;


import com.alibaba.dubbo.config.annotation.Reference;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.passport.api.AccountQueryService;
import com.yupaopao.platform.passport.constant.NationCodeEnum;
import com.yupaopao.platform.passport.response.AccountDto;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.console.vo.LoginGrayListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OperationLoginListRedisSupport {


    @Autowired
    private RedisService redisService;

    @Reference(check = false)
    private AccountQueryService accountQueryService;

    private BeanCopier beanCopier = BeanCopier.create(GrayList.class, LoginGrayListVO.class, false);

    private static final String MOBILE_NO = "MOBILENO";
    private static final String VALID = "有效";
    private static final String INVALID = "无效";
    private static final String NO_DETECT = "未检测";

    private static final String LIST_KEY = "login_noExist_mobileNo_list";
    private static final String KEY_PREFIX = "login_noExist_mobileNo_";
    private static final Long EXPIRES_TIME = 24*60*60-1L;
    private static final String EVENT_PREFIX = "opt_login_event_filter_";


    public void doCheckSave(GrayList grayList) {
        log.debug("检测运营管理-登陆名单有效性:{}", grayList);
        String[] items = grayList.getValue().split("\n");
        for (String item : items) {
            Response<AccountDto> accountInfoByMobile = accountQueryService.getAccountInfoByMobile(NationCodeEnum.CHINA.getCode(), item);
            log.debug("检测运营管理-登陆名单有效性:响应结果{}", accountInfoByMobile);
            if (MOBILE_NO.equals(grayList.getDimension()) && !accountInfoByMobile.isSuccess() && null == accountInfoByMobile.getResult()){
                redisService.set(buildKey(item), grayList.getValue(), EXPIRES_TIME);
                LoginGrayListVO loginGrayListVO = new LoginGrayListVO();
                beanCopier.copy(grayList, loginGrayListVO, null);
                loginGrayListVO.setHasExist(INVALID);
                loginGrayListVO.setValue(item);
                redisService.lAdd(LIST_KEY, loginGrayListVO, EXPIRES_TIME);
            }
        }
    }


    /**
     * 多个value换行 '\n'
     * */
    public void addEventFilterOfNameList(LoginGrayListVO grayList){
        if (CollectionUtils.isEmpty(grayList.getEventOptList())){
            return;
        }
        String[] items = grayList.getValue().split("\n");
        for (String item : items) {
            Date startTime = grayList.getStartTime();
            Date expireTime = grayList.getExpireTime();
            long expire = expireTime.getTime() - startTime.getTime();
            redisService.lAdd(getEventOfUser(grayList.getType(), item), grayList.getEventOptList(),expire);
        }
    }

    public void updateEventFilter(LoginGrayListVO grayListVO){
        delEventByUser(grayListVO.getType(), grayListVO.getValue());
        addEventFilterOfNameList(grayListVO);
    }

    public void delEventByUser(String type, String key){
        redisService.del(getEventOfUser(type, key));
    }

    public List getEventList(String type, String key){
        return redisService.lGet(getEventOfUser(type, key), 0, -1);
    }

    private String getEventOfUser(String type, String key){
        return EVENT_PREFIX + type + key;
    }

    public void delete(GrayList grayList){
        int del = redisService.del(buildKey(grayList.getValue()));
        LoginGrayListVO loginGrayListVO = new LoginGrayListVO();
        beanCopier.copy(grayList, loginGrayListVO, null);
        loginGrayListVO.setHasExist(INVALID);
        long remove = redisService.lRemove(LIST_KEY, 0, loginGrayListVO);
        log.debug("删除运营管理-登陆名单:{},{},{}", grayList, del, remove);
    }
    public void clearList(){
        int del = redisService.del(LIST_KEY);
        log.debug("删除运营管理-登陆名单列表:{}",del);
    }

    public List getAll(){
        return redisService.lGet(LIST_KEY, 0, -1);
    }

    public List getList(int page, int size){
        long startIndex = 0;
        long endIndex = size-1;
        if (page>1){
            endIndex = page * size-1;
            startIndex = endIndex - size + 1;
        }
        List objects = redisService.lGet(LIST_KEY, startIndex, endIndex);
        log.debug("获取第{}页{}条数据,结果:{}",page , size, CollectionUtils.isNotEmpty(objects) ? objects.size() : 0);
        return objects;
    }

    public long getSize(){
        return redisService.lGetListSize(LIST_KEY);
    }

    public List checkBatch(List<GrayList> grayListList){
        List newList = new ArrayList();
        if (CollectionUtils.isNotEmpty(grayListList)) {
            for (GrayList grayList : grayListList) {
                LoginGrayListVO loginGrayList = new LoginGrayListVO();
                beanCopier.copy(grayList, loginGrayList, null);
                if (StringUtils.isNotEmpty((CharSequence) redisService.get(buildKey(grayList.getValue())))) {
                    loginGrayList.setHasExist(INVALID);
                } else if (MOBILE_NO.equals(grayList.getDimension())) {
                    loginGrayList.setHasExist(VALID);
                }else {
                    loginGrayList.setHasExist(NO_DETECT);
                }
                newList.add(loginGrayList);
            }
        }
        return newList;
    }

    private String buildKey(String mobileNo) {
        StringBuilder buildKey = new StringBuilder();
        buildKey.append(KEY_PREFIX).append(mobileNo);
        return buildKey.toString();
    }


}
