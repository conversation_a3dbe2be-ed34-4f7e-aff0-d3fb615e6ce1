package com.yupaopao.risk.console.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class DropResult implements Serializable {

    private Integer appId;
    private long uid;
    private Boolean done;

    public DropResult(Integer appId, long uid, Boolean done) {
        this.appId = appId;
        this.uid = uid;
        this.done = done;
    }
}