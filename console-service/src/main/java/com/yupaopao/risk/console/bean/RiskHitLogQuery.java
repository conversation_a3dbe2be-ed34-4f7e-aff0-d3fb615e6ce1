package com.yupaopao.risk.console.bean;


import com.yupaopao.platform.common.annotation.Description;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
public class RiskHitLogQuery implements Serializable {

    /**
     * 事件code
     */
    @Description("必填，事件code")
    private String eventCode;

    /**
     * 开始时间
     */
    @Description("必填，开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @Description("必填，结束时间")
    private Date endTime;

    /**
     * 第几页
     */
    @Description("必填，第几页")
    private Integer page;

    /**
     * 每页大小
     */
    @Description("必填，每页大小")
    private Integer size;

    public RiskHitLogQuery(String eventCode, Date startTime, Date endTime, Integer page, Integer size) {
        this.eventCode = eventCode;
        this.startTime = startTime;
        this.endTime = endTime;
        this.page = page;
        this.size = size;
    }

}
