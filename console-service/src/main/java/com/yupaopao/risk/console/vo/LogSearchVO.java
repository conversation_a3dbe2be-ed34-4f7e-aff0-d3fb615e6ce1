package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.access.bean.RiskResult;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.common.model.MessageRecord;
import com.yupaopao.risk.common.model.RiskHitLogSearch;
import com.yupaopao.risk.common.vo.ContentHitLog;
import com.yupaopao.risk.punish.bean.PunishInfo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

/**
 * ES搜索请求VO
 *
 * <AUTHOR>
 * @date 2019/2/27 4:06 PM
 */
public class LogSearchVO<T> implements Serializable {

    private String text;
    private T query;
    private Date startTime;
    private String time;
    private Date endTime;
    private int page = 1;
    private int size = 10;

    private boolean rmMobile; // 移除手机号，true:移除，false:不移除，默认不移除

    public String getText() {
        return text;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTime() {
        return time;
    }

    public void setText(String text) {
        this.text = text;
    }

    public T getQuery() {
        return query;
    }

    public void setQuery(T query) {
        this.query = query;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isRmMobile() {
        return rmMobile;
    }

    public void setRmMobile(boolean rmMobile) {
        this.rmMobile = rmMobile;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", LogSearchVO.class.getSimpleName() + "[", "]")
                .add("text=" + text)
                .add("query=" + query)
                .add("startTime=" + startTime)
                .add("endTime=" + endTime)
                .add("page=" + page)
                .add("size=" + size)
                .toString();
    }

    public static class HitLogSearchVO extends LogSearchVO<HitResult> {

        private String batchId;
        private String accountType;
        private String appId;
        private Long resultRule;

        public String getBatchId() {
            return batchId;
        }

        public void setBatchId(String batchId) {
            this.batchId = batchId;
        }

        public String getAccountType() {
            return accountType;
        }

        public void setAccountType(String accountType) {
            this.accountType = accountType;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public Long getResultRule() {
            return resultRule;
        }

        public void setResultRule(Long resultRule) {
            this.resultRule = resultRule;
        }
    }

    public static class PunishLogSearchVO extends LogSearchVO<PunishInfo> {
        private String targetType;
        private String targetValue;

        public PunishLogSearchVO() {
        }

        public String getTargetType() {
            return targetType;
        }

        public void setTargetType(String targetType) {
            this.targetType = targetType;
        }

        public String getTargetValue() {
            return targetValue;
        }

        public void setTargetValue(String targetValue) {
            this.targetValue = targetValue;
        }
    }

    public static class HitLogRequestSearchVO extends LogSearchVO<RiskHitLogSearch> {

    }

    public static class HitBizLogSearchVO extends LogSearchVO<Map<String, Object>> {
        public static String USERID = "userId";
        private String accountType;
        private String appId;
        private Long resultRule;

        public String getAccountType() {
            return accountType;
        }

        public void setAccountType(String accountType) {
            this.accountType = accountType;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public Long getResultRule() {
            return resultRule;
        }

        public void setResultRule(Long resultRule) {
            this.resultRule = resultRule;
        }
    }

    public static class ThirdLogSearchVO extends LogSearchVO<MessageRecord> {
    }

    public static class AuditLogSearchVO extends LogSearchVO<AuditResultVO> {
    }

    public static class AuditNotifyLogSearchVO extends LogSearchVO<AuditNotifyResultVO> {
    }

    public static class NotifyLogSearchVO extends LogSearchVO<RiskResult> {
    }

    public static class TraceSearchVO extends LogSearchVO<MessageRecord> {
        private String traceType;

        public String getTraceType() {
            return traceType;
        }

        public void setTraceType(String traceType) {
            this.traceType = traceType;
        }
    }

    public static class ContentLogSearchVO extends LogSearchVO<ContentHitLog> {
    }

    public static class DiffLogSearchVO extends LogSearchVO<DiffResult> {
        private boolean origin; // true:带index、id、type信息

        public boolean isOrigin() {
            return origin;
        }

        public void setOrigin(boolean origin) {
            this.origin = origin;
        }
    }

    public static class OperationLogSearchVO extends LogSearchVO<OperationResultVO> {
    }

    public static class ImUserLogSearchVO extends LogSearchVO<ImResultVO> {
        private String userId;
        private String targetUserId;
        private String uid;

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getUid() {
            return uid;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getTargetUserId() {
            return targetUserId;
        }

        public void setTargetUserId(String targetUserId) {
            this.targetUserId = targetUserId;
        }
    }

    public static class ImContentLogSearchVO extends LogSearchVO<ImResultVO> {
        // IM单聊、私聊
        private String userId;
        private String targetUserId;
        private String eventCode;

        // 聊天室
        private String roomId;

        private boolean self; // 自己

        private boolean desc; // 默认升序

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getTargetUserId() {
            return targetUserId;
        }

        public void setTargetUserId(String targetUserId) {
            this.targetUserId = targetUserId;
        }

        public String getRoomId() {
            return roomId;
        }

        public void setRoomId(String roomId) {
            this.roomId = roomId;
        }

        public boolean isSelf() {
            return self;
        }

        public void setSelf(boolean self) {
            this.self = self;
        }

        public boolean isDesc() {
            return desc;
        }

        public void setDesc(boolean desc) {
            this.desc = desc;
        }

        public String getEventCode() {
            return eventCode;
        }

        public void setEventCode(String eventCode) {
            this.eventCode = eventCode;
        }
    }

    public static class HitLogImageSearchVO extends LogSearchVO<HitResult> {
        private List<Map<String, Object>> filters;

        public List<Map<String, Object>> getFilters() {
            return filters;
        }

        public void setFilters(List<Map<String, Object>> filters) {
            this.filters = filters;
        }
    }

    public static class HitLogExtendSearchVO extends LogSearchVO<HitResult> {
        private List<Map<String, Object>> includes;
        private List<Map<String, Object>> excludes;
        private String queryString;
        private boolean asc; // 默认降序

        public List<Map<String, Object>> getIncludes() {
            return includes;
        }

        public void setIncludes(List<Map<String, Object>> includes) {
            this.includes = includes;
        }

        public List<Map<String, Object>> getExcludes() {
            return excludes;
        }

        public void setExcludes(List<Map<String, Object>> excludes) {
            this.excludes = excludes;
        }

        public String getQueryString() {
            return queryString;
        }

        public void setQueryString(String queryString) {
            this.queryString = queryString;
        }

        public boolean isAsc() {
            return asc;
        }

        public void setAsc(boolean asc) {
            this.asc = asc;
        }
    }

    public static class HitLogAggregationVO extends LogSearchVO<HitResult> {
        private List<Map<String, Object>> includes;
        private List<Map<String, Object>> excludes;
        private String queryString;
        private List<Map<String, Object>> aggregations;

        public List<Map<String, Object>> getIncludes() {
            return includes;
        }

        public void setIncludes(List<Map<String, Object>> includes) {
            this.includes = includes;
        }

        public List<Map<String, Object>> getExcludes() {
            return excludes;
        }

        public void setExcludes(List<Map<String, Object>> excludes) {
            this.excludes = excludes;
        }

        public String getQueryString() {
            return queryString;
        }

        public void setQueryString(String queryString) {
            this.queryString = queryString;
        }

        public List<Map<String, Object>> getAggregations() {
            return aggregations;
        }

        public void setAggregations(List<Map<String, Object>> aggregations) {
            this.aggregations = aggregations;
        }
    }
}
