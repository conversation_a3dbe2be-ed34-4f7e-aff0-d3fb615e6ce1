package com.yupaopao.risk.console.utils;

import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.common.model.Permission;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class AuthUtil {

    public static boolean hasPermission(String permissionName, List<Permission> permissionList, List<com.yupaopao.risk.common.model.Role> roleList){
        //未定义权限标识，则不做权限控制
        if(StringUtils.isBlank(permissionName)){
            return true;
        }
        //基础权限
        if(Constants.PERMISSION_BASIC.equals(permissionName)){
            return true;
        }
        if(CollectionUtils.isEmpty(permissionList)){
            System.out.println("当前用户无权限");
            return false;
        }
        if(CollectionUtils.isEmpty(roleList)){
            System.out.println("当前用户无角色");
            return false;
        }
        Boolean result = false;
        for(com.yupaopao.risk.common.model.Role r : roleList){
            if(Constants.ADMIN_ROLENAME.equals(r.getName())){
                result = true;
                break;
            }
        }
        if(result){
            return result;
        }
        return hasPermission(permissionList,permissionName);
    }

    public static boolean hasPermission(List<Permission> permissionList,String permissionName){
        Boolean result = false;
        for(Permission p : permissionList){
            if(CollectionUtils.isNotEmpty(p.getChildren())){
                if(hasPermission(p.getChildren(),permissionName)){
                    result = true;
                    break;
                }
            }
            if(permissionName.equals(p.getName())){
                result = true;
                break;
            }
        }
        return result;
    }

    public static boolean hasPermission(Role[] requiredRoles, String[] extRoles, List<com.yupaopao.risk.common.model.Role> userRoles) {
        Set<String> roles = new HashSet<>();
        for (Role role : requiredRoles) {
            roles.add(role.name());
        }
        for (String role : extRoles) {
            roles.add(role);
        }
        if (roles == null || roles.isEmpty()) {
            return true;
        }
        return hasPermission(roles, userRoles);
    }

    public static boolean hasPermission(Set<String> requiredRoles, List<com.yupaopao.risk.common.model.Role> userRoles) {
        if (requiredRoles == null || requiredRoles.isEmpty()) {
            return true;
        }
        for (String requiredRole : requiredRoles) {
            if (hasPermission(requiredRole, userRoles)) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasPermission(String requiredRole, List<com.yupaopao.risk.common.model.Role> userRoles) {
        if (userRoles == null) {
            userRoles = new ArrayList<>();
        }
        for (com.yupaopao.risk.common.model.Role role : userRoles) {
            if (hasPermission(requiredRole, role)) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasPermission(String requiredRole, com.yupaopao.risk.common.model.Role role) {
        if (StringUtils.isBlank(requiredRole)) {
            return true;
        }
        if (role != null) {
            if (Role.ADMIN.name().equalsIgnoreCase(role.getName()) || requiredRole.equalsIgnoreCase(role.getName())) {
                return true;
            }
        }
        return false;
    }

}
