package com.yupaopao.risk.console.vo;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.common.model.StrategySampleMark;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/6/23 19:12
 */
@Data
public class StrategySampleMarkVO extends StrategySampleMark implements Serializable{

    private static final long serialVersionUID = -8549262164718868712L;

    /**
     * 标注状态
     */
    private Integer markStatus;

    /**
     * 样本详情
     */
    private JSONObject infoJson;

    /**
     * 用户id列表
     */
    private String uids;

    /**
     * 设备id列表
     */
    private String deviceIds;

    /**
     * uid String 类型
     */
    private String strUid;

    public StrategySampleMarkVO() {

    }

    public StrategySampleMarkVO(StrategySampleMark strategySampleMark) {
        if (strategySampleMark != null) {
            BeanUtils.copyProperties(strategySampleMark,this);
        }
    }
}
