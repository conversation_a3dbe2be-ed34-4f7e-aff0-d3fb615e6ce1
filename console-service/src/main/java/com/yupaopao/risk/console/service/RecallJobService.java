package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.RecallJob;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.WordVO;
import com.yupaopao.risk.console.bean.ExportedRecallWord;
import com.yupaopao.risk.console.bean.ExportedWord;
import com.yupaopao.risk.console.enums.RecallType;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-5-7
 */
public interface RecallJobService extends BaseService<RecallJob> {

    /**
     * 自动执行回溯任务
     */
    void autoExecuteRecall();

    /**
     * 不分页 按照搜索条件查询times最大的任务列表 （不分页）(times最大)
     * @param record
     * @param order
     * @return
     */
    Map<String,RecallJob> searchMaxTimesTasks(RecallJob record, String order);

    /**
     * 分页 按照搜索条件查询times最大的任务列表 （分页）(times最大)
     * @param record
     * @param page
     * @param size
     * @param order
     * @return
     */
    PageInfo<RecallJob> searchTask(RecallJob record, int page, int size, String order);

    /**
     * 按照搜索条件查询任务列表 (包含所有的times)
     * @param record
     * @param order
     * @return
     */
    List<RecallJob> searchTask(RecallJob record,String order);

    /**
     * 根据主回溯任务查询关联的
     */
    List<RecallJob> searchChildJob(Integer batchId,List<Object> statusList);

    /**
     * 更新主任务的状态及对应的回溯内容的状态
     * @param jobNo
     * @param jobStatus
     * @param message
     */
    void updatePJobStatusByJobNo(int times,String jobNo, int jobStatus, String message,int count);

    /**
     * 根据数据平台返回的子任务的状态结果，刷新对应主任务的状态
     * @return
     */
    boolean updateParentJobStatus(Integer batchId);

    /**
     *
     * @param recallJobs
     */
    int batchInsert(List<RecallJob> recallJobs);

    /**
     * 新增回溯任务，并更新对应回溯内容的状态、任务编号
     */
    void saveAll(RecallJob record);

    /**
     * 批量新增回溯任务，并更新对应回溯内容的状态、任务编号
     * @param scenes
     * @param userName
     */
    void batchSaveAll(RecallType recallType, String scenes, Boolean onlyOne, Date startTime, Date endTime, String userName);

    /**
     * 执行"待执行"状态的回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void runJob(Long id);

    /**
     * 取消进行中的回溯任务，并更新对应回溯内容的状态
     */
    void cancelById(Long id);

    /**
     * 取消进行中的主回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void cancelParentJob(Long id);

    /**
     * 取消进行中的子回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void cancelChildJob(Long id);

    /**
     * 重新统计主回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void statisticParentJob(Long id,User user);

    /**
     * 重新统计子回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void statisticChildJob(Long id,User user);

    /**
     * 执行主回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void executeParentJob(Long id,User user);

    /**
     * 执行子回溯任务，并更新对应回溯内容的状态
     * @param id
     */
    void executeChildJob(Long id,User user);

    /**
     * 重做该回溯任务，并更新对应回溯内容的状态
     */
    void reRecall(Long id, User user);

    /**
     * 重做该子回溯任务，并更新对应回溯内容的状态
     * @param id
     * @param user
     */
    void redoChildJob(Long id,User user);

    /**
     * 重做该主回溯任务，并更新对应回溯内容的状态
     * @param id
     * @param user
     */
    void redoParentJob(Long id,User user);

    /**
     * 对源有的回溯任务进行升级
     * @param oldJob
     * @return
     */
    boolean addJobTimes(RecallJob oldJob, String userName);

    /**
     * 打开或关闭 回溯消费明细功能
     * @param toOpen
     */
    void openRecall(String toOpen,User user);

    /**
     * 获取 回溯消费明细功能
     */
    boolean getRecallOpenStatus();

    /**
     * 根据任务编号，更新回溯任务、回溯内容的状态、结果信息
     */
    void updateStatusByJobNo(String jobNo, int times, int status, String message,int count);

    /**
     *
     * @param user
     * @param jobId
     * @return
     */
    List<ExportedRecallWord> export(User user, String jobNo);

    /**
     *
     * @param record
     * @return
     */
    Integer executeLimit(RecallJob record);
    /**
     * 发送回溯任务消息
     */
    void sendCommand(String jobNo,int times, List<String> sensitiveWords,String scenes,Date startTime,Date endTime);

    /**
     * 自动回溯job执行"待统计"的回溯任务 或者 "待执行"的回溯任务
     * @param recallJob
     */
    void executeRecallJob(RecallJob recallJob);

    /**
     * 自动回溯job 执行"执行异常"的回溯任务
     * @param recallJob
     */
    void executeExceptJob(RecallJob recallJob);

    /**
     * 自动回溯job 发送回溯命令
     * @param recallType
     * @param status
     * @param jobNo
     * @param times
     * @param sensitiveWords
     * @param scenes
     * @param startTime
     * @param endTime
     */
    void sendCommand(Integer recallType,Integer status,String jobNo,int times, List<String> sensitiveWords,String scenes,Date startTime,Date endTime);
}
