package com.yupaopao.risk.console.bean;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @since Version
 * <p>
 * 2020/11/25 20:15
 */
@Data
public class FeedbackBO {
    /**
     * 三方检测渠道名(SHUMEI/NETEASE)
     */
    private String channel;
    /**
     * 三方属性名
     */
    private String attributeName;
    /**
     * 检测内容(图文音内容)
     */
    private String content;
    /**
     * 检测结果详细内容
     */
    private Map<String, Object> checkDetail;
    /**
     * 暂用
     *
     * @see ShumeiFeedBackType#name()
     */
    private String sourceType;

    /**
     * 原始级别
     */
    private String originLevel;

    /**
     * 欲纠正成的目标级别，PASS/REJECT
     */
    private String targetLevel;
    /**
     * 欲纠正成的目标标签，targetLevel=PASS时可不填
     */
    private String targetLabel;

    private List<String> thirdRequestIds;
    private String thirdTimeStamp;
    private String traceId;
    private Map<String, Object> data;
}
