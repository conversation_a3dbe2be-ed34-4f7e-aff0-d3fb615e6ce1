package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.PatrolRecord;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.vo.PatrolRecordVO;

import java.util.List;

public interface PatrolRecordService extends BaseService<PatrolRecord> {

    PageInfo<PatrolRecord> hit(PatrolRecordVO record, Integer page, Integer size);

    void fetchRelations(List<PatrolRecord> patrolRecordList);

    List<PatrolRecord> batchSearchByIds(List<Long> ids);
}
