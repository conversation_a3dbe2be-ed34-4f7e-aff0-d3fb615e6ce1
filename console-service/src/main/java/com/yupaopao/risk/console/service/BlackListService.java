package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.BlackList;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.BlackListVO;
import com.yupaopao.risk.punish.request.BatchPunishRequest;

import java.util.List;
import java.util.Map;

public interface BlackListService extends BaseService<BlackList> {

    /**
     * 获取加密身份证
     *
     * @param id
     * @return
     */
    String getEncId(String id);

    /**
     * 对限制能力调用惩罚服务执行惩罚
     *
     * @param record
     * @return
     */
    boolean blackPunish(BlackListVO record);

    Map<String, Object> getUserData(String idNo);

    void punish(int type, List<String> abilities, BatchPunishRequest request, List<Long> uids);
}
