package com.yupaopao.risk.console.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.bixin.biggie.api.admin.AdminBiggieService;
import com.yupaopao.bixin.biggie.api.entity.param.BiggieFrozenReq;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.BiggieFrozenResult;
import com.yupaopao.risk.console.bean.PunishResponse;
import com.yupaopao.risk.console.service.AbstractPunishService;
import com.yupaopao.risk.console.utils.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BiggieFrozenServiceImpl extends AbstractPunishService {

    // 冻结大神
    @Reference(check = false)
    public AdminBiggieService adminBiggieService;

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Override
    public String getName() {
        return "BiggieFrozen";
    }

    @Override
    protected List<Long> validateParameter(Map<String,Object> param) {

        Integer quantity = punishApolloConfig.getQuantity();
        if(quantity==null){
            throw new RiskException("apollo上没有配置一次惩罚用户的数量！");
        }

        if(param.get("frozenReason")==null){
            throw new RiskException("冻结大神资质用户冻结原因不能为空!");
        }

        if(param.get("frozenDays")==null){
            throw new RiskException("冻结大神资质用户冻结时长不能为空!");
        }

        if (param.get("uids") == null) {
            throw new RiskException("冻结大神资质用户uid入参不能为空！");
        }

        String uids = param.get("uids").toString();
        if (StringUtils.isBlank(uids)) {
            throw new RiskException("冻结大神资质uids不能为空");
        } else {
            List<Long> uidList = new ArrayList<>();
            for (String uidStr : param.get("uids").toString().split("\n")) {
                try{
                    if(StringUtils.isNotBlank(uidStr)&& NumberUtils.isNumber(uidStr)){
                        long uid=Long.parseLong(uidStr);
                        uidList.add(uid);
                    }
                }catch(Exception exp){
                    log.error("转换uid发生异常:{}",exp.getMessage());
                }
            }

            if (uidList.size() > quantity) {
                throw new RiskException("冻结大神资质uids总数不能超过"+quantity+"个！");
            }

            return uidList;
        }
    }

    @Override
    public PunishResponse punish(User user, Map<String,Object> param) {

        log.info("需要踢出以下用户:" + JSONObject.toJSONString(param));

        try {
            List<Long> uids = validateParameter(param);
            convertPageInputFields(param);
            mergeDefaultInputFields(user,param);

            List<Long> unDoneUids = new ArrayList<>();
            AtomicInteger count = new AtomicInteger(0);

            List<Future<BiggieFrozenResult>> futureList = new ArrayList<>();

            for (Long uid : uids) {
                try{
                    String frozenReason = "";
                    if(param.get("frozenReason")!=null){
                        frozenReason = param.get("frozenReason").toString();
                    }

                    Integer frozenDays = 0;
                    if((param.get("frozenDays")!=null) && NumberUtils.isNumber(param.get("frozenDays").toString())){
                        frozenDays =Integer.valueOf(param.get("frozenDays").toString());
                    }

                    String operator = "";
                    if(param.get("operator")!=null){
                        operator = param.get("operator").toString();
                    }

                    BiggieFrozenCallable callable=new BiggieFrozenCallable(this.adminBiggieService,uid,frozenReason,frozenDays,operator);
                    Future<BiggieFrozenResult> future = threadPoolUtils.submit(callable);
                    futureList.add(future);

                }catch(Exception exp){
                    log.error("执行大神资质冻结惩罚发生异常:{},uid:{}",exp.getMessage(),uid);
                }
            }

            for(Future<BiggieFrozenResult> future:futureList){
                BiggieFrozenResult dropResult = future.get();
                if(dropResult.getDone()){
                    count.incrementAndGet();
                }else{
                    unDoneUids.add(dropResult.getUid());
                }
            }

            Map<String, Object> map = new HashMap<>(1);
            map.put("count", count);
            map.put("unDoneUids", printUidList(unDoneUids));

            return PunishResponse.success(map);
        } catch (RiskException exception){
            log.error("冻结大神资质产生异常"+JSONObject.toJSONString(exception));
            return PunishResponse.failure(exception.getMessage());
        }catch (Exception exception) {
            log.error("调用业务方接口冻结大神资质产生异常", exception);
            return PunishResponse.failure(ErrorMessage.SYSTEM_ERROR.getMsg());
        }
    }

}

@Slf4j
class BiggieFrozenCallable implements Callable<BiggieFrozenResult>{

    private AdminBiggieService adminBiggieService;

    private Long uid;
    private String frozenReason;
    private Integer frozenDays;
    private String operator;

    public BiggieFrozenCallable(AdminBiggieService adminBiggieService, Long uid, String frozenReason, Integer frozenDays, String operator) {
        this.adminBiggieService = adminBiggieService;
        this.uid = uid;
        this.frozenReason = frozenReason;
        this.frozenDays = frozenDays;
        this.operator = operator;
    }

    @Override
    public BiggieFrozenResult call() throws Exception {
        try{
            BiggieFrozenReq request = new BiggieFrozenReq();
            request.setUid(uid);
            request.setFrozenDays(this.frozenDays);
            request.setFrozenReason(this.frozenReason);
            request.setOperator(this.operator);

            Response<Boolean> response = adminBiggieService.frozenBiggie(request);
            if (response != null) {
                if (response.isSuccess() && BooleanUtils.isTrue(response.getResult())) {
                    return new BiggieFrozenResult(uid,this.frozenReason,this.frozenDays,this.operator,true);
                } else {
                    return new BiggieFrozenResult(uid,this.frozenReason,this.frozenDays,this.operator,false);
                }
            } else {
                return new BiggieFrozenResult(uid,this.frozenReason,this.frozenDays,this.operator,false);
            }
        }catch (Exception exp){
            log.error("调用业务方接口冻结资质发生异常:{},uid:{},frozenDays:{},frozenReason:{},operator:{}",exp,uid,this.frozenDays,this.frozenReason,this.operator);
            return new BiggieFrozenResult(uid,this.frozenReason,this.frozenDays,this.operator,false);
        }
    }
}