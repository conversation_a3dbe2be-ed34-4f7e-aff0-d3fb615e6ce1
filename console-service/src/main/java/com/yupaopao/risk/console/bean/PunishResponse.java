package com.yupaopao.risk.console.bean;


import java.io.Serializable;

/**
 * 惩罚响应结果
 *
 * <AUTHOR>
 * @date 2020/07/22 05:17 PM
 */
public class PunishResponse implements Serializable {

    private boolean success;
    private String message;

    private Object result;

    public PunishResponse(){

    }

    public PunishResponse(boolean success, String message, Object result) {
        this.success = success;
        this.message = message;
        this.result = result;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getResult() {
        return result;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public static PunishResponse failure(String message){
        PunishResponse response = new PunishResponse();
        response.setSuccess(false);
        response.setMessage(message);

        return response;
    }

    public static PunishResponse success(Object result){
        PunishResponse response = new PunishResponse();
        response.setSuccess(true);
        response.setResult(result);

        return  response;
    }

    @Override
    public String toString() {
        return "PunishResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", result=" + result +
                '}';
    }
}
