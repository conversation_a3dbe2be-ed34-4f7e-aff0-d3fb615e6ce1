package com.yupaopao.risk.console.service;


import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.engine.bean.TestRuleResult;
import com.yupaopao.risk.engine.bean.TestRuleVO;

/**
 * <AUTHOR>
 * @date 2019-5-20
 */
public interface RiskTestService {

    String execute(String content);

    String batchExecute(String content);

    TestRuleResult testDetect(TestRuleVO testRuleVO);

    String getRequestParameter(LogSearchVO.HitLogSearchVO vo);

}
