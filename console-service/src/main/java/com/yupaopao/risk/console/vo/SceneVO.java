package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.*;
import org.springframework.beans.BeanUtils;

import java.util.Set;

public class SceneVO extends Scene {

    /**
     * 关联的规则
     */
    private Set<AtomRule> atomRules;

    /**
     * 关联的事件
     */
    private Set<Event> events;

    /**
     * 关联的通道
     */
    private Set<ThirdChannel> channels;

    /**
     * 关联的业务类型
     * @return
     */
    private Set<BizType> bizTypes;

    public Set<AtomRule> getAtomRules() {
        return atomRules;
    }

    public void setAtomRules(Set<AtomRule> atomRules) {
        this.atomRules = atomRules;
    }

    public Set<Event> getEvents() {
        return events;
    }

    public void setEvents(Set<Event> events) {
        this.events = events;
    }

    public Set<ThirdChannel> getChannels() {
        return channels;
    }

    public void setChannels(Set<ThirdChannel> channels) {
        this.channels = channels;
    }

    public Set<BizType> getBizTypes() {
        return bizTypes;
    }

    public void setBizTypes(Set<BizType> bizTypes) {
        this.bizTypes = bizTypes;
    }
}
