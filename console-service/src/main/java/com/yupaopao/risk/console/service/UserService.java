package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface UserService extends BaseService<User> {

    PageInfo<User> findAll(String username, int page, int size);

    User findByUsername(String username);

    User add(User user);

    List<User> existName(User user);

    void updateRole(User user);

    User selectOrAdd(String username);
}

