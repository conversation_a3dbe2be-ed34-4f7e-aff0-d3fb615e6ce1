package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.*;

/**
 * 三方服务
 */
public interface ThirdService {
    /**
     * 纠错反馈
     * */
    @Deprecated
    FeedbackResult feedback(FeedBackRequest request);


    /**
     * 数美-纠错
     * @param request
     * @return
     */
    CorrectionResult correction(CorrectionRequest request);

    /**
     * 新版纠错接口
     *
     * @param fb
     * @return
     */
    FeedbackResult feedback(FeedbackBO fb, User user);

}
