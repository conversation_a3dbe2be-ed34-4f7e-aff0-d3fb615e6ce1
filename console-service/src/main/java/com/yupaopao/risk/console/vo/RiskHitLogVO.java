package com.yupaopao.risk.console.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class RiskHitLogVO implements Serializable {

    private Integer queryDays;
    private String uid;
    private String targetUserId;

    private Date startTime;
    private Date endTime;
    private String value;

    private Integer page;
    private Integer size;

    private Integer limitCount;
    private Long ruleId;
    private List<String> eventCode;
    private List<String> level;
}
