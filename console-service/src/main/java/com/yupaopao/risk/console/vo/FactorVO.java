package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.Factor;
import org.apache.commons.beanutils.BeanUtils;

import java.io.Serializable;
import java.util.List;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/28 14:24
 */
public class FactorVO extends Factor implements Serializable {

    private static final long serialVersionUID = 5164248615695852760L;

    List<Attribute> attributeList;

    String attributeName;

    public FactorVO() {
    }

    public FactorVO(Factor factor) {
        if (factor != null) {
            try {
                BeanUtils.copyProperties(this, factor);
            } catch (Exception e) {
                throw new RiskException(e);
            }
        }
    }

    public List<Attribute> getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(List<Attribute> attributeList) {
        this.attributeList = attributeList;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }
}
