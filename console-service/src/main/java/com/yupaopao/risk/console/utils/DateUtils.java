package com.yupaopao.risk.console.utils;

import com.google.common.collect.Lists;
import com.yupaopao.risk.access.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
public class DateUtils {

    public static final String YYYYMMDDHHMM = "yyyyMMddHHmm";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String MM_DD_HH_MM = "MM-dd HH:mm";
    public static final String YYYYMMDDHH = "yyyyMMddHH";
    public static final String MM_DD_HH_00 = "MM-dd HH:00";
    public static final String YYYYMMDD = "yyyyMMdd";
    public static final String MM_DD_00_00 = "MM-dd 00:00";
    public static final String YYYY_MM_DD_00_00_00 = "yyyy-MM-dd 00:00:00";
    public static final String YYYY_MM_DD_23_59_59 = "yyyy-MM-dd 23:59:59";
    public static final String HHMM = "HHmm";

    public static String formatDate(Date date, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    public static Date formatDate(String date, String format) {
        if (StringUtils.isEmpty(format)) {
            throw new IllegalArgumentException("日期不能为空,date=" + date);
        }
        return DateUtil.parse(date, format);
    }

    public static Date getBeforeDayStr(Integer days, String format) {
        Date now = new Date();
        String beforeDateStr = DateFormatUtils.format(org.apache.commons.lang3.time.DateUtils.addDays(now, -days), format);
        Date date = formatDate(beforeDateStr, format);
        return date;
    }

    public static String getBeforeDay(Integer days, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        String beforeDateStr = DateFormatUtils.format(org.apache.commons.lang3.time.DateUtils.addDays(zero, -days + 1), format);
        return beforeDateStr;
    }

    /**
     * 获取距离当前时间单位为degree间隔为time的时间
     *
     * @param time
     * @return
     */
    public static Date getTime(int time, int degree, Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(degree, -time);
        return calendar.getTime();
    }


    public static List<String> findDates(Date dBegin, Date dEnd)
    {
        List<Date> dates = new ArrayList();
        dates.add(dBegin);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime()))
        {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            if(dEnd.after(calBegin.getTime())){
                dates.add(calBegin.getTime());
            }
        }
        List<String> strDates = Lists.newArrayList();
        for(Date date : dates){
            strDates.add(formatDate(date,YYYY_MM_DD));
        }
        return strDates;
    }

    public static int getDays(Date dBegin, Date dEnd){
        int days = (int) ((dEnd.getTime() - dBegin.getTime()) / (1000*3600*24));
        return days;
    }

    public static Date addSeconds(Date date, int seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(13, seconds);
        return calendar.getTime();
    }

    public static Date addMinutes(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(12, minutes);
        return calendar.getTime();
    }

    public static Date addHours(Date date, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(10, hours);
        return calendar.getTime();
    }

    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(5, days);
        return calendar.getTime();
    }

    public static Date addCurrentDays(int days) {
        return addDays(new Date(), days);
    }

    /**
     * 获取day天前零点时间
     *
     * @param day 距今day天前的零点时间，0：今天 6：最近七天，29：最近30天
     */
    public static Date getBeforeDayZeroTime(int day) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date endTime = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(endTime);
        c.add(Calendar.DATE, -day);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }

    public static void main(String[] args) {
        Date dBegin = formatDate("2021-01-21 00:00:00",YYYY_MM_DD_HH_MM_SS);
        Date dEnd = formatDate("2021-01-27 23:59:59",YYYY_MM_DD_HH_MM_SS);
        System.out.println(getDays(dBegin,dEnd));
    }
}
