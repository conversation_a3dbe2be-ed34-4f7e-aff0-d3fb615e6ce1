package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.ReissueLog;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.AuditReissueCondition;
import com.yupaopao.risk.console.bean.AuditReissueConf;
import com.yupaopao.risk.console.bean.ReissueLogVO;
import com.yupaopao.risk.console.vo.ReissueVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 审核相关
 */
public interface AuditService {

    boolean isNeedAudit(String eventCode);

    Set<String> getAuditEvents();

    Map<String, AuditReissueConf> getAuditReissueMap();

    PageInfo<Map<String, Object>> getHitLogData(Date start, Date end, String eventCode, List<String> levels, List<AuditReissueCondition> conditions);

    PageInfo<Map<String, Object>> getExclusion(String[] certIds, String eventCode, Date start, Date end);

    boolean isNeedReissue(String traceId, String batchId, Date startTime);

    /**
     * 审核/机审通知补偿-当batchId存在时，自动补偿当前批次所有数据
     *
     * @param reissueVO
     * @return
     */
    boolean reissue(ReissueVO reissueVO, User user);

    /**
     * 审核/机审通知补偿-当batchId存在时，自动补偿当前批次所有数据
     *
     * @param reissueVO
     * @return
     */
    boolean doReissue(ReissueVO reissueVO);

    /**
     * 人审通知补偿-当batchId存在时，自动补偿当前批次所有数据
     *
     * @param reissueVO
     * @return
     */
    boolean reissueAuditNotify(ReissueVO reissueVO, User user);

    /**
     * 人审通知补偿
     *
     * @param reissueVO
     * @return
     */
    boolean doReissueAuditNotify(ReissueVO reissueVO);

    /**
     * 手动批量补偿
     *
     * @param record
     * @param user
     * @return
     */
    boolean batchReissue(ReissueLogVO record, User user);

    /**
     * 手动批量补偿数预览
     *
     * @param record
     * @param user
     * @return
     */
    int previewBatchReissue(ReissueLogVO record, User user);

}
