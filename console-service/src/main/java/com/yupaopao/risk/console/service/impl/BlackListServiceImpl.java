package com.yupaopao.risk.console.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.yupaopao.platform.common.constant.APP;
import com.yupaopao.platform.common.utils.BitMapUtil;
import com.yupaopao.platform.user.api.AuthDataMaskingQueryService;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.platform.user.api.entity.param.QueryAuthByIdCardReq;
import com.yupaopao.platform.user.api.enums.BitMap;
import com.yupaopao.risk.common.mapper.BlackListMapper;
import com.yupaopao.risk.common.model.BlackList;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.BlackListVO;
import com.yupaopao.risk.console.config.BlackLimitAbilityConfig;
import com.yupaopao.risk.console.service.BlackListService;
import com.yupaopao.risk.console.service.UserDetailService;
import com.yupaopao.risk.console.utils.CheckIdCard;
import com.yupaopao.risk.punish.api.RiskPunishService;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@Transactional
public class BlackListServiceImpl extends BaseServiceImpl<BlackList, BlackListMapper> implements BlackListService {

    @Reference
    private AuthDataMaskingQueryService authDataMaskingQueryService;
    @DubboReference
    private RiskPunishService riskPunishService;
    @Autowired
    private BlackLimitAbilityConfig blackLimitAbilityConfig;
    @Autowired
    private UserDetailService userDetailService;

    @Override
    public String getEncId(String id) {
        try {
            QueryAuthByIdCardReq req = new QueryAuthByIdCardReq();
            req.setIdCard(id);
            return authDataMaskingQueryService.getAesIdCardByIdCard(req).getResult();
        } catch (Exception e) {
            log.error("获取加密身份证异常", e);
            return null;
        }
    }

    @Override
    public boolean blackPunish(BlackListVO record) {
        if (CollectionUtils.isEmpty(record.getLimitAbilities())) {
            return false;
        }
        //筛选新的限制能力调用惩罚服务 剔除的限制能力取消风控端惩罚
        List<String> canceled = new ArrayList<>();
        List<String> uptodate = new ArrayList<>();
        uptodate.addAll(record.getLimitAbilities());
        if (StringUtils.isNotBlank(record.getLimitAbility())) {
            String[] abilities = record.getLimitAbility().split(",");
            for (String ability : abilities) {
                if (record.getLimitAbilities().contains(ability)) {
                    uptodate.remove(ability);
                } else {
                    canceled.add(ability);
                }
            }
        }
        //根据身份证查询uid
        QueryAuthByIdCardReq req = new QueryAuthByIdCardReq();
        //新增为明文 修改为脱敏后结果需要再次从数据库获取
        String idNo = record.getId() == null ? record.getIdNo() : get(record.getId()).getIdNo();
        req.setIdCard(idNo);
        List<Long> uids = authDataMaskingQueryService.queryUidListByIdCard(req).getResult();
        log.info("根据身份证查询uid idNo:{} uids:{}", idNo, uids);
        String encryptId = getEncId(idNo);
        log.info("身份证加密 idNo:{} encryptId:{}", idNo, encryptId);
        if (StringUtils.isBlank(encryptId)) {
            return false;
        }

        //公共请求参数
        //设值名单生效和过期时间
        Map<String, Object> extMap = new HashMap<>();
        extMap.put("expireTime", record.getExpireTime());
        //计算用户惩罚天数 +60000排除分钟级误差
        int days = (int) (record.getExpireTime().getTime() - record.getStartTime().getTime() + 60000) / (24 * 60 * 60 * 1000);
        extMap.put("duration", days);

        BatchPunishRequest request = new BatchPunishRequest();
        request.setChannel("REALTIME_RISK_PUNISH");
        request.setEncryptId(encryptId);
        request.setOperator(record.getModifier());
        request.setInternalReason(record.getMemo());
        request.setExtMap(extMap);

        //惩罚
        //身份证
        punish(0, uptodate, request, null);
        //非身份证
        punish(1, uptodate, request, uids);
        //取消惩罚-风控端
        punish(2, canceled, request, null);
        return true;
    }

    @Override
    public void punish(int type, List<String> abilities, BatchPunishRequest request, List<Long> uids) {
        for (String ability : abilities) {
            //根据限制能力code获取惩罚包id type:0=身份证加黑；1=非身份证加黑的惩罚；2=解除惩罚
            Long punishId;
            if (type == 0) {
                punishId = blackLimitAbilityConfig.getIdNoPkgId(ability);
            } else if (type == 1) {
                punishId = blackLimitAbilityConfig.getPkgId(ability);
            } else {
                punishId = blackLimitAbilityConfig.getUnPkgId(ability);
            }
            if (punishId == null || punishId < 1) {
                log.info("G侧黑名单{}尚未配置惩罚包id type:{}", ability, type);
                continue;
            }
            request.setPackageId(punishId);
            //只有惩罚非身份证时需要对uid进行惩罚 取消惩罚和惩罚身份证只针对身份证维度加解黑名单
            if (CollectionUtils.isNotEmpty(uids)) {
                for (Long uid : uids) {
                    request.setUid(uid);
                    riskPunishService.batchPunish(request);
                }
            } else {
                riskPunishService.batchPunish(request);
            }
        }
    }

    @Override
    public Map<String, Object> getUserData(String idNo) {
        Map<String, Object> map = new HashMap<>();
        if (!CheckIdCard.check(idNo)) {
            map.put("msg", "非正确的身份证号");
            return map;
        }
        //uid
        //根据身份证查询uid
        QueryAuthByIdCardReq req = new QueryAuthByIdCardReq();
        req.setIdCard(idNo);
        List<Long> uids = authDataMaskingQueryService.queryUidListByIdCard(req).getResult();
        log.info("根据身份证查询uid idNo:{} uids:{}", idNo, uids);
        if (CollectionUtils.isEmpty(uids)) {
            map.put("msg", "该身份证尚未绑定账号");
            return map;
        }

        for (Long uid : uids) {
            Map<String, Object> subMap = new HashMap<>();
            map.put(String.valueOf(uid), subMap);

            UserInfoDTO user = userDetailService.getUserInfoByUID(uid, APP.BIXIN.getCode());
            if (user != null) {
                subMap.put("外显ID", user.getShowNo());
                subMap.put("实名", BitMapUtil.getBitValue(user.getBitMap(), BitMap.IS_AUTH.getBit()) == 1);
                subMap.put("大神", BitMapUtil.getBitValue(user.getBitMap(), BitMap.IS_GOD.getBit()) == 1);
            }
            subMap.put("主播", userDetailService.isAnchor(uid));
            subMap.put("主持", userDetailService.isChatroomHost(uid));
        }

        return map;
    }
}
