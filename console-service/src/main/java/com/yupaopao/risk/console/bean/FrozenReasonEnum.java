package com.yupaopao.risk.console.bean;

import java.util.Objects;

public enum FrozenReasonEnum {

    GUANGGAO(1,"广告、代练、买卖、拉人"),
    SEQING(2,"色情、骚扰"),
    EYIGONGJI(3,"恶意攻击大神、平台"),
    QITA(4,"其他[输入原因]");

    FrozenReasonEnum(Integer code, String reason) {
        this.code = code;
        this.reason = reason;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    private Integer code;
    private String reason;

    public static  String getReasonByCode(Integer code){

        for(FrozenReasonEnum frozenReasonEnum:FrozenReasonEnum.values()){
            if(Objects.equals(frozenReasonEnum.getCode(),code)){
                return frozenReasonEnum.getReason();
            }
        }

        return "";
    }

}
