package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.PatrolRecord;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.model.PatrolStatistic;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @since Version
 * <p>
 * 2020/9/22 16:15
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PatrolStatisticVO extends PatrolStatistic {
    private static final long serialVersionUID = -5551399931300518445L;

    /**
     * 查询条件
     */
    private Map<String, Object> condition;

    public PatrolStatisticVO() {
    }

    public PatrolStatisticVO(PatrolStatistic patrolStatistic) {
        if (patrolStatistic != null) {
            try {
                BeanUtils.copyProperties(patrolStatistic, this);
            } catch (Exception e) {
                throw new RiskException(e);
            }
        }
    }
}

