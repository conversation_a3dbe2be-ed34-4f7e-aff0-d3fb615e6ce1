package com.yupaopao.risk.console.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * ExportedWord 用于导出违禁词Excel
 *
 * <AUTHOR>
 * @date 2020/3/18 4:08 下午
 */
public class ExportedWord implements Serializable {

    private String content;
    private Integer weight;
    private String firstTagNames;
    private String tagNames;
    private String sceneNames;

    private String matchTypeName;
    private String wordLocationTypeName;

    private String comment;
    private String author;
    private String modifier;
    private String updatedAt;

    public String getFirstTagNames() {
        return firstTagNames;
    }

    public void setFirstTagNames(String firstTagNames) {
        this.firstTagNames = firstTagNames;
    }

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

    public String getSceneNames() {
        return sceneNames;
    }

    public void setSceneNames(String sceneNames) {
        this.sceneNames = sceneNames;
    }

    public String getMatchTypeName() {
        return matchTypeName;
    }

    public void setMatchTypeName(String matchTypeName) {
        this.matchTypeName = matchTypeName;
    }

    public String getWordLocationTypeName() {
        return wordLocationTypeName;
    }

    public void setWordLocationTypeName(String wordLocationTypeName) {
        this.wordLocationTypeName = wordLocationTypeName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }
}
