package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.ViolationOperation;
import com.yupaopao.risk.common.model.ViolationStatistics;
import com.yupaopao.risk.common.service.BaseService;
import groovy.util.logging.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Transactional
@Slf4j
public interface ViolationStatisticsService extends BaseService<ViolationStatistics> {

    boolean update(ViolationStatistics record);

    Boolean batchInsert(List<ViolationStatistics> violationStatics);

    List<Map<String,String>> getViolationList();

    List<ViolationStatistics> search(ViolationStatistics record, String order);

}
