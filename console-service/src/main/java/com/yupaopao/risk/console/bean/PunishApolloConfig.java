package com.yupaopao.risk.console.bean;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public class PunishApolloConfig {

    private String punishName;
    private String punishDesc;
    private Integer quantity;
    private InputFields inputFields;

    public void setPunishName(String punishName) {
        this.punishName = punishName;
    }

    public String getPunishName() {
        return punishName;
    }

    public void setPunishDesc(String punishDesc) {
        this.punishDesc = punishDesc;
    }

    public String getPunishDesc() {
        return punishDesc;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public void setInputFields(InputFields inputFields) {
        this.inputFields = inputFields;
    }

    public InputFields getInputFields() {
        return inputFields;
    }

    public static PunishApolloConfig parse(String json){
        return StringUtils.isNotBlank(json)?JSON.parseObject(json,PunishApolloConfig.class):null;
    }

}

