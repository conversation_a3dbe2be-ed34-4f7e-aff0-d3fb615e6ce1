package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.audit.appeal.api.entity.response.AppealRecordResponse;
import com.yupaopao.platform.audit.appeal.api.entity.response.PunishRecordResponse;
import com.yupaopao.platform.audit.complain.api.entity.response.ComplainUserInfoResponse;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.ExportedSearchRecord;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.vo.RiskHitLogVO;

import java.util.List;

public interface UserProductionActivitiesQueryService {

    PageInfo queryOrderRecord(RiskHitLogVO vo);

    PageInfo queryKFRecordList(RiskHitLogVO vo);

    PageInfo querySearchRecordList(LogSearchVO.HitLogSearchVO vo);

    List<ExportedSearchRecord> download(User user,LogSearchVO.HitLogSearchVO vo);

    Response queryKFRecordDetail(RiskHitLogVO vo);

    Response<List<PunishRecordResponse>> queryPunishRecord(RiskHitLogVO vo);

    Response<List<AppealRecordResponse>> queryAppealRecord(RiskHitLogVO vo);

    Response<List<ComplainUserInfoResponse>> queryComplainRecord(RiskHitLogVO vo);

}
