package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.ReissueLog;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * 风控事件配置VO
 *
 * <AUTHOR>
 * @date 2018/8/30 上午10:44
 */
@ToString
@NoArgsConstructor
@Data
public class ReissueLogVO extends ReissueLog {

    //预览数量
    private String count;
    //查询ES的时间间隔 防止时间段过长数据遗漏 支持前端指定 默认10分钟
    private Integer interval;
    //指定风控级别
    private String levels;

    public ReissueLogVO(ReissueLog reissueLog) {
        if (reissueLog != null) {
            try {
                BeanUtils.copyProperties(reissueLog, this);
            } catch (Exception e) {
                throw new RiskException(e);
            }
        }
    }

}
