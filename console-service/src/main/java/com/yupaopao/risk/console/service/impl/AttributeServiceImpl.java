package com.yupaopao.risk.console.service.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.AttributeType;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.mapper.AttributeMapper;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.vo.AttributeVO;
import com.yupaopao.risk.console.bean.AttributeReq;
import com.yupaopao.risk.console.bean.AttributeRuleReq;
import com.yupaopao.risk.console.service.AtomRuleService;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.service.PatrolRuleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Service
@Transactional
public class AttributeServiceImpl extends BaseServiceImpl<Attribute, AttributeMapper> implements AttributeService {

    @Autowired
    private AtomRuleService atomRuleService;

    @Autowired
    private PatrolRuleService patrolRuleService;

    @Override
    public PageInfo<Attribute> queryLocal(int page, int size, String name) {
        return query(AttributeType.LOCAL, name, page, size);
    }

    @Override
    public boolean insertSelective(Attribute record) {//唯一需要判断远程属性和本地属性
        if (AttributeType.nameOf(record.getType()) == null) {
            throw new RiskException(ErrorMessage.ATTRIBUTE_TYPE_IS_NULL);
        }
        return super.insertSelective(record);
    }

    @Override
    public PageInfo<Attribute> queryRemote(int page, int size, String name) {
        return query(AttributeType.REMOTE, name, page, size);
    }

    @Override
    public List<Attribute> relationFetch(List<Attribute> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                AttributeVO vo = new AttributeVO(list.get(i));
                vo.setAtomRules(atomRuleService.selectByDependent(vo.getName()));
                vo.setPatrolRules(patrolRuleService.selectByDependent(vo.getName()));
                list.set(i, vo);
            }
        }
        return list;
    }

    @Override
    public List<Attribute> queryRemote(List<String> names) {
        return query(names,AttributeType.REMOTE.name());
    }

    @Override
    public List<Attribute> queryLocal(List<String> names) {
        return query(names,AttributeType.LOCAL.name());
    }

    private List<Attribute> query(List<String> names,String type){
        Example example = new Example(Attribute.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type",type);
        criteria.andIn("name",names);
        return this.getMapper().selectByExample(example);
    }

    @Override
    public List<Attribute> queryRemoteAndLocal(Set<String> names) {
        Example example = new Example(Attribute.class);
        Example.Criteria criteria =example.createCriteria();
        if(CollectionUtils.isNotEmpty(names)){
            criteria.andIn("name",names);
        }

        return this.getMapper().selectByExample(example);
    }

    @Override
    public List<Attribute> queryByFactorId(Long factorId) {
        Example example = new Example(Attribute.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("type",AttributeType.LOCAL.name());
        criteria.andLike("dependent","\""+factorId+"\"::::%");
        return this.getMapper().selectByExample(example);
    }

    private PageInfo<Attribute> query(AttributeType type, String name, int page, int size) {
        page = page == 0 ? 1 : page;
        Attribute attribute = new Attribute();
        attribute.setType(type.name());
        attribute.setName(name);
        return super.search(attribute, page, size);
    }

    @Override
    public boolean deleteById(Long id) {
        return super.deleteById(id);
    }

    @Override
    public List<Attribute> list(AttributeReq req) {
        List<Long> ids = Lists.newArrayList(req.getRuleGroupId().split(",")).stream().filter(id -> StringUtils.isNotBlank(id))
                .map(id -> Long.valueOf(id)).collect(Collectors.toList());
        List<AtomRule> atomRuleList = atomRuleService.listByGroupIds(ids);
        if(CollectionUtils.isNotEmpty(atomRuleList)){
            List<String> depents = Lists.newArrayList();
            Map<String,List<AtomRule>> deptAtomMap = Maps.newHashMap();
            for(AtomRule atomRule : atomRuleList){
                if(StringUtils.isNotBlank(atomRule.getDependent())){
                    List<String> atomDependents = Lists.newArrayList(atomRule.getDependent().split(","));
                    for(String dept : atomDependents){
                        List<AtomRule> list = deptAtomMap.get(dept);
                        if(null == list){
                            list = Lists.newArrayList();
                            deptAtomMap.put(dept,list);
                        }
                        list.add(atomRule);
                    }
                    depents.addAll(atomDependents);
                }
            }
            if(CollectionUtils.isNotEmpty(depents)){
                List<Attribute> list = loopSearch(depents,depents,req.getType());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (int i = 0; i < list.size(); i++) {
                        AttributeVO vo = new AttributeVO(list.get(i));
                        vo.setAtomRules(deptAtomMap.get(vo.getName()));
                        list.set(i, vo);
                    }
                    return list;
                }
            }
        }
        return null;
    }

    private List<Attribute> loopSearch(List<String> allDepents,List<String> depents,String type){
        List<Attribute> list = this.query(depents,type);
        if(CollectionUtils.isNotEmpty(list)){
            List<String> newDepents = Lists.newArrayList();
            for(Attribute attribute : list){
                if(StringUtils.isNotBlank(attribute.getDependent())){
                    String[] keys = attribute.getDependent().split("::::");
                    for(String key : keys){
                        if(key.indexOf(".") > 0){
                            String itemKey = key.split("\\.")[0];
                            if(!allDepents.contains(itemKey)){
                                newDepents.add(itemKey);
                            }
                        }
                    }
                }
            }
            if(CollectionUtils.isNotEmpty(newDepents)){
                allDepents.addAll(newDepents);
                list.addAll(loopSearch(allDepents,newDepents,type));
            }
        }
        return null == list ? Collections.EMPTY_LIST : list;
    }

    @Override
    public boolean updateBizTypeFlag(AttributeVO attributeVO, User user) {
        Attribute attribute = new Attribute();
        attribute.setId(attributeVO.getId());
        attribute.setBizTypeFlag(attributeVO.getBizTypeFlag());
        attribute.setModifier(user.getName());
        this.getMapper().updateByPrimaryKeySelective(attribute);
        return true;
    }

    @Override
    public List<Attribute> listByRuleId(AttributeRuleReq req) {
        AtomRule atomRule = atomRuleService.get(req.getRuleId());
        if(atomRule != null){
            List<String> depents = Lists.newArrayList();
            Map<String,List<AtomRule>> deptAtomMap = Maps.newHashMap();
            if(StringUtils.isNotBlank(atomRule.getDependent())){
                List<String> atomDependents = Lists.newArrayList(atomRule.getDependent().split(","));
                for(String dept : atomDependents){
                    List<AtomRule> list = deptAtomMap.get(dept);
                    if(null == list){
                        list = Lists.newArrayList();
                        deptAtomMap.put(dept,list);
                    }
                    list.add(atomRule);
                }
                depents.addAll(atomDependents);
            }

            if(CollectionUtils.isNotEmpty(depents)){
                List<Attribute> list = loopSearch(depents,depents,req.getType());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (int i = 0; i < list.size(); i++) {
                        AttributeVO vo = new AttributeVO(list.get(i));
                        vo.setAtomRules(deptAtomMap.get(vo.getName()));
                        list.set(i, vo);
                    }
                    return list;
                }
            }
        }
        return null;
    }

    @Override
    public List<String> listAllNames() {
        Example example = new Example(Attribute.class);
        example.selectProperties("name");
        Example.Criteria criteria = example.createCriteria();
        List<Attribute> list = this.getMapper().selectByExample(example);
        return null == list ? Collections.EMPTY_LIST : list.stream().map(Attribute::getName).collect(Collectors.toList());
    }
}
