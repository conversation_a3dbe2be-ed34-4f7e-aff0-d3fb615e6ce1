package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.model.GrayList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LoginGrayListVO extends GrayList implements Serializable {
    private static final long serialVersionUID = 7349917569245348357L;
    private String hasExist;

    private Long id;

    /**
     * 名单类型(黑名单 | 白名单)
     */
    private String type;

    /**
     * 维度
     */
    private String dimension;

    /**
     * 维度值
     */
    private String value;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    private Long groupId;

    /**
     * 说明
     */
    private String comment;

    /**
     * 创建人
     */
    private String author;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
    private String eventList;

    private List eventOptList;
}
