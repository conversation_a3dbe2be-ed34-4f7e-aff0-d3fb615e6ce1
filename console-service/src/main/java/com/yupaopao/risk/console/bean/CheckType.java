package com.yupaopao.risk.console.bean;

import java.util.List;

/**
 * 检测类型
 * author: lijianjun
 * date: 2021/3/25 16:09
 */
public class CheckType {
    /**
     * 检测类型名称
     */
    private String name;
    /**
     * 检测类型code
     */
    private String code;
    /**
     * 三方
     */
    private List<Channel> channels;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<Channel> getChannels() {
        return channels;
    }

    public void setChannels(List<Channel> channels) {
        this.channels = channels;
    }

    /**
     * 三方通道
     */
    public static class Channel{
        /**
         * 三方标识
         */
        private String code;
        /**
         * 三方依赖属性
         */
        private List<Attr> attrs;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public List<Attr> getAttrs() {
            return attrs;
        }

        public void setAttrs(List<Attr> attrs) {
            this.attrs = attrs;
        }
    }

    /**
     * 属性
     */
    public static class  Attr{
        /**
         * 字段名
         */
        private String key;
        /**
         * 默认值
         */
        private String defaultValue;
        /**
         * 是否必填
         */
        private Boolean required;
        /**
         * 备注
         */
        private String memo;
        /**
         * 可选值
         */
        private List<Suggest> suggestions;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public Boolean getRequired() {
            return required;
        }

        public void setRequired(Boolean required) {
            this.required = required;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }

        public List<Suggest> getSuggestions() {
            return suggestions;
        }

        public void setSuggestions(List<Suggest> suggestions) {
            this.suggestions = suggestions;
        }
    }

    /**
     * 属性可选值
     */
    public static class Suggest{
        /**
         * 值
         */
        private String value;
        /**
         * 备注
         */
        private String memo;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }
    }
}
