package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Permission;
import com.yupaopao.risk.common.model.Role;
import com.yupaopao.risk.common.model.RolePermission;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.vo.PermissionVO;
import com.yupaopao.risk.console.vo.RolePermissionsVO;

import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-05-27
 */
public interface RoleService extends BaseService<Role> {
    List<PermissionVO> getPermissions(long id); // 获取所有权限列表并标注指定角色拥有的权限
    void updatePermissions(long id, RolePermissionsVO vo); // 修改指定角色权限
    void delById(Long id); // 删除角色
}
