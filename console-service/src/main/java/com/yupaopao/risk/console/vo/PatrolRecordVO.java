package com.yupaopao.risk.console.vo;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.PatrolRecord;
import com.yupaopao.risk.common.model.PatrolRule;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @since Version
 * <p>
 * 2020/9/22 16:15
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PatrolRecordVO extends PatrolRecord {
    private static final long serialVersionUID = -5551399939500518445L;

    /**
     * 查询条件
     */
    private Map<String, Object> condition;

    private Event event;
    private PatrolRule patrolRule;


    public PatrolRecordVO() {
    }

    public PatrolRecordVO(PatrolRecord patrolRecord) {
        if (patrolRecord != null) {
            try {
                BeanUtils.copyProperties(patrolRecord, this);
            } catch (Exception e) {
                throw new RiskException(e);
            }
        }
    }
}

