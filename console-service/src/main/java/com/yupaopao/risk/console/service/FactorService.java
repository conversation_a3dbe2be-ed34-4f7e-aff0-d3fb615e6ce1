package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Factor;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.FactorReq;
import com.yupaopao.risk.console.bean.FactorRuleReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/12/25 10:47 PM
 */
public interface FactorService extends BaseService<Factor> {
    List<Factor> relationFetch(List<Factor> list);

    List<Factor> list(List<Long> ids);

    List<Factor> list(FactorReq req);

    List<Factor> listByRuleId(FactorRuleReq req);
}
