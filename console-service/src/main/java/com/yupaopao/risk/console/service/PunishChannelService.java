package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.PunishChannel;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PunishChannelService extends BaseService<PunishChannel> {

    /**
     * 渠道唯一校验
     *
     * @param name
     * @return
     */
    boolean isRepeat(String code, String name);

    /**
     * 渠道是否已订阅惩罚包
     *
     * @param code
     * @return
     */
    boolean isSubscribedPkg(String code);

    /**
     * 近一周是否含惩罚记录
     *
     * @param code
     * @return
     */
    boolean hasPunishRecord(String code);

    /**
     * 根据业务方id查询其下所有渠道
     * @param bid
     * @return
     */
    List<PunishChannel> getByBid(long bid);

}
