package com.yupaopao.risk.console;

public class SqlConstants {

    public static final String ACTIVE_BY_CREATE_AT_PREFIX = "select count() as count,formatDateTime(createdAt, '%Y-%m-%d') as createTime  from risk_hit_log where createdAt BETWEEN ";
    public static final String ACTIVE_BY_CREATE_AT_SUFFIX = "'%s' and '%s' and userId = '%s' group by createTime order by createTime asc ";
    public static final String ACTIVE_SCENE_TOP = "select eventCode, count() as count from risk_hit_log where createdAt BETWEEN '%s' AND  '%s'  and userId = '%s' group by eventCode order by count desc limit %d ";
    public static final String VIOLATION_SCENE_TOP = "select eventCode, count() as count from risk_hit_log where createdAt BETWEEN '%s' AND  '%s' and userId = '%s'  and level = 'REJECT' group by eventCode order by count desc limit %d ";
    public static final String VIOLATION_TEXT_TOP = "select name, count() as weight from ( select trimBoth(data_content) as name   from risk_hit_log   where createdAt BETWEEN '%s' AND  '%s'   and level='REJECT'   and userId='%s' and name !=''  union all   select trimBoth(data_body) as name   from risk_hit_log   where createdAt BETWEEN '%s' AND  '%s'  and level='REJECT' and userId='%s' and name !='' ) group by name order by weight desc limit %d";
    public static final String VIOLATION_IMAGE_TOP = "select data_images, count() as count  from risk_hit_log where createdAt BETWEEN  '%s' AND  '%s' and userId = '%s'    and data_images !=''   and level = 'REJECT' group by data_images order by count() desc limit %d ";
    public static final String QUERY_DATA_BY_EVENT_PREFIX = "select count() as count,formatDateTime(createdAt, '%Y-%m-%d') as createTime,level  from risk_hit_log where ";
    public static final String QUERY_DATA_BY_EVENT_SUFFIX = " createdAt BETWEEN '%s' and '%s' and eventCode = '%s' group by createTime,level order by createTime asc";
    public static final String QUERY_IM_BODY_BY_PAGE = "select data_fromUid as uid,data_body as body from risk_hit_log where eventCode == '%s' and createdAt BETWEEN '%s' AND  '%s' limit ((%d)-1)*(%d),%d";
    public static final String QUERY_ROOM_CONTENT_BY_PAGE = "select data_UserId as uid,data_content as content from risk_hit_log where eventCode == '%s' and createdAt BETWEEN '%s' AND  '%s' limit ((%d)-1)*(%d),%d";
    public static final String QUERY_COUNT = "select count() as sum from risk_hit_log where createdAt BETWEEN '%s' AND  '%s' and eventCode == '%s'";
    public static final String QUERY_REAL_RULE_REVIEW_REJECT_RISK_RESULT = "select distinct userId as uid,deviceId,level from risk_hit_log where level in (%s) and eventCode in (%s) and result_rule = %d and createdAt>'%s' and createdAt<'%s' limit %d";
    public static final String QUERY_REAL_RULE_PASS_RISK_RESULT = "select distinct userId as uid,deviceId,level from risk_hit_log where level in (%s) and eventCode in (%s) and createdAt>'%s' and createdAt<'%s' limit %d";

    public static final String QUERY_ASY_RULE_ALL_EVENT_RISK_RESULT = "select distinct uid,deviceId from risk_punish_record_inside where bizType like '%s' and createdAt>'%s' and createdAt<'%s' limit %d";
    public static final String QUERY_ASY_RULE_RISK_RESULT = "select distinct uid,deviceId from risk_punish_record_inside where bizType='%s' and createdAt>'%s' and createdAt<'%s' limit %d";

    public static final String QUERY_CEP_RULE_RISK_RESULT = "select userId as uid,deviceId from risk_insight_cep_match_record where patternId=%d and createdAt>'%s' and createdAt<'%s' limit %d";
    public static final String QUERY_OFFLINE_TASK_RISK_RESULT = "select distinct uid,deviceId from risk_punish_record_inside where bizType='%s' and createdAt>'%s' and createdAt<'%s' limit %d";

    public static final String COUNT_WORD_QUANTITY = "select count(*) as count\n" +
            "from (\n" +
            "  select arrayJoin(JSONExtract(result_wordList,'Array(String)')) word,  result_detectChannel detectChannel, count(1) wordCount,eventCode, 1 fencingWord\n" +
            "  from risk_hit_content_business\n" +
            "  where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_wordList != '[]'\n" +
            "  group by word,detectChannel,eventCode\n" +
            "  order by count(*) desc\n" +
            "  union all\n" +
            "  select arrayJoin(JSONExtract(result_whiteWordList,'Array(String)')) word, result_detectChannel detectChannel, count(1) wordCount,eventCode, 0 fencingWord\n" +
            "  from risk_hit_content_business\n" +
            "  where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_whiteWordList != '[]'\n" +
            "  group by word,detectChannel,eventCode\n" +
            "  order by count(*) desc\n" +
            ")";

    public static final String COUNT_WORD_LIST = "select word,wordCount,detectChannel,eventCode,fencingWord\n" +
            "from (select arrayJoin(JSONExtract(result_wordList,'Array(String)')) word, result_detectChannel detectChannel, count(1) wordCount,eventCode,1 fencingWord\n" +
            "from risk_hit_content_business\n" +
            "where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_wordList != '[]'\n" +
            "group by word,detectChannel,eventCode\n" +
            "order by count(*) desc\n" +
            "union all\n" +
            "select arrayJoin(JSONExtract(result_whiteWordList,'Array(String)')) word, result_detectChannel detectChannel,count(1) wordCount,eventCode,0 fencingWord\n" +
            "from risk_hit_content_business\n" +
            "where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_whiteWordList != '[]'\n" +
            "group by word,detectChannel,eventCode\n" +
            "order by count(*) desc)\n" +
            "limit (%d-1)*(%d),%d";

    public static final String COUNT_WORD_BY =  "select word,detectChannel,wordCount,eventCode,fencingWord\n" +
            "from (select arrayJoin(JSONExtract(result_wordList,'Array(String)')) word, result_detectChannel detectChannel, count(1) wordCount,eventCode,1 fencingWord\n" +
            "from risk_hit_content_business\n" +
            "where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_wordList != '[]'\n" +
            "group by word,detectChannel,eventCode\n" +
            "order by count(*) desc\n" +
            "union all\n" +
            "select arrayJoin(JSONExtract(result_whiteWordList,'Array(String)')) word, result_detectChannel detectChannel, count(1) wordCount,eventCode,0 fencingWord\n" +
            "from risk_hit_content_business\n" +
            "where type == 'TEXT' and createdAt BETWEEN '%s' AND '%s' and result_whiteWordList != '[]'\n" +
            "group by word,detectChannel,eventCode\n" +
            "order by count(*) desc)\n" +
            "order by wordCount desc\n" +
            "limit 10000";

    public static final String QUERY_ASYNC_RULE_ATTR_RESULT = "select jsonData from risk_async_rule_result_log where TraceId = '%s' and createdAt>'%s' and createdAt<'%s'";

}
