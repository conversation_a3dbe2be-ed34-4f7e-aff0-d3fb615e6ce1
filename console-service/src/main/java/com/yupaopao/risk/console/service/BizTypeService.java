package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.BizChannel;
import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.BizTypeVO;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/3/25 14:41
 */
public interface BizTypeService extends BaseService<BizType> {

    PageInfo<BizTypeVO> search(BizTypeVO bizType, int page, int size);

    boolean insert(BizTypeVO bizTypeVO);

    BizType getByName(String name);

    BizType getByCode(String code);

    boolean update(BizTypeVO bizTypeVO);

    boolean switchThirdChannel(BizChannel record, String modifier);

    boolean delete(Long id,String modifier);

    boolean resetPriority(BizTypeVO bizTypeVO, String modifier);

    List<BizType> listByIds(List<Long> ids);

    BizTypeVO getRelateRules(BizTypeVO bizTypeVO);
}
