package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.PunishBusiness;
import com.yupaopao.risk.common.service.BaseService;

/**
 * <AUTHOR>
 */
public interface PunishBusinessService extends BaseService<PunishBusiness> {

    /**
     * 业务方唯一校验
     *
     * @param name
     * @return
     */
    boolean isRepeat(String code, String name);

    boolean hasChannel(long id);

}
