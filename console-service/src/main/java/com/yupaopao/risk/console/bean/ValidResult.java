package com.yupaopao.risk.console.bean;


import lombok.Data;
import java.io.Serializable;

@Data
public
class ValidResult implements Serializable {

    private String code;
    private String msg;
    private boolean success;

    public static ValidResult error(String code,String msg){
        ValidResult validResult = new ValidResult();

        validResult.setCode(code);
        validResult.setMsg(msg);
        validResult.setSuccess(false);

        return validResult;
    }

    public static ValidResult success(){
        ValidResult validResult = new ValidResult();

        validResult.setSuccess(true);

        return validResult;
    }

}
