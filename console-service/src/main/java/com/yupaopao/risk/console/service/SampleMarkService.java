package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.common.model.RecallJob;
import com.yupaopao.risk.common.model.SampleMark;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.vo.CheckTaskVO;
import com.yupaopao.risk.console.vo.SampleMarkVO;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/6/23 16:42
 */
public interface SampleMarkService extends BaseService<SampleMark> {

    int countByTaskId(Long taskId, List<Integer> results);

    PageInfo<SampleMarkVO> searchVO(SampleMarkVO sampleMarkVO, Integer page, Integer size);

    int batchInsert(List<SampleMark> list);

}


