package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.AuditTextStatistics;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AuditTextStatisticsVO;
import com.yupaopao.risk.console.bean.ExportedAuditTextStatistic;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuditTextStatisticsService extends BaseService<AuditTextStatistics> {

    PageInfo<AuditTextStatisticsVO> complexSearch(AuditTextStatisticsVO record, Integer page, Integer size);

    List<ExportedAuditTextStatistic> export(User user, AuditTextStatisticsVO record);


}
