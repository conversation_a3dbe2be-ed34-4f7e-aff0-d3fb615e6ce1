package com.yupaopao.risk.console.service;


import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.PunishResponse;
import com.yupaopao.risk.console.bean.PunishType;

import java.util.List;
import java.util.Map;

public interface PunishManageService {

    /**
     * 获取惩罚服务类型列表
     * @return
     */
    List<PunishType> getPunishServiceTypes();

    /**
     * 渲染页面
     * 根据惩罚类型获取页面入参配置
     * @param punishType
     * @return
     */
    PunishResponse renderPage(String punishType);

    /**
     * 执行惩罚服务
     * @param request
     * @return
     */
    PunishResponse executePunish(User user,Map<String, Object> request);

    PunishResponse invalidAccessToken(Long uid,Long pkgId,String operator);

}
