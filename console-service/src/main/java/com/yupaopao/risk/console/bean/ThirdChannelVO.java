package com.yupaopao.risk.console.bean;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.ThirdChannel;
import org.apache.commons.beanutils.BeanUtils;

import java.util.List;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/3/25 20:04
 */
public class ThirdChannelVO extends ThirdChannel {

    /**
     * 检测类型
     */
    private CheckType checkTypeDto;

    /**
     * 和业务类型关联关系状态
     */
    private Integer relationState;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 业务类型id
     */
    private Long bizTypeId;

    /**
     * 业务类型列表
     */
    List<BizType> bizTypeList;


    public ThirdChannelVO() {
    }

    public ThirdChannelVO(ThirdChannel thirdChannel) {
        if (thirdChannel != null) {
            try {
                BeanUtils.copyProperties(this, thirdChannel);
            } catch (Exception e) {
                throw new RiskException(e);
            }
        }
    }

    public CheckType getCheckTypeDto() {
        return checkTypeDto;
    }

    public void setCheckTypeDto(CheckType checkTypeDto) {
        this.checkTypeDto = checkTypeDto;
    }

    public Integer getRelationState() {
        return relationState;
    }

    public void setRelationState(Integer relationState) {
        this.relationState = relationState;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Long getBizTypeId() {
        return bizTypeId;
    }

    public void setBizTypeId(Long bizTypeId) {
        this.bizTypeId = bizTypeId;
    }

    public List<BizType> getBizTypeList() {
        return bizTypeList;
    }

    public void setBizTypeList(List<BizType> bizTypeList) {
        this.bizTypeList = bizTypeList;
    }
}
