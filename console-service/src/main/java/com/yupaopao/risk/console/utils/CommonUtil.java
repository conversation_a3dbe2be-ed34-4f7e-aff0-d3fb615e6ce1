package com.yupaopao.risk.console.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.TypeUtils;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.util.List;

@Slf4j
public class CommonUtil {

    public static String subStr(String content) {
        return subStr(content, null);
    }

    /**
     * 截取字符串，默认取长度8
     *
     * @param content
     * @param length
     * @return
     */
    public static String subStr(String content, Integer length) {
        if (StringUtils.isNotBlank(content)) {
            if (length == null || length < 1) {
                length = 8;
            }
            if (content.length() >= length) {
                return content.substring(0, length) + "...";
            } else {
                return content;
            }
        }
        return content;
    }

    /**
     * 判断是否存在指定的值
     *
     * @param arr
     * @param value
     * @return
     */
    public static boolean isExistInArr(String arr, Long value) {
        try {
            List<Long> list = JSON.parseArray(arr, Long.class);
            if (CollectionUtils.isNotEmpty(list)) {
                for (int i = 0; i < list.size(); i++) {
                    Long val = list.get(i);
                    if (val != null && val.longValue() == value) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("判断是否存在指定的值失败", e);
        }
        return false;
    }

    /**
     * 快速解析对象，获取特定对象
     * 支持解析JSONArray、JSONObject等Map类型的数据结构
     *
     * @param obj   对象
     * @param expr  解析表达式
     * @param clazz 待转换Class
     * @return
     */
    public static <T> T read(Object obj, String expr, Class<T> clazz) {
        Object result = JSONPath.eval(obj, expr);
        return TypeUtils.castToJavaBean(result, clazz);
    }

    /**
     * 快速解析对象，获取特定对象
     * 支持解析JSONArray、JSONObject等Map类型的数据结构
     *
     * @param obj  对象
     * @param expr 解析表达式
     * @return
     */
    public static String read(Object obj, String expr) {
        return read(obj, expr, String.class);
    }

    /**
     * 过滤属性,支持三层，如
     *
     * @param origin
     * @param filter
     * @return
     */
    public static String filterPropertyString(String origin, String filter) {
        if (StringUtils.isBlank(origin) || StringUtils.isBlank(filter)) {
            return origin;
        }

        try {
            JSONObject originJson = JSON.parseObject(origin);
            JSONArray filterArray = JSON.parseArray(filter);
            JSONObject resultJson = new JSONObject();
            for (int i = 0; i < filterArray.size(); i++) {
                String key = filterArray.getString(i);
                if (StringUtils.isNotBlank(key)) {
                    if (key.indexOf(".") != -1) {
                        String[] keys = key.split("\\.");
                        int len = keys.length;
                        if (len == 2) {
                            String key0 = keys[0];
                            String key1 = keys[1];
                            if (StringUtils.isNotBlank(key0) && StringUtils.isNotBlank(key1)) {
                                JSONObject key0Json = null;
                                if (resultJson.containsKey(key0)) {
                                    if (resultJson.get(key0) instanceof JSONObject) {
                                        key0Json = resultJson.getJSONObject(key0);
                                    } else {
                                        continue;
                                    }
                                } else {
                                    key0Json = new JSONObject();
                                }
                                String readVal = CommonUtil.read(originJson, "$." + key0 + "." + key1);
                                if (StringUtils.isNotBlank(readVal)) {
                                    key0Json.put(key1, readVal);
                                    resultJson.put(key0, key0Json);
                                }
                            }
                        } else if (len == 3) {
                            String key0 = keys[0];
                            String key1 = keys[1];
                            String key2 = keys[2];
                            if (StringUtils.isNotBlank(key0) && StringUtils.isNotBlank(key1) && StringUtils.isNotBlank(key2)) {
                                JSONObject key0Json = null;
                                if (resultJson.containsKey(key0)) {
                                    if (resultJson.get(key0) instanceof JSONObject) {
                                        key0Json = resultJson.getJSONObject(key0);
                                    } else {
                                        continue;
                                    }
                                } else {
                                    key0Json = new JSONObject();
                                }
                                JSONObject key1Json = null;
                                if (key0Json.containsKey(key1)) {
                                    if (key0Json.get(key1) instanceof JSONObject) {
                                        key1Json = key0Json.getJSONObject(key1);
                                    } else {
                                        continue;
                                    }
                                } else {
                                    key1Json = new JSONObject();
                                }
                                String readVal = CommonUtil.read(originJson, "$." + key0 + "." + key1 + "." + key2);
                                if (StringUtils.isNotBlank(readVal)) {
                                    key1Json.put(key2, readVal);
                                    key0Json.put(key1, key1Json);
                                    resultJson.put(key0, key0Json);
                                }
                            }
                        }
                    } else if (originJson.containsKey(key) && !resultJson.containsKey(key)) {
                        resultJson.put(key, originJson.get(key));
                    }
                }
            }
            return resultJson.toJSONString();
        } catch (Exception e) {
            log.warn("过滤属性失败", e);
        }
        return origin;
    }

    // apollo配置异常处理
    public static void apolloConfigException(RuntimeException e1) throws RuntimeException {
        String message = e1.getMessage();
        if (StringUtils.isNotBlank(message) && "apollo配置修改异常".equals(message)) {
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "请检察apollo配置权限");
        } else {
            throw e1;
        }
    }

    public static void setFieldValue(Object bean, String field, String value) {
        try {
            if (StringUtils.isBlank(value) || StringUtils.isBlank(field)) {
                return;
            }
            PropertyDescriptor pd = new PropertyDescriptor(field, bean.getClass());
            Class<?> c = pd.getPropertyType();
            Object o = TypeUtils.cast(value, c, null);
            pd.getWriteMethod().invoke(bean, o);
        } catch (Exception e) {
            log.error("bean属性设值异常 bean:{} field:{} value:{}", bean, field, value, e);
        }
    }

    /**
     * bean字段取值
     *
     * @param bean
     * @param field
     */
    public static Object getFieldValue(Object bean, String field) {
        if (StringUtils.isBlank(field)) {
            return null;
        }
        try {
            PropertyDescriptor pd = new PropertyDescriptor(field, bean.getClass());
            return pd.getReadMethod().invoke(bean);
        } catch (Exception e) {
            log.error("bean属性取值异常 bean:{} field:{} ", bean, field, e);
        }
        return null;
    }

}
