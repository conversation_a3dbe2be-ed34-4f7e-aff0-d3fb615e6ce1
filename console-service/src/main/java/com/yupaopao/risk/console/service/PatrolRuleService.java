package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.enums.SupportRuleType;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.bean.PunishPkgVO;
import com.yupaopao.risk.punish.request.manage.PkgBO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface PatrolRuleService extends BaseService<PatrolRule> {

    List<Map<String, Object>> getPatrolRuleStates();

    List<Map<String, Object>> getPatrolRuleTypes();

    List<PatrolRule> searchNotPatrolRules();

    List<PunishPkgVO> getPunishPackages(SupportRuleType supportRuleType);

    Map<String, Object> getConstFormatter(List<Event> relateEvents, String dependent, Long id);

    Map<String, Object> getConstFormatterForEdit(List<Event> relateEvents, String dependent, Long id);

    List<PatrolRule> selectByDependent(String attrName);

    PkgBO getPunishPkgDetail(String pkgId);

    List<String> checkConstKeys(String riskConst,List<Event> relateEvents);

    List<PatrolRule> listByBizTypeCode(String bizTypeCode);

}
