package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.RecallWord;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.WordVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-5-7
 */
public interface RecallWordService extends BaseService<RecallWord> {

    /**
     * 批量删除未关联jobNo的回溯内容
     * @return
     */
    void batchDelete(Set<String> contents);

    /**
     * 获取待执行的回溯内容
     * @return
     */
    List<RecallWord> getWait();

    /**
     * 查询可操作的回溯内容列表
     */
    List<RecallWord> getActive();

    /**
     * 查询特定回溯任务所关联的回溯内容明细
     * @param jobNo
     * @return
     */
    List<RecallWord> getRecallWords(String jobNo);

    /**
     * 查找特定回溯任务的回溯内容静态集合
     * @param jobNo
     * @return
     */
    String getJobWords(String jobNo);

    /**
     * 重置所有的回溯内容状态为完成
     */
    int reset();

    /**
     * 批量插入回溯内容
     * @param recallWords
     * @return 成功插入的条数
     */
    int batchInsert(List<RecallWord> recallWords);

    /**
     * 更新回溯内容
     * @param record
     * @param wordList
     * @param jobNo
     * @param statusList
     * @return
     */
    int updateBySelective(RecallWord record, List<String> wordList,String jobNo,List<Object> statusList);

    /**
     * 将特定回溯任务jobNo关联的回溯内容更新为"待执行"
     * @return
     */
    int updateToWait(String jobNo);

    /**
     * 更新可操作的回溯内容
     */
    int updateActiveToRunning(RecallWord record);

    int updateWaitToRunningBySelective(RecallWord record, List<String> wordList,String jobNo);

    int updateActiveToRunningBySelective(RecallWord record,List<String> wordList);

    /**
     * 将RecallWord转换为FencingWord
     */
    WordVO toFencingWordVO(RecallWord record);
}
