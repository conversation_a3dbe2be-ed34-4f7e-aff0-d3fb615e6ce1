package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.BizChannel;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2021/3/25 14:41
 */
public interface BizChannelService extends BaseService<BizChannel> {
    int countByThirdChannelId(Long thirdChannelId);
    List<BizChannel> listByBizTypeIds(List<Long> bizTypeIdList);
    List<BizChannel> listByThirdChannelIds(List<Long> thirdChannelIdList);
    void updateState(BizChannel bizChannel,String modifyer);
    void deleteByBizTypeId(Long bizTypeId,String modifyer);
}
