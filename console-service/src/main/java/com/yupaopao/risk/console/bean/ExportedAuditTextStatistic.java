package com.yupaopao.risk.console.bean;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ExportedAuditTextStatistic implements Serializable {

    private String content;
    private String firstTagNames;
    private String tagNames;
    private String sceneNames;
    private String channelName;
    private String wordLocationTypeName;
    private Integer hitCount;
    private Integer rejectCount;
    private Integer passCount;
    private Double effectiveRate;
    private Double rejectRate;
}
