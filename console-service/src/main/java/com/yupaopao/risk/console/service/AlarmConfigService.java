package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.AlarmConfig;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AlarmConfigVO;

import java.util.List;
import java.util.Map;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2020/2/18 14:50
 */
public interface AlarmConfigService extends BaseService<AlarmConfig> {
    Long saveAlarmConfig(AlarmConfigVO vo);

    AlarmConfigVO getDetailById(Long id);

    boolean updateAlarmConfig(AlarmConfigVO vo);

    List<Map<String, Object>> simpleAll();

    AlarmConfig getByName(String name);

}
