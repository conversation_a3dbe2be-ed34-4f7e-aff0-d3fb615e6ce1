package com.yupaopao.risk.console.bean;

public enum FrozenTimeEnum {
    ONEDAY(1,"1天"),THREEDAY(3,"3天"),SEVENDAY(7,"7天"),THIRTYDAY(30,"30天"),FOREVER(-1,"永久");

    FrozenTimeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
