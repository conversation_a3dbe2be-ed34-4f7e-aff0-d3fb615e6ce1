package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.dto.RiskSubLabel;
import com.yupaopao.risk.common.enums.RuleStatus;
import com.yupaopao.risk.common.mapper.AtomRuleMapper;
import com.yupaopao.risk.common.mapper.RuleRelationMapper;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.service.impl.BaseServiceImpl;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.utils.RiskModelIdUtils;
import com.yupaopao.risk.common.vo.AtomRuleVO;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.common.vo.GroupRuleVO;
import com.yupaopao.risk.console.bean.AtomRuleDTO;
import com.yupaopao.risk.console.bean.ConflictRulesCheckReq;
import com.yupaopao.risk.console.bean.PunishPkgVO;
import com.yupaopao.risk.console.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class AtomRuleServiceImpl extends BaseServiceImpl<AtomRule, AtomRuleMapper> implements AtomRuleService {

    private static List<String> SPECIAL_CH = Lists.newArrayList(Constants.CONST_SIGN,Constants.ALLOW_EMPTY_SIGN);
    public final static String REMOTE_ATTR_CONST_PRIORITY_KEY = "pr";
    private static final Long BLACK_RULE_HIT_ID = 110L;

    @Autowired
    RuleRelationMapper ruleRelationMapper;
    @Autowired
    RuleRelationService ruleRelationService;
    @Autowired
    GroupRuleService groupRuleService;
    @Autowired
    AttributeService attributeService;
    @Autowired
    private EventService eventService;
    @Autowired
    private AllPunishService allPunishService;
    @ApolloConfig
    private Config config;
    @Autowired
    private RiskLabelsService riskLabelsService;

    @Override
    public List<AtomRuleDTO> relationFetch(List<AtomRule> list) {
        List<AtomRuleDTO> dtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            for(AtomRule atomRule : list){
                AtomRuleDTO atomRuleDTO = new AtomRuleDTO(atomRule);
                dtoList.add(atomRuleDTO);
            }
            relationSubLabelCodes(dtoList);
            relationGroupRules(dtoList);
            relationPunishPackagesByDto(dtoList);
            relationSubLabelAttrs(dtoList);
        }
        return dtoList;
    }

    private void relationGroupRules(List<AtomRuleDTO> dtoList){
        List<Long> ruleIds = dtoList.stream().map(AtomRule::getId).collect(Collectors.toList());
        List<RuleRelation> relations = ruleRelationService.getByRuleIds(ruleIds);
        if(CollectionUtils.isNotEmpty(relations)){
            Map<Long,List<Long>> ruleGroupMap = relations.stream().collect(Collectors.groupingBy(RuleRelation::getRuleId,Collectors.mapping(RuleRelation::getGroupId,Collectors.toList())));
            List<Long> groupRuleIds = relations.stream().map(RuleRelation::getGroupId).collect(Collectors.toList());
            List<GroupRule> groupRules = groupRuleService.searchByIds(groupRuleIds);
            Map<Long,GroupRule> groupRuleMap = groupRules.stream().collect(Collectors.toMap(GroupRule::getId,g->g));
            for(AtomRuleDTO dto : dtoList){
                List<Long> itemGroupRuleIds = ruleGroupMap.get(dto.getId());
                if(CollectionUtils.isNotEmpty(itemGroupRuleIds)){
                    List<GroupRule> groupRuleList = Lists.newArrayList();
                    dto.setGroupRules(groupRuleList);
                    for(Long groupRuleId:itemGroupRuleIds){
                        groupRuleList.add(groupRuleMap.get(groupRuleId));
                    }
                }
            }
        }
    }

    private void relationSubLabelCodes(List<AtomRuleDTO> dtoList){
        for(AtomRuleDTO atomRuleDTO : dtoList){
            if(StringUtils.isNotBlank(atomRuleDTO.getSubLabelCodes())){
                String[] subLabelCodes = atomRuleDTO.getSubLabelCodes().split(",");
                List<RiskSubLabel> subLabels = Lists.newArrayList();
                for(String subLabelCode : subLabelCodes){
                    subLabels.add(riskLabelsService.getByCode(atomRuleDTO.getType(),subLabelCode));
                }
                atomRuleDTO.setSubLabels(subLabels);
            }
        }
    }

    private void relationSubLabelAttrs(List<AtomRuleDTO> dtoList){
        Set<String> attrSet = dtoList.stream().filter(dto -> StringUtils.isNotBlank(dto.getSubLabelAttrs()))
                .flatMap(dto->Lists.newArrayList(dto.getSubLabelAttrs().split(",")).stream()).collect(Collectors.toSet());
        if(attrSet.size()>0){
            List<Attribute> attributeList = attributeService.queryRemote(Lists.newArrayList(attrSet));
            if(null != attributeList){
                Map<String,Attribute> attributeMap = attributeList.stream().collect(Collectors.toMap(Attribute::getName,a->a));
                dtoList.forEach(dto -> {
                    if(StringUtils.isNotBlank(dto.getSubLabelAttrs())){
                        List<Attribute> labelRemoteAttrs = Lists.newArrayList();
                        String[] subLabelAttrs = dto.getSubLabelAttrs().split(",");
                        for(String subLabelAttr:subLabelAttrs){
                            labelRemoteAttrs.add(attributeMap.get(subLabelAttr));
                        }
                        dto.setLabelRemoteAttrs(labelRemoteAttrs);
                    }
                });
            }
        }
    }

    private List<AtomRuleDTO> relationPunishPackages(List<AtomRule> list){
        List<AtomRuleDTO> dtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            for(AtomRule atomRule : list){
                dtoList.add(new AtomRuleDTO(atomRule));
            }
            relationPunishPackagesByDto(dtoList);
        }
        return dtoList;
    }

    private void relationPunishPackagesByDto(List<AtomRuleDTO> dtoList){
        Set<Long> pkgIds = Sets.newHashSet();
        for(AtomRuleDTO dto : dtoList){
            if(StringUtils.isNotBlank(dto.getPunishPackageIds())){
                pkgIds.addAll(RiskModelIdUtils.split(dto.getPunishPackageIds()));
            }
        }
        if(CollectionUtils.isNotEmpty(pkgIds)){
            List<PunishPkgVO> punishPkgVOList = allPunishService.listPackagesByIds(Lists.newArrayList(pkgIds));
            if(CollectionUtils.isNotEmpty(punishPkgVOList)){
                Map<Long,PunishPkgVO> punishPkgVOMap = punishPkgVOList.stream().collect(Collectors.toMap(p->p.getCode(),p->p,(k1,k2)->k2));
                for(AtomRuleDTO dto : dtoList){
                    if(StringUtils.isNotBlank(dto.getPunishPackageIds())){
                        List<Long> atomRulePkgIds = RiskModelIdUtils.split(dto.getPunishPackageIds());
                        List<PunishPkgVO> punishPkgVOS = Lists.newArrayList();
                        for(Long punishPkgId : atomRulePkgIds){
                            punishPkgVOS.add(punishPkgVOMap.get(punishPkgId));
                        }
                        dto.setPunishPackages(punishPkgVOS);
                    }
                }
            }
        }
    }

    @Override
    public List<AtomRule> selectByGroupId(Long groupId) {
        if(groupId != null){
            List<AtomRule> atomRules = getMapper().selectByGroupId(groupId);
            if(CollectionUtils.isNotEmpty(atomRules)){
                List<AtomRuleDTO> dtoList = relationPunishPackages(atomRules);
                relationSubLabelCodes(dtoList);
                relationSubLabelAttrs(dtoList);
                for (int i = 0; i < atomRules.size(); i++) {
                    atomRules.set(i,dtoList.get(i));
                }
                return atomRules;
            }
        }
        return null;
    }

    @Override
    public List<AtomRule> selectByIds(List<Long> ids) {
        Example example = new Example(AtomRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        example.setOrderByClause("priority");
        return getMapper().selectByExample(example);
    }

    @Override
    public boolean changeStatus(long id, RuleStatus status,User user) {
        if (status == null) {
            return false;
        }
        AtomRule record = new AtomRule();
        record.setId(id);
        record.setStatus(status.name());
        record.setModifier(user.getName());
        return super.updateSelectiveById(record);
    }

    @Override
    public List<AtomRule> selectByDependent(String dependent) {
        Example example = new Example(AtomRule.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andCondition("FIND_IN_SET('"+dependent+"',dependent)>0");
        return this.getMapper().selectByExample(example);
    }

    @Override
    public boolean insertSelective(AtomRule record) {
        record.setStatus(RuleStatus.DISABLE.name());
        return super.insertSelective(record);
    }

    @Override
    public boolean updateSelectiveById(AtomRule record) {
        record.setStatus(RuleStatus.TEST.name()); // 规则修改后状态强制更新为测试状态
        return super.updateSelectiveById(record);
    }

    @Override
    public boolean deleteById(Long id) {
        if (id == null) {
            return false;
        }
        RuleRelation relation = new RuleRelation();
        relation.setRuleId(id);
        Assert.isTrue(ruleRelationMapper.delete(relation) == 0 && super.deleteById(id), "当前规则正在被使用");
        return true;
    }
    @Override
    public Map<String,Object> getConstFormatter(String dependent,Long id) {
        if(StringUtils.isNotBlank(dependent)){
            List<Attribute> attributes = attributeService.queryRemote(Lists.newArrayList(dependent.split(",")));
            if(CollectionUtils.isNotEmpty(attributes)){
                Map<String,Object> map = null;
                if(null != id){
                    AtomRule atomRule = this.get(id);
                    if(StringUtils.isNotBlank(atomRule.getRiskConst())){
                        JSONObject  jsonObject = JSONObject.parseObject(atomRule.getRiskConst());
                        map = jsonObject;
                    }
                }
                if(null == map){
                    map = new HashMap<>();
                }
                for(Attribute attribute : attributes){
                    if(map.containsKey(attribute.getName())){
                        continue;
                    }
                    String attrDpt = attribute.getDependent();
                    if(StringUtils.isNotBlank(attrDpt)){
                        List<String> attrDpts = Lists.newArrayList(attrDpt.split(Constants.ATTRIBUTE_SEP));
                        Map<String,Object> itemMap = new HashMap<>();
                        for(String itemAttrDpt : attrDpts){
                            if(itemAttrDpt.contains(Constants.CONST_SIGN)){
                                itemMap.put(getAttrName(itemAttrDpt),"");
                            }
                        }
                        if(attribute.getBizTypeFlag()){
                            itemMap.put(Constants.BIZ_TYPE_CODE_KEY,"");
                        }
                        if(MapUtils.isNotEmpty(itemMap)){
                            map.put(attribute.getName(),itemMap);
                        }
                    }

                }
                return MapUtils.isNotEmpty(map) ? map : null;
            }
        }
        return null;
    }

    @Override
    public Map<String,Object> getConstList(List<Event> events) {
        if(CollectionUtils.isEmpty(events)){
            return Collections.EMPTY_MAP;
        }

        List<AtomRule> atomRules = eventService.relateAtomRule(events);
        if(CollectionUtils.isNotEmpty(atomRules)){
             Map<String,Object> constList = Maps.newHashMap();
            for(AtomRule atomRule:atomRules){
                if(StringUtils.isNotEmpty(atomRule.getRiskConst())){
                    JSONObject constItem = JSON.parseObject(atomRule.getRiskConst());
                    constList.putAll(constItem);
                }
            }
            return constList;
        }

        return Collections.EMPTY_MAP;
    }

    private static String getAttrName(String attrName){
        int index = 0;
        for(String ch : SPECIAL_CH){
            int chIndex = attrName.indexOf(ch);
            if(chIndex>0 && (index == 0 || index > chIndex)){
                index = chIndex;
            }
        }
        return index == 0 ? attrName : attrName.substring(0,index);
    }

    @Override
    public List<AtomRule> getListRelatedScene() {
        return this.getMapper().getListRelatedScene();
    }

    @Override
    public List<AtomRule> getListRelatedBizType() {
        return this.getMapper().getListRelatedBizType();
    }

    @Override
    public List<AtomRule> listByGroupIds(List<Long> groupIds) {
        List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(groupIds);
        if(CollectionUtils.isNotEmpty(relations)){
            List<Long> atomRuleIds = relations.stream().map(re -> re.getRuleId()).collect(Collectors.toList());
            return this.selectByIds(atomRuleIds);
        }
        return null;
    }

    @Override
    public List<AtomRuleDTO> listByGroupIdsWithGroup(List<Long> groupIds) {
        List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(groupIds);
        if(CollectionUtils.isNotEmpty(relations)){
            List<Long> atomRuleIds = relations.stream().map(re -> re.getRuleId()).collect(Collectors.toList());
            List<AtomRule> atomRuleList = this.selectByIds(atomRuleIds);
            List<AtomRuleDTO> dtoList = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(atomRuleList)){
                for(AtomRule atomRule : atomRuleList){
                    dtoList.add(new AtomRuleDTO(atomRule));
                }
                List<GroupRule> groupRules = groupRuleService.searchByIds(groupIds);
                Map<Long,GroupRule> groupRuleMap = groupRules.stream().collect(Collectors.toMap(GroupRule::getId,g->g));
                Map<Long,List<Long>> ruleGroupMap = new HashMap<>();
                relations.forEach(r->{
                    List<Long> groupRuleIds = ruleGroupMap.get(r.getRuleId());
                    if(null == groupRuleIds){
                        groupRuleIds = new ArrayList<>();
                        ruleGroupMap.put(r.getRuleId(),groupRuleIds);
                    }
                    if(groupIds.contains(r.getGroupId())){
                        groupRuleIds.add(r.getGroupId());
                    }
                });
                for(AtomRuleDTO dto : dtoList){
                    List<Long> itemGroupRuleIds = ruleGroupMap.get(dto.getId());
                    if(CollectionUtils.isNotEmpty(itemGroupRuleIds)){
                        List<GroupRule> groupRuleList = Lists.newArrayList();
                        dto.setGroupRules(groupRuleList);
                        for(Long groupRuleId:itemGroupRuleIds){
                            groupRuleList.add(groupRuleMap.get(groupRuleId));
                        }
                    }
                }
                relationPunishPackagesByDto(dtoList);
            }
            return dtoList;
        }
        return null;
    }

    @Override
    public Map<Long, List<AtomRule>> mapByGroupIds(List<Long> groupIds) {
        Map<Long, List<AtomRule>> resultMap = Maps.newHashMap();
        List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(groupIds);
        if(CollectionUtils.isNotEmpty(relations)){
            Map<Long,List<Long>> relationMap = relations.stream().collect(Collectors.groupingBy(RuleRelation::getGroupId,Collectors.mapping(RuleRelation::getRuleId,Collectors.toList())));
            List atomRuleIds = relations.stream().map(re -> re.getRuleId()).collect(Collectors.toList());
            List<AtomRule> list = this.selectByIds(atomRuleIds);
            List<AtomRuleDTO> dtoList = relationPunishPackages(list);
            relationSubLabelCodes(dtoList);
            relationSubLabelAttrs(dtoList);
            for(AtomRule atomRule : dtoList){
                relationMap.forEach((k,v) -> {
                    if(v.contains(atomRule.getId())){
                        List<AtomRule> groupAtomList = resultMap.get(k);
                        if(null == groupAtomList){
                            groupAtomList = Lists.newArrayList();
                            resultMap.put(k,groupAtomList);
                        }
                        groupAtomList.add(atomRule);
                    }
                });
            }
        }
        return resultMap;
    }

    @Override
    public List<Map<String,Object>> conflictRulesCheck(ConflictRulesCheckReq req) {
        List<Map<String,List<AtomRule>>> list = null;
        if(req.getType() == ConflictRulesCheckReq.CheckType.ATOM_RULE.getType()){
            Long id = req.getAtomRuleVO().getId();
            List<RuleRelation> relations = ruleRelationService.getByRuleIds(Lists.newArrayList(id));
            if(CollectionUtils.isNotEmpty(relations)){
                List<Long> groupIds = relations.stream().map(RuleRelation::getGroupId).collect(Collectors.toList());
                list = conflictRulesCheck(groupRuleService.searchByIds(groupIds),req.getAtomRuleVO());
            }
        }else if(req.getType() == ConflictRulesCheckReq.CheckType.GROUP_RULE.getType()){
            list = conflictRulesCheck(req.getGroupRuleVO());
        }else if(req.getType() == ConflictRulesCheckReq.CheckType.EVENT.getType()){
            list = conflictRulesCheck(req.getEventVO());
        }else if(req.getType() == ConflictRulesCheckReq.CheckType.BUILD_TREE.getType()){
            List<Event> eventList = eventService.selectAll();
            List<RuleRelation> ruleRelationList = ruleRelationService.selectAll();
            List<AtomRule> atomRuleList = this.selectAll();
            if(CollectionUtils.isNotEmpty(eventList)){
                list = Lists.newArrayList();
                Map<Long,Set<Long>> groupRuleMapping = ruleRelationList.stream().collect(Collectors.groupingBy(RuleRelation::getGroupId,Collectors.mapping(RuleRelation::getRuleId, Collectors.toSet())));
                Map<Long,AtomRule> atomRuleMap = atomRuleList.stream().collect(Collectors.toMap(AtomRule::getId,r -> r,(k1,k2)->k2));
                for(Event event:eventList){
                    List<AtomRule> eventAtomRuleList = listAtomRule(event,groupRuleMapping,atomRuleMap);
                    if(null != eventAtomRuleList && eventAtomRuleList.size()>1){
                        list.add(conflictRulesCheckByAtomRuleList(eventAtomRuleList));
                    }
                }
            }
        }
        return removeDuplicate(list);
    }

    @Override
    public Map<String, String> fetchReasons(Long ruleId) {
        String innerReason;
        String outerReason;
        if (null == ruleId) {
            throw new RiskException("未获取到正确的规则Id");
        }
        if (BLACK_RULE_HIT_ID.equals(ruleId)) {
            // 查询名单的通用话术
            String replyJson = config.getProperty("common.list.hit.reply", "{}");
            JSONObject json = JSONObject.parseObject(replyJson);
            innerReason = json.containsKey("innerReason") ? json.getString("innerReason") : "命中风控名单";
            outerReason = json.containsKey("outerReason") ? json.getString("outerReason") : "命中风控名单";
        } else {
            AtomRule atomRule = this.get(ruleId);
            if (null != atomRule) {
                innerReason = atomRule.getInnerReason();
                outerReason = atomRule.getOuterReason();
            } else {
                throw new RiskException("未查到对应的规则, ruleId: " + ruleId);
            }
        }

        Map<String, String> map = new HashMap<>(4);
        map.put("innerReason", innerReason);
        map.put("outerReason", outerReason);
        return map;
    }

    private List<Map<String,Object>> removeDuplicate(List<Map<String,List<AtomRule>>> list){
        if(CollectionUtils.isNotEmpty(list)){
            List<Map<String,Object>> newList = Lists.newArrayList();
            Set<String> keys = Sets.newHashSet();
            for(Map<String,List<AtomRule>> map : list){
                if(map != null){
                    map.forEach((k,v) -> {
                        Set<Long> atomRuleIds = new TreeSet<>(v.stream().map(atomRule -> atomRule.getId()).collect(Collectors.toSet()));
                        String newKey = k.concat("####").concat(StringUtils.join(atomRuleIds,"::::"));
                        if(!keys.contains(newKey)){
                            keys.add(newKey);
                            Map<String,Object> item = Maps.newHashMap();
                            item.put("attrName",k);
                            item.put("atomRules",v);
                            newList.add(item);
                        }
                    });
                }
            }
            return newList;
        }
        return null;
    }

    private List<AtomRule> listAtomRule(Event event,Map<Long,Set<Long>> groupRuleMapping,Map<Long,AtomRule> atomRuleMap){
        List<Long> groupRuleIdList = Lists.newArrayList(event.getRuleGroupId().split(",")).stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
        Set<Long> allAtomRuleIds = Sets.newHashSet();
        groupRuleIdList.forEach(groupRuleId -> {
          Set<Long> atomRuleIds = groupRuleMapping.get(groupRuleId);
          if(CollectionUtils.isNotEmpty(atomRuleIds)){
              allAtomRuleIds.addAll(atomRuleIds);
          }
        });
        return allAtomRuleIds.stream().map(atomRuleId -> atomRuleMap.get(atomRuleId)).collect(Collectors.toList());
    }

    private List<Map<String,List<AtomRule>>> conflictRulesCheck(List<GroupRule> groupRuleList,AtomRuleVO currentAtomRuleVO){
        if(CollectionUtils.isEmpty(groupRuleList)){
            return null;
        }
        AtomRule atomRule = new AtomRule();
        BeanUtils.copyProperties(currentAtomRuleVO,atomRule);
        List<Map<String,List<AtomRule>>> list = Lists.newArrayList();
        Set<Event> eventSet = Sets.newHashSet();
        for(GroupRule groupRule : groupRuleList){
            List<Event> eventList = eventService.getEventsByRuleGroupId(groupRule.getId());
            if(CollectionUtils.isNotEmpty(eventList)){
                eventSet.addAll(eventList);
            }else{
                list.add(conflictRulesCheck(groupRule,atomRule));
            }
        }
        if(eventSet.size()>0){
            for(Event event : eventSet){
                String groupRuleIds = event.getRuleGroupId();
                List<Long> groupRuleIdList = Lists.newArrayList(groupRuleIds.split(",")).stream().map(id -> Long.valueOf(id)).collect(Collectors.toList());
                List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(groupRuleIdList);
                List<Long> atomRuleIds = relations.stream().filter(r -> !r.getRuleId().equals(currentAtomRuleVO.getId())).map(RuleRelation::getRuleId).collect(Collectors.toList());
                if(atomRuleIds.size()>0){
                    List<AtomRule> atomRuleList = this.selectByIds(atomRuleIds);
                    atomRuleList.add(atomRule);
                    list.add(conflictRulesCheckByAtomRuleList(atomRuleList));
                }
            }
        }
        return list;
    }

    private Map<String,List<AtomRule>> conflictRulesCheck(GroupRule groupRule,AtomRule currentAtomRule){
        List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(Lists.newArrayList(groupRule.getId()));
        List<Long> atomRuleIds = relations.stream().filter(r -> !r.getRuleId().equals(currentAtomRule.getId())).map(RuleRelation::getRuleId).collect(Collectors.toList());
        if(atomRuleIds.size()>0){
            List<AtomRule> atomRuleList = this.selectByIds(atomRuleIds);
            atomRuleList.add(currentAtomRule);
            return conflictRulesCheckByAtomRuleList(atomRuleList);
        }
        return null;
    }
    private List<Map<String,List<AtomRule>>> conflictRulesCheck(GroupRuleVO groupRuleVO){
        List<Map<String,List<AtomRule>>> list = Lists.newArrayList();
        List<Event> eventList = null;
        if(null != groupRuleVO.getId() && groupRuleVO.getId()>0){
            eventList = eventService.getEventsByRuleGroupId(groupRuleVO.getId());
        }
        List<Long> paramAtomRuleIds = groupRuleVO.getAtomRules().stream().map(atomRule -> atomRule.getId()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(eventList)){
            for(Event event : eventList){
                List<Long> atomRuleIds = Lists.newArrayList(paramAtomRuleIds);
                String groupRuleIds = event.getRuleGroupId();
                List<Long> groupRuleIdList = Lists.newArrayList(groupRuleIds.split(",")).stream().map(id -> Long.valueOf(id)).filter(id->!id.equals(groupRuleVO.getId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(groupRuleIdList)){
                    List<RuleRelation> relations = ruleRelationService.getByGroupRuleIds(groupRuleIdList);
                    relations.forEach(r -> {
                        if(!atomRuleIds.contains(r.getRuleId())){
                            atomRuleIds.add(r.getRuleId());
                        }
                    });
                }
                if(atomRuleIds.size()>1){
                    List<AtomRule> atomRuleList = this.selectByIds(atomRuleIds);
                    list.add(conflictRulesCheckByAtomRuleList(atomRuleList));
                }
            }
        }else if(paramAtomRuleIds.size()>1){
            List<AtomRule> atomRuleList = this.selectByIds(paramAtomRuleIds);
            list.add(conflictRulesCheckByAtomRuleList(atomRuleList));
        }
        return list;
    }

    private List<Map<String,List<AtomRule>>> conflictRulesCheck(EventVO eventVO){
        List<Map<String,List<AtomRule>>> list = Lists.newArrayList();
        List<GroupRule> groupRuleList = eventVO.getGroupRules();
        List<Long> groupRuleIds = groupRuleList.stream().map(GroupRule::getId).collect(Collectors.toList());
        List<AtomRule> atomRuleList = listByGroupIds(groupRuleIds);
        list.add(conflictRulesCheckByAtomRuleList(atomRuleList));
        return list;
    }

    private Map<String,List<AtomRule>> conflictRulesCheckByAtomRuleList(List<AtomRule> atomRuleList){
        if(CollectionUtils.isNotEmpty(atomRuleList)){
            Map<String,List<AtomRule>> conflictRulesMap = Maps.newHashMap();
            Map<String,Set<String>> keyValueMap = Maps.newHashMap();
            Map<String,List<AtomRule>> rulesMap = Maps.newHashMap();
            Map<String,Integer> priorityMap  = Maps.newHashMap();
            for(AtomRule atomRule : atomRuleList){
                if(StringUtils.isNotBlank(atomRule.getRiskConst())){
                    JSONObject riskConst = JSON.parseObject(atomRule.getRiskConst());
                    if(MapUtils.isNotEmpty(riskConst)){
                        riskConst.forEach((k,v) -> {
                            JSONObject constValue = JSON.parseObject(JSON.toJSONString(v));
                            String composeName = getComposeName(constValue);
                            Set<String> composeNames = keyValueMap.computeIfAbsent(k,key->Sets.newHashSet());
                            composeNames.add(composeName);
                            List<AtomRule> rules = rulesMap.computeIfAbsent(k,key -> Lists.newArrayList());
                            rules.add(atomRule);
                            int priority = getConstPriority(constValue);
                            String key = k.concat(composeName);
                            if(priorityMap.containsKey(key)){
                                if(priority<priorityMap.get(key).intValue()){
                                    priorityMap.put(key,priority);
                                }
                            }else{
                                priorityMap.put(key,priority);
                            }
                        });
                    }
                }
            }
            Set<String> keys = keyValueMap.keySet();
            for(String key : keys){
                Set<String> composeNames = keyValueMap.get(key);
                if(composeNames.size()>1){
                    Integer minPriority = null;
                    int count = 0;
                    for(String composeName:composeNames){
                        int currentPriority = priorityMap.get(key.concat(composeName));
                        if(minPriority == null || minPriority.intValue()>currentPriority){
                            minPriority = currentPriority;
                            count = 1;
                        }else if(minPriority.intValue() == currentPriority){
                            count++;
                        }
                    }
                    if(count>1){
                        conflictRulesMap.put(key,rulesMap.get(key));
                    }
                }
            }
            return conflictRulesMap;
        }
        return null;
    }

    private String getComposeName(JSONObject attrConst){
        String composeName = "";
        if(MapUtils.isNotEmpty(attrConst)){
            TreeMap<String,String> constTreeMap = Maps.newTreeMap();
            attrConst.forEach((k,v)->{
                String itemValue = null == v ? null : String.valueOf(v);
                if(StringUtils.isNotBlank(itemValue) && !REMOTE_ATTR_CONST_PRIORITY_KEY.equals(k)){
                    constTreeMap.put(k,itemValue.replaceAll("\"", "").replaceAll(" ",""));
                }
            });
            StringBuilder sb = new StringBuilder();
            constTreeMap.forEach((k,v) -> {
                sb.append(k).append(v);
            });
            String beforeMd5ComposeName = sb.toString();
            if(StringUtils.isNotBlank(beforeMd5ComposeName)){
                composeName = Md5Util.md5(beforeMd5ComposeName);
            }
        }
        return composeName;
    }

    private int getConstPriority(JSONObject constValue){
        String priorityStr = constValue.getString(REMOTE_ATTR_CONST_PRIORITY_KEY);
        return StringUtils.isNumeric(priorityStr) ? Integer.parseInt(priorityStr) : 999999;
    }

    @Override
    public PageInfo<AtomRule> search(AtomRule record, int page, int size) {
        Example example = new Example(AtomRule.class, false);
        example.setOrderByClause("id");
        Example.Criteria criteria = example.createCriteria();
        if (null != record.getId()) {
            criteria.andEqualTo("id", record.getId());
        }
        if (StringUtils.isNotBlank(record.getName())) {
            criteria.andLike("name", "%" + record.getName() + "%");
        }
        if (StringUtils.isNotBlank(record.getCondition())) {
            criteria.andLike("condition", "%" + record.getCondition() + "%");
        }
        if (StringUtils.isNotBlank(record.getRiskConst())) {
            criteria.andLike("riskConst", "%" + record.getRiskConst() + "%");
        }
        if (StringUtils.isNotBlank(record.getReturnJson())) {
            criteria.andLike("returnJson", "%" + record.getReturnJson() + "%");
        }
        if (null != record.getType()){
            criteria.andEqualTo("type", record.getType());
        }
        if (null != record.getStatus()){
            criteria.andEqualTo("status", record.getStatus());
        }
        if(StringUtils.isNotBlank(record.getPunishPackageIds())){
            criteria.andCondition("FIND_IN_SET('"+record.getPunishPackageIds()+"',punish_package_ids)>0");
        }
        PageHelper.startPage(page, size);
        List<AtomRule> atomRuleList = this.getMapper().selectByExample(example);
        return new PageInfo(atomRuleList);
    }

    @Override
    public int countByBizTypeCode(String bizTypeCode) {
        Example example = new Example(AtomRule.class, false);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("riskConst", "%\"bizTypeCode\":\""+bizTypeCode+"\"%");
        return this.getMapper().selectCountByExample(example);
    }

    @Override
    public List<AtomRule> listByBizTypeCode(String bizTypeCode) {
        Example example = new Example(AtomRule.class, false);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("riskConst", "%\"bizTypeCode\":\""+bizTypeCode+"\"%");
        return this.getMapper().selectByExample(example);
    }

    @Override
    public Map<String, Object> simpleAll() {
        Map<String, Object> result = new HashMap<>();
        Map<Long, String> events = new HashMap<>();

        for (AtomRule rule : super.selectAll()) {
            events.put(rule.getId(), rule.getName());
        }
        result.put("data", events);
        return result;
    }

    @Override
    public List<Event> selectEventsByAtomRuleId(Long atomRuleId) {
        List<RuleRelation> relations = ruleRelationService.getByRuleIds(Lists.newArrayList(atomRuleId));
        Set<Event> events = Sets.newHashSet();
        if(CollectionUtils.isNotEmpty(relations)){
            List<Long> groupRuleIds = relations.stream().map(RuleRelation::getGroupId).collect(Collectors.toList());
            for(Long groupRuleId : groupRuleIds){
                List<Event> eventList = groupRuleService.selectEventsByGroupRuleId(groupRuleId);
                if(CollectionUtils.isNotEmpty(eventList)){
                    events.addAll(eventList);
                }
            }
        }
        return Lists.newArrayList(events);
    }

    @Override
    public List<RiskSubLabel> getSubLabels(AtomRule atomRule) {
        if(StringUtils.isNotBlank(atomRule.getSubLabelCodes())){
            String[] subLabelCodes = atomRule.getSubLabelCodes().split(",");
            List<RiskSubLabel> subLabels = Lists.newArrayList();
            for(String subLabelCode : subLabelCodes){
                subLabels.add(riskLabelsService.getByCode(atomRule.getType(),subLabelCode));
            }
            return subLabels;
        }
        return null;
    }
}
