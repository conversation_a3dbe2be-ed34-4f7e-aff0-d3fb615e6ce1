package com.yupaopao.risk.console.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.bixin.user.operation.api.request.FreezeAccountReq;
import com.yupaopao.bixin.user.operation.api.service.AccountInfoApi;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.AccountFreezeResult;
import com.yupaopao.risk.console.bean.BiggieFrozenResult;
import com.yupaopao.risk.console.bean.PunishResponse;
import com.yupaopao.risk.console.bean.PunishResult;
import com.yupaopao.risk.console.service.AbstractPunishService;
import com.yupaopao.risk.console.utils.ThreadPoolUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class AccountFreezeServiceImpl extends AbstractPunishService {

    @Reference(check = false)
    public AccountInfoApi accountInfoApi; // 冻结用户账号

    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Override
    public String getName() {
        return "AccountFreeze";
    }

    @Override
    protected List<Long> validateParameter(Map<String, Object> param) {

        Integer quantity = punishApolloConfig.getQuantity();
        if(quantity==null){
            throw new RiskException("apollo上没有配置一次惩罚用户的数量！");
        }

        String appId = MapUtils.getString(param,"appId");
        if(StringUtils.isBlank(appId)){
            throw new RiskException("冻结用户账号时平台不能为空！");
        }

        String mode = MapUtils.getString(param, "mode");
        if(StringUtils.isBlank(mode)){
            throw new RiskException("冻结用户账号冻结类型不能为空！");
        }

        String frozenDays = MapUtils.getString(param, "frozenDays");
        if(StringUtils.isBlank(frozenDays)){
            throw new RiskException("冻结用户账号时冻结时长不能为空！");
        }

        if(!StringUtils.isNumeric(frozenDays)){
            throw new RiskException("冻结用户账号时冻结天数必须是数字");
        }

        String toUserReason = MapUtils.getString(param,"toUserReason");
        if(StringUtils.isBlank(toUserReason)){
           throw new RiskException("冻结用户账号时对外冻结原因不能为空");
        }

        String otherReason = MapUtils.getString(param, "otherReason");
        if(StringUtils.isBlank(otherReason)){
            throw new RiskException("冻结用户账号时对内冻结原因不能为空");
        }

        String uids = MapUtils.getString(param,"uids");
        if (StringUtils.isBlank(uids)) {
            throw new RiskException("冻结大神资质uids不能为空");
        } else {
            List<Long> uidList = new ArrayList<>();
            for (String uidStr : param.get("uids").toString().split("\n")) {
                try{
                    if(StringUtils.isNotBlank(uidStr)&& NumberUtils.isNumber(uidStr)){
                        long uid=Long.parseLong(uidStr);
                        uidList.add(uid);
                    }
                }catch(Exception exp){
                    log.error("转换uid发生异常:{}",exp.getMessage());
                }
            }

            if (uidList.size() > quantity) {
                throw new RiskException("踢出用户uids总数不能超过"+quantity+"个");
            }

            return uidList;
        }
    }

    @Override
    public PunishResponse punish(User user, Map<String, Object> param) {
        log.info("需要冻结以下用户的账户:" + JSONObject.toJSONString(param));

        try {
            List<Long> uids = validateParameter(param);
            convertPageInputFields(param);
            mergeDefaultInputFields(user,param);

            List<Long> unDoneUids = new ArrayList<>();
            AtomicInteger count = new AtomicInteger(0);

            List<Future<PunishResult<FreezeAccountReq>>> futureList = new ArrayList<>();

            for (Long uid : uids) {
                try{
                    FreezeAccountReq req = new FreezeAccountReq();

                    req.setUid(uid);

                    Integer appId = Integer.parseInt(param.get("appId").toString());
                    req.setAppId(appId);

                    int mode = Integer.parseInt(param.get("mode").toString());
                    req.setMode(mode);

                    int frozenDays = Integer.parseInt(param.get("frozenDays").toString());
                    req.setFreezeDays(frozenDays);

                    String toUserReason = param.get("toUserReason").toString();
                    req.setToUserReason(toUserReason);

                    String otherReason = param.get("otherReason").toString();
                    req.setOtherReason(otherReason);

                    boolean isNotSend = false;
                    if(param.get("isNotSend")!=null){
                        isNotSend = Boolean.parseBoolean(param.get("isNotSend").toString());
                    }
                    req.setNotSend(isNotSend);

                    List<String> evidence = null;
                    if(param.get("evidence")!=null){
                        evidence = (List<String>) param.get("evidence");
                        req.setEvidence(evidence);
                    }

                    Integer operateOrigin = 3;
                    if(param.get("operateOrigin")!=null){
                        operateOrigin = Integer.parseInt(param.get("operateOrigin").toString());
                    }
                    req.setOperateOrigin(operateOrigin);

                    String operator = "";
                    if(param.get("operator")!=null){
                        operator = param.get("operator").toString();
                    }
                    req.setOpertator(operator);

                    AccountFreezeCallable callable=new AccountFreezeCallable(this.accountInfoApi,req);
                    Future<PunishResult<FreezeAccountReq>> future = threadPoolUtils.submit(callable);
                    futureList.add(future);

                }catch(Exception exp){
                    log.error("执行大神资质冻结惩罚发生异常:{},uid:{}",exp.getMessage(),uid);
                }
            }

            for(Future<PunishResult<FreezeAccountReq>> future:futureList){
                PunishResult<FreezeAccountReq> punishResult = future.get();
                if(punishResult.getDone()){
                    count.incrementAndGet();
                }else{
                    unDoneUids.add(punishResult.getReq().getUid());
                }
            }

            Map<String, Object> map = new HashMap<>(1);
            map.put("count", count);
            map.put("unDoneUids", printUidList(unDoneUids));

            return PunishResponse.success(map);
        } catch (RiskException exception){
            log.error("冻结用户账号产生异常"+JSONObject.toJSONString(exception));
            return PunishResponse.failure(exception.getMessage());
        }catch (Exception exception) {
            log.error("调用业务方接口冻结用户账号产生异常", exception);
            return PunishResponse.failure(ErrorMessage.SYSTEM_ERROR.getMsg());
        }
    }
}

@Slf4j
class AccountFreezeCallable implements Callable<PunishResult<FreezeAccountReq>> {

    private AccountInfoApi accountInfoApi;

    //冻结用户账号req
    private FreezeAccountReq req;

    public AccountFreezeCallable(AccountInfoApi accountInfoApi,FreezeAccountReq req){
        this.accountInfoApi = accountInfoApi;
        this.req = req;
    }

    @Override
    public PunishResult<FreezeAccountReq> call() throws Exception {
        try{
            Response<Boolean> response = this.accountInfoApi.freezeAccount(this.req);
            if (response != null) {
                if (response.isSuccess() && BooleanUtils.isTrue(response.getResult())) {
                    return new PunishResult<>(this.req,true);
                } else {
                    return new PunishResult<>(this.req,false);
                }
            } else {
                return new PunishResult<>(this.req,false);
            }
        }catch(Exception exp){
            log.error("调用业务方接口冻结用户账号发生异常:{},惩罚请求:{}",exp,JSONObject.toJSONString(this.req));
            return new PunishResult<>(this.req,false);
        }
    }
}