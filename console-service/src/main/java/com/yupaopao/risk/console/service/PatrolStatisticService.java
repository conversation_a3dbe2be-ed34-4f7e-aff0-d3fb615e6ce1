package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.PatrolStatistic;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.vo.PatrolStatisticVO;

import java.util.Date;

public interface PatrolStatisticService extends BaseService<PatrolStatistic> {

    PageInfo<PatrolStatistic> hit(PatrolStatisticVO record, Integer page, Integer size);

    void rebuild(Date from, Date to, Long patrolRuleId);

}
