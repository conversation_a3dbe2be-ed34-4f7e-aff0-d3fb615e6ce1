package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.google.common.collect.Maps;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.vo.AlarmRuleAtomVO;
import com.yupaopao.risk.common.vo.AlarmRuleGroupVO;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.AlarmJobService;
import com.yupaopao.risk.console.service.AlarmLogService;
import com.yupaopao.risk.console.service.MsgCenterService;
import com.yupaopao.risk.console.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class AlarmJobServiceImpl implements AlarmJobService {

    @Value("${alarm.dingding.accessToken:a87755d9fd814e6af6a04913d5d765fc2f2c95229d7acc80b18e32b3b705a5f6}")
    private String dingToken;
    @Value("${spring.cas.server-name:http://localhost:8081}")
    private String serverName;
    /**
     * 反作弊默认关联人
     */
    @Value("${cheat.users:}")
    private String cheatUsers;
    /**
     * 反垃圾默认关联人
     */
    @Value("${spam.users:}")
    private String spamUsers;
    /**
     * 告警机器人Webhook
     */
    @Value("${alarm.audit.accessToken:a87755d9fd814e6af6a04913d5d765fc2f2c95229d7acc80b18e32b3b705a5f6}")
    private String auditAlarmToken;
    /**
     * 告警联系人
     */
    @Value("${alarm.audit.users:xiezhenjue}")
    private String auditAlarmUsers;
    /**
     * 告警区分环境
     */
    @Value("${spring.profiles.active:local}")
    private String profiles;
    /**
     * 熔断任务延迟时间
     */
    @Value("${fuse.job.delay.time:180}")
    private Integer fuseJobDelayTime;
    /**
     * 电话模板code
     */
    @Value("${alarm.phone.template.code:TTS_226521002}")
    private String phoneTemplateCode;

    @Autowired
    private AlarmLogService alarmLogService;
    @Autowired
    private MsgCenterService msgCenterService;
    @RedisAutowired("middleware.redis.risk-magic")
    private RedisService redisService;

    @Override
    public void alarm(AlarmVO alarmVO, Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups,Date jobTime) {
        String timeRange = getTimeRange(alarmConfig,jobTime);
        String content = "标签名称：" + alarmVO.getName() + "，\n" +
                "类型：事件告警，\n" +
                "风控事件：" + event.getCode() + "，\n" +
                "风控结果：" + alarmVO.getRiskResult() + "，\n" +
                "告警时间点：" + DateUtils.formatDate(jobTime, DateUtils.YYYY_MM_DD_HH_MM_SS) + "，\n" +
                "统计时间窗口：" + AlarmConfigCountType.codeOf(alarmConfig.getCountType()).getName() + "，\n" +
                "统计时间范围：" + timeRange + "，\n" +
                "命中规则组内容：" + JSON.toJSONString(hitGroups) + "，\n" +
                "点击暂停告警一小时：" + serverName + "/alarm/pause/" + alarmVO.getId() + "\n" +
                "告警补充信息：" + alarmVO.getSupplementaryInfo();
        List<String> relatedPersons = getRelatedPersons(alarmVO,alarmConfig,null);
        String accessToken = StringUtils.isBlank(alarmConfig.getDdGroup()) ? dingToken : alarmConfig.getDdGroup();
        msgCenterService.dingTalkText(accessToken,content, relatedPersons);
        //保存告警记录
        saveAlarmLog(alarmVO,event.getCode(),null,alarmConfig,StringUtils.join(relatedPersons,";"),
                accessToken,timeRange,hitGroups,AlarmHandState.UN_PROCESSED.getCode(),null,AlarmFuseState.NOT.getCode());
    }

    @Override
    public void ruleAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups, Date jobTime) {
        String timeRange = getTimeRange(alarmConfig,jobTime);
        String content = "标签名称：" + alarmVO.getName() + "，\n" +
                "类型：规则告警，\n" +
                "风控规则：" + atomRule.getName() + "，\n" +
                "风控事件：" + event.getCode()+"（"+event.getName()+ "），\n" +
                "风控结果：" + alarmVO.getRiskResult() + "，\n" +
                "告警时间点：" + DateUtils.formatDate(jobTime, DateUtils.YYYY_MM_DD_HH_MM_SS) + "，\n" +
                "统计时间窗口：" + AlarmConfigCountType.codeOf(alarmConfig.getCountType()).getName() + "，\n" +
                "统计时间范围：" + timeRange + "，\n" +
                "命中规则组内容：" + JSON.toJSONString(hitGroups) + "，\n" +
                "点击暂停告警一小时：" + serverName + "/alarm/pause/" + alarmVO.getId() + "\n" +
                "告警补充信息：" + alarmVO.getSupplementaryInfo();
        List<String> relatedPersons = getRelatedPersons(alarmVO,alarmConfig,atomRule);
        String accessToken = StringUtils.isBlank(alarmConfig.getDdGroup()) ? dingToken : alarmConfig.getDdGroup();
        msgCenterService.dingTalkText(accessToken,content, relatedPersons);
        //保存告警记录
        saveAlarmLog(alarmVO,event.getCode(),atomRule.getId(),alarmConfig,StringUtils.join(relatedPersons,";"),accessToken,timeRange,hitGroups,AlarmHandState.UN_PROCESSED.getCode(),null,AlarmFuseState.NOT.getCode());
    }

    @Override
    public Long ruleWillFuseAlarm(AlarmVO alarmVO, AtomRule atomRule,Event event, AlarmConfig alarmConfig, List<Map<String, String>> hitGroups, Date jobTime) {
        String timeRange = getTimeRange(alarmConfig,jobTime);
        List<String> relatedPersons = getRelatedPersons(alarmVO,alarmConfig,atomRule);
        String accessToken = StringUtils.isBlank(alarmConfig.getDdGroup()) ? dingToken : alarmConfig.getDdGroup();
        //保存告警记录
        Long logId = saveAlarmLog(alarmVO,event.getCode(),atomRule.getId(),alarmConfig,StringUtils.join(relatedPersons,";"),accessToken,timeRange,hitGroups,AlarmHandState.UN_PROCESSED.getCode(),null,AlarmFuseState.WAITING_FOR.getCode());
        String content = "标签名称：" + alarmVO.getName() + "，\n" +
                "类型：规则即将熔断告警，\n" +
                "风控规则：" + atomRule.getName() + "【将于"+(fuseJobDelayTime/60)+"分钟后熔断】，\n" +
                "风控事件：" + event.getCode()+"（"+event.getName()+ "），\n" +
                "风控结果：" + alarmVO.getRiskResult() + "，\n" +
                "告警时间点：" + DateUtils.formatDate(jobTime, DateUtils.YYYY_MM_DD_HH_MM_SS) + "，\n" +
                "统计时间窗口：" + AlarmConfigCountType.codeOf(alarmConfig.getCountType()).getName() + "，\n" +
                "统计时间范围：" + timeRange + "，\n" +
                "命中规则组内容：" + JSON.toJSONString(hitGroups) + "，\n" +
                "告警补充信息：" + alarmVO.getSupplementaryInfo()+ "\n" +
                "点击查看告警记录："+ serverName + "/index.html#/home/<USER>/fuseLog?id=" + logId;

        msgCenterService.dingTalkText(accessToken,content, relatedPersons);
        //电话预警
        Map<String,String> phoneParams = Maps.newHashMap();
        phoneParams.put("ruleName",atomRule.getName());
        phoneParams.put("eventCode",event.getCode());
        for(String person : relatedPersons){
            if(StringUtils.isNotBlank(person)){
                msgCenterService.phone(phoneTemplateCode,phoneParams,person,1);
            }
        }
        return logId;
    }

    @Override
    public void ruleNotFuseAlarm(AlarmVO alarmVO, AtomRule atomRule, Event event, AlarmConfig alarmConfig,
                                 List<Map<String, String>> hitGroups, Date jobTime,String reason) {
        String timeRange = getTimeRange(alarmConfig,jobTime);
        List<String> relatedPersons = getRelatedPersons(alarmVO,alarmConfig,atomRule);
        String accessToken = StringUtils.isBlank(alarmConfig.getDdGroup()) ? dingToken : alarmConfig.getDdGroup();
        //保存告警记录
        Long logId = saveAlarmLog(alarmVO,event.getCode(),atomRule.getId(),alarmConfig,StringUtils.join(relatedPersons,";"),accessToken,timeRange,hitGroups,AlarmHandState.PROCESSED.getCode(),reason,AlarmFuseState.NOT.getCode());
        String content = "标签名称：" + alarmVO.getName() + "，\n" +
                "类型：规则达成阈值不熔断告警，\n" +
                "风控规则：" + atomRule.getName() + "【"+reason+"】，\n" +
                "风控事件：" + event.getCode()+"（"+event.getName()+ "），\n" +
                "风控结果：" + alarmVO.getRiskResult() + "，\n" +
                "告警时间点：" + DateUtils.formatDate(jobTime, DateUtils.YYYY_MM_DD_HH_MM_SS) + "，\n" +
                "统计时间窗口：" + AlarmConfigCountType.codeOf(alarmConfig.getCountType()).getName() + "，\n" +
                "统计时间范围：" + timeRange + "，\n" +
                "命中规则组内容：" + JSON.toJSONString(hitGroups) + "，\n" +
                "告警补充信息：" + alarmVO.getSupplementaryInfo()+ "\n" +
                "点击查看告警记录："+ serverName + "/index.html#/home/<USER>/fuseLog?id=" + logId;

        msgCenterService.dingTalkText(accessToken,content, relatedPersons);
    }

    @Override
    public void ruleAlreadyFuseAlarm(AtomRule atomRule,AlarmLog alarmLog,Date jobTime,String eventName) {
        String content = "标签名称：" + alarmLog.getAlarmName() + "，\n" +
                "类型：规则已熔断告警，\n" +
                "风控规则：" + atomRule.getName() + "【已熔断】，\n" +
                "风控事件：" + alarmLog.getEventCode() +"（"+eventName+ "），\n" +
                "风控结果：" + alarmLog.getRiskResult() + "，\n" +
                "熔断时间点：" + DateUtils.formatDate(jobTime, DateUtils.YYYY_MM_DD_HH_MM_SS) + "，\n" +
                "统计时间窗口：" + AlarmConfigCountType.codeOf(alarmLog.getCountType()).getName() + "，\n" +
                "统计时间范围：" + alarmLog.getTimeRange() + "，\n" +
                "命中规则组内容：" + alarmLog.getContent() + "，\n" +
                "告警补充信息：" + alarmLog.getSupplementaryInfo()+ "\n" +
                "点击查看告警记录："+ serverName + "/index.html#/home/<USER>/fuseLog?id=" + alarmLog.getId();
        msgCenterService.dingTalkText(alarmLog.getDdGroup(),content, Lists.newArrayList(alarmLog.getRelatedPersons().split(";")));
    }

    public String getTimeRange(AlarmConfig alarmConfig,Date jobTime){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(jobTime);
        if(alarmConfig.getCountType() == AlarmConfigCountType.WEEK.getCode()){
            String end = DateUtils.formatDate(jobTime, DateUtils.MM_DD_00_00);
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            String start = DateUtils.formatDate(calendar.getTime(), DateUtils.MM_DD_00_00);
            return start + " 到 " + end;
        }else if(alarmConfig.getCountType() == AlarmConfigCountType.DAY.getCode()){
            String end = DateUtils.formatDate(jobTime, DateUtils.MM_DD_HH_00);
            calendar.add(Calendar.HOUR, -24);
            String start = DateUtils.formatDate(calendar.getTime(), DateUtils.MM_DD_HH_00);
            return start + " 到 " + end;
        }else if(alarmConfig.getCountType() == AlarmConfigCountType.HOUR.getCode()){
            String end = DateUtils.formatDate(jobTime, DateUtils.MM_DD_HH_MM);
            calendar.add(Calendar.MINUTE, -60);
            String start = DateUtils.formatDate(calendar.getTime(), DateUtils.MM_DD_HH_MM);
            return start + " 到 " + end;
        }else if(alarmConfig.getCountType() == AlarmConfigCountType.MINUTE.getCode()){
            String end = DateUtils.formatDate(jobTime, DateUtils.MM_DD_HH_MM);
            calendar.add(Calendar.MINUTE, -1);
            String start = DateUtils.formatDate(calendar.getTime(), DateUtils.MM_DD_HH_MM);
            return start + " 到 " + end;
        }
        return "";
    }

    private Long saveAlarmLog(AlarmVO alarmVo,String eventCode,Long ruleId, AlarmConfig alarmConfig,String relatedPersons,
                              String accessToken,String timeRange,List<Map<String, String>> hitGroups,Integer handleState,
                              String handleResult,Integer fuseState){
        //保存告警记录
        AlarmLog alarmLog = new AlarmLog();
        alarmLog.setAlarmId(alarmVo.getId());
        alarmLog.setAlarmName(alarmVo.getName());
        alarmLog.setType(alarmVo.getType());
        alarmLog.setEventCode(eventCode);
        alarmLog.setRuleId(ruleId);
        alarmLog.setRiskResult(alarmVo.getRiskResult());
        alarmLog.setCountType(alarmConfig.getCountType());
        alarmLog.setDdGroup(accessToken);
        alarmLog.setRelatedPersons(relatedPersons);
        alarmLog.setTimeRange(timeRange);
        alarmLog.setContent(JSON.toJSONString(hitGroups));
        alarmLog.setSupplementaryInfo(alarmVo.getSupplementaryInfo());
        alarmLog.setActionType(alarmVo.getActionType());
        alarmLog.setHandleState(handleState);
        alarmLog.setHandleResult(handleResult);
        alarmLog.setFuseState(fuseState);
        alarmLog.setRuleType(alarmVo.getRuleType());
        alarmLogService.insertSelective(alarmLog);
        return alarmLog.getId();
    }

    @Override
    public boolean hitRule(AlarmRuleAtom ruleAtom, Map<String, Integer> map, Map<String, String> hitRules) {
        try {
            if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_GREATER.getCode()) {
                int total = map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT);
                log.info("实际值:{}",total);

                if (total >= Integer.parseInt(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_GREATER.getName(),
                            total + ">=" + ruleAtom.getThreshold());
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_LESS.getCode()) {
                int total = map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT);
                log.info("实际值:{}",total);

                if (total <= Integer.parseInt(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_LESS.getName(),
                            total + "<=" + ruleAtom.getThreshold());
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_ADD_GREATER.getCode()) {
                if (map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT) < map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT)) {
                    return false;
                }
                double up = changeRatio(map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT),
                        map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT), true);
                log.info("实际值:{}",up);

                if (up >= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_ADD_GREATER.getName(),
                            up + "%>=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_DECREASE_GREATER.getCode()) {
                if (map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT) > map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT)) {
                    return false;
                }
                double down = changeRatio(map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT),
                        map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT), false);
                log.info("实际值:{}",down);
                if (down >= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_DECREASE_GREATER.getName(),
                            down + "%>=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_PERCENT_GREATER.getCode()) { //最近N
                // 分钟占该事件统计结果的百分比>=
                double countRatio = this.countRatio(map.get(ConsoleConstants.BEHIND_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT));
                log.info("实际值:{}",countRatio);
                if (countRatio >= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_PERCENT_GREATER.getName(),
                            countRatio + "%>=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_PERCENT_LESS.getCode()) { //最近N
                // 分钟占该事件统计结果的百分比<=
                double countRatio = this.countRatio(map.get(ConsoleConstants.BEHIND_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT));
                log.info("实际值:{}",countRatio);
                if (countRatio <= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_PERCENT_LESS.getName(),
                            countRatio + "%<=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_ADD_PERCENT_GREATER.getCode()) {
                //最近N分钟占该事件统计结果的百分比上升%>=
                double behindCountRatio = this.countRatio(map.get(ConsoleConstants.BEHIND_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT));
                double frontCountRatio = this.countRatio(map.get(ConsoleConstants.FRONT_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT));
                if (behindCountRatio <= frontCountRatio) {
                    return false;
                }
                double upRatio = changeRatio((int) behindCountRatio, (int) frontCountRatio, true);
                log.info("实际值:{}",upRatio);
                if (upRatio >= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_ADD_PERCENT_GREATER.getName(),
                            upRatio + "%>=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            } else if (ruleAtom.getType() == AlarmRuleType.LATELY_N_MINUTE_TOTAL_DECREASE_PERCENT_GREATER.getCode()) { //最近N分钟占该事件统计结果的百分比下降%>=
                double behindCountRatio = this.countRatio(map.get(ConsoleConstants.BEHIND_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.BEHIND_GIVEN_RESULT_COUNT));
                double frontCountRatio = this.countRatio(map.get(ConsoleConstants.FRONT_ALL_RESULT_COUNT),
                        map.get(ConsoleConstants.FRONT_GIVEN_RESULT_COUNT));
                if (behindCountRatio >= frontCountRatio) {
                    return false;
                }
                double downRatio = changeRatio((int) behindCountRatio, (int) frontCountRatio, true);
                log.info("实际值:{}",downRatio);
                if (downRatio >= Double.parseDouble(ruleAtom.getThreshold())) {
                    hitRules.put(AlarmRuleType.LATELY_N_MINUTE_TOTAL_DECREASE_PERCENT_GREATER.getName(), downRatio +
                            "%>=" + ruleAtom.getThreshold() + "%");
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("规则命中处理异常", e);
        }
        return false;
    }

    @Async
    @Override
    public boolean alarm(String alarmContent) {
        String content = "环境：" + profiles + "，\n" +
                "内容: " + alarmContent;
        msgCenterService.dingTalkText(auditAlarmToken,content,Lists.newArrayList(auditAlarmUsers.split(";")));
        return true;
    }

    @Override
    public List<AlarmRuleGroupVO> timeScopeCheck(Alarm alarm,AlarmConfig alarmConfig,List<AlarmRuleGroupVO> ruleGroups, Date timeNow) {
        List<AlarmRuleGroupVO> hitRuleGroups = Lists.newArrayList();
        for(AlarmRuleGroupVO ruleGroupVO : ruleGroups){
            List<AlarmRuleAtomVO> ruleAtomVOS = ruleGroupVO.getRules();
            if (CollectionUtils.isEmpty(ruleAtomVOS)) {
                log.info("告警Job 获取规则组中规则为空 alarm:{} ruleGroup:{}", JSON.toJSONString(alarm), JSON.toJSONString(ruleGroupVO));
                continue;
            }
            boolean isHit = true;
            if(alarmConfig.getCountType() == AlarmConfigCountType.HOUR.getCode() ||
                    alarmConfig.getCountType() == AlarmConfigCountType.MINUTE.getCode()){
                for(AlarmRuleAtomVO ruleAtomVO : ruleAtomVOS){
                    //小时窗口需判断时间范围
                    if (ruleAtomVO.getType() == AlarmRuleType.ALARM_TIME_RANGE.getCode()) {
                        if (!isInTimeScope(ruleAtomVO, timeNow)) {
                            isHit = false;
                            break;
                        }
                    }
                }
            }
            if(isHit){
                hitRuleGroups.add(ruleGroupVO);
            }
        }
        return hitRuleGroups;
    }

    @Override
    public List<Map<String, Integer>> batchGet(List<String> keys) {
        List<Map<String, Integer>> list = null;
        try {
            list = redisService.getRedisTemplate().executePipelined(new SessionCallback<Object>() {
                @Override
                public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                    HashOperations hashOperations = operations.opsForHash();
                    for (String key : keys) {
                        hashOperations.entries(key);
                    }
                    return null;
                }
            });
        }catch (Exception e){
            log.error("告警获取缓存数据失败",e);
        }
        return null == list ? Lists.newArrayList() : list;
    }

    //计算变化率（上升率或者下降率）
    public double changeRatio(int numerator, int denominator, boolean asc) {
        try {
            //极限值判断
            if (numerator == 0 && denominator == 0) {
                return 0;
            } else if (asc && denominator == 0) {
                return 9999;
            } else if (!asc && numerator == 0) {
                return 9999;
            } else if (numerator == denominator) {
                return 0;
            }
            //计算上升下降比值
            if (asc) {
                numerator = numerator - denominator;
            } else {
                numerator = denominator - numerator;
            }
            double radio = (numerator * 1.0 / denominator) * 100;
            return new BigDecimal(radio).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        } catch (Exception e) {
            log.error("计算变化率异常", e);
        }
        return 0;
    }

    //计算总量占比
    public double countRatio(int count, int part) {
        try {
            if (count == 0) {
                return 0;
            } else {
                double ratio = (part * 1.0 / count) * 100;
                return new BigDecimal(ratio).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
        } catch (Exception exp) {
            log.error("计算总量占比异常", exp);
       }
        return 0;
    }

    private List<String> getRelatedPersons(Alarm alarm, AlarmConfig alarmConfig,AtomRule atomRule){
        Set<String> userSets = Sets.newHashSet();
        if(alarmConfig.getRelatedPersons().contains("default") && alarm.getActionType() == AlarmActionType.FUSE.getCode()
            && null != atomRule){
            if(atomRule.getType() == RuleType.DEFAULT.getCode()){
                userSets.addAll(Lists.newArrayList(cheatUsers.split(";")));
                userSets.addAll(Lists.newArrayList(spamUsers.split(";")));
            }else if(atomRule.getType() == RuleType.CHEAT.getCode()){
                userSets.addAll(Lists.newArrayList(cheatUsers.split(";")));
            }else if(atomRule.getType() == RuleType.SPAM.getCode()){
                userSets.addAll(Lists.newArrayList(spamUsers.split(";")));
            }
        }
        String[] users = alarmConfig.getRelatedPersons().split(";");
        for(String user : users){
            if(StringUtils.isNotBlank(user) && !"default".equals(user)){
                userSets.add(user);
            }
        }
        return Lists.newArrayList(userSets);
    }

    private boolean isInTimeScope(AlarmRuleAtom ruleAtom, Date date) {
        try {
            int timeNow = Integer.parseInt(DateUtils.formatDate(date, DateUtils.HHMM));
            String[] timeScope = ruleAtom.getThreshold().replace(":", "").split("-");
            if (timeNow < Integer.parseInt(timeScope[0]) || timeNow > Integer.parseInt(timeScope[1])) {
                return false;
            }
        } catch (Exception e) {
            log.error("告警时间范围转换异常", e);
        }
        return true;
    }
}
