package com.yupaopao.risk.console.bean;


public class PageInputFields {

    private String property;
    private String desc;
    private boolean required;
    private String tip;
    private String type;
    private InitData initData;
    private String defaultValue;

    public void setProperty(String property) {
        this.property = property;
    }
    public String getProperty() {
        return property;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public String getDesc() {
        return desc;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }
    public boolean getRequired() {
        return required;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }
    public String getTip() {
        return tip;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getType() {
        return type;
    }

    public void setInitData(InitData initData) {
        this.initData = initData;
    }
    public InitData getInitData() {
        return initData;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }
    public String getDefaultValue() {
        return defaultValue;
    }
}
