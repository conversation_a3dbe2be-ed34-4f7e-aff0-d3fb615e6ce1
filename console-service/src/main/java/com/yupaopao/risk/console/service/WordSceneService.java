package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.common.model.WordScene;
import com.yupaopao.risk.common.model.WordTag;
import com.yupaopao.risk.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface WordSceneService extends BaseService<WordScene> {

    /**
     * 根据wordId列表，查询word与场景之间的关系
     * @param wordIds
     * @return
     */
    List<WordScene> batchSearch(List<Long> wordIds);

    /**
     * 根据wordScenes（不含有id）去库中查询已存在的关系
     * @param wordScenes
     * @return
     */
    List<WordScene> search(List<WordScene> wordScenes);

    /**
     * 根据场景ID查询关系
     * @param sceneId
     * @return
     */
    public List<WordScene> selectBySceneId(long sceneId);

    /**
     * 根据词条ID查询关系
     *
     * @param wordId
     * @return
     */
    List<WordScene> selectByWordId(long wordId);

    boolean add(long wordId, List<Scene> scenes);

    /**
     * 更新word与scene关系,保留原关系
     *
     * @param wordId
     * @param scenes
     * @return
     */
    public boolean updatePart(long wordId, List<Scene> scenes);

    /**
     * 更新word与scene关系
     *
     * @param wordId
     * @param scenes
     * @return
     */
    public boolean update(long wordId, List<Scene> scenes);

    /**
     * 更新 审核系统 或 风控系统 或两个系统 下 word与scene关系
     * @param wordId
     * @param scenes
     * @param toSystem
     * @return
     */
    public boolean updateByToSystem(long wordId,List<Scene> scenes,Integer toSystem);

    /**
     * 根据wordId与toSystem来删除相应的关系
     *
     * @param wordId
     * @param toSystem
     * @return
     */
    boolean deleteByWordIdAndToSystem(Long wordId,Integer toSystem);

    /**
     * 根据sceneId来删除相应的关系
     *
     * @param sceneId
     * @return
     */
    boolean deleteBySceneId(long sceneId);

    /**
     * 批量删除
     *
     * @param wordScenes
     * @return
     */
    int batchDelete(List<WordScene> wordScenes);

    /**
     * 批量插入
     *
     * @param wordScenes
     * @return
     */
    int batchInsertList(List<WordScene> wordScenes);

}
