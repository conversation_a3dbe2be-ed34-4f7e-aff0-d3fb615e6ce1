package com.yupaopao.risk.console.service;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.common.vo.AttributeVO;
import com.yupaopao.risk.console.bean.AttributeReq;
import com.yupaopao.risk.console.bean.AttributeRuleReq;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public interface AttributeService extends BaseService<Attribute> {

    PageInfo<Attribute> queryLocal(int page, int size, String likeValue);

    PageInfo<Attribute> queryRemote(int page, int size, String likeValue);

    List<Attribute> relationFetch(List<Attribute> list);

    List<Attribute> queryRemote(List<String> names);

    List<Attribute> queryLocal(List<String> names);

    List<Attribute> queryRemoteAndLocal(Set<String> names);

    List<Attribute> queryByFactorId(Long factorId);

    List<Attribute> list(AttributeReq req);

    boolean updateBizTypeFlag(AttributeVO attributeVO, User user);

    List<Attribute> listByRuleId(AttributeRuleReq req); // 根据规则ID、属性类型获取列表

    List<String> listAllNames();
}
