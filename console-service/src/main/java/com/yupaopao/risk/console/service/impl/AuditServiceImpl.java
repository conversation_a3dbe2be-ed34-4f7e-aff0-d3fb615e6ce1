package com.yupaopao.risk.console.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageInfo;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.common.model.ReissueLog;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.TopicConstants;
import com.yupaopao.risk.console.bean.AuditReissueCondition;
import com.yupaopao.risk.console.bean.AuditReissueConf;
import com.yupaopao.risk.console.bean.ReissueLogVO;
import com.yupaopao.risk.console.enums.ReissueTypeEnum;
import com.yupaopao.risk.console.enums.TimeDegreeEnum;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.vo.ReissueVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审核相关
 */
@Slf4j
@Service
public class AuditServiceImpl implements AuditService {

    @ApolloConfig("middleware.event-mapping-pub")
    private Config eventMappingConfig;
    @ApolloConfig("audit-reissue-mapping")
    private Config auditMapping;
    @Autowired
    private ElasticSearchService elasticSearchService;
    @KafkaAutowired("middleware.kafka.risk")
    private KafkaProducer kafkaProducer;
    @Autowired
    private AlarmJobService alarmJobService;
    @Autowired
    private ReissueLogService reissueLogService;
    @Autowired
    private EventService eventService;
    @Value("${reissue.switch:true}")
    private boolean reissueSwitch;

    private static final long THREE_MIN = 180000;
    private static Set<String> eventSet;
    private static Map<String, AuditReissueConf> auditReissueMap = new ConcurrentHashMap<>();
    private static final String NOT = "!=";

    @Override
    public boolean isNeedAudit(String eventCode) {
        return eventSet.contains(eventCode);
    }

    @Override
    public Set<String> getAuditEvents() {
        return eventSet;
    }

    @PostConstruct
    public void init() {
        Set<String> set = new HashSet<>();
        eventMappingConfig.getPropertyNames().forEach(key -> {
            set.add(key);
        });
        eventSet = set;
        eventMappingConfig.addChangeListener(event -> {
            Set<String> keys = event.changedKeys();
            if (CollectionUtils.isNotEmpty(keys)) {
                keys.forEach(key -> { // key即为eventCode
                    PropertyChangeType type = event.getChange(key).getChangeType();
                    if (type == PropertyChangeType.DELETED) {
                        eventSet.remove(key);
                    } else {
                        eventSet.add(key);
                    }
                });
            }
        });
        auditMapping.getPropertyNames().forEach(key -> {
            addMapping(key);
        });
        auditMapping.addChangeListener(event -> {
            Set<String> keys = event.changedKeys();
            if (CollectionUtils.isNotEmpty(keys)) {
                keys.forEach(key -> { // key即为eventCode
                    PropertyChangeType type = event.getChange(key).getChangeType();
                    if (type == PropertyChangeType.DELETED) {
                        auditReissueMap.remove(key);
                    } else {
                        addMapping(key);
                    }
                });
            }
        });
    }

    private void addMapping(String key) {
        try {
            String config = auditMapping.getProperty(key, "");
            AuditReissueConf info = JSON.parseObject(config, AuditReissueConf.class);
            if (info == null) {
                log.info("审核数据补偿mapping解析为空 key:{}", key);
                return;
            }
            if (info.getEnd() >= info.getStart()) {
                log.info("审核数据补偿mapping解析 结束时间点不能小于开始时间点 key:{}", key);
                return;
            }
            if (StringUtils.isNotEmpty(info.getDegree()) && !TimeDegreeEnum.exist(info.getDegree())) {
                log.info("审核数据补偿mapping解析 时间精度不存在 key:{}", key);
                return;
            }
            auditReissueMap.put(key, info);
        } catch (Exception e) {
            log.info("审核数据补偿mapping解析异常 key:{}", key, e);
        }
    }

    @Override
    public Map<String, AuditReissueConf> getAuditReissueMap() {
        return auditReissueMap;
    }

    @Override
    public boolean reissue(ReissueVO reissueVO, User user) {
        try {
            JSONObject json = JSON.parseObject(reissueVO.getLog());
            if (json == null) {
                log.info("数据补偿 json为空 reissueVO:{}", reissueVO);
                return false;
            }
            boolean result = doReissue(reissueVO);
            ReissueLog reissueLog = new ReissueLog();
            reissueLog.setEventCode(json.getString("eventCode"));
            reissueLog.setReissueType(reissueVO.getReissueType());
            reissueLog.setTaskStatus(1);
            if (result) {
                reissueLog.setSuccessCount(1);
            } else {
                reissueLog.setFailCount(1);
            }
            reissueLog.setModifier(user.getName());
            Date createTime = json.getDate("createdAt");
            reissueLog.setStartTime(createTime);
            reissueLog.setEndTime(createTime);
            reissueLogService.insertSelective(reissueLog);
            return true;
        } catch (Exception e) {
            log.error("数据补偿 执行异常 reissueVO:{}", reissueVO, e);
        }
        return false;
    }

    @Override
    public boolean doReissue(ReissueVO reissueVO) {
        log.info("数据补偿开始");
        try {
            String result = reissueVO.getLog();
            if (StringUtils.isEmpty(result)) {
                log.info("数据补偿 result为空");
                return false;
            }
            JSONObject json = JSON.parseObject(result);
            if (json == null) {
                log.info("数据补偿 json为空 reissueVO:{}", reissueVO);
                return false;
            }
            String batchId = json.getJSONObject("data").getString("BatchId");
            if (StringUtils.isNotBlank(batchId)) {
                long uid = json.getLong("userId");
                Date createTime = json.getDate("createdAt");
                PageInfo<Map<String, Object>> pageInfo = getBatchData(batchId, uid, null, createTime);
                if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                    for (Map<String, Object> map : pageInfo.getList()) {
                        sendMsg(map, reissueVO);
                    }
                } else {
                    log.info("数据补偿 查询es list为空 reissueVO:{}", reissueVO);
                    return false;
                }
            } else {
                sendMsg(json, reissueVO);
            }
            log.info("数据补偿成功");
            return true;
        } catch (Exception e) {
            log.error("数据补偿 执行异常 reissueVO:{}", reissueVO, e);
            return false;
        }
    }

    @Override
    public boolean reissueAuditNotify(ReissueVO reissueVO, User user) {
        try {
            JSONObject json = JSON.parseObject(reissueVO.getLog());
            if (json == null) {
                log.info("数据补偿 json为空 reissueVO:{}", reissueVO);
                return false;
            }
            boolean result = doReissueAuditNotify(reissueVO);
            ReissueLog reissueLog = new ReissueLog();
            reissueLog.setReissueType(reissueVO.getReissueType());
            reissueLog.setTaskStatus(1);
            reissueLog.setModifier(user.getName());
            reissueLog.setEventCode(json.getString("eventCode"));
            if (result) {
                reissueLog.setSuccessCount(1);
            } else {
                reissueLog.setFailCount(1);
            }
            reissueLog.setModifier(user.getName());
            Date createTime = json.getDate("createdAt");
            reissueLog.setStartTime(createTime);
            reissueLog.setEndTime(createTime);
            reissueLogService.insertSelective(reissueLog);
            return true;
        } catch (Exception e) {
            log.error("数据补偿 执行异常 reissueVO:{}", reissueVO, e);
        }
        return false;
    }

    @Override
    public boolean doReissueAuditNotify(ReissueVO reissueVO) {
        log.info("人审数据补偿开始");
        try {
            String result = reissueVO.getLog();
            if (StringUtils.isEmpty(result)) {
                log.info("人审数据补偿 result为空");
                return false;
            }
            JSONObject json = JSON.parseObject(result);
            if (json == null) {
                log.info("人审数据补偿 json为空 reissueVO:{}", reissueVO);
                return false;
            }
            Date createTime = json.getDate("createdAt");

            //批次数据list，根据batchId+traceId依次查询risk_audit_notify_log进行通知补偿
            Set<String> set = new HashSet<>();
            String batchId = json.getJSONObject("data").getString("BatchId");
            if (StringUtils.isNotBlank(batchId)) {
                long uid = json.getLong("userId");
                PageInfo<Map<String, Object>> pageInfo = getBatchData(batchId, uid, null, createTime);
                if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                    for (Map<String, Object> map : pageInfo.getList()) {
                        set.add(String.valueOf(map.get("traceId")));
                    }
                } else {
                    log.info("人审数据补偿 根据BatchId查询es list为空 reissueVO:{}", reissueVO);
                    return false;
                }
            } else {
                set.add(json.getString("traceId"));
            }
            //一期根据traceId补发，二期批次数据直接根据batchId查询即可(需确认审核回调入参batchId)
            for (String traceId : set) {
                PageInfo<Map<String, Object>> pageInfo = getAuditNotifyLog(traceId, createTime);
                if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                    //因补偿逻辑 es中可能存在多条记录 此处限制补偿只取最后一条记录
                    Map<String, Object> map = pageInfo.getList().get(0);
                    map.put("reissue", 1);
                    sendMsg(map, reissueVO);
                } else {
                    log.info("人审数据补偿 根据traceId查询es list为空 reissueVO:{}", reissueVO);
                    return false;
                }
            }
            log.info("人审数据补偿成功");
            return true;
        } catch (Exception e) {
            log.error("人审数据补偿 执行异常 reissueVO:{}", reissueVO, e);
            return false;
        }
    }

    @Override
    public boolean batchReissue(ReissueLogVO record, User user) {
        String content;
        int success = 0;
        int fail = 0;
        ReissueLog reissueLog = new ReissueLog();
        reissueLog.setId(record.getId());
        String taskType = ReissueTypeEnum.getDescByCode(record.getReissueType());
        String eventName = "";
        try {
            log.info("批量补偿开始");
            //钉钉通知
            eventName = eventService.getEventName(record.getEventCode());
            content = "【补偿任务开始通知】\n事件：" + eventName + "， \n操作人：" + user.getName() + "，\n任务类型：" + taskType +
                    "，\n预计补偿条数：" + record.getCount() + "，\n任务内容：" + JSON.toJSONString(record) + "。";
            alarmJobService.alarm(content);
            //根据补偿类型查询es批量数据，依次调用单条补偿方法
            List<String> levels = getLevels(record.getLevels());
            List<AuditReissueCondition> conditions = getConditions(record.getCondition());
            Set<String> batchIds = new HashSet<>(4096);
            String eventCode = record.getEventCode();
            //时间分段处理 按时间逆序查询
            Date end;
            Date start = record.getEndTime();
            while (start.getTime() > record.getStartTime().getTime()) {
                end = start;
                start = getStartTime(start, record.getInterval());
                start = start.getTime() > record.getStartTime().getTime() ? start : record.getStartTime();
                PageInfo<Map<String, Object>> pageInfo = getHitLogData(start, end, eventCode, levels, conditions);
                List<Map<String, Object>> list = pageInfo.getList();
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                //一页查询数过多
                if (list.size() > 9000) {
                    log.info("批量补偿 当前页数据过多 请及时修正查询时间间隔 事件:{} start:{} end:{} size:{}", eventName, start, end, list.size());
                    alarmJobService.alarm("批量补偿 当前页数据过多 \n事件:" + eventName + " \nsize:" + list.size());
                }
                for (Map<String, Object> hitLog : list) {
                    JSONObject json = new JSONObject(hitLog);
                    String batchId = json.getJSONObject("data").getString("BatchId");
                    //batchId批次数据过滤
                    if (StringUtils.isNotEmpty(batchId) && batchIds.contains(batchId)) {
                        continue;
                    }
                    boolean result = false;
                    switch (record.getReissueType()) {
                        case 0:
                            result = doReissue(new ReissueVO(JSON.toJSONString(hitLog), TopicConstants.RISK_AUDIT_REISSUE));
                            break;
                        case 1:
                            result = doReissue(new ReissueVO(JSON.toJSONString(hitLog), TopicConstants.RISK_BUSINESS_REISSUE));
                            break;
                        case 2:
                            result = doReissueAuditNotify(new ReissueVO(JSON.toJSONString(hitLog), TopicConstants.RISK_BUSINESS_REISSUE, 2));
                            break;
                        default:
                            break;
                    }
                    //统计成功/失败数
                    if (result) {
                        success++;
                        batchIds.add(batchId);
                    } else {
                        fail++;
                    }
                }
            }
            log.info("批量补偿成功");
            reissueLog.setTaskStatus(1);
            reissueLog.setSuccessCount(success);
            reissueLog.setFailCount(fail);
            return true;
        } catch (Exception e) {
            reissueLog.setTaskStatus(2);
            log.error("批量补偿异常", e);
        } finally {
            //入库
            reissueLogService.updateSelectiveById(reissueLog);
            //钉钉通知
            content = "【补偿任务结束通知】\n事件：" + eventName + "， \n操作人：" + user.getName() + "，\n任务类型：" + taskType +
                    "，\n实际补偿条数：" + (success + fail) + "，\n任务内容：" + JSON.toJSONString(record) + "。";
            alarmJobService.alarm(content);
        }
        return false;
    }

    @Override
    public int previewBatchReissue(ReissueLogVO record, User user) {
        int count = 0;
        try {
            log.info("批量补偿预览开始");
            //根据补偿类型查询es批量数据
            List<String> levels = getLevels(record.getLevels());
            List<AuditReissueCondition> conditions = getConditions(record.getCondition());
            String eventCode = record.getEventCode();
            Set<String> batchIds = new HashSet<>(4096);
            //时间分段处理 按时间逆序查询
            Date end;
            Date start = record.getEndTime();
            while (start.getTime() > record.getStartTime().getTime()) {
                end = start;
                start = getStartTime(start, record.getInterval());
                start = start.getTime() > record.getStartTime().getTime() ? start : record.getStartTime();
                PageInfo<Map<String, Object>> pageInfo = getHitLogData(start, end, eventCode, levels, conditions);
                List<Map<String, Object>> list = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(list)) {
                    for (Map<String, Object> hitLog : list) {
                        JSONObject json = new JSONObject(hitLog);
                        String batchId = json.getJSONObject("data").getString("BatchId");
                        //batchId批次数据过滤
                        if (StringUtils.isNotEmpty(batchId) && batchIds.contains(batchId)) {
                            continue;
                        }
                        batchIds.add(batchId);
                        count++;
                    }

                }
            }
            log.info("批量补偿预览成功");
        } catch (Exception e) {
            log.error("批量补偿预览异常", e);
        }
        return count;
    }

    private List<String> getLevels(String levels) {
        try {
            if (StringUtils.isNotEmpty(levels)) {
                return Arrays.asList(levels.split(","));
            }
        } catch (Exception e) {
            log.error("数据批量补偿 解析level异常", e);
        }
        return new ArrayList<>();
    }

    private List<AuditReissueCondition> getConditions(String condition) {
        try {
            if (StringUtils.isNotEmpty(condition)) {
                return JSON.parseArray(condition, AuditReissueCondition.class);
            }
        } catch (Exception e) {
            log.error("数据批量补偿 解析condition异常", e);
        }
        return new ArrayList<>();
    }

    private Date getStartTime(Date end, Integer interval) {
        interval = (interval == null || interval <= 0) ? 30 : interval;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(end);
        calendar.add(Calendar.MINUTE, -interval);
        return calendar.getTime();
    }

    private LogSearchVO.HitLogSearchVO getVo(String eventCode, Date start, Date end) {
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setPage(1);
        vo.setSize(10000);
        vo.setStartTime(start);
        vo.setEndTime(end);
        HitResult hitResult = new HitResult();
        hitResult.setEventCode(eventCode);
        vo.setQuery(hitResult);
        return vo;
    }

    private void sendMsg(Map json, ReissueVO vo) {
        if (!reissueSwitch) {
            return;
        }
        json.put("reissueType", vo.getReissueType());
        // 通过MQ转发数据到magic处理补偿
        ListenableFuture<SendResult<String, String>> future = kafkaProducer.send(vo.getTopic(), JSON.toJSONString(json));
        future.addCallback(r -> log.info("数据补偿 转发数据流到magic成功"), e -> log.error("数据补偿 转发数据流到magic失败 json:{}", json, e));
    }

    private LogSearchVO.HitLogSearchVO getVo(Date time, String eventCode) {
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setPage(1);
        vo.setSize(100);
        vo.setStartTime(new Date(time.getTime() - THREE_MIN));
        vo.setEndTime(new Date(time.getTime() + THREE_MIN));
        HitResult hitResult = new HitResult();
        if (StringUtils.isNotBlank(eventCode)) {
            hitResult.setEventCode(eventCode);
        }
        vo.setQuery(hitResult);
        return vo;
    }

    private PageInfo<Map<String, Object>> getBatchData(String batchId, Long uid, String eventCode, Date time) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            SearchRequest searchRequest = RiskLogESUtil.buildSearch(getVo(time, eventCode), RiskLogESUtil.HIT_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            query.must(QueryBuilders.termQuery("data.BatchId", batchId));
            query.must(QueryBuilders.termQuery("userId", uid));
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补偿 获取文档getBatchData异常", e);
        }
        return pageInfo;
    }

    @Override
    public PageInfo<Map<String, Object>> getExclusion(String[] certIds, String eventCode, Date start, Date end) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            SearchRequest searchRequest = RiskLogESUtil.buildSearch(getVo(eventCode, start, end), RiskLogESUtil.HIT_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            for (int i = 0; i < certIds.length; i++) {
                queryBuilder.should(QueryBuilders.termQuery("data.certId", certIds[i]));
            }
            query.must(queryBuilder);
            query.must(QueryBuilders.termQuery("data.type", "other"));
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补偿 获取文档getExclusion异常", e);
        }
        return pageInfo;
    }

    private PageInfo<Map<String, Object>> getAuditNotifyLog(String traceId, Date time) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            LogSearchVO vo = new LogSearchVO();
            vo.setPage(1);
            //取最后一条人审记录
            vo.setSize(1);
            vo.setStartTime(new Date(time.getTime() - THREE_MIN));
            vo.setEndTime(new Date());
            SearchRequest searchRequest = RiskLogESUtil.buildSearch(vo, RiskLogESUtil.AUDIT_NOTIFY_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            query.must(QueryBuilders.termQuery("traceId", traceId));
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.AUDIT_NOTIFY_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补偿 获取文档getAuditNotifyLog异常", e);
        }
        return pageInfo;
    }

    @Override
    public PageInfo<Map<String, Object>> getHitLogData(Date start, Date end, String eventCode, List<String> levels, List<AuditReissueCondition> conditions) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            SearchRequest searchRequest = RiskLogESUtil.buildSearch(getVo(eventCode, start, end), RiskLogESUtil.HIT_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            if (CollectionUtils.isNotEmpty(levels)) {
                BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
                for (String level : levels) {
                    queryBuilder.should(QueryBuilders.termQuery("level", level.toUpperCase()));
                }
                query.must(queryBuilder);
            }
            //过滤回溯数据 需判断是否人审事件 防止个别无人审事件含有source字段
            if (getAuditEvents().contains(eventCode)) {
                query.mustNot(QueryBuilders.termQuery("data.source", "1"));
            }
            //配置化条件
            if (CollectionUtils.isNotEmpty(conditions)) {
                for (AuditReissueCondition conditon : conditions) {
                    Object value = conditon.getValue();
                    String expression = conditon.getExpression();
                    if (value instanceof JSONArray) {
                        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
                        for (int i = 0; i < ((JSONArray) value).size(); i++) {
                            queryBuilder.should(QueryBuilders.termQuery(conditon.getKey(), ((JSONArray) value).get(i)));
                        }
                        if (NOT.equals(expression)) {
                            query.mustNot(queryBuilder);
                        } else {
                            query.must(queryBuilder);
                        }
                    } else {
                        if (NOT.equals(expression)) {
                            query.mustNot(QueryBuilders.termQuery(conditon.getKey(), conditon.getValue()));
                        } else {
                            query.must(QueryBuilders.termQuery(conditon.getKey(), conditon.getValue()));
                        }
                    }
                }
            }
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补偿 获取文档getAuditData1异常", e);
        }
        return pageInfo;
    }

    @Override
    public boolean isNeedReissue(String traceId, String batchId, Date startTime) {
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        try {
            LogSearchVO vo = new LogSearchVO();
            vo.setPage(1);
            vo.setSize(1);
            vo.setStartTime(startTime);
            vo.setEndTime(new Date());
            SearchRequest searchRequest = RiskLogESUtil.buildSearch(vo, RiskLogESUtil.AUDIT_NOTIFY_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            if (StringUtils.isEmpty(batchId)) {
                query.must(QueryBuilders.termQuery("traceId", traceId));
            } else {
                query.must(QueryBuilders.termQuery("batchId", batchId));
            }
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.AUDIT_NOTIFY_LOG_INDEX);
            return CollectionUtils.isEmpty(pageInfo.getList());
        } catch (Exception e) {
            log.error("审核数据补偿 获取文档getData异常", e);
            //异常默认补偿
            return true;
        }
    }

    public void handleResponse(SearchRequest request, PageInfo pageInfo, String index) {
        try {
            SearchResponse response = elasticSearchService.getClient().search(request);
            if (response != null && response.status() == RestStatus.OK) {
                RiskLogESUtil.handleResponse(response, pageInfo);
            } else {
                log.error("审核数据补偿 搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("审核数据补偿 搜索文档异常", e);
        }
    }
}
