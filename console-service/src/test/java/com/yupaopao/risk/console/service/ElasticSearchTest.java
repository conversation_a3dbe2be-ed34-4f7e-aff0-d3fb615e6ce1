package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpHost;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.action.support.replication.ReplicationResponse;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.ScriptQueryBuilder;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.tophits.TopHits;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/2/21 5:04 PM
 */
@Slf4j
public class ElasticSearchTest {
    private static final String DATE_FORMART = "yyyy-MM-dd\'T\'HH:mm:ss+08:00";
    private static final String pattern_default = "yyyyMMdd";

    private RestHighLevelClient client;
    private String index;

    @Before
    public void init() {
        RestClientBuilder builder = RestClient.builder(new HttpHost("test-es-new.yupaopao.com", 80, "http"));
//        RestClientBuilder builder = RestClient.builder(new HttpHost("localhost", 9200, "http"));
        client = new RestHighLevelClient(builder);
        index = "risk_hit_log_" + DateFormatUtils.format(new Date(), "yyyyMMdd");
    }

    public RestHighLevelClient getClient() {
        return client;
    }

    public void setClient(RestHighLevelClient client) {
        this.client = client;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    @Test
    public void index() {
        try {
            // 1、创建索引请求
            IndexRequest request = new IndexRequest(index, "log", "477ae7b23e8a4eaa9ad987b77dd0c391");

            // 2、准备文档数据
            // 方式一：直接给JSON串
            String json = "{\n" +
                    "\t\"traceId\": \"ac76654cdffc4aba88d3512be3ecc122\",\n" +
                    "\t\"safeDeviceCheck\": {\n" +
                    "\t\t\"reason\": \"无任何登录记录\",\n" +
                    "\t\t\"riskLevel\": \"REVIEW\"\n" +
                    "\t},\n" +
                    "\t\"level\": \"REVIEW\",\n" +
                    "\t\"DeviceId\": \"20181212110135a7c46eb0da6b4fe0dff74a417665bf08018ef31a1fbdc738\",\n" +
                    "\t\"Timeout\": \"3000\",\n" +
                    "\t\"clientIpIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"userIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"ClientIp\": \"*************\",\n" +
                    "\t\"Mobile\": \"15000000015\",\n" +
                    "\t\"mobileIsWhite\": {\n" +
                    "\t\t\"riskLevel\": \"REVIEW\"\n" +
                    "\t},\n" +
                    "\t\"returnMap\": {},\n" +
                    "\t\"eventCode\": \"user-login\",\n" +
                    "\t\"clientIpDetail\": {\n" +
                    "\t\t\"country\": \"0\",\n" +
                    "\t\t\"riskLevel\": \"PASS\",\n" +
                    "\t\t\"province\": \"0\",\n" +
                    "\t\t\"city\": \"内网IP\",\n" +
                    "\t\t\"isp\": \"内网IP\",\n" +
                    "\t\t\"cityId\": 0,\n" +
                    "\t\t\"region\": \"0\"\n" +
                    "\t},\n" +
                    "\t\"createdAt\": 1551167914804,\n" +
                    "\t\"UserId\": \"8a9c9c0467868af601679600d8c2000d\",\n" +
                    "\t\"success\": true,\n" +
                    "\t\"Event\": \"user-login\",\n" +
                    "\t\"TraceId\": \"ac76654cdffc4aba88d3512be3ecc122\",\n" +
                    "\t\"mobileIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"loginCheck\": {},\n" +
                    "\t\"Level\": \"REVIEW\",\n" +
                    "\t\"Business\": \"userService\"\n" +
                    "}";
            request.source(json, XContentType.JSON);

            // 方式二：以map对象来表示文档
            /*
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("user", "kimchy");
            jsonMap.put("postDate", new Date());
            jsonMap.put("message", "trying out Elasticsearch");
            request.source(jsonMap);
            */

            //3、其他的一些可选设置
//            request.routing("routing");  //设置routing值
//            request.timeout(TimeValue.timeValueSeconds(1));  //设置主分片等待时长
//            request.setRefreshPolicy("wait_for");  //设置重刷新策略
//            request.version(2);  //设置版本号
//            request.opType(DocWriteRequest.OpType.CREATE);  //操作类别

            //4、发送请求
            IndexResponse response = null;
            try {
                // 同步方式
                response = client.index(request);
            } catch (ElasticsearchException e) {
                // 捕获，并处理异常
                //判断是否版本冲突、create但文档已存在冲突
                if (e.status() == RestStatus.CONFLICT) {
                    log.error("冲突了，请在此写冲突处理逻辑！\n" + e.getDetailedMessage());
                }
                log.error("索引异常", e);
            }

            //5、处理响应
            if (response != null) {
                String index = response.getIndex();
                String type = response.getType();
                String id = response.getId();
                long version = response.getVersion();
                if (response.getResult() == DocWriteResponse.Result.CREATED) {
                    log.info("添加索引成功");
                } else if (response.getResult() == DocWriteResponse.Result.UPDATED) {
                    log.info("修改索引成功");
                }
                // 分片处理信息
                ReplicationResponse.ShardInfo shardInfo = response.getShardInfo();
                if (shardInfo.getTotal() != shardInfo.getSuccessful()) {

                }
                // 如果有分片副本失败，可以获得失败原因信息
                if (shardInfo.getFailed() > 0) {
                    for (ReplicationResponse.ShardInfo.Failure failure : shardInfo.getFailures()) {
                        log.error("副本失败原因:{}", failure.reason());
                    }
                }
            }
            //异步方式发送索引请求
            /*ActionListener<IndexResponse> listener = new ActionListener<IndexResponse>() {
                @Override
                public void onResponse(IndexResponse response) {

                }

                @Override
                public void onFailure(Exception e) {

                }
            };
            client.indexAsync(request, listener);
            */

        } catch (Exception e) {
            log.error("请求ES添加索引失败", e);
        }
    }

    @Test
    public void get() {
        GetRequest request = new GetRequest(index, "user-login", "477ae7b23e8a4eaa9ad987b77dd0c391");
        GetResponse response = null;
        try {
            response = client.get(request);// 同步请求
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }

        //4、处理响应
        if (response != null) {
            String index = response.getIndex();
            String type = response.getType();
            String id = response.getId();
            if (response.isExists()) { // 文档存在
                log.info("index:" + index + "  type:" + type + "  id:" + id);
                log.info("文档:{}", response.getSource());
            } else {
                log.error("没有找到该id的文档");
            }
        }
    }

    @Test
    public void search() {
        SearchRequest request = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchAllQuery());
//        boolQuery.filter(QueryBuilders.termQuery("Mobile", "***********"));
        boolQuery.filter(QueryBuilders.rangeQuery("createdAt").gte(System.currentTimeMillis() - 1000 * 60 * 60 * 6));
        boolQuery.filter(QueryBuilders.termQuery("level", "REJECT"));
        boolQuery.filter(QueryBuilders.termQuery("Event", "wechat-h5-pay-pre"));
//        boolQuery.filter(QueryBuilders.termQuery("success", true));
//        boolQuery.filter(QueryBuilders.termQuery("Business", "payment"));
//        boolQuery.filter(QueryBuilders.termQuery("ClientIp", "************"));
//        boolQuery.filter(QueryBuilders.termQuery("rid", "20181218165933a29d3f86274853b140"));
//        boolQuery.filter(QueryBuilders.termQuery("avatar", "http://pic32.photophoto.cn/20140825/0036036889302739_b.jpg"));
        System.out.println(boolQuery.toString());
        request.source().query(boolQuery);
        request.source().from(0);
        request.source().size(5);
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        SearchResponse response = null;
        try {
            response = client.search(request);
        } catch (Exception e) {
            log.error("搜索文档异常", e);
        }

        //4、处理响应
        if (response != null && response.status() == RestStatus.OK) {
            SearchHits hits = response.getHits();
            if (hits != null && hits.totalHits > 0) {
                log.info("搜索结果数量:{},耗时:{}", hits.totalHits, response.getTook());
                hits.forEach(hit -> log.info("命中记录:{}", hit.getSourceAsMap()));
            } else {
                log.info("无搜索结果");
            }
        } else {
            log.info("搜索失败");
        }
    }

    @Test
    public void test01() {
        String json = "{\n" +
                "  \"safeDeviceCheck\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"DeviceId\": \"20190113141033898e88730daff05fc38396b4d785d7c80168f59fbd59ee87\",\n" +
                "  \"Timeout\": \"3000\",\n" +
                "  \"clientIpIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"userIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"ClientIp\": \"**************\",\n" +
                "  \"Mobile\": \"13824676396\",\n" +
                "  \"mobileIsWhite\": {\n" +
                "    \"riskLevel\": \"REVIEW\"\n" +
                "  },\n" +
                "  \"clientIpDetail\": {\n" +
                "    \"country\": \"中国\",\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"province\": \"广东省\",\n" +
                "    \"city\": \"云浮市\",\n" +
                "    \"isp\": \"电信\",\n" +
                "    \"cityId\": 2273,\n" +
                "    \"region\": \"华南\"\n" +
                "  },\n" +
                "  \"UserId\": \"44020da70884420c98fb23814b46e703\",\n" +
                "  \"Event\": \"user-login\",\n" +
                "  \"TraceId\": \"4fa7a9f228d442fc84a78cf0d087b648\",\n" +
                "  \"mobileIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"loginCheck\": {\n" +
                "    \"score\": 0,\n" +
                "    \"code\": 1100,\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"requestId\": \"269255e5d3f61d44018fc6f1f2c5cccb\",\n" +
                "    \"detail\": {\n" +
                "      \"hits\": [],\n" +
                "      \"relatedItems\": [],\n" +
                "      \"description\": \"正常\",\n" +
                "      \"model\": \"M1000\"\n" +
                "    },\n" +
                "    \"message\": \"成功\"\n" +
                "  },\n" +
                "  \"Business\": \"userService\"\n" +
                "}";
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            HitResult result = JSONObject.parseObject(json, HitResult.class);
            json = JSONObject.toJSONString(result);
            result = JSONObject.parseObject(json, HitResult.class);
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    @Test
    public void test02() {
        String json = "{\n" +
                "  \"safeDeviceCheck\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"DeviceId\": \"20190113141033898e88730daff05fc38396b4d785d7c80168f59fbd59ee87\",\n" +
                "  \"Timeout\": \"3000\",\n" +
                "  \"clientIpIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"userIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"ClientIp\": \"**************\",\n" +
                "  \"Mobile\": \"13824676396\",\n" +
                "  \"mobileIsWhite\": {\n" +
                "    \"riskLevel\": \"REVIEW\"\n" +
                "  },\n" +
                "  \"clientIpDetail\": {\n" +
                "    \"country\": \"中国\",\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"province\": \"广东省\",\n" +
                "    \"city\": \"云浮市\",\n" +
                "    \"isp\": \"电信\",\n" +
                "    \"cityId\": 2273,\n" +
                "    \"region\": \"华南\"\n" +
                "  },\n" +
                "  \"UserId\": \"44020da70884420c98fb23814b46e703\",\n" +
                "  \"Event\": \"user-login\",\n" +
                "  \"TraceId\": \"4fa7a9f228d442fc84a78cf0d087b648\",\n" +
                "  \"mobileIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"loginCheck\": {\n" +
                "    \"score\": 0,\n" +
                "    \"code\": 1100,\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"requestId\": \"269255e5d3f61d44018fc6f1f2c5cccb\",\n" +
                "    \"detail\": {\n" +
                "      \"hits\": [],\n" +
                "      \"relatedItems\": [],\n" +
                "      \"description\": \"正常\",\n" +
                "      \"model\": \"M1000\"\n" +
                "    },\n" +
                "    \"message\": \"成功\"\n" +
                "  },\n" +
                "  \"Business\": \"userService\"\n" +
                "}";
        HitResult result = JSONObject.parseObject(json, HitResult.class);
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            try {
                PropertyUtils.describe(result);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    @Test
    public void searchTest() {
        SearchRequest request = new SearchRequest("risk_hit_log*");

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchAllQuery());
        boolQuery.filter(QueryBuilders.termQuery("traceId", "47dae091f7eb4cebbaeb0479b7b18054"));
        //boolQuery.filter(QueryBuilders.termQuery("id", "47dae091f7eb4cebbaeb0479b7b18054"));
        System.out.println(boolQuery.toString());
        request.source().query(boolQuery);
        request.source().from(0);
        request.source().size(5);
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        SearchResponse response = null;
        try {
            response = client.search(request);
        } catch (Exception e) {
            log.error("搜索文档异常", e);
        }

        //4、处理响应
        if (response != null && response.status() == RestStatus.OK) {
            SearchHits hits = response.getHits();
            if (hits != null && hits.totalHits > 0) {
                log.info("搜索结果数量:{},耗时:{}", hits.totalHits, response.getTook());
                hits.forEach(hit -> log.info("命中记录:{}", hit.getSourceAsMap()));
                System.out.println("hits:"+ JSON.toJSONString(hits));
            } else {
                log.info("无搜索结果");
            }
        } else {
            log.info("搜索失败");
        }
    }

    protected LinkedList<Map<String, Object>> search(String traceId, String index) {
        log.info("搜索请求:{}", traceId);
        LinkedList<Map<String, Object>> list = new LinkedList<>();
        try {
            SearchRequest request = buildSearchRequest(traceId, index);
            log.debug("搜索参数:\n{}", request.source().query());
            SearchResponse response = client.search(request);
            if (response != null && response.status() == RestStatus.OK) {
                SearchHits hits = response.getHits();
                log.info("命中数量:{}，耗时:{}", hits.getTotalHits(), response.getTook());
                hits.forEach(hit -> list.add(hit.getSourceAsMap()));
            } else {
                log.error("搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("搜索文档异常", e);
        }
        return list;
    }

    protected SearchRequest buildSearchRequest(String traceId, String index) {
        SearchRequest request = new SearchRequest(index);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.filter(QueryBuilders.termQuery("traceId", traceId));

        // 忽略无效索引
        IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
        request.indicesOptions(indicesOptions);
        request.source().query(query);
        request.source().sort("createdAt", SortOrder.DESC);
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        return request;
    }

    @Test
    public void searchTest2() {
        try {
            LinkedList<Map<String,Object>> list = search("9f28ab556a4748f29769ce05b8087b5f","risk_hit_log");
            System.out.println("list:"+ JSON.toJSONString(list));
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }
    }

    @Test
    public void getTest() {
        try {
            index = "risk_hit_log";
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date start = format.parse("2019-07-10 00:00:00");
            Date end = format.parse("2019-07-30 23:59:59");

            LinkedList<String> indexList = new LinkedList();
            Calendar calendar = Calendar.getInstance();
            for (Date current = start; !current.after(end); ) {
                indexList.add(String.format("%s_%s", index, DateFormatUtils.format(current == null ? new Date() : current, pattern_default)));
                calendar.setTime(current);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                current = calendar.getTime();
            }
            String[] indexArr = indexList.toArray(new String[0]);
            System.out.println("indexArr:"+JSON.toJSONString(indexArr));

//            SearchRequest request = new SearchRequest("risk_hit_log");
            SearchRequest request = new SearchRequest(indexArr);
            BoolQueryBuilder query = QueryBuilders.boolQuery();

//            query.must(QueryBuilders.termQuery("userId", "10bbf58b1d2740adc3b9ca33dcbab125"));
            query.must(QueryBuilders.termQuery("eventCode", "im-message"));
//            query.mustNot(QueryBuilders.existsQuery("data.content"));
            /*String userId = "d57fd7eb46544c94907f456f2ddc3377";
            BoolQueryBuilder privateQuery = new BoolQueryBuilder();
            BoolQueryBuilder userIdQuery = new BoolQueryBuilder();
            userIdQuery.should(QueryBuilders.termQuery("data.UserId",userId));
            userIdQuery.should(QueryBuilders.termQuery("data.targetUserId",userId));
            privateQuery.must(userIdQuery);
            query.must(privateQuery);*/

            //query.must(QueryBuilders.existsQuery("full_text"));
//            query.filter(QueryBuilders.regexpQuery("full_text","^[\\s\\S]*.*[^\\s][\\s\\S]*$"));

            // 此处空是null，不是空字符串
//            query.mustNot(QueryBuilders.existsQuery("full_text")); // 查询full_text为空
//            query.must(QueryBuilders.existsQuery("full_text")); // 查message为非空的数据

//            query.must(QueryBuilders.existsQuery("full_text"))
//                    .must(QueryBuilders.wildcardQuery("full_text", "*"));

//            BoolQueryBuilder textQuery = new BoolQueryBuilder();
//            textQuery.should(QueryBuilders.existsQuery("data.body")); // IM单聊、私聊
//            textQuery.should(QueryBuilders.existsQuery("data.content")); // 聊天室

            // 时间范围
//            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
//            builder.gte(DateFormatUtils.format(start, DATE_FORMART));
//            builder.lte(DateFormatUtils.format(end, DATE_FORMART));
//            query.must(builder);

            // 忽略无效索引
            IndicesOptions indicesOptions = IndicesOptions.fromOptions(true, true, true, false);
            request.indicesOptions(indicesOptions);
            request.source().query(query);
            request.source().sort("createdAt", SortOrder.DESC);
            request.source().size(10000);
//            request.source().size(0); // 设置size为0是只要聚合结果的方式
            request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));

            // 聚合
            /*TermsAggregationBuilder agg = new TermsAggregationBuilder("1", ValueType.LONG);
            agg.field("userId");
//            agg.field("data.ext.toUid");
//            agg.size(0);
            request.source().aggregation(agg);*/

            /*String[] includes = {"userId","level","full_text","data.ext"};
            AggregationBuilder aggregation = AggregationBuilders.terms("1").field("data.ext.toUid").size((1<<31)-1)
                    .subAggregation(AggregationBuilders.topHits("top").fetchSource(includes, Strings.EMPTY_ARRAY).size(1));
            request.source().aggregation(aggregation);*/

            Map<String, Object> params = new HashMap<>();
            Script script = new Script(ScriptType.INLINE,"painless","doc['data.ext.uid'].value != doc['data.ext.toUid'].value",params);
            ScriptQueryBuilder scriptQueryBuilder = new ScriptQueryBuilder(script);
            query.must(scriptQueryBuilder);
            /*long uid = 190270857471470004l;
            long uid2 = 190450930281850020l;*/

            // 获取IM聊天内容方式
            /*BoolQueryBuilder uidQuery = new BoolQueryBuilder();
            BoolQueryBuilder userQuery = new BoolQueryBuilder();
            userQuery.must(QueryBuilders.termQuery("data.ext.uid",uid));
            userQuery.must(QueryBuilders.termQuery("data.ext.toUid",uid2));
            uidQuery.should(userQuery);

            BoolQueryBuilder userQuery2 = new BoolQueryBuilder();
            userQuery2.must(QueryBuilders.termQuery("data.ext.uid",uid2));
            userQuery2.must(QueryBuilders.termQuery("data.ext.toUid",uid));
            uidQuery.should(userQuery2);
            query.must(uidQuery);*/

            // 获取IM用户列表信息方式
            /*BoolQueryBuilder userQuery = new BoolQueryBuilder();
            BoolQueryBuilder uidQuery = new BoolQueryBuilder();
            uidQuery.should(QueryBuilders.termQuery("data.ext.uid",uid));
            uidQuery.should(QueryBuilders.termQuery("data.ext.toUid",uid));
            userQuery.must(uidQuery);
            query.must(userQuery);*/

            System.out.println(query);
            LinkedList<Map<String, Object>> list = new LinkedList<>();
            SearchResponse response = client.search(request);
            if (response != null && response.status() == RestStatus.OK) {
                SearchHits hits = response.getHits();
                log.info("命中数量:{}，耗时:{}", hits.getTotalHits(), response.getTook());
                hits.forEach(hit -> list.add(hit.getSourceAsMap()));
            } else {
                log.error("搜索失败:{} / {}", index, request.source().query());
            }
            System.out.println("list:"+list.size()+JSON.toJSONString(list));

            // 聚合的结果
            Aggregations aggregations = response.getAggregations();
            Terms aggResult = aggregations.get("1");
            for (Terms.Bucket entry : aggResult.getBuckets()) {
                String key = (String) entry.getKey(); // bucket key
                long docCount = entry.getDocCount(); // Doc count
                System.out.println("=============================:"+"key " + key + " doc_count " + docCount);
                TopHits topHits = entry.getAggregations().get("top");
                for(SearchHit hit : topHits.getHits()){
                    System.out.println(hit.getSourceAsMap());
                }
            }
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }
    }

    // user detail
    static void handleUserDetailData(){
        String data = "[{\"traceId\":\"81b4486153aa4e7bbb523c1fc40b07fd\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"81b4486153aa4e7bbb523c1fc40b07fd\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"81b4486153aa4e7bbb523c1fc40b07fd\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-05T14:24:05+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"6402199053fe4fa3b0b54ccb5bf98df5\",\"reason\":\"PASS\",\"data\":{\"userDeviceHit308Count7Days\":0.0,\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Async\":\"false\",\"Timeout\":\"2450\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"6402199053fe4fa3b0b54ccb5bf98df5\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"6402199053fe4fa3b0b54ccb5bf98df5\",\"reason\":\"PASS\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-05T11:47:37+08:00\",\"costTime\":225,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"4e390ccbe8854367acd015f8b3cf5517\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"4e390ccbe8854367acd015f8b3cf5517\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"4e390ccbe8854367acd015f8b3cf5517\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-05T11:16:29+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"a240d221b20b4fff8b522740d5f5e092\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"a240d221b20b4fff8b522740d5f5e092\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"a240d221b20b4fff8b522740d5f5e092\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T20:31:36+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"bb21eb48c18d48b6a42aefdc8268c8d6\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"bb21eb48c18d48b6a42aefdc8268c8d6\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"bb21eb48c18d48b6a42aefdc8268c8d6\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T20:21:42+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"53794e151d5f40558b544777b3c9000d\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"53794e151d5f40558b544777b3c9000d\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"53794e151d5f40558b544777b3c9000d\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T20:20:20+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"109a3c0293344b14ad7a921790d6fe75\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"109a3c0293344b14ad7a921790d6fe75\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"109a3c0293344b14ad7a921790d6fe75\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T20:16:06+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"dba182ac43f2421eafaae405705904b9\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"dba182ac43f2421eafaae405705904b9\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"dba182ac43f2421eafaae405705904b9\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T19:57:28+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"6057305f233a4c1bb45d531e22dcbb72\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"6057305f233a4c1bb45d531e22dcbb72\",\"ClientIp\":\"************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"6057305f233a4c1bb45d531e22dcbb72\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T15:57:22+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"************\"},{\"traceId\":\"2829219113234361a17a1f6f8dba761c\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"2829219113234361a17a1f6f8dba761c\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"2829219113234361a17a1f6f8dba761c\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-04T15:52:58+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"cdf668be03fa4c1f88d39d195c083bf9\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"cdf668be03fa4c1f88d39d195c083bf9\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"cdf668be03fa4c1f88d39d195c083bf9\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-03T16:43:29+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"30557e0201224630848393c29779ce43\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"30557e0201224630848393c29779ce43\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"30557e0201224630848393c29779ce43\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-03T15:49:13+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"66907de13dea482d9025f7d83303891b\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"66907de13dea482d9025f7d83303891b\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"66907de13dea482d9025f7d83303891b\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-03T14:44:53+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"9f762a79567f49b5b038cddc35784182\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"9f762a79567f49b5b038cddc35784182\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"9f762a79567f49b5b038cddc35784182\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-03T10:37:23+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"b2f753226087405e93a9878bdafc87f0\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"b2f753226087405e93a9878bdafc87f0\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"b2f753226087405e93a9878bdafc87f0\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-02T19:14:13+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"e99aa3c37f6e408493259e3817e738e4\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"e99aa3c37f6e408493259e3817e738e4\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"e99aa3c37f6e408493259e3817e738e4\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-02T14:46:10+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"dfb220bd8eca47fe826da16f4207d803\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"dfb220bd8eca47fe826da16f4207d803\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"dfb220bd8eca47fe826da16f4207d803\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-01T15:05:24+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"6e5ff202224a4db8a6e02d0fe0c34fd0\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"6e5ff202224a4db8a6e02d0fe0c34fd0\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"6e5ff202224a4db8a6e02d0fe0c34fd0\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-01T15:03:13+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"3e1a11bb4db04fbbbbdf56d92efda150\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"3e1a11bb4db04fbbbbdf56d92efda150\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"3e1a11bb4db04fbbbbdf56d92efda150\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-01T14:18:57+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"f9f1a5ded98b49e1bcef294ea9d7d600\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"f9f1a5ded98b49e1bcef294ea9d7d600\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"f9f1a5ded98b49e1bcef294ea9d7d600\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-01T14:13:14+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"001df2612fae4b71989c0f8266feab2e\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"001df2612fae4b71989c0f8266feab2e\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"001df2612fae4b71989c0f8266feab2e\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-07-01T10:30:33+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"4448ef3923bc4f7683d5a5c1c10f54e0\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"4448ef3923bc4f7683d5a5c1c10f54e0\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"4448ef3923bc4f7683d5a5c1c10f54e0\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T19:16:30+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"964bddcc66cf44939e464dba0dc0415a\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"964bddcc66cf44939e464dba0dc0415a\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"964bddcc66cf44939e464dba0dc0415a\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T19:15:23+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"39a9eb1f95374ffa8fdb265d8bd4ba5a\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"39a9eb1f95374ffa8fdb265d8bd4ba5a\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"39a9eb1f95374ffa8fdb265d8bd4ba5a\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T10:56:43+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"c95562e6f1ee4cbba80d5633ea1579c0\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"c95562e6f1ee4cbba80d5633ea1579c0\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"c95562e6f1ee4cbba80d5633ea1579c0\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T10:51:38+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"31a531eafe6e49e39f30f3555922b919\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"31a531eafe6e49e39f30f3555922b919\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"31a531eafe6e49e39f30f3555922b919\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T10:49:13+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"0aae1e444b0c493d8db00f9f8c2f1cd2\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"0aae1e444b0c493d8db00f9f8c2f1cd2\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"0aae1e444b0c493d8db00f9f8c2f1cd2\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T10:48:48+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"b403aa68924549318db42b7acd27432f\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"b403aa68924549318db42b7acd27432f\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"b403aa68924549318db42b7acd27432f\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-29T10:48:36+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"7c0bc19ff34749bd93e9f5757015fc72\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"7c0bc19ff34749bd93e9f5757015fc72\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"201904150500289eeb20c6d1ed8691d20a6d8eec9c3e550145ab6a9b55bc6a\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"7c0bc19ff34749bd93e9f5757015fc72\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-28T17:48:42+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"9b8c9eb484974d1d9e231f8c0aa2f2d8\",\"reason\":\"命中白名单\",\"data\":{\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"Event\":\"user-login\",\"TraceId\":\"9b8c9eb484974d1d9e231f8c0aa2f2d8\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"9b8c9eb484974d1d9e231f8c0aa2f2d8\",\"reason\":\"命中白名单\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-28T14:36:06+08:00\",\"costTime\":0,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"8d8df5274fa34387bfa136eb619d5e7e\",\"reason\":\"PASS\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"登陆次数:2.0\",\"riskLevel\":\"PASS\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"8d8df5274fa34387bfa136eb619d5e7e\",\"Business\":\"userService\"},\"level\":\"PASS\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"8d8df5274fa34387bfa136eb619d5e7e\",\"reason\":\"PASS\",\"businessCode\":\"userService\",\"level\":\"PASS\",\"success\":true},\"createdAt\":\"2019-06-27T11:44:51+08:00\",\"costTime\":182,\"full_text\":\"\",\"clientIp\":\"**************\"},{\"traceId\":\"6d97a191f8d64735b64d92db9a34373b\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"6d97a191f8d64735b64d92db9a34373b\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"6d97a191f8d64735b64d92db9a34373b\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-25T11:27:14+08:00\",\"costTime\":143,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"b206e076ecfe4acf8885964839822dc0\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"b206e076ecfe4acf8885964839822dc0\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"b206e076ecfe4acf8885964839822dc0\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T20:00:37+08:00\",\"costTime\":141,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"2396f26943934d1c82dc692571a1111f\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":1.0,\"Event\":\"user-login\",\"TraceId\":\"2396f26943934d1c82dc692571a1111f\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"2396f26943934d1c82dc692571a1111f\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:59:25+08:00\",\"costTime\":112,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"4545d6e98da64ff099ed8445747fb95f\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"4545d6e98da64ff099ed8445747fb95f\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"4545d6e98da64ff099ed8445747fb95f\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:59:05+08:00\",\"costTime\":154,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"fe6ccf57c011466aac396404698b6dd8\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":1.0,\"Event\":\"user-login\",\"TraceId\":\"fe6ccf57c011466aac396404698b6dd8\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"fe6ccf57c011466aac396404698b6dd8\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:50:26+08:00\",\"costTime\":127,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"18048a198e2541478d217c9e966200e7\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"18048a198e2541478d217c9e966200e7\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"18048a198e2541478d217c9e966200e7\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:46:15+08:00\",\"costTime\":108,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"ad88246bea1f4559bd7412da0a32f11a\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"ad88246bea1f4559bd7412da0a32f11a\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"ad88246bea1f4559bd7412da0a32f11a\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:43:50+08:00\",\"costTime\":111,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"ee481fcc0fec4592a0109e798ee7c6d7\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":4.0,\"Event\":\"user-login\",\"TraceId\":\"ee481fcc0fec4592a0109e798ee7c6d7\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"ee481fcc0fec4592a0109e798ee7c6d7\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:27:44+08:00\",\"costTime\":110,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"3647449b5e7745029ca1addb83664427\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":3.0,\"Event\":\"user-login\",\"TraceId\":\"3647449b5e7745029ca1addb83664427\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"3647449b5e7745029ca1addb83664427\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:26:16+08:00\",\"costTime\":111,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"843534ae855846c18a5932e0e3b1eac9\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":2.0,\"Event\":\"user-login\",\"TraceId\":\"843534ae855846c18a5932e0e3b1eac9\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"843534ae855846c18a5932e0e3b1eac9\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:25:49+08:00\",\"costTime\":110,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"6eb34e8d36c04bdb99d9e601d51b6472\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":1.0,\"Event\":\"user-login\",\"TraceId\":\"6eb34e8d36c04bdb99d9e601d51b6472\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"6eb34e8d36c04bdb99d9e601d51b6472\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:25:34+08:00\",\"costTime\":107,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"3bb92f0851b345d0bda829b265ac27d3\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":1.0,\"Event\":\"user-login\",\"TraceId\":\"3bb92f0851b345d0bda829b265ac27d3\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"3bb92f0851b345d0bda829b265ac27d3\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:25:00+08:00\",\"costTime\":130,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"8613e65f3e3642d596d0b9a542001e61\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"8613e65f3e3642d596d0b9a542001e61\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"8613e65f3e3642d596d0b9a542001e61\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:24:49+08:00\",\"costTime\":113,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"7f295e9b298043f090004efc29ab1b4a\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":3.0,\"Event\":\"user-login\",\"TraceId\":\"7f295e9b298043f090004efc29ab1b4a\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"7f295e9b298043f090004efc29ab1b4a\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:16:43+08:00\",\"costTime\":108,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"a58e4e3781784e5490f6001014615bb5\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":2.0,\"Event\":\"user-login\",\"TraceId\":\"a58e4e3781784e5490f6001014615bb5\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"a58e4e3781784e5490f6001014615bb5\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:16:23+08:00\",\"costTime\":109,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"778d8c1e58be40549511de2fb37b6e24\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"778d8c1e58be40549511de2fb37b6e24\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"778d8c1e58be40549511de2fb37b6e24\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:15:47+08:00\",\"costTime\":129,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"0a4a90fa0a154399904fb4d09eddf92a\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"0a4a90fa0a154399904fb4d09eddf92a\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"0a4a90fa0a154399904fb4d09eddf92a\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T19:14:36+08:00\",\"costTime\":142,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"e7677d974e794ecbb95d6f494a08705b\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"e7677d974e794ecbb95d6f494a08705b\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"e7677d974e794ecbb95d6f494a08705b\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T18:55:23+08:00\",\"costTime\":109,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"be127917aa20451f83a2aa9f0eba2ba1\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"be127917aa20451f83a2aa9f0eba2ba1\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"be127917aa20451f83a2aa9f0eba2ba1\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T18:01:07+08:00\",\"costTime\":107,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"2865a4551d694bd9b38b405d206b24f7\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"2865a4551d694bd9b38b405d206b24f7\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"2865a4551d694bd9b38b405d206b24f7\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T17:58:42+08:00\",\"costTime\":175,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"},{\"traceId\":\"0d0efe0e9e1740d2bd662f36ec7ae84d\",\"reason\":\"安全设备校验\",\"data\":{\"safeDeviceCheck\":{\"reason\":\"近期未使用过该设备\",\"riskLevel\":\"REVIEW\"},\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"createdAt\":1557201152000,\"uid\":191270712320970005,\"vipLevel\":0,\"gender\":1,\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"value\":0},\"Platform\":\"ANDROID\",\"DeviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"Timeout\":\"2450\",\"ClientIp\":\"**************\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"0\",\"riskLevel\":\"PASS\",\"province\":\"0\",\"city\":\"内网IP\",\"isp\":\"内网IP\",\"cityId\":0,\"region\":\"0\"},\"Async\":\"false\",\"UserId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"userDeviceLoginCount5Minutes\":0.0,\"Event\":\"user-login\",\"TraceId\":\"0d0efe0e9e1740d2bd662f36ec7ae84d\",\"Business\":\"userService\"},\"level\":\"REVIEW\",\"mobileNo\":\"***********\",\"deviceId\":\"2019041701112055a74bd001bbafb520438037bb3305570101604ce56e8533\",\"userId\":\"192de7d98cec4e9e907b8a81ad145b2d\",\"eventCode\":\"user-login\",\"result\":{\"eventCode\":\"user-login\",\"traceId\":\"0d0efe0e9e1740d2bd662f36ec7ae84d\",\"reason\":\"安全设备校验\",\"businessCode\":\"userService\",\"level\":\"REVIEW\",\"success\":true,\"rule\":294,\"reply\":\"\"},\"createdAt\":\"2019-06-24T15:42:03+08:00\",\"costTime\":195,\"full_text\":\"\",\"clientIp\":\"**************\",\"reply\":\"\"}]\n";
        JSONArray dataArr = JSON.parseArray(data);
        Set<String> deviceSet = new HashSet<>();
        Set<String> ipSet = new HashSet<>();
        Set<String> mobileSet = new HashSet<>();
        for(int i=0;i<dataArr.size();i++){
            JSONObject dataJson = dataArr.getJSONObject(i);
            String device = dataJson.getString("deviceId");
            if(StringUtils.isNotBlank(device)){
                deviceSet.add(device);
            }
            String ip = dataJson.getString("clientIp");
            if(StringUtils.isNotBlank(ip)){
                ipSet.add(ip);
            }
            String mobile = dataJson.getString("mobileNo");
            if(StringUtils.isNotBlank(mobile)){
                mobileSet.add(mobile);
            }
        }
        System.out.println("device:"+JSON.toJSONString(deviceSet));
        System.out.println("ip:"+JSON.toJSONString(ipSet));
        System.out.println("mobile:"+JSON.toJSONString(mobileSet));
    }

    //// 工具方法
    protected PageInfo<Map<String, Object>> getRiskHitLogTest(LogSearchVO.HitLogSearchVO vo) {
        index = "risk_hit_log";
        PageInfo<Map<String, Object>> pageInfo = RiskLogESUtil.handlePageInfo(vo);
        try {
            SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, index,RiskLogESUtil.pattern_default);
            log.debug("搜索参数:\n{}", request.source().query());
            SearchResponse response = client.search(request);
            if (response != null && response.status() == RestStatus.OK) {
                RiskLogESUtil.handleResponse(response,pageInfo);
            } else {
                log.error("搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }
        return pageInfo;
    }

    protected Map<String,Object> getRiskHitLogAggTest(LogSearchVO.HitLogSearchVO vo,AggregationBuilder agg,boolean nested) {
        index = "risk_hit_log";
        Map<String,Object> rtnMap = new HashMap<>();
        try {
            SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, index,RiskLogESUtil.pattern_default);
            request.source().aggregation(agg);
            log.debug("搜索参数:\n{}", request.source().query());
            SearchResponse response = client.search(request);
            if (response != null && response.status() == RestStatus.OK) {
                Aggregations aggregations = response.getAggregations();
                rtnMap = RiskLogESUtil.parseAggregations(aggregations,nested);
            } else {
                log.error("搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }
        return rtnMap;
    }

    public static void main(String[] args){
        //handleUserDetailData();
        Map<Long,String> map = new HashMap<>();
        map.put(1l,"a");
        map.put(2l,"b");
        map.put(3l,"c");
        for(Map.Entry<Long,String> entry : map.entrySet()){
            Long key = entry.getKey();
            if(key == 2){
                //map.remove(key); // 此操作导致异常
            }else{
                map.put(key,entry.getValue()+key);
            }
        }
        System.out.println(JSON.toJSONString(map));
    }

}
