package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.openapi.client.ApolloOpenApiClient;
import com.ctrip.framework.apollo.openapi.dto.OpenEnvClusterDTO;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.impl.ApolloOpenApiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ApolloServiceTest {

    @Autowired
    private ApolloOpenApiServiceImpl apolloServiceImpl;

    @MockBean
    private ApolloOpenApiClient openApiClient;


    @Before
    public void init(){
        ApplicationContext context = Mockito.mock(ApplicationContext.class);
        Mockito.when(context.getEnvironment()).thenReturn(new MockEnvironment() {
            @Override
            public String[] getActiveProfiles() {
                return new String[]{"local"};
            }
        });
        apolloServiceImpl.setApplicationContext(context);
    }

    @Test
    public void given_correctParam_when_updateApollo_thenTrue(){
        List<OpenEnvClusterDTO> mockEnvCluster = JSON.parseArray("[{\n" +
                "\t\t\"clusters\": [\"default\", \"local-avalon\"],\n" +
                "\t\t\"env\": \"TEST\"\n" +
                "\t}, {\n" +
                "\t\t\"clusters\": [\"default\"],\n" +
                "\t\t\"env\": \"PRO\"\n" +
                "\t}\n" +
                "]",OpenEnvClusterDTO.class);
        Mockito.when(openApiClient.getEnvClusterInfo("risk-text")).thenReturn(mockEnvCluster);

        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId("risk-text")
                .namespace("application")
                .itemKey("dev.openapi.test")
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser("zengxiangcai")
                .build();

        boolean result = apolloServiceImpl.updateItem(updateItem);
        Assert.assertTrue(result);
    }

    @Test
    public void given_incorrectParam_when_updateApollo_thenFalse() {

        List<OpenEnvClusterDTO> mockEnvCluster = JSON.parseArray("[{\n" +
                "\t\t\"clusters\": [\"default\", \"local-avalon\"],\n" +
                "\t\t\"env\": \"TEST\"\n" +
                "\t}, {\n" +
                "\t\t\"clusters\": [\"default\"],\n" +
                "\t\t\"env\": \"PRO\"\n" +
                "\t}\n" +
                "]",OpenEnvClusterDTO.class);
        Mockito.when(openApiClient.getEnvClusterInfo("risk-text")).thenReturn(mockEnvCluster);

        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId("risk-text")
                .namespace("application")
                .itemKey("dev.openapi.test")
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .build();

        boolean result = apolloServiceImpl.updateItem(updateItem);
        Assert.assertFalse(result);
    }

}
