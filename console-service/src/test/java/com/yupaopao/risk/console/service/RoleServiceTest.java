package com.yupaopao.risk.console.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.access.api.RiskService;
import com.yupaopao.risk.access.bean.RiskAction;
import com.yupaopao.risk.access.bean.RiskLevel;
import com.yupaopao.risk.access.bean.RiskResult;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.console.TestApplication;
import com.yupaopao.risk.console.bean.FeedBackRequest;
import com.yupaopao.risk.console.bean.FeedbackResult;
import com.yupaopao.risk.console.bean.ThirdType;
import com.yupaopao.risk.console.service.impl.RoleServiceImpl;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.vo.PermissionVO;
import com.yupaopao.risk.console.vo.UserDataAssociate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/15 10:50 AM
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class) // SpringJUnit支持，由此引入Spring-Test框架支持！
@SpringBootTest(classes = TestApplication.class) // 指定我们SpringBoot工程的Application启动类
public class RoleServiceTest {

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RolePermissionService rolePermissionService;
    private RoleServiceImpl roleserviceImpl;
    @Reference(timeout = 10000)
    private RiskService riskService;
    @Autowired
    private GrayListService grayListService;
    @Autowired
    private ThirdService thirdService;
    @Autowired
    private DiffLogService diffLogService;
    @Autowired
    private UserDetailService userDetailService;
    @Autowired
    private UserImDetailService userImDetailService;

    @Before
    public void init(){
        roleserviceImpl = new RoleServiceImpl();
    }

    @Test
    public void getTest() {
        List<Role> roles = roleService.selectAll();
        System.out.println("-----------------------------------------------roles:"+ JSON.toJSONString(roles));
        Map<String, Object> equalMap = new HashMap<>();
        equalMap.put("roleId","7");
        List<RolePermission> rolePermissions = rolePermissionService.selectByCondition(equalMap,null,RolePermission.class);
        System.out.println("-----------------------------------------------rolePermissions:"+ JSON.toJSONString(rolePermissions));
        List<Permission> permissions = permissionService.selectAll();
        System.out.println("-----------------------------------------------permissions:"+ JSON.toJSONString(permissions));
        List<PermissionVO> voList = roleserviceImpl.getPermissionVOList(permissions,rolePermissions);
        System.out.println("voList:"+JSON.toJSONString(voList));
    }

    protected void printPermissionsTree(){
        String rolePermissionsStr = "[{\"createdAt\":null,\"id\":6,\"permissionId\":6,\"roleId\":7},{\"createdAt\":null,\"id\":6,\"permissionId\":14,\"roleId\":7}]";
        String permissionsStr = "[{\"author\":\"张三\",\"createdAt\":null,\"delFlag\":false,\"description\":\"权限1\",\"id\":1,\"name\":\"123:asdf:fda\",\"orderNum\":1,\"parentId\":1,\"supplement\":\"{\\\"orgId\\\":\\\"TEST1\\\",\\\"orgName\\\":\\\"样例部门1\\\"}\",\"type\":1,\"updatedAt\":null,\"url\":\"asdf/asdf\"},{\"author\":\"张三\",\"createdAt\":null,\"delFlag\":false,\"description\":\"权限2\",\"id\":2,\"name\":\"123:asdf:fda\",\"orderNum\":1,\"parentId\":1,\"supplement\":\"{\\\"orgId\\\":\\\"TEST1\\\",\\\"orgName\\\":\\\"样例部门1\\\"}\",\"type\":1,\"updatedAt\":null,\"url\":\"asdf/asdf\"},{\"author\":\"张三\",\"createdAt\":null,\"delFlag\":false,\"description\":\"parent1\",\"id\":3,\"name\":\"100\",\"orderNum\":1,\"parentId\":0,\"supplement\":\"{\\\"orgId\\\":\\\"TEST1\\\",\\\"orgName\\\":\\\"样例部门1\\\"}\",\"type\":1,\"updatedAt\":null,\"url\":\"asdf/asdf\"},{\"author\":\"李四\",\"createdAt\":null,\"delFlag\":false,\"description\":\"child1\",\"id\":4,\"name\":\"100100\",\"orderNum\":1,\"parentId\":3,\"supplement\":\"{\\\"orgId\\\":\\\"TEST1\\\",\\\"orgName\\\":\\\"样例部门2\\\"}\",\"type\":1,\"updatedAt\":null,\"url\":\"asdf/asdf\"},{\"author\":\"王五\",\"createdAt\":null,\"delFlag\":false,\"description\":\"child2\",\"id\":5,\"name\":\"100101\",\"orderNum\":1,\"parentId\":3,\"supplement\":\"{\\\"orgId\\\":\\\"TEST1\\\",\\\"orgName\\\":\\\"样例部门3\\\"}\",\"type\":1,\"updatedAt\":null,\"url\":\"asdf/asdf\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":false,\"description\":\"sdf1234\",\"id\":6,\"name\":\"sdf123\",\"orderNum\":1234,\"parentId\":5,\"supplement\":\"{org: \\\"123\\\"}\",\"type\":2,\"updatedAt\":null,\"url\":\"/a/a\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":true,\"description\":\"sdf001\",\"id\":8,\"name\":\"sdf001\",\"orderNum\":1,\"parentId\":2,\"supplement\":\"212234\",\"type\":2,\"updatedAt\":null,\"url\":\"/a/b\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":true,\"description\":\"subtest\",\"id\":9,\"name\":\"sdf0000001\",\"orderNum\":213,\"parentId\":8,\"supplement\":\"12323\",\"type\":2,\"updatedAt\":null,\"url\":\"/a/b/c\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":true,\"description\":\"subtest\",\"id\":10,\"name\":\"sdf0000001\",\"orderNum\":213,\"parentId\":8,\"supplement\":\"12323\",\"type\":2,\"updatedAt\":null,\"url\":\"/a/b/c\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":false,\"description\":\"根菜单1\",\"id\":11,\"name\":\"root1\",\"orderNum\":1,\"parentId\":0,\"supplement\":\"root1\",\"type\":1,\"updatedAt\":null,\"url\":\"/root1\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":true,\"description\":\"根菜单2\",\"id\":12,\"name\":\"root2\",\"orderNum\":123,\"parentId\":0,\"supplement\":\"root2\",\"type\":1,\"updatedAt\":null,\"url\":\"/root2\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":false,\"description\":\"根菜单1/子菜单1\",\"id\":13,\"name\":\"root1:sub1\",\"orderNum\":10,\"parentId\":11,\"supplement\":\"root1:sub1\",\"type\":1,\"updatedAt\":null,\"url\":\"/root1/sub1\"},{\"author\":\"sikaijian\",\"createdAt\":null,\"delFlag\":false,\"description\":\"根菜单2/子菜单1\",\"id\":14,\"name\":\"root2:sub1\",\"orderNum\":1,\"parentId\":4,\"supplement\":\"root2:sub1\",\"type\":1,\"updatedAt\":null,\"url\":\"/root2/sub1\"}]";
        List<RolePermission> rolePermissions = JSON.parseArray(rolePermissionsStr,RolePermission.class);
        List<Permission> permissions = JSON.parseArray(permissionsStr,Permission.class);
        roleserviceImpl = new RoleServiceImpl();
        List<PermissionVO> voList = roleserviceImpl.getPermissionVOList(permissions,rolePermissions);
        System.out.println("voList:"+JSON.toJSONString(voList));
    }

    @Test
    public void riskServiceTest(){
//        RiskAction action = RiskAction.create("user-update-nickname").setClientIp("*************").put("nickName", "心奇测试");
        RiskAction action = RiskAction.create("user-complete-avatar").setClientIp("*******").put("avatar", "https://photo.xxqapp.cn/upload/37afda29-2cb0-46be-aa10-1e8ff661947f.jpg");
        action.setAsync(true);
//        action.setTimeout(5000); // 为当前请求单独设定超时时间,该时间需小于步骤3上指定的超时时间
        RiskResult riskResult = riskService.detect(action); // 发送同步风控请求并阻塞等待返回结果
        System.out.println("=========================riskResult:"+JSON.toJSONString(riskResult));
    }

    @Test
    public void riskGrayListTest(){
        GrayList grayList = new GrayList();
        grayList.setValue("testwxquserid");
        List<GrayList> list = grayListService.searchRiskGray(grayList,true);
        System.out.println(JSON.toJSONString(list));
        grayList.setType("BLACK");
        grayList.setDimension("USERID");
        list = grayListService.searchRiskGray(grayList,true);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void feedbackTest(){
        FeedBackRequest request = new FeedBackRequest();
        request.setFeedbackType(ThirdType.SHUMEI.getCode());
        request.setResourceType("text");
        Map<String,Object> params = new HashMap<>();
        params.put("reason","测试");
        params.put("level", RiskLevel.PASS);

        // 流水号和请求时间要正确一致
        params.put("requestId","da69e59516b98e76d01b8dd6a3f9c1dd");  // 流水号
        params.put("timestamp",1567755163658l); // 该流水号数据的请求时间，毫秒时间

        // 非必须
        // params.put("bzId","429ea5974751472d8d7523334d1305f2"); // 业务唯一标识，当不存在会报400错误
        params.put("category","category");
        request.setParams(params);

        FeedbackResult response = thirdService.feedback(request);
        System.out.println("response:"+ JSON.toJSONString(response));
    }

    @Test
    public void diffLogFeedbackTest(){
        diffLogService.feedback("0be461fe342e4db38b654a3b307879b8");
    }

    @Test
    public void getAssociateInfoByUidTest(){
        UserDataAssociate userDataAssociate = userDetailService.getAssociateInfoByUid(1835212114300008l,1);
        System.out.println(JSON.toJSONString(userDataAssociate));
    }

    @Test
    public void getImageTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-10-10 00:00:00");
        Date endTime = format.parse("2019-10-14 23:59:59");
        LogSearchVO.HitLogImageSearchVO vo = new LogSearchVO.HitLogImageSearchVO();
        vo.setSize(100);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
        hitResult.setUserId("1131c346d2b44ff9acafb32840b83402");
        vo.setQuery(hitResult);
        PageInfo<Map<String, Object>> pageInfo = userImDetailService.getImage(vo);
        System.out.println(JSON.toJSON(pageInfo));
    }

    public static void main(String[] args){
        RoleServiceTest test = new RoleServiceTest();
//        test.printPermissions();
        test.printPermissionsTree();
    }

}
