package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/3/13 5:29 PM
 */
public class TT {

    @Test
    public void search() {
        SearchRequest request = new SearchRequest("risk_hit_log");
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.matchAllQuery());
        query.mustNot(QueryBuilders.termQuery("rule", "296"));
        query.filter().add(QueryBuilders.termQuery("riskLevel", "REJECT"));
        query.filter().add(QueryBuilders.termQuery("eventCode", "timeline-comment"));
        request.source().query(query);
        request.source().size(1);
        System.out.println(request.source().toString());
    }

    @Test
    public void json() throws IOException {
        String text = FileUtils.readFileToString(new File("/Users/<USER>/工作空间/timeline-comment.json"));
        JSONObject object = JSONObject.parseObject(text);
        object = object.getJSONObject("hits");
//        System.out.println("记录总量:" + object.getLong("total"));
        object.getJSONArray("hits").forEach(item -> {
            JSONObject row = (JSONObject) item;
            System.out.println(row.getJSONObject("_source").toString() + ",");
        });
    }

    @Test
    public void dateFormat(){
        Date date = new Date();
        System.out.println(date.getDate());
        System.out.println(DateFormatUtils.format(date, "yyyy-MM-dd\'T\'HH:mm:ss+08:00"));
    }

    @Test
    public void test01(){
        String config = "{\"scenes\":\"[\\\"public\\\",\\\"评论回复\\\"]\"}";

        Map<String,Object> map = JSONObject.parseObject(config, Map.class);

        if(map.containsKey("scenes")){
            List<String> scenes = JSONArray.parseArray(map.get("scenes").toString(),String.class);

            System.out.println("scenes.size()="+scenes.size());
        }

    }

}
