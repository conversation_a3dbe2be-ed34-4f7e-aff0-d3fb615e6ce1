package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.TestApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = TestApplication.class)
public class AlarmServiceTest {

    @Autowired
    private AlarmService alarmService;

    @Test
    public void testSearch01(){
        AlarmVO vo=new AlarmVO();
        int page=1;
        int size=10;

        PageInfo<AlarmVO>  pageInfo = alarmService.searchAlarm(vo,page,size);
        System.out.println("pageInfo="+ JSONObject.toJSONString(pageInfo));

    }


}
