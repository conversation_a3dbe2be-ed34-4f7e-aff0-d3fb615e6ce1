package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.access.bean.Handler;
import com.yupaopao.risk.access.bean.RiskResult;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2019/7/27 11:15 AM
 */
public class NotifyLogReparse {

    @Test
    public void reparse() throws IOException {
        File file = new File("/data/risk/risk_notify_log.json");
        System.out.println("开始读取文件 . . .");
        List<String> lines = FileUtils.readLines(file);
        System.out.println("文件读取完成,行数:" + lines.size());
        Map<String, List<String>> data = new HashMap<>();
        data.put("timeline-publish-text", new LinkedList<>());
        data.put("timeline-publish-image", new LinkedList<>());
        data.put("timeline-publish-video", new LinkedList<>());
        AtomicInteger count = new AtomicInteger();
        System.out.println("重新解析数据记录 . . .");
        lines.forEach(line -> {
            JSONObject object = JSONObject.parseObject(line);
            object = object.getJSONObject("_source");
            object.put("returnMap", JSONObject.parseObject(object.getString("returnMap")));
            RiskResult result = object.toJavaObject(RiskResult.class);
            if (result.getHandler() == null) {
                result.setHandler(Handler.RISK); // 没有指定handler时默认为RISK
            }
            data.get(result.getEventCode()).add(JSONObject.toJSONString(result));
            count.incrementAndGet();
        });
        System.out.println("数据记录解析完成,数量:" + count.get());
        data.forEach((key, value) -> {
            File target = new File("/data/risk/risk_notify_message_" + key + ".json");
            try {
                FileUtils.writeLines(target, value, false);
            } catch (Exception e) {
                System.out.println("数据保存失败:" + key);
            }
        });
        System.out.println("数据记录保存完成");
    }


}
