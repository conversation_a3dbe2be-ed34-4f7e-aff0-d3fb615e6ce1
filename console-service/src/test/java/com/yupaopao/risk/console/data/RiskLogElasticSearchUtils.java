package com.yupaopao.risk.console.data;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;

import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/2/25 4:14 PM
 */
@Slf4j
public class RiskLogElasticSearchUtils {

    public static final String HIT_LOG_INDEX = "risk_hit_log";
    public static final String DATE_FORMART = "yyyy-MM-dd HH:mm:ss";

    public static SearchRequest buildSearchRequest(Map<String, Object> map, Date startTime, Date endTime) {
//        log.info("搜索请求:{}", map);
        SearchRequest request = new SearchRequest(buildSearchIndices(HIT_LOG_INDEX, startTime, endTime));
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        query.must(QueryBuilders.matchAllQuery());
        if (startTime != null || endTime != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery("createdAt");
            if (startTime != null) {
                builder.gte(DateFormatUtils.format(startTime, DATE_FORMART));
            }
            if (endTime != null) {
                builder.lte(DateFormatUtils.format(endTime, DATE_FORMART));
            }
            query.filter(builder);
        }
        if (MapUtils.isNotEmpty(map)) {
            map.forEach((key, value) -> query.filter(QueryBuilders.termQuery(key, value)));
        }
        request.source().query(query);
        request.source().size(0);
        request.source().timeout(new TimeValue(1, TimeUnit.SECONDS));
        return request;
    }

    public static String[] buildSearchIndices(String index, Date start, Date end) {
        LinkedList<String> list = new LinkedList();
        end = end == null ? new Date() : end;
        if (start == null || start.after(end)) {
            list.add(index);
        } else {
            Calendar calendar = Calendar.getInstance();
            for (Date current = start; !current.after(end); ) {
                list.add(formatIndexName(index, current));
                calendar.setTime(current);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                current = calendar.getTime();
            }
        }
        return list.toArray(new String[0]);
    }

    public static String formatIndexName(String index, Date date) {
        return String.format("%s_%s", index, DateFormatUtils.format(date == null ? new Date() : date, "yyyyMMdd"));
    }
}
