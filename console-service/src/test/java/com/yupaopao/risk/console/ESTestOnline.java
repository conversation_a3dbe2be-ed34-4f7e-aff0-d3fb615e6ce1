package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.console.service.ElasticSearchTest;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.junit.Before;
import org.junit.Test;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class ESTestOnline extends ElasticSearchTest {
    private RestHighLevelClient client;
    private String index;

    private final int WORD_TOP_30 = 30;
    private final int WORD_CONTENT_TOP_10 = 10;

    @Before
    public void init() {
        RestClientBuilder builder = RestClient.builder(new HttpHost("mwes.yupaopao.com", 80, "http"));
        final CredentialsProvider credentialsProvider =
                new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials("risk", "sMFS3gwdtSfojoTWr9aDxrXcMC"));
        builder.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
            @Override
            public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
                httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                //线程设置
                httpClientBuilder.setDefaultIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(10).build());
                return httpClientBuilder;
            }
        });
        client = new RestHighLevelClient(builder);
        index = "risk_hit_log_" + DateFormatUtils.format(new Date(), "yyyyMMdd");
    }

    public RestHighLevelClient getClient() {
        return client;
    }

    public void setClient(RestHighLevelClient client) {
        this.client = client;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    @Test
    public void getRiskHitTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-10-09 22:50:00");
        Date endTime = format.parse("2020-10-10 23:30:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(10000);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
//        hitResult.setEventCode("user-update-signature");
        hitResult.setEventCode("im-message");
        hitResult.setUserId("191460489252108545");
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("data.body","就咋还充不了电呢"));
//        query.must(QueryBuilders.termQuery("data.body","你要等我"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(pageInfo.getTotal());
        System.out.println("list=="+JSON.toJSONString(pageInfo.getList()));
    }

    // 指定违禁词内容TOP10
    @Test
    public void getWordContentTop10Test() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-09-01 00:00:00");
        Date endTime = format.parse("2019-09-31 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);

        HitResult hitResult = new HitResult();
        hitResult.setEventCode("user-update-signature");
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);

        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.termsQuery("data.signatureCheck.wordList","线下"));

        AggregationBuilder agg = AggregationBuilders.terms("1").field("data.signature");
        request.source().aggregation(agg);

        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),false);
        System.out.println(JSON.toJSONString(aggMap));
    }

    // 违禁词TOP30
    @Test
    public void getWordTop30Test() throws  Exception{
        String[] eventCodes = new String[]{
                "user-complete-nickname,user-update-nickname",
                "user-update-signature",
                "timeline-publish-text,timeline-comment",
                "play-order-user-comment,play-order-biggie-comment",
                "im-message",
                "chat-room"
        };
        String[] wordListKeys = new String[]{
                "data.nickNameCheck.wordList",
                "data.signatureCheck.wordList",
                "data.textCheck.wordList,data.commentTextCheck.wordList",
                "data.commentTextCheck.wordList,data.shumeiCommentTextCheck.wordList,data.neteaseCommentTextCheck.wordList",
                "data.imTextCheck.wordList,data.imNormalTextCheck.wordList",
                "data.chatTextCheck.wordList"
        };
        String[] contentKeys = new String[]{"data.nickName","data.signature","data.content","data.content","data.body","data.content"};

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-10-01 00:00:00");
        Date endTime = format.parse("2019-10-31 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);

        HitResult hitResult = new HitResult();
        hitResult.setEventCode("play-order-user-comment");
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);

        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.termsQuery("data.nickNameCheck.wordList","da亡者"));

        AggregationBuilder agg = AggregationBuilders.terms("1").field("data.nickNameCheck.wordList");
        request.source().aggregation(agg);

        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),false);
        System.out.println(JSON.toJSONString(aggMap));
    }

    // 指定违禁词内容TOP10
    protected Map<String,Object> getWordContentTop10(LogSearchVO.HitLogSearchVO vo,String eventCode,String wordListKey,String word,String contentKey) throws  Exception{
        SearchRequest request = this.getWordRequest(vo,eventCode);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        List<String> wordListKeys = this.getKeys(wordListKey);
        if(wordListKeys.size()==1){
            query.filter(QueryBuilders.termsQuery(wordListKeys.get(0),word));
        }else if(wordListKeys.size()>1){
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            for(int i=0;i<wordListKeys.size();i++){
                boolQuery.should(QueryBuilders.termQuery(wordListKeys.get(i),word));
            }
            query.filter(boolQuery);
        }

        AggregationBuilder agg = AggregationBuilders.terms("1").field(contentKey).size(WORD_CONTENT_TOP_10);
        request.source().aggregation(agg);

        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),false);
        return aggMap;
    }

    // 违禁词TOP30
    protected Map<String,Object> getWordTop30(LogSearchVO.HitLogSearchVO vo,String eventCode,String wordListKey) throws  Exception{
        SearchRequest request = this.getWordRequest(vo,eventCode);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.mustNot(QueryBuilders.termsQuery(wordListKey,""));

        AggregationBuilder agg = AggregationBuilders.terms("1").field(wordListKey).size(WORD_TOP_30);
        request.source().aggregation(agg);

        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),false);
        System.out.println("===getWordTop30:"+JSON.toJSONString(aggMap));
        return aggMap;
    }

    protected  List<String> getKeys(String keyStr){
        List<String> list = new ArrayList<>();
        if(StringUtils.isNotBlank(keyStr)){
            if(keyStr.indexOf(",")!=-1){
                String[] keys = keyStr.split("[,]+");
                if(keys.length>0){
                    for(int i=0;i<keys.length;i++){
                        String key = keys[i];
                        if(StringUtils.isNotBlank(key)){
                            list.add(key);
                        }
                    }
                }
            }
            if(list.isEmpty()){
                list.add(keyStr);
            }
        }
        return list;
    }

    // MAP数据降序排序及截取指定长度
    protected Map<String,Object> sortMap(Map<String,Object> map,Integer size){
        Map<String,Object> sortMap = new LinkedHashMap<>();
        List<Map.Entry<String, Object>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> m1, Map.Entry<String, Object> m2) {
                if(m1.getValue()==null || m2.getValue()==null){
                    return 0;
                }
                return (int)(Long.parseLong(m2.getValue()+"")-Long.parseLong(m1.getValue()+""));
            }
        });

        int len = list.size();
        if(len > size){
            len = size;
        }
        for(int i=0;i<len;i++){
            Map.Entry<String, Object> m = list.get(i);
            sortMap.put(m.getKey(),m.getValue());
        }
        return sortMap;
    }

    // 数据合并，以MAP的key来进行value相加
    protected Map<String,Object> mergeMap(Map<String,Object> m1,Map<String,Object> m2){
        if(m2.size()>0){
            for(Map.Entry<String,Object> entry : m2.entrySet() ){
               String key = entry.getKey();
               if(m1.containsKey(key)){
                   long m1Value = m1.get(key)==null?0:Long.parseLong(m1.get(key)+"");
                   long m2Value = m2.get(key)==null?0:Long.parseLong(m2.get(key)+"");
                   m1.put(key,m1Value+m2Value);
               }else{
                   m1.put(key,m2.get(key));
               }
            }
        }
        return m1;
    }

    // 违禁词TOP30-多事件
    protected Map<String,Object> getWordTop30Multi(LogSearchVO.HitLogSearchVO vo,String eventCode,String wordListKey) throws  Exception{
        List<String> keys = this.getKeys(wordListKey);
        if(keys.size()==1){
            return this.getWordTop30(vo,eventCode,keys.get(0));
        }else if(keys.size()>1){
            Map<String,Object> rtnMap = new LinkedHashMap<>();
            for(int i=0;i<keys.size();i++){
                this.mergeMap(rtnMap,this.getWordTop30(vo,eventCode,keys.get(i)));
            }
            return this.sortMap(rtnMap,WORD_TOP_30);
        }
        return MapUtils.EMPTY_MAP;
    }

    protected SearchRequest getWordRequest(LogSearchVO.HitLogSearchVO vo,String eventCode){
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        List<String> eventCodes = this.getKeys(eventCode);
        if(eventCodes.size()>0){
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            for(int i=0;i<eventCodes.size();i++){
                boolQuery.should(QueryBuilders.termQuery("eventCode",eventCodes.get(i)));
            }
            query.filter(boolQuery);
        }else if(eventCodes.size()==1){
            query.filter(QueryBuilders.termQuery("eventCode", eventCodes.get(0)));
        }
        return request;
    }

    // 违禁词TOP30-最终抽样数据获取
    @Test
    public void getWordTop30FinalTest() throws  Exception{
        String[] eventCodes = new String[]{
                "user-complete-nickname,user-update-nickname",
                "user-update-signature",
                "timeline-publish-text,timeline-comment",
                "play-order-user-comment,play-order-biggie-comment",
                "im-message",
                "chat-room"
        };
        String[] wordListKeys = new String[]{
                "data.textCheck.wordList",
                "data.textDetect.wordList",
                "data.textCheck.wordList",
                "data.textCheck.wordList",
                "data.textCheck.wordList",
//                "data.textCheck.wordList,data.textDetect.wordList,data.textLocalCheck.wordList,data.neteaseImTextCheck.wordList",
                "data.textCheck.wordList"
//                "data.textCheck.wordList,data.textDetect.wordList,data.textLocalCheck.wordList"
        };
        String[] contentKeys = new String[]{"data.nickName","data.signature","data.content","data.content","data.body","data.content"};

        List<List<Object>> rtnList = new ArrayList();
        List<List<Object>> top30RtnList = new ArrayList();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-04-01 00:00:00");
        Date endTime = format.parse("2020-04-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        for(int i=0;i<eventCodes.length;i++){
            String eventCode = eventCodes[i];
            String wordListKey = wordListKeys[i];
            String contentKey = contentKeys[i];
            HitResult hitResult = new HitResult();
            hitResult.setLevel("REJECT");
            vo.setQuery(hitResult);
            Map<String,Object> top30Map = this.getWordTop30Multi(vo,eventCode,wordListKey);
            System.out.println(eventCode+"===top30Map:"+JSON.toJSONString(top30Map));
            if(top30Map!=null&&top30Map.size()>0){
                for(Map.Entry<String,Object> entry : top30Map.entrySet() ){
                    String word = entry.getKey();
                    List<Object> top30List = new ArrayList();
                    top30List.add(eventCode);
                    top30List.add(word);
                    top30List.add(entry.getValue());
                    top30RtnList.add(top30List);
                    if(StringUtils.isNotBlank(word)){
                        Map<String,Object> top10Map = this.getWordContentTop10(vo,eventCode,wordListKey,word,contentKey);
                        System.out.println(eventCode+"===top10Map:"+JSON.toJSONString(top10Map));
                        for(Map.Entry<String,Object> ent : top10Map.entrySet() ){
                            List<Object> list = new ArrayList();
                            list.add(eventCode);
                            list.add(word);
                            list.add(ent.getKey());
                            list.add(ent.getValue());
                            rtnList.add(list);
                        }
                    }
                }
            }
        }
        System.out.println("top30带数目:"+JSON.toJSONString(top30RtnList));
        System.out.println("最终结果:"+JSON.toJSONString(rtnList));
    }

    // 例：昵称风险分析-违禁词Top30
    @Test
    public void wordTop30(){
        String[] words = new String[]{
                "哄睡","比心币","笔芯币","比芯币","线下","代陪","皇色","白嫖","EV2","抠扣","口扣","叩扣","寇扣","cnm","雞女","包養","阿刁","逍遥阁","fan墙","脑残","毒药","硬币","点头像","色色","chuB","出b","v-p-n","v.p.n","薇我","習x"
        };
        Integer[] counts = new Integer[]{
                145615,54380,41083,31622,27340,10641,7677,7472,6509,5865,5191,3701,3434,3284,3220,2547,2259,2040,2011,1718,1446,1345,1279,1270,1270,1267,1264,1260,1194,1151,7827,4578,4308,4280,3027,2476,2033,1665,1307,1177,1168,1165,1165,999,844,837,678,623,621,607,550,539,508,502,414,401,387,387,339,318
        };
        List<Map<String,Object>> list = new ArrayList<>();
        for(int i=0;i<words.length;i++){
            Map<String,Object> map = new HashMap<>();
            map.put("word",words[i]);
            map.put("count",counts[i]);
            map.put("rate","0.00%");
            list.add(map);
        }
        System.out.println(JSON.toJSONString(list));
    }

    public static double formatDouble1(double d) {
        return (double)Math.round(d*100)/100;
    }

    public String formatDouble2(double d) {
        DecimalFormat df = new DecimalFormat("#.00");
        return df.format(d);
    }

    @Test
    public void wordTop30Acc(){
        String[] words = new String[]{
                "陪玩","陪玩","陪玩","陪玩","陪玩","陪玩","陪玩","陪玩","陪玩","陪玩","微信","微信","微信","微信","微信","微信","微信","微信","微信","微信","QQ","QQ","QQ","QQ","QQ","QQ","QQ","QQ","QQ","QQ","线下","线下","线下","线下","线下","线下","线下","线下","线下","线下","连麦","连麦","连麦","连麦","连麦","连麦","连麦","连麦","连麦","连麦","接通宵","接通宵","接通宵","接通宵","接通宵","接通宵","接通宵","接通宵","接通宵","接通宵","pei聊","pei聊","pei聊","pei聊","pei聊","pei聊","pei聊","pei聊","pei聊","pei聊","da亡者","da亡者","da亡者","da亡者","da亡者","da亡者","da亡者","da亡者","da亡者","da亡者","da王者","da王者","da王者","da王者","da王者","da王者","da王者","da王者","da王者","da王者","打亡者","打亡者","打亡者","打亡者","打亡者","打亡者","打亡者","打亡者","打亡者","打亡者","打王者","打王者","打王者","打王者","打王者","打王者","打王者","打王者","打王者","打王者","k|接","k|接","k|接","k|接","k|接","k|接","k|接","k|接","k|接","k|接","愛愛","愛愛","愛愛","愛愛","愛愛","愛愛","愛愛","愛愛","愛愛","愛愛","互关","互关","互关","互关","互关","互关","互关","互关","互关","互关","接线下","接线下","接线下","接线下","接线下","接线下","接线下","接线下","接线下","接线下","jie聊","jie聊","jie聊","jie聊","jie聊","jie聊","jie聊","jie聊","jie聊","jie聊","接聊","接聊","接聊","接聊","接聊","接聊","接聊","接聊","接聊","接聊","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","jie聊天","接聊天","接聊天","接聊天","接聊天","接聊天","接聊天","接聊天","接聊天","接聊天","接聊天","k|聊","k|聊","k|聊","k|聊","k|聊","k|聊","k|聊","k|聊","k|聊","k|聊","可撩","可撩","可撩","可撩","可撩","可撩","可撩","可撩","可撩","可撩","ke撩","ke撩","ke撩","ke撩","ke撩","ke撩","ke撩","ke撩","ke撩","ke撩","k|私","k|私","k|私","k|私","k|私","k|私","k|私","k|私","k|私","k|私","哄睡","哄睡","哄睡","哄睡","哄睡","哄睡","哄睡","哄睡","哄睡","哄睡","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie亡者","jie王者","jie王者","jie王者","jie王者","jie王者","jie王者","jie王者","jie王者","jie王者","jie王者","接亡者","接亡者","接亡者","接亡者","接亡者","接亡者","接亡者","接亡者","接亡者","接亡者","接王者","接王者","接王者","接王者","接王者","接王者","接王者","接王者","接王者","接王者","代练","代练","代练","代练","代练","代练","代练","代练","代练","代练","虎牙","虎牙","虎牙","虎牙","虎牙","虎牙","虎牙","虎牙","虎牙","虎牙"
        };
        Double[] accs = new Double[]{
                0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,6.542056075D,6.542056075D,12.03703704D,17.43119266D,17.43119266D,17.43119266D,17.27272727D,23.17073171D,24.35897436D,25.67567568D,16.90140845D,17.64705882D,13.84615385D,9.677419355D,10D,10.52631579D,11.11111111D,59.74025974D,78.57142857D,85.56701031D,90.29126214D,93.51851852D,93.63636364D,93.69369369D,96.42857143D,98.24561404D,100D,100D,100D,100D,100D,100D,100D,100D,100D,94.02985075D,88.05970149D,90.12345679D,89.74358974D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,94.80519481D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,23.61111111D,11.42857143D,7.540983607D,5.263157895D,4.043126685D,3.157894737D,2.325581395D,1.52284264D,0.748129676D,0D,13.48973607D,18.47389558D,24.33862434D,38.05970149D,44.35483871D,50.86206897D,57.27272727D,64.42307692D,72.44897959D,75.53191489D,26.88172043D,37.64705882D,37.20930233D,31.39534884D,26.43678161D,21.59090909D,21.59090909D,17.04545455D,12.64367816D,12.64367816D,46.55172414D,51.5625D,53.22580645D,60.6557377D,68.33333333D,76.27118644D,75.86206897D,77.19298246D,82.45614035D,87.71929825D,59.64912281D,36.84210526D,36.84210526D,29.8245614D,22.80701754D,15.78947368D,10.52631579D,10.52631579D,5.263157895D,0D,28.07017544D,50.87719298D,50.87719298D,57.89473684D,64.9122807D,71.92982456D,77.19298246D,77.19298246D,82.45614035D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,85.10638298D,82.05128205D,92.30769231D,92.10526316D,91.89189189D,91.66666667D,91.66666667D,100D,100D,100D,32.25806452D,25.77319588D,21D,17.47572816D,14.28571429D,11.32075472D,8.411214953D,5.555555556D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,14.28571429D,16.27906977D,17.94871795D,19.44444444D,20.58823529D,21.875D,23.33333333D,25D,25.92592593D,15.38461538D,15.38461538D,29.62962963D,42.85714286D,53.57142857D,64.28571429D,72.4137931D,80D,86.66666667D,93.33333333D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D
        };
        System.out.println(words.length);
        System.out.println(accs.length);
        List<String> wos = new ArrayList<>();
        List<Double> dous = new ArrayList<>();
        for(int i=0;i<accs.length;i++){
            if(i%10==0){
                wos.add(words[i]);
                dous.add(formatDouble1(accs[i]));
            }
        }
        System.out.println(JSON.toJSONString(wos));
        System.out.println(JSON.toJSONString(dous));
    }

    @Test
    public void top30Avg(){
        Double[] accs = new Double[]{
                100D,100D,90.87452471D,86.25D,81.10599078D,75.51020408D,68.57142857D,66.25766871D,64.28571429D,62.83783784D,38.68613139D,17.5D,20.19230769D,20.58823529D,20.79207921D,20.79207921D,21D,14.14141414D,7.142857143D,0D,0D,0D,0D,11.53846154D,10.77844311D,10.28571429D,9.782608696D,9.32642487D,8.955223881D,8.612440191D,41.07883817D,60.6271777D,55.41401274D,57.44680851D,63.69047619D,68.71345029D,73.17784257D,74.26035503D,78.07807808D,82.01219512D,77.13178295D,69.10994764D,93.2885906D,91.8699187D,90.29126214D,88.37209302D,86.48648649D,100D,100D,100D,59.74025974D,44.70588235D,34.06593407D,25.26315789D,19.19191919D,14.42307692D,10.09174312D,6.194690265D,2.564102564D,0D,0D,0D,0D,0D,0D,0D,6.666666667D,6.818181818D,13.95348837D,14.45783133D,10.43478261D,9.523809524D,9.448818898D,9.6D,9.756097561D,13.93442623D,9.090909091D,9.166666667D,4.201680672D,4.237288136D,15.58441558D,30.50847458D,44.23076923D,53.84615385D,63.46153846D,63.46153846D,72.54901961D,82D,91.83673469D,100D,22.6519337D,17.5D,14.70588235D,12.13592233D,9.661835749D,7.246376812D,5.288461538D,3.365384615D,1.449275362D,0D,20.23809524D,37.31343284D,47.61904762D,57.37704918D,66.10169492D,74.13793103D,82.45614035D,89.28571429D,94.64285714D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,67.39130435D,56.25D,46.93877551D,38D,29.41176471D,22.64150943D,16.36363636D,10.52631579D,5.084745763D,0D,31.34328358D,47.36842105D,45.56962025D,45D,45.56962025D,46.15384615D,46.75324675D,52.63157895D,54.05405405D,55.55555556D,32.20338983D,7.843137255D,8.333333333D,8.510638298D,8.333333333D,8.333333333D,8.333333333D,0D,0D,0D,19.23076923D,30D,38.7755102D,46.80851064D,55.55555556D,63.63636364D,72.09302326D,80.95238095D,90.24390244D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,87.71929825D,75.92592593D,64.70588235D,54D,44D,34D,24D,16D,8D,0D,60.55045872D,72.59259259D,77.62237762D,81.25D,84.82758621D,88.35616438D,91.72413793D,94.48275862D,97.22222222D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,35.05154639D,22.30769231D,15.58441558D,11.30952381D,8.426966292D,6.382978723D,4.591836735D,2.955665025D,1.428571429D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,6.542056075D,6.542056075D,12.03703704D,17.43119266D,17.43119266D,17.43119266D,17.27272727D,23.17073171D,24.35897436D,25.67567568D,16.90140845D,17.64705882D,13.84615385D,9.677419355D,10D,10.52631579D,11.11111111D,59.74025974D,78.57142857D,85.56701031D,90.29126214D,93.51851852D,93.63636364D,93.69369369D,96.42857143D,98.24561404D,100D,100D,100D,100D,100D,100D,100D,100D,100D,94.02985075D,88.05970149D,90.12345679D,89.74358974D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,89.61038961D,94.80519481D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,23.61111111D,11.42857143D,7.540983607D,5.263157895D,4.043126685D,3.157894737D,2.325581395D,1.52284264D,0.748129676D,0D,13.48973607D,18.47389558D,24.33862434D,38.05970149D,44.35483871D,50.86206897D,57.27272727D,64.42307692D,72.44897959D,75.53191489D,26.88172043D,37.64705882D,37.20930233D,31.39534884D,26.43678161D,21.59090909D,21.59090909D,17.04545455D,12.64367816D,12.64367816D,46.55172414D,51.5625D,53.22580645D,60.6557377D,68.33333333D,76.27118644D,75.86206897D,77.19298246D,82.45614035D,87.71929825D,59.64912281D,36.84210526D,36.84210526D,29.8245614D,22.80701754D,15.78947368D,10.52631579D,10.52631579D,5.263157895D,0D,28.07017544D,50.87719298D,50.87719298D,57.89473684D,64.9122807D,71.92982456D,77.19298246D,77.19298246D,82.45614035D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,87.71929825D,85.10638298D,82.05128205D,92.30769231D,92.10526316D,91.89189189D,91.66666667D,91.66666667D,100D,100D,100D,32.25806452D,25.77319588D,21D,17.47572816D,14.28571429D,11.32075472D,8.411214953D,5.555555556D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,2.777777778D,14.28571429D,16.27906977D,17.94871795D,19.44444444D,20.58823529D,21.875D,23.33333333D,25D,25.92592593D,15.38461538D,15.38461538D,29.62962963D,42.85714286D,53.57142857D,64.28571429D,72.4137931D,80D,86.66666667D,93.33333333D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,63.7254902D,65.09433962D,66.66666667D,67.82608696D,63.02521008D,59.01639344D,60D,56.25D,56.92307692D,51.72413793D,60D,48.57142857D,50.90909091D,53.44827586D,60.50420168D,58.53658537D,58.53658537D,59.50413223D,55.83333333D,62.14285714D,79.74683544D,89.47368421D,89.77272727D,89.77272727D,89.83050847D,95.48022599D,95.5801105D,97.84946237D,100D,33.64269142D,20.4238921D,13.18864775D,9.554140127D,7.401574803D,5.651491366D,4.06885759D,2.496099844D,1.088646967D,0D,2.956989247D,6.024096386D,12.4137931D,21D,28.91566265D,36.11111111D,45.16129032D,57.69230769D,74.41860465D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,81.3559322D,71.42857143D,62.68656716D,55.71428571D,50D,45.33333333D,41.02564103D,43.20987654D,45.23809524D,35.21126761D,46.15384615D,55.55555556D,65D,74.13793103D,82.45614035D,91.07142857D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,51.1627907D,34.7826087D,28D,24D,20D,16D,12D,8D,4D,0D,48.21428571D,65.51724138D,76.27118644D,71.42857143D,76.11940299D,80D,83.33333333D,86.48648649D,89.47368421D,92.30769231D,94.82758621D,94.64285714D,94.49541284D,100D,100D,100D,100D,100D,100D,100D,93.33333333D,84D,73.91304348D,61.9047619D,50D,40D,30D,20D,10D,0D,30.76923077D,26.66666667D,39.39393939D,48.57142857D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,26.47058824D,27.27272727D,12.5D,0D,0D,0D,0D,0D,0D,0D,21.05263158D,20.51282051D,32.5D,41.46341463D,41.46341463D,42.5D,43.58974359D,44.73684211D,45.94594595D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,47.22222222D,72.85714286D,81.15942029D,75D,75D,75D,73.91304348D,77.14285714D,80.28169014D,83.09859155D,83.09859155D,40.47619048D,40.47619048D,41.46341463D,32.5D,32.5D,38.46153846D,36.84210526D,29.72972973D,24.32432432D,29.72972973D,53.57142857D,37.03703704D,37.03703704D,38.46153846D,40D,32D,24D,24D,24D,16D,0D,0D,0D,0D,9.090909091D,9.090909091D,9.090909091D,9.090909091D,14.28571429D,20D,21.05263158D,22.22222222D,22.22222222D,23.52941176D,18.75D,26.66666667D,28.57142857D,30.76923077D,30.76923077D,30.76923077D,85.71428571D,92.95774648D,96.62921348D,82.69230769D,84.87394958D,86.46616541D,87.94326241D,89.18918919D,89.61038961D,89.93710692D,75.53956835D,63.28125D,50.41322314D,53.98230088D,44.55445545D,33.33333333D,25D,16.4556962D,8.108108108D,0D,0D,9.803921569D,21.42857143D,32.43243243D,40D,48.48484848D,56.25D,64.51612903D,70.96774194D,73.33333333D,75.86206897D,65.38461538D,54.16666667D,43.47826087D,34.7826087D,26.08695652D,18.18181818D,9.523809524D,0D,0D,0D,0D,0D,0D,0D,10.52631579D,10D,9.523809524D,9.090909091D,8.695652174D,21.73913043D,31.81818182D,42.85714286D,52.38095238D,60D,57.89473684D,66.66666667D,76.47058824D,87.5D,100D,7.453416149D,5.464480874D,3.96039604D,2.857142857D,2.293577982D,1.793721973D,1.315789474D,0.862068966D,2.542372881D,2.092050209D,7.608695652D,10.14492754D,14.28571429D,17.5D,21.875D,25.92592593D,31.81818182D,38.88888889D,14.28571429D,18.18181818D,55D,66.66666667D,72D,76.92307692D,81.48148148D,85.18518519D,88.88888889D,92.59259259D,96.2962963D,100D,88.88888889D,73.33333333D,60D,46.66666667D,35.71428571D,28.57142857D,21.42857143D,14.28571429D,7.142857143D,0D,14.28571429D,28.57142857D,38.46153846D,50D,58.33333333D,66.66666667D,75D,83.33333333D,91.66666667D,100D,38.46153846D,29.62962963D,25.92592593D,22.22222222D,18.51851852D,14.81481481D,11.11111111D,7.407407407D,3.703703704D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,10D,10D,10D,10D,10D,10D,10D,10D,7.142857143D,7.142857143D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,8.333333333D,20D,30D,40D,50D,60D,70D,80D,90D,100D,100D,100D,100D,90D,90D,90D,90D,90D,90D,90D,94.44444444D,88.88888889D,83.33333333D,83.33333333D,77.77777778D,77.77777778D,77.77777778D,72.22222222D,66.66666667D,61.11111111D,18.18181818D,27.27272727D,36.36363636D,36.36363636D,45.45454545D,45.45454545D,45.45454545D,54.54545455D,63.63636364D,72.72727273D,90D,90D,90D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,81.81818182D,72.72727273D,63.63636364D,54.54545455D,45.45454545D,36.36363636D,27.27272727D,18.18181818D,9.090909091D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,10D,20D,30D,40D,50D,60D,70D,80D,90D,100D,100D,100D,70.22974451D,75.42498938D,64.39424865D,68.58994878D,70.59264254D,68.59483072D,69.27487713D,69.9019642D,65.06297331D,58.82886322D,73.73048627D,68.10920709D,87.00491606D,80.14449427D,73.26932012D,81.93626602D,80.14089871D,76.27368421D,80.83333333D,80.15146178D,88.08483082D,88.34889979D,92.05245694D,95.13190955D,97.2390259D,98.70908253D,98.71638142D,100D,43.7294733D,53.8553616D,45.18488827D,45.80402259D,47.09352935D,48.06797683D,48.98339862D,46.83509417D,47.64791026D,48.32574337D,84.8215453D,77.11413365D,93.04061471D,92.34299517D,91.66885677D,91.01983003D,90.32946919D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,95.57522124D,90.04975124D,84.61538462D,78.94736842D,73.40425532D,68.10810811D,63.04347826D,69.36936937D,75.45126354D,76.63230241D,80.92105263D,84.7133758D,88.23529412D,91.46341463D,94.56193353D,97.2972973D,100D,94.02390438D,82.35294118D,71.79487179D,60.95890411D,50.3649635D,39.0625D,28.45528455D,18.33333333D,9.322033898D,0D,81.89964158D,84.44846293D,86.8852459D,89.17431193D,91.14391144D,92.96296296D,94.7761194D,96.61654135D,98.29867675D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,25.56390977D,36.33440514D,41.59292035D,46.04904632D,48.03149606D,48.96907216D,49.61832061D,50.25125628D,48.25870647D,46.43734644D,93.2885906D,93.00699301D,92.45283019D,91.73553719D,91.45299145D,91.37931034D,91.37931034D,86.63793103D,90.94827586D,95.25862069D,99.09613804D,99.15123457D,99.20634921D,99.22261484D,99.23822715D,99.25322471D,99.26470588D,100D,100D,100D,57.14285714D,37.78705637D,23.92120075D,17.27791361D,12.28197674D,8.449767132D,5.754950495D,3.476245655D,1.535087719D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,4.458598726D,4.458598726D,4.458598726D,4.458598726D,4.456233422D,4.456233422D,4.456233422D,4.456233422D,4.456233422D,4.456233422D,4.456233422D,3.916083916D,4.164600892D,4.435058078D,4.743083004D,5.090909091D,5.511811024D,5.919661734D,6.382978723D,6.851549755D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,49.20634921D,65.56016598D,73.00380228D,78.54545455D,82.74647887D,80.47945205D,79.66101695D,83.05084746D,86.34812287D,89.38356164D,89.49152542D,87.84313725D,87.19008264D,86.69527897D,86.28318584D,94.03669725D,100D,100D,100D,100D,85.18518519D,70.3125D,55.2D,43.2D,33.07086614D,24.61538462D,17.77777778D,11.51079137D,5.594405594D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,71.79487179D,80.21390374D,87.2600349D,90.56291391D,93.28968903D,95.45454545D,96.91056911D,98.04878049D,99.02597403D,100D,91.05691057D,82.17821782D,70.3030303D,54.76190476D,41.81818182D,28.57142857D,21.27659574D,14.28571429D,6.818181818D,0D,46.21848739D,57.98319328D,69.16666667D,76.2295082D,81.96721311D,86.8852459D,90.32258065D,93.6D,96.82539683D,100D,100D,87.95180723D,81.02766798D,75.58139535D,76.04562738D,72.11895911D,68.61313869D,69.28571429D,66.7844523D,64.33566434D,70.08797654D,83.56164384D,88.31168831D,91.84100418D,92.07317073D,94.50101833D,96.73469388D,96.72801636D,98.36400818D,100D,100D,100D,100D,100D,100D,94.73684211D,94.44444444D,94.11764706D,93.82716049D,88.46153846D,90.10989011D,91.26213592D,91.66666667D,92.03539823D,92.30769231D,96.69421488D,96.74796748D,96.77419355D,96.8D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,78.69822485D,60.60606061D,49.32432432D,39.28571429D,30.3030303D,22.4D,20.49180328D,15.12605042D,9.482758621D,4.385964912D,4.464285714D,29.72972973D,25.78125D,22.91666667D,20.75471698D,19.41176471D,15.73033708D,15.13513514D,14.58333333D,14.07035176D,6.862745098D,10.16548463D,12.62376238D,13.24675325D,15.2173913D,17.08683473D,17.52873563D,17.94117647D,19.03323263D,19.56521739D,54.7826087D,24.69135802D,15D,15D,8.536585366D,2.43902439D,2.43902439D,2.409638554D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,33.78378378D,49.36708861D,58.2278481D,66.66666667D,75D,81.33333333D,86.30136986D,91.54929577D,95.71428571D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,100D,81.48148148D,63.82978723D,52.27272727D,40.47619048D,29.26829268D,19.51219512D,14.28571429D,9.302325581D,4.545454545D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D,0D
        };
        double total = 0;
        int count = 0;
        for(int i=0;i<accs.length;i++){
            if(i%10==0){
                count++;
                total += accs[i];
            }
        }
        System.out.println(total);
        System.out.println(count);
        System.out.println(formatDouble1(total/count));
    }

    @Test
    public void top30AvgAll(){
        String str = " [\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 5991,\n" +
                "          \"word\": \"陪玩\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"62.84%\",\n" +
                "          \"count\": 1920,\n" +
                "          \"word\": \"虎牙\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1681,\n" +
                "          \"word\": \"ོ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"8.61%\",\n" +
                "          \"count\": 1630,\n" +
                "          \"word\": \"AV\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"82.01%\",\n" +
                "          \"count\": 1564,\n" +
                "          \"word\": \"QQ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1520,\n" +
                "          \"word\": \"接通宵\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1510,\n" +
                "          \"word\": \"sp\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"14.46%\",\n" +
                "          \"count\": 1468,\n" +
                "          \"word\": \"SM\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"4.24%\",\n" +
                "          \"count\": 1068,\n" +
                "          \"word\": \"ིྀ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 876,\n" +
                "          \"word\": \"抖音\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 872,\n" +
                "          \"word\": \"ྀི\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 861,\n" +
                "          \"word\": \"互粉\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 837,\n" +
                "          \"word\": \"互关\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 826,\n" +
                "          \"word\": \"༺\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"55.56%\",\n" +
                "          \"count\": 736,\n" +
                "          \"word\": \"SB\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 695,\n" +
                "          \"word\": \"EV2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 624,\n" +
                "          \"word\": \"连麦\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 594,\n" +
                "          \"word\": \"pei聊\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 584,\n" +
                "          \"word\": \"连休\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 548,\n" +
                "          \"word\": \"࿐\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 511,\n" +
                "          \"word\": \"代练\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 506,\n" +
                "          \"word\": \"毒药\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 479,\n" +
                "          \"word\": \"代打\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 394,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 312,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 311,\n" +
                "          \"word\": \"ke咳\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 295,\n" +
                "          \"word\": \"可咳\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 286,\n" +
                "          \"word\": \"愛愛\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 280,\n" +
                "          \"word\": \"斗鱼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 275,\n" +
                "          \"word\": \"da亡者\"\n" +
                "        },{\n" +
                "\t\"rate\": \"0.00%\",\n" +
                "\t\"count\": 8250,\n" +
                "\t\"word\": \"陪玩\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"0.00%\",\n" +
                "\t\"count\": 2847,\n" +
                "\t\"word\": \"微信\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"0.00%\",\n" +
                "\t\"count\": 2401,\n" +
                "\t\"word\": \"QQ\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"23.17%\",\n" +
                "\t\"count\": 1859,\n" +
                "\t\"word\": \"线下\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"59.74%\",\n" +
                "\t\"count\": 1668,\n" +
                "\t\"word\": \"连麦\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 1561,\n" +
                "\t\"word\": \"接通宵\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"90.12%\",\n" +
                "\t\"count\": 1148,\n" +
                "\t\"word\": \"pei聊\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 1108,\n" +
                "\t\"word\": \"da亡者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 1108,\n" +
                "\t\"word\": \"da王者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 1108,\n" +
                "\t\"word\": \"打亡者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 1108,\n" +
                "\t\"word\": \"打王者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"23.61%\",\n" +
                "\t\"count\": 1023,\n" +
                "\t\"word\": \"k|接\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"13.49%\",\n" +
                "\t\"count\": 948,\n" +
                "\t\"word\": \"愛愛\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"26.88%\",\n" +
                "\t\"count\": 898,\n" +
                "\t\"word\": \"互关\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"46.55%\",\n" +
                "\t\"count\": 827,\n" +
                "\t\"word\": \"接线下\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"59.65%\",\n" +
                "\t\"count\": 693,\n" +
                "\t\"word\": \"jie聊\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"28.07%\",\n" +
                "\t\"count\": 691,\n" +
                "\t\"word\": \"接聊\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"87.72%\",\n" +
                "\t\"count\": 669,\n" +
                "\t\"word\": \"jie聊天\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"85.11%\",\n" +
                "\t\"count\": 665,\n" +
                "\t\"word\": \"接聊天\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"32.26%\",\n" +
                "\t\"count\": 610,\n" +
                "\t\"word\": \"k|聊\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"2.78%\",\n" +
                "\t\"count\": 503,\n" +
                "\t\"word\": \"可撩\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"14.29%\",\n" +
                "\t\"count\": 501,\n" +
                "\t\"word\": \"ke撩\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"15.38%\",\n" +
                "\t\"count\": 462,\n" +
                "\t\"word\": \"k|私\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 430,\n" +
                "\t\"word\": \"哄睡\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 377,\n" +
                "\t\"word\": \"jie亡者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 376,\n" +
                "\t\"word\": \"jie王者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 362,\n" +
                "\t\"word\": \"接亡者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 358,\n" +
                "\t\"word\": \"接王者\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 326,\n" +
                "\t\"word\": \"代练\"\n" +
                "},\n" +
                "{\n" +
                "\t\"rate\": \"100.00%\",\n" +
                "\t\"count\": 319,\n" +
                "\t\"word\": \"虎牙\"\n" +
                "},{\n" +
                "          \"rate\": \"56.92%\",\n" +
                "          \"count\": 5905,\n" +
                "          \"word\": \"微信\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"55.83%\",\n" +
                "          \"count\": 3581,\n" +
                "          \"word\": \"QQ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2645,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"45.24%\",\n" +
                "          \"count\": 2634,\n" +
                "          \"word\": \"抖音\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"89.94%\",\n" +
                "          \"count\": 1877,\n" +
                "          \"word\": \"虎牙\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1712,\n" +
                "          \"word\": \"愛愛\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1168,\n" +
                "          \"word\": \"白嫖\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1046,\n" +
                "          \"word\": \"代练\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 848,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 765,\n" +
                "          \"word\": \"傻逼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 708,\n" +
                "          \"word\": \"毒药\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"92.31%\",\n" +
                "          \"count\": 602,\n" +
                "          \"word\": \"接线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"83.1%\",\n" +
                "          \"count\": 545,\n" +
                "          \"word\": \"SB\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"47.22%\",\n" +
                "          \"count\": 495,\n" +
                "          \"word\": \"口扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"47.22%\",\n" +
                "          \"count\": 494,\n" +
                "          \"word\": \"叩扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"47.22%\",\n" +
                "          \"count\": 484,\n" +
                "          \"word\": \"寇扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"47.22%\",\n" +
                "          \"count\": 473,\n" +
                "          \"word\": \"扣扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"47.22%\",\n" +
                "          \"count\": 472,\n" +
                "          \"word\": \"抠扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 449,\n" +
                "          \"word\": \"国母\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"16.0%\",\n" +
                "          \"count\": 410,\n" +
                "          \"word\": \"电台\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"20.0%\",\n" +
                "          \"count\": 367,\n" +
                "          \"word\": \"我Q\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"29.73%\",\n" +
                "          \"count\": 343,\n" +
                "          \"word\": \"AV\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 338,\n" +
                "          \"word\": \"FM2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"73.33%\",\n" +
                "          \"count\": 332,\n" +
                "          \"word\": \"加V\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"8.7%\",\n" +
                "          \"count\": 329,\n" +
                "          \"word\": \"白粉\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 301,\n" +
                "          \"word\": \"EV2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"30.77%\",\n" +
                "          \"count\": 219,\n" +
                "          \"word\": \"包c\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 196,\n" +
                "          \"word\": \"飞草\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 164,\n" +
                "          \"word\": \"比心币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 154,\n" +
                "          \"word\": \"代陪\"\n" +
                "        },{\n" +
                "          \"rate\": \"2.09%\",\n" +
                "          \"count\": 832,\n" +
                "          \"word\": \"愛愛\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"18.18%\",\n" +
                "          \"count\": 229,\n" +
                "          \"word\": \"微信\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 126,\n" +
                "          \"word\": \"傻逼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 83,\n" +
                "          \"word\": \"哥哥射\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 82,\n" +
                "          \"word\": \"白嫖\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 81,\n" +
                "          \"word\": \"比心币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 74,\n" +
                "          \"word\": \"哥哥操\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 73,\n" +
                "          \"word\": \"比心比\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 73,\n" +
                "          \"word\": \"笔芯比\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"10.0%\",\n" +
                "          \"count\": 69,\n" +
                "          \"word\": \"QQ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 66,\n" +
                "          \"word\": \"代练\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 66,\n" +
                "          \"word\": \"电台\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 48,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"90.0%\",\n" +
                "          \"count\": 48,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"61.11%\",\n" +
                "          \"count\": 47,\n" +
                "          \"word\": \"SB\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"72.73%\",\n" +
                "          \"count\": 36,\n" +
                "          \"word\": \"我操\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 35,\n" +
                "          \"word\": \"娇喘\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 34,\n" +
                "          \"word\": \"shua单\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 34,\n" +
                "          \"word\": \"刷单\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 34,\n" +
                "          \"word\": \"口活\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 30,\n" +
                "          \"word\": \"包養\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 30,\n" +
                "          \"word\": \"撩骚\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 29,\n" +
                "          \"word\": \"鸡巴\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 28,\n" +
                "          \"word\": \"AV\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 28,\n" +
                "          \"word\": \"six 四\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 28,\n" +
                "          \"word\": \"six四\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 28,\n" +
                "          \"word\": \"比心必\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 28,\n" +
                "          \"word\": \"笔芯必\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 24,\n" +
                "          \"word\": \"EV2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 24,\n" +
                "          \"word\": \"他妈的\"\n" +
                "        },{\n" +
                "          \"rate\": \"69.9%\",\n" +
                "          \"count\": 145615,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"76.27%\",\n" +
                "          \"count\": 54380,\n" +
                "          \"word\": \"抖音\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 41083,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"48.33%\",\n" +
                "          \"count\": 31622,\n" +
                "          \"word\": \"接线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 27340,\n" +
                "          \"word\": \"白嫖\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 10641,\n" +
                "          \"word\": \"斗鱼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 7677,\n" +
                "          \"word\": \"雞吧\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 7472,\n" +
                "          \"word\": \"撩骚\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 6509,\n" +
                "          \"word\": \"皇色\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 5865,\n" +
                "          \"word\": \"聊骚\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 5191,\n" +
                "          \"word\": \"包養\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 3701,\n" +
                "          \"word\": \"白砒\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 3434,\n" +
                "          \"word\": \"脑残\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 3284,\n" +
                "          \"word\": \"陪睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 3220,\n" +
                "          \"word\": \"cnm\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2547,\n" +
                "          \"word\": \"卖身\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2259,\n" +
                "          \"word\": \"涩情\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2040,\n" +
                "          \"word\": \"接K\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2011,\n" +
                "          \"word\": \"穷逼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"63.04%\",\n" +
                "          \"count\": 1718,\n" +
                "          \"word\": \"b站\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1446,\n" +
                "          \"word\": \"撩s\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1345,\n" +
                "          \"word\": \"雞女\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1279,\n" +
                "          \"word\": \"你他妈的\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1270,\n" +
                "          \"word\": \"你他马的\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1270,\n" +
                "          \"word\": \"你她马的\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1267,\n" +
                "          \"word\": \"你它妈的\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1264,\n" +
                "          \"word\": \"吗逼\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1260,\n" +
                "          \"word\": \"你它马的\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"46.44%\",\n" +
                "          \"count\": 1194,\n" +
                "          \"word\": \"革命\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"95.26%\",\n" +
                "          \"count\": 1151,\n" +
                "          \"word\": \"全民K歌\"\n" +
                "        },{\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 145615,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 54380,\n" +
                "          \"word\": \"比心币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"4.46%\",\n" +
                "          \"count\": 41083,\n" +
                "          \"word\": \"笔芯币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"4.46%\",\n" +
                "          \"count\": 31622,\n" +
                "          \"word\": \"比芯币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 27340,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 10641,\n" +
                "          \"word\": \"代陪\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"89.38%\",\n" +
                "          \"count\": 7677,\n" +
                "          \"word\": \"皇色\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 7472,\n" +
                "          \"word\": \"白嫖\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 6509,\n" +
                "          \"word\": \"EV2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 5865,\n" +
                "          \"word\": \"抠扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 5191,\n" +
                "          \"word\": \"口扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 3701,\n" +
                "          \"word\": \"叩扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 3434,\n" +
                "          \"word\": \"寇扣\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 3284,\n" +
                "          \"word\": \"cnm\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 3220,\n" +
                "          \"word\": \"雞女\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2547,\n" +
                "          \"word\": \"包養\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"64.34%\",\n" +
                "          \"count\": 2259,\n" +
                "          \"word\": \"阿刁\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 2040,\n" +
                "          \"word\": \"逍遥阁\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"88.46%\",\n" +
                "          \"count\": 2011,\n" +
                "          \"word\": \"fan墙\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1718,\n" +
                "          \"word\": \"脑残\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1446,\n" +
                "          \"word\": \"毒药\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"4.39%\",\n" +
                "          \"count\": 1345,\n" +
                "          \"word\": \"硬币\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"14.07%\",\n" +
                "          \"count\": 1279,\n" +
                "          \"word\": \"点头像\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"19.57%\",\n" +
                "          \"count\": 1270,\n" +
                "          \"word\": \"色色\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1270,\n" +
                "          \"word\": \"chuB\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1267,\n" +
                "          \"word\": \"出b\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1264,\n" +
                "          \"word\": \"v-p-n\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1260,\n" +
                "          \"word\": \"v.p.n\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1194,\n" +
                "          \"word\": \"薇我\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1151,\n" +
                "          \"word\": \"習x\"\n" +
                "        }\n" +
                "      ]";
        int totalCount = 0;
        double rateCount = 0;
        JSONArray jsonArray = JSON.parseArray(str);
        for(int i=0;i<jsonArray.size();i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            int count = jsonObject.getInteger("count");
            totalCount+= jsonObject.getInteger("count");
            String rate = jsonObject.getString("rate");
            rateCount+=count*(Double.parseDouble(rate.substring(0,rate.length()-1)))*100;
        }
        System.out.println(rateCount);
        System.out.println(totalCount);
        System.out.println(rateCount/totalCount);
    }

    // 一个维度的合并
    public void top30OneDimensionTest(){
        String str = "[\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 8250,\n" +
                "          \"word\": \"陪玩\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 2847,\n" +
                "          \"word\": \"微信\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 2401,\n" +
                "          \"word\": \"QQ\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"17.27%\",\n" +
                "          \"count\": 1859,\n" +
                "          \"word\": \"线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"11.11%\",\n" +
                "          \"count\": 1668,\n" +
                "          \"word\": \"连麦\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 1561,\n" +
                "          \"word\": \"接通宵\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"16.42%\",\n" +
                "          \"count\": 1148,\n" +
                "          \"word\": \"pei聊\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1108,\n" +
                "          \"word\": \"da亡者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1108,\n" +
                "          \"word\": \"da王者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1108,\n" +
                "          \"word\": \"打亡者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1108,\n" +
                "          \"word\": \"打王者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 1023,\n" +
                "          \"word\": \"k|接\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 948,\n" +
                "          \"word\": \"愛愛\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"75.53%\",\n" +
                "          \"count\": 898,\n" +
                "          \"word\": \"互关\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"12.64%\",\n" +
                "          \"count\": 827,\n" +
                "          \"word\": \"接线下\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 693,\n" +
                "          \"word\": \"jie聊\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 691,\n" +
                "          \"word\": \"接聊\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 669,\n" +
                "          \"word\": \"jie聊天\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 665,\n" +
                "          \"word\": \"接聊天\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"83.33%\",\n" +
                "          \"count\": 610,\n" +
                "          \"word\": \"k|聊\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"2.78%\",\n" +
                "          \"count\": 503,\n" +
                "          \"word\": \"可撩\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"2.78%\",\n" +
                "          \"count\": 501,\n" +
                "          \"word\": \"ke撩\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"15.38%\",\n" +
                "          \"count\": 462,\n" +
                "          \"word\": \"k|私\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 430,\n" +
                "          \"word\": \"哄睡\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 377,\n" +
                "          \"word\": \"jie亡者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 376,\n" +
                "          \"word\": \"jie王者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 362,\n" +
                "          \"word\": \"接亡者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"0.00%\",\n" +
                "          \"count\": 358,\n" +
                "          \"word\": \"接王者\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 326,\n" +
                "          \"word\": \"代练\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"rate\": \"100.00%\",\n" +
                "          \"count\": 319,\n" +
                "          \"word\": \"虎牙\"\n" +
                "        }\n" +
                "      ]";
        JSONArray jsonArray = JSON.parseArray(str);
        Double[] dous = new Double[]{
                0.0D,0.0D,0.0D,23.17D,59.74D,100.0D,90.12D,100.0D,100.0D,100.0D,100.0D,23.61D,13.49D,26.88D,46.55D,59.65D,28.07D,87.72D,85.11D,32.26D,2.78D,14.29D,15.38D,100.0D,100.0D,100.0D,100.0D,100.0D,100.0D,100.0D
        };
        List<JSONObject> list = new ArrayList<>();
        for(int i=0;i<jsonArray.size();i++){
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Double dou = dous[i];
            if(dou==0){
                jsonObject.put("rate","0.00%");
            }else if(dou == 100){
                jsonObject.put("rate","100.00%");
            }else{
                jsonObject.put("rate",dou+"%");
            }
            list.add(jsonObject);
        }
        System.out.println(JSON.toJSONString(list));
    }

    // MAP合并排序测试
    @Test
    public void mapMergeTest(){
        String aMapStr = "{\"微信\":3319,\"QQ\":2001,\"线下\":1285,\"愛愛\":966,\"代练\":645,\"哄睡\":606,\"抖音\":506,\"白嫖\":466,\"毒药\":457,\"国母\":448,\"接线下\":341,\"FM2\":338,\"傻逼\":299,\"口扣\":242,\"叩扣\":242,\"寇扣\":237,\"扣扣\":231,\"抠扣\":230,\"EV2\":225,\"SB\":215,\"飞草\":189,\"电台\":185,\"我Q\":184,\"AV\":180,\"虎牙\":173,\"白粉\":164,\"包c\":160,\"我草\":137,\"大麻\":133,\"加V\":132}";
        String bMapStr = "{\"抖音\":1481,\"虎牙\":1359,\"微信\":1230,\"斗鱼\":1043,\"QQ\":717,\"线下\":679,\"白嫖\":438,\"傻逼\":306,\"互粉\":260,\"毒药\":242,\"愛愛\":200,\"SB\":199,\"大麻\":169,\"白粉\":165,\"电台\":143,\"口扣\":130,\"叩扣\":130,\"寇扣\":128,\"抠扣\":126,\"加V\":125,\"扣扣\":125,\"代练\":123,\"我Q\":122,\"接线下\":110,\"虎牙直播\":83,\"皇色\":70,\"曹你\":68,\"蹭蹭\":68,\"哄睡\":65,\"我操\":63}";
        Map<String,Object> aMap = JSON.parseObject(aMapStr,LinkedHashMap.class);
        Map<String,Object> bMap = JSON.parseObject(bMapStr,LinkedHashMap.class);
        Map<String,Object> rtnMap = new LinkedHashMap<>();
        for(Map.Entry<String,Object> entry : aMap.entrySet() ){
            String key = entry.getKey();
            if(rtnMap.containsKey(key)){
                rtnMap.put(key,Long.parseLong(rtnMap.get(key)+"")+Long.parseLong(aMap.get(key)+""));
            }else{
                rtnMap.put(key,aMap.get(key));
            }
        }

        List<Map.Entry<String, Object>> list = new ArrayList<Map.Entry<String, Object>>(rtnMap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> m1, Map.Entry<String, Object> m2) {
                if(m1.getValue()==null || m2.getValue()==null){
                    return 0;
                }
                return Integer.parseInt(m2.getValue()+"")-Integer.parseInt(m1.getValue()+"");
            }
        });

        for (Map.Entry<String, Object> mapping : list) {
            rtnMap.put(mapping.getKey(),mapping.getValue());
        }

        System.out.println(JSON.toJSONString(rtnMap));

//        for (Map.Entry<String, Object> entry : rtnMap.entrySet()) {
//            System.out.println(entry.getKey()+"=="+entry.getValue());
//        }

        for(Map.Entry<String,Object> entry : bMap.entrySet() ){
            String key = entry.getKey();
            if(rtnMap.containsKey(key)){
                rtnMap.put(key,Long.parseLong(rtnMap.get(key)+"")+Long.parseLong(bMap.get(key)+""));
            }else{
                rtnMap.put(key,bMap.get(key));
            }
        }

        List<Map.Entry<String, Object>> list2 = new ArrayList<Map.Entry<String, Object>>(rtnMap.entrySet());
        Collections.sort(list2, new Comparator<Map.Entry<String, Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> m1, Map.Entry<String, Object> m2) {
                if(m1.getValue()==null || m2.getValue()==null){
                    return 0;
                }
                return Integer.parseInt(m2.getValue()+"")-Integer.parseInt(m1.getValue()+"");
            }
        });

        Map<String,Object> sortMap = new LinkedHashMap<>();
        for (Map.Entry<String, Object> mapping : list2) {
            sortMap.put(mapping.getKey(),mapping.getValue());
        }

        System.out.println(JSON.toJSONString(sortMap));
    }

    @Test
    public void matchTextTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-05-13 15:41:03");
        Date endTime = format.parse("2020-05-13 15:44:03");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(1000);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();

        String eventCode = "active-anonymity-message";

        hitResult.setEventCode(eventCode);
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("data.signature","陪玩"));

        Long fromUid = 193080032574186140L;
        Long toUid = 201340797298838360L;

        BoolQueryBuilder boolQuery = new BoolQueryBuilder();

        BoolQueryBuilder query1 = new BoolQueryBuilder();
        query1.must(QueryBuilders.termQuery("data.targetUserId", toUid));
        query1.must(QueryBuilders.termQuery("userId", fromUid));
        query1.must(QueryBuilders.termQuery("eventCode", eventCode));
        boolQuery.should(query1);

        BoolQueryBuilder query2 = new BoolQueryBuilder();
        query2.must(QueryBuilders.termQuery("data.targetUserId", fromUid));
        query2.must(QueryBuilders.termQuery("userId", toUid));
        query2.must(QueryBuilders.termQuery("eventCode", eventCode));
        boolQuery.should(query2);

        query.must(boolQuery);

        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println("list:"+JSON.toJSONString(pageInfo.getList()));
    }

    @Test
    public void getAuditLogNotifyTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Date startTime = format.parse("2020-05-18 00:00:00");
//        Date endTime = format.parse("2020-05-18 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(1000);
//        vo.setStartTime(startTime);
//        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
//        hitResult.setEventCode("user-update-signature");
        hitResult.setTraceId("773704aa2ab0431a802bb6c7d49c1530");
        vo.setQuery(hitResult);
//        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
//        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_audit_notify_log",RiskLogESUtil.pattern_default);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_*_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("data.signature","陪玩"));
//        query.must(QueryBuilders.termQuery("uid",200331114446366850l));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(JSON.toJSONString(pageInfo));
    }

    @Test
    public void getRisPunishTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-08-25 15:00:00");
        Date endTime = format.parse("2020-08-27 00:00:00");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(10000);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        /*HitResult hitResult = new HitResult();
        hitResult.setEventCode("user-update-signature");
        vo.setQuery(hitResult);*/
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_punish_log",RiskLogESUtil.pattern_default);
        /*BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.termQuery("data.signature","陪玩"));*/
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.termQuery("channel","COMPLAIN"));
//        query.must(QueryBuilders.termQuery("uid",191460489252108545l));
//        query.must(QueryBuilders.termQuery("pkgName","IM涉黄-永冻"));
        RangeQueryBuilder builder = QueryBuilders.rangeQuery("punishPackageId");
//        builder.lte(388);
        builder.lte(875);
        query.filter(builder);
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(pageInfo.getTotal());
        System.out.println("list========="+pageInfo.getList());
        List list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            Set<Long> uids = new HashSet<>();
            for(int i=0;i<list.size();i++){
                JSONObject json = JSON.parseObject(JSON.toJSONString(list.get(i)));
                if(json.getLong("uid") != null){
                    uids.add(json.getLong("uid"));
                }
            }
            System.out.println("uids===="+JSON.toJSONString(uids));
        }
    }

    @Test
    public void getRiskHitImTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-11-01 00:00:00");
//        Date endTime = format.parse("2019-09-27 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(100);
        vo.setStartTime(startTime);
//        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_im_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("traceId","003d8b2b30b44beeb3af908abc5fdac5"));
        query.must(QueryBuilders.termQuery("userId","191460489252108545"));
        query.must(QueryBuilders.termQuery("eventCode","im-message"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(JSON.toJSON(pageInfo));
    }

    @Test
    public void hitAggTest2() throws  Exception{
        Date date = com.yupaopao.risk.console.utils.DateUtils.getBeforeDayZeroTime(1);
        System.out.println(date);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = date;
//        Date endTime = format.parse("2021-06-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
//        vo.setEndTime(new Date());
        HitResult hitResult = new HitResult();
//        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        vo.setQuery(hitResult);

        /*AggregationBuilder agg = AggregationBuilders.terms("1").field("level").
                subAggregation(AggregationBuilders.terms("2").field("userId"));*/


        SearchRequest request = RiskLogESUtil.buildSearchRequestIm(vo, RiskLogESUtil.HIT_LOG_INDEX,RiskLogESUtil.pattern_default);
//        BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
//        query.must(QueryBuilders.termQuery("result.rule", 273));
//        AggregationBuilder agg = AggregationBuilders.terms("1").field("level");

        BoolQueryBuilder query = (BoolQueryBuilder) request.source().query();
        query.must(QueryBuilders.termQuery("result.rule", 273));
        BoolQueryBuilder levelQuery = new BoolQueryBuilder();
        levelQuery.should(QueryBuilders.termQuery("level", "REJECT"));
        levelQuery.should(QueryBuilders.termQuery("level", "REVIEW"));
        query.must(levelQuery);

        query.must(QueryBuilders.existsQuery("mobileNo"));
        query.mustNot(QueryBuilders.termQuery("mobileNo", ""));

        /*query.must(QueryBuilders.existsQuery("userId"));
        query.mustNot(QueryBuilders.termQuery("userId", ""));*/

        /*AggregationBuilder agg = AggregationBuilders.terms("1").field("userId").size(1000);
        request.source().aggregation(agg);
        SearchResponse response = client.search(request);
        Map<String, Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(), CollectionUtils.isNotEmpty(agg.getSubAggregations()));
//        System.out.println(JSON.toJSONString(aggMap));
        System.out.println(aggMap.size());*/

//        AggregationBuilder agg2 = AggregationBuilders.cardinality("1").field("userId");
        AggregationBuilder agg2 = AggregationBuilders.cardinality("1").field("mobileNo");
        request.source().aggregation(agg2);
        SearchResponse response2 = client.search(request);
        Long value = RiskLogESUtil.getCardinalityValue(response2.getAggregations());
        System.out.println(value);
    }

    public static void main(String[] args) {

    }

}