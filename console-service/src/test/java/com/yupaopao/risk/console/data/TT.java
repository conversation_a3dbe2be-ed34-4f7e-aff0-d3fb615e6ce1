package com.yupaopao.risk.console.data;

import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/1 10:14 AM
 */
public class TT {

    public static void main(String[] args) throws Exception {
        List<String> lines = FileUtils.readLines(new File("/data/risk-data/risk-images.txt"));
        StringBuilder html = new StringBuilder();
        for (String line : lines) {
            String[] items = line.split("\t");
            html.append("<a href=\"").append(items[0]).append("\"><img width=\"200\" src=\"").append(items[0]).append("\"/>").append(items[1]).append("</a><br/><br/>");
        }
        FileUtils.write(new File("/data/risk-data/risk-image.html"), html);
    }

}
