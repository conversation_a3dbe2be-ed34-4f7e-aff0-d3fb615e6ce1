package com.yupaopao.risk.console;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Before;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/2/21 5:04 PM
 */
@Slf4j
public class ElasticSearchTestCase {

    protected RestHighLevelClient client;
    protected String index;

    @Before
    public void init() {
        String url = "mwes.yupaopao.com"; // test-es.yupaopao.com
        int port = 80;
        String protocol = "http";
        String username = "middleware";
        String passowrd = "WJqtMRtwPiHKRHFXuHGL9";
        RestClientBuilder builder = RestClient.builder(new HttpHost(url, port, protocol));
        if (StringUtils.isNotBlank(username) && StringUtils.isNotBlank(passowrd)) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, passowrd));
            builder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
        }
        builder.setMaxRetryTimeoutMillis(10000);

        client = new RestHighLevelClient(builder);
        index = "risk_hit_log_" + DateFormatUtils.format(new Date(), "yyyyMMdd");
    }

}
