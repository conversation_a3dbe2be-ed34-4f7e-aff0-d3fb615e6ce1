package com.yupaopao.risk.console.job;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.payment.common.Page;
import com.yupaopao.payment.common.enums.App;
import com.yupaopao.payment.open.account.api.JournalService;
import com.yupaopao.payment.open.account.request.*;
import com.yupaopao.payment.open.account.response.*;
import com.yupaopao.platform.audit.appeal.api.AppealApiService;
import com.yupaopao.platform.audit.appeal.api.entity.request.AppealRequest;
import com.yupaopao.platform.audit.appeal.api.entity.request.PunishRequest;
import com.yupaopao.platform.audit.appeal.api.entity.response.PunishRecordResponse;
import com.yupaopao.platform.audit.appeal.api.punish.PunishApiService;
import com.yupaopao.platform.audit.complain.api.entity.request.ComplainUserInfoRequest;
import com.yupaopao.platform.audit.complain.api.entity.response.ComplainUserInfoResponse;
import com.yupaopao.platform.audit.complain.api.service.ComplainUserInfoService;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.passport.api.AccountQueryService;
import com.yupaopao.risk.console.TestApplication;
import com.yupaopao.risk.console.service.HttpClientService;
import com.yupaopao.risk.console.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class) // SpringJUnit支持，由此引入Spring-Test框架支持！
@SpringBootTest(classes = TestApplication.class) // 指定我们SpringBoot工程的Application启动类
public class LoginNameListCheckJobTest {

    @Reference(check = false)
    private AccountQueryService accountQueryService;

    @Autowired
    LoginNameListCheckJob loginNameListCheckJob;


    @Autowired
    private RedisService redisService;

    @Test
    public void execute() {
  /*      for (int i=1;i<=10;i++){
            Map map = new HashMap<>();
            map.put(i, i);
            redisService.lAdd("abc", map, 5*60);
        }*/
        List<Object> abc = redisService.lGet("abc", 0, -1);
        System.out.println(abc);

 /*       JobExecutionContext jobExecutionContext = new JobExecutionContext();
        loginNameListCheckJob.execute(jobExecutionContext);*/
/*        Response<AccountDto> accountInfoByMobile = accountQueryService.getAccountInfoByMobile("86", "***********");
        if (accountInfoByMobile.isSuccess()) {
            System.out.println(accountInfoByMobile.getResult().getUserId());
        }else if (!accountInfoByMobile.isSuccess() && null == accountInfoByMobile.getResult()){
            System.out.println("false");
        }else {
            System.out.println("nothing");
        }*/
    }

    @Reference(check = false)
    private JournalService journalService;

    @Test
    public void test1(){
        CashJournalRequest request = CashJournalRequest.builder().uid(192680946574710068L).app(App.BIXIN).build();
        Response<Page<CashJournalResponse>> pageResponse = journalService.cashJournal(request);
        System.out.println(pageResponse);

        IncomeJournalRequest build1 = IncomeJournalRequest.builder().uid(192680946574710068L).app(App.BIXIN).build();
        Response<Page<IncomeJournalResponse>> pageResponse1 = journalService.incomeJournal(build1);

        DiamondJournalRequest build2 = DiamondJournalRequest.builder().app(App.BIXIN).uid(192680946574710068L).build();
        Response<Page<DiamondJournalResponse>> pageResponse2 = journalService.diamondJournal(build2);

        CharmJournalRequest build3 = CharmJournalRequest.builder().app(App.BIXIN).uid(192680946574710068L).build();
        Response<Page<CharmJournalResponse>> pageResponse3 = journalService.charmJournal(build3);

        StarDiamondJournalRequest build4 = StarDiamondJournalRequest.builder().app(App.BIXIN).uid(192680946574710068L).build();
        Response<Page<StarDiamondJournalResponse>> pageResponse4 = journalService.starDiamondJournal(build4);

        StarJournalRequest build = StarJournalRequest.builder().app(App.BIXIN).uid(192680946574710068L).build();
        Response<Page<StarJournalResponse>> pageResponse5 = journalService.starJournal(build);


        System.out.println(pageResponse);
    }


    @Autowired
    HttpClientService httpClientService;

    @Reference
    private PunishApiService punishApiService;

    @Reference
    private AppealApiService appealApiService;

    @Reference
    private ComplainUserInfoService complainUserInfoService;

    @Test
    public void test(){
        PunishRequest punishRequest = new PunishRequest();
        punishRequest.setUid(192400859423940002L);
        punishRequest.setStartTime(DateUtils.getBeforeDayStr(1,DateUtils.YYYY_MM_DD_HH_MM_SS));
        punishRequest.setEndTime(new Date());
        Response<List<PunishRecordResponse>> search = punishApiService.search(punishRequest);

        AppealRequest appealRequest = new AppealRequest();
        appealApiService.search(appealRequest);

        ComplainUserInfoRequest complainUserInfoRequest = new ComplainUserInfoRequest();
//        complainUserInfoRequest.setToUid();
        Response<List<ComplainUserInfoResponse>> complainHistory = complainUserInfoService.getComplainHistory(complainUserInfoRequest);
    }

    @Test
    public void test100(){
        String conf = "{\"hisComplainUrl\":\"https://audit.yupaopao.com/#/record/queryReport\",\"complainUrl\":\"https://audit.yupaopao.com/#/report/reportList\",\"cashJournalUrl\":\"https://pay.yupaopao.com/account/details/balance-journal\",\"incomeJournalUrl\":\"https://pay.yupaopao.com/account/details/income-journal\",\"diamondJournalUrl\":\"https://pay.yupaopao.com/account/details/diamond-journal\",\"charmJournalUrl\":\"https://pay.yupaopao.com/account/details/charm-journal\",\"starDiamondJournalUrl\":\"https://pay.yupaopao.com/account/details/starDiamond-journal\",\"starJournalUrl\":\"https://pay.yupaopao.com/account/details/star-journal\",\"orderUrl\":\"https://order-tools.yupaopao.com/order/orderInfo.html\",\"cdsUrl\":\"https://op.yupaopao.com/#/customer/16102\",\"appealUrl\":\"https://audit.yupaopao.com/#/appeal/appealAccount\"}";

        if (conf.startsWith("[")){
            List list = JSONObject.parseObject(conf, List.class);
            Object urlObj = list.get(0);
            Map map = JSONObject.parseObject(JSONObject.toJSONString(urlObj), Map.class);
        }else {
            Map map = JSONObject.parseObject(conf, Map.class);
            System.out.println(map);
        }

//        return JsonResult.success(map);
    }
}
