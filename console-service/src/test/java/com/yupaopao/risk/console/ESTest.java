package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.access.bean.RiskLevel;
import com.yupaopao.risk.access.bean.RiskResult;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.console.service.ElasticSearchTest;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.*;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.tophits.TopHits;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class ESTest extends ElasticSearchTest {
    private Date date;

    // 获取用户事件级别聚合数据
    @Test
    public void getRiskHit() throws Exception {
        SimpleDateFormat  format1 = new SimpleDateFormat("yyyy-MM-dd\'T\'HH:mm:ss+08:00");
        Date date = format1.parse("2022-01-29T12:00:00+08:00");
        System.out.println("date="+date);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = format.parse("2019-07-10 00:00:00");
        Date end = format.parse("2019-07-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(10000);
        vo.setStartTime(start);
        vo.setEndTime(end);
        HitResult hitResult = new HitResult();
        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        hitResult.setEventCode("chat-room");
        vo.setQuery(hitResult);
        PageInfo<Map<String, Object>> pageInfo = this.getRiskHitLogTest(vo);
        System.out.println(JSON.toJSONString(pageInfo.getList()));
    }

    @Test
    public void getRiskHitAgg() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = format.parse("2019-11-10 00:00:00");
        Date end = format.parse("2019-11-15 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(start);
        vo.setEndTime(end);
        HitResult hitResult = new HitResult();
//        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        vo.setQuery(hitResult);
        AggregationBuilder agg = AggregationBuilders.terms("1").field("eventCode").size(10).
                subAggregation(AggregationBuilders.terms("2").field("level").size(5));
        Map<String,Object> aggMap = this.getRiskHitLogAggTest(vo,agg,true);
        System.out.println(JSON.toJSONString(aggMap));
    }

    // 日期、客户端IP聚合
    @Test
    public void getRiskHitAggClentIP() throws  Exception{
        /*Date endTime = new Date();
        Date startTime = new Date(endTime.getTime()-6*24*60*60);*/
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-07-10 00:00:00");
        Date endTime = format.parse("2019-07-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.existsQuery("clientIp"));
        query.mustNot(QueryBuilders.termQuery("clientIp","0.0.0.0"));
        String[] includes = {"userId","data.targetUserId","createdAt","clientIp","traceId"};
        AggregationBuilder agg = AggregationBuilders.dateHistogram("1").field("createdAt").format("yyyy-MM-dd")
                .dateHistogramInterval(DateHistogramInterval.DAY).order(BucketOrder.key(false))
                .subAggregation(AggregationBuilders.terms("2").field("clientIp").order(BucketOrder.count(false)).
                        subAggregation(AggregationBuilders.topHits("top").fetchSource(includes,Strings.EMPTY_ARRAY).size(1)));
        request.source().aggregation(agg);
//        System.out.println(request.source());
        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),true);
        System.out.println(JSON.toJSONString(aggMap));

        // top hits
        if (response != null && response.status() == RestStatus.OK) {
            List<Map<String,Object>> list = new ArrayList<>();
            Aggregations aggregations = response.getAggregations();
            Histogram aggResult = aggregations.get("1");
            for (Histogram.Bucket entry : aggResult.getBuckets()) {
                Terms aggResult2 = entry.getAggregations().get("2");
                for (Terms.Bucket entry2 : aggResult2.getBuckets()) {
                    TopHits topHits = entry2.getAggregations().get("top");
                    for(SearchHit hit : topHits.getHits()){
                        list.add(hit.getSourceAsMap());
                    }
                }
            }
            System.out.println(list);
        }
    }

    // userId、targetUserId聚合 - IM单聊
    @Test
    public void imMessageUserAgg() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2021-06-30 00:00:00");
        Date endTime = format.parse("2021-06-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        String targetUserId = "data.targetUserId";

        query.must(QueryBuilders.existsQuery("userId"));
        query.must(QueryBuilders.existsQuery(targetUserId));

        String userId = "190910675010560001";
        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
        BoolQueryBuilder idQuery = new BoolQueryBuilder();
        idQuery.should(QueryBuilders.termQuery("userId",userId));
        idQuery.should(QueryBuilders.termQuery(targetUserId,userId));
        boolQuery.must(idQuery);
        query.must(boolQuery);

        AggregationBuilder agg = AggregationBuilders.terms("1").field("userId").
                subAggregation(AggregationBuilders.terms("2").field(targetUserId));
        request.source().aggregation(agg);
        System.out.println(request.source());
        SearchResponse response = this.getClient().search(request);
        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),true);
        System.out.println(JSON.toJSONString(aggMap));
    }

    // userId、targetUserId聚合 - IM单聊 top sub
    @Test
    public void imMessageUserAggTopSub() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2021-06-30 00:00:00");
        Date endTime = format.parse("2021-06-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        String targetUserId = "data.targetUserId";

        query.must(QueryBuilders.existsQuery("userId"));
        query.must(QueryBuilders.existsQuery(targetUserId));

        String userId = "190910675010560001";
        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
        BoolQueryBuilder idQuery = new BoolQueryBuilder();
        idQuery.should(QueryBuilders.termQuery("userId",userId));
        idQuery.should(QueryBuilders.termQuery(targetUserId,userId));
        boolQuery.must(idQuery);
        query.must(boolQuery);

        String[] includes = {"userId","data.targetUserId","createdAt"};
        AggregationBuilder agg = AggregationBuilders.terms("1").field("userId").size((1<<31)-1).subAggregation(AggregationBuilders.topHits("top1").fetchSource(includes, Strings.EMPTY_ARRAY).size(1))
                .subAggregation(AggregationBuilders.terms("2").field(targetUserId).size((1<<31)-1).subAggregation(AggregationBuilders.topHits("top2").fetchSource(includes, Strings.EMPTY_ARRAY).size(1)));
        request.source().aggregation(agg);
        System.out.println(request.source());

        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo);
        System.out.println(pageInfo);

        Map<String,Object> aggMap = RiskLogESUtil.parseAggregations(response.getAggregations(),true);
        System.out.println(JSON.toJSONString(aggMap));

        // top hits
        if (response != null && response.status() == RestStatus.OK) {
            List<Map<String,Object>> list = new ArrayList<>();
            Aggregations aggregations = response.getAggregations();
            Terms aggResult = aggregations.get("1");
            for (Terms.Bucket entry : aggResult.getBuckets()) {
                TopHits topHits = entry.getAggregations().get("top1");
                for(SearchHit hit : topHits.getHits()){
                    list.add(hit.getSourceAsMap());
                }

                Terms aggResult2 = entry.getAggregations().get("2");
                for (Terms.Bucket entry2 : aggResult2.getBuckets()) {
                    TopHits topHits2 = entry2.getAggregations().get("top2");
                    for(SearchHit hit : topHits2.getHits()){
                        list.add(hit.getSourceAsMap());
                    }
                }
            }
            System.out.println(list);
        }

    }

    @Test
    public void getRiskHitTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-10-14 00:00:00");
        Date endTime = format.parse("2019-10-14 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(100);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
        hitResult.setUserId("1131c346d2b44ff9acafb32840b83402");
        vo.setQuery(hitResult);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("eventCode","active-partner-invite"));
//        query.must(QueryBuilders.termQuery("data.Mobile","b14a6842361c4dd39432bd3402aad2c4"));
        query.should(QueryBuilders.existsQuery("data.images"));
        query.should(QueryBuilders.existsQuery("data.avatar"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(pageInfo);
    }

    @Test
    public void getOperationLogTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-09-29 00:00:00");
        Date endTime = format.parse("2019-09-29 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(10);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequestTime(vo, RiskLogESUtil.OPERATION_LOG_INDEX,RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("userName","wangxinqi"));
//        query.filter(QueryBuilders.wildcardQuery("userName","*wangxin*"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo);
        System.out.println(pageInfo);
    }

    @Test
    public void getContentLogTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2019-09-01 00:00:00");
        Date endTime = format.parse("2019-09-11 23:59:59");
        LogSearchVO.ContentLogSearchVO vo = new LogSearchVO.ContentLogSearchVO();
        vo.setSize(10);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, RiskLogESUtil.CONTENT_LOG_INDEX,RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        query.must(QueryBuilders.termQuery("type","TEXT"));
        query.must(QueryBuilders.existsQuery("shumeiTextCheck"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo);
        System.out.println(pageInfo);
    }

    @Test
    public void createIndex() throws Exception{
        RiskResult riskResult = new RiskResult();
        riskResult.setLevel(RiskLevel.PASS);
        riskResult.setReason("wxqtest");
        String json = JSON.toJSONString(riskResult);
        IndexRequest request = new IndexRequest("risk_hit_log_wxq", "V1");
        request.source(json, XContentType.JSON);
        this.getClient().index(request);
    }

    @Test
    public void getRiskHitLogTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-11-01 00:00:00");
//        Date endTime = format.parse("2019-09-27 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(100);
        vo.setStartTime(startTime);
//        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("traceId","003d8b2b30b44beeb3af908abc5fdac5"));
        query.must(QueryBuilders.termQuery("userId","193130967535741974"));
        query.must(QueryBuilders.termQuery("eventCode","im-message"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(JSON.toJSON(pageInfo));
    }

    @Test
    public void getRiskHitImTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2020-11-01 00:00:00");
//        Date endTime = format.parse("2019-09-27 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(100);
        vo.setStartTime(startTime);
//        vo.setEndTime(endTime);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_im_log",RiskLogESUtil.pattern_default);
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
//        query.must(QueryBuilders.termQuery("traceId","003d8b2b30b44beeb3af908abc5fdac5"));
        query.must(QueryBuilders.termQuery("userId","193130967535741974"));
        query.must(QueryBuilders.termQuery("eventCode","im-message"));
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(JSON.toJSON(pageInfo));
    }

    @Test
    public void query_stringTest() throws  Exception{
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(10);
        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, "risk_hit_im_log",RiskLogESUtil.pattern_default);
        System.out.println(request);
        String param = "((eventCode: \"chat-room\" OR eventCode: \"search\" OR eventCode: \"timeline-publish-text\" OR eventCode: \"timeline-comment\" OR eventCode: \"play-order-user-comment\" OR eventCode: \"user-complete-nickname\" OR eventCode: \"user-update-nickname\" OR eventCode: \"play-order-biggie-comment\" OR eventCode: \"user-update-signature\" OR eventCode: \"biggie-cert-apply\" OR eventCode: \"voice-comment\" OR eventCode: \"active-mid-autumn\" OR eventCode: \"voice-publish\" OR eventCode: \"play-order-create\" OR eventCode: \"private-chat\" OR eventCode: \"red-envelope-create\" OR eventCode: \"active-comment-template\" OR eventCode: \"bx-room-create\" OR eventCode: \"bx-room-nickname\") AND level: \"REJECT\") OR (eventCode: \"im-message\" AND data.imTextCheck.riskLevel: \"REJECT\" AND level: \"REJECT\")";
        BoolQueryBuilder query = (BoolQueryBuilder)request.source().query();
        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
        boolQuery.must(QueryBuilders.queryStringQuery(param));
        query.must(boolQuery);
        System.out.println(request);
        SearchResponse response = this.getClient().search(request);
        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        RiskLogESUtil.handleResponse(response,pageInfo,false);
        System.out.println(pageInfo);
    }

    @Test
    public void hitAggTest() throws  Exception{
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = format.parse("2021-06-30 00:00:00");
        Date endTime = format.parse("2021-06-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        HitResult hitResult = new HitResult();
//        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        vo.setQuery(hitResult);
        AggregationBuilder agg = AggregationBuilders.terms(UUID.randomUUID().toString()).field("eventCode").size(10)
                .subAggregation(AggregationBuilders.terms(UUID.randomUUID().toString()).field("clientIp").size(5)
                        .subAggregation(AggregationBuilders.dateHistogram(UUID.randomUUID().toString()).field("createdAt").dateHistogramInterval(DateHistogramInterval.DAY)
                                .subAggregation(AggregationBuilders.terms(UUID.randomUUID().toString()).field("level").size(5))));
//                .subAggregation(AggregationBuilders.dateHistogram(UUID.randomUUID().toString()).field("createdAt").dateHistogramInterval(DateHistogramInterval.DAY))
//                .subAggregation(AggregationBuilders.terms(UUID.randomUUID().toString()).field("level").size(5));
//        AggregationBuilder agg = AggregationBuilders.dateHistogram(UUID.randomUUID().toString()).field("createdAt").dateHistogramInterval(DateHistogramInterval.DAY);

        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, RiskLogESUtil.HIT_LOG_INDEX,RiskLogESUtil.pattern_default);
        request.source().aggregation(agg);
        SearchResponse response = this.getClient().search(request);
        if (response != null && response.status() == RestStatus.OK) {
            Aggregations aggregations = response.getAggregations();
            Map<String, Object> aggMap = parseAggregations(aggregations);
            System.out.println(JSON.toJSONString(aggMap));
        }
    }

    // 聚合结果解析
    public static Map<String, Object> parseAggregations(Aggregations aggregations) throws Exception{
        Map<String, Object> data = new LinkedHashMap<>();
        if (aggregations != null) {
            List<Aggregation> list = aggregations.asList();
            if (CollectionUtils.isNotEmpty(list)) {
                for(int i=0;i<list.size();i++){
                    parseAggregations(list.get(i), data);
                }
            }
        }
        return data;
    }

    // 聚合结果解析
    public static void parseAggregations(Aggregation agg, Map<String, Object> data) throws Exception{
        log.debug("聚合结果类型:{}", agg.getClass());
        boolean dateKey = false;
        List<? extends MultiBucketsAggregation.Bucket> buckets = new ArrayList<>();
        if (agg instanceof Terms) {
            Terms terms = (Terms) agg;
            buckets = terms.getBuckets();
        }else if(agg instanceof ParsedDateHistogram){
            dateKey = true;
            ParsedDateHistogram parsedDateHistogram = (ParsedDateHistogram) agg;
            buckets = parsedDateHistogram.getBuckets();
        } else if (agg instanceof Histogram) {
            Histogram histogram = (Histogram) agg;
            buckets = histogram.getBuckets();
        }
        if(buckets!=null&&buckets.size()>0){
            for(int i=0;i<buckets.size();i++){
                MultiBucketsAggregation.Bucket bucket = buckets.get(i);
                Aggregations aggs = bucket.getAggregations();
                Map<String,Object> map = parseAggregations(aggs);
                String key = bucket.getKeyAsString();
                if(dateKey){
                    key = DateFormatUtils.format(DateUtils.parseDate(key,RiskLogESUtil.DATE_FORMART), RiskLogESUtil.format_year_month_day);
                }
                if(map.size()>0){
                    data.put(key, map);
                }else{
                    data.put(key, bucket.getDocCount());
                }
            }
        }
    }

    @Test
    public void hitAggTest2() throws  Exception{
        Date date = com.yupaopao.risk.console.utils.DateUtils.getBeforeDayZeroTime(1);
        System.out.println(date);

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = date;
//        Date endTime = format.parse("2021-06-30 23:59:59");
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setSize(0);
        vo.setStartTime(startTime);
//        vo.setEndTime(new Date());
        HitResult hitResult = new HitResult();
//        hitResult.setUserId("58ad807f491d4800bc77fb8e2a94a1a2");
        vo.setQuery(hitResult);

        /*AggregationBuilder agg = AggregationBuilders.terms("1").field("level").
                subAggregation(AggregationBuilders.terms("2").field("userId"));*/

//        AggregationBuilder agg = AggregationBuilders.terms("1").field("userId");
        AggregationBuilder agg = AggregationBuilders.terms("1").field("level");

        SearchRequest request = RiskLogESUtil.buildSearchRequest(vo, RiskLogESUtil.HIT_LOG_INDEX,RiskLogESUtil.pattern_default);
        SearchRequest searchRequest = RiskLogESUtil.buildSearchRequest(vo, RiskLogESUtil.HIT_LOG_INDEX);
        BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
        query.must(QueryBuilders.termQuery("result.rule", 358));

//        BoolQueryBuilder levelQuery = new BoolQueryBuilder();
//        levelQuery.should(QueryBuilders.termQuery("level", "REJECT"));
//        levelQuery.should(QueryBuilders.termQuery("level", "REVIEW"));
//        query.must(levelQuery);

        request.source().aggregation(agg);
        SearchResponse response = this.getClient().search(request);
        if (response != null && response.status() == RestStatus.OK) {
            Aggregations aggregations = response.getAggregations();
            Map<String, Object> aggMap = parseAggregations(aggregations);
            System.out.println(JSON.toJSONString(aggMap));
        }
    }



    public static void main(String[] args){
        System.out.println(JSON.toJSONString(".a.".split("\\.")));
        Map<String,Object> map3 = new HashMap<>();
        map3.put("e","e");
        map3.put("f","f");
        Map<String,Object> map2 = new HashMap<>();
        map2.put("c","c");
        map2.put("d","d");
        map2.put("map3",map3);
        Map<String,Object> map = new HashMap<>();
        map.put("a","a");
        map.put("b","b");
        map.put("map2",map2);
        System.out.println(JSON.toJSONString(map));
        String[] list = new String[]{"a","c","a.map2.c","a.c","a.e","a.c.e","a.c.f","a.map2","a.map2.map3","map2.a","map2.c","a.map2.map3.e","map2"};
        Map<String,Object> targetMap = new HashMap<>();
        targetMap.put("traceId","");
        Map<String,Object> mm = new HashMap<>();
        mm.put("a","");
        targetMap.put("returnMap",mm);
        String c = CommonUtil.read(map, "$.map2.c");
        String e = CommonUtil.read(map, "$.map2.map3.e");
        System.out.println(c+"=="+e);
        String emp = CommonUtil.read(map, "$.map2.map3.emp");
        System.out.println(emp);
        String result = CommonUtil.filterPropertyString(JSON.toJSONString(map),JSON.toJSONString(list));
        System.out.println(result);
        System.out.println(Md5Util.md5("13918729921"));
        Object type = "haha";
        System.out.println(type.equals("haha"));
    }

}