package com.yupaopao.risk.console.service;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.risk.console.TestApplication;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2019/2/26 12:50 PM
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class) // SpringJUnit支持，由此引入Spring-Test框架支持！
@SpringBootTest(classes = TestApplication.class) // 指定我们SpringBoot工程的Application启动类
public class ElasticSearchServiceTest {

    @Autowired
    private RestHighLevelClient client;
    @ApolloConfig
    private Config config;

    @Test
    public void configTest() {
        for (int i = 0; i < 100; i++) {
            try {
                String[] columns = config.getArrayProperty("hit.log.text", ",", new String[0]);
                log.info("需要全文索引的字段:{}", Arrays.deepToString(columns));
                Thread.sleep(1000);
            } catch (Exception e) {
            }
        }
    }


    @Test
    public void getTest() {
        GetRequest request = new GetRequest("risk_hit_log", "log", "477ae7b23e8a4eaa9ad987b77dd0c391");
        try {
            GetResponse response = client.get(request);// 同步请求
            log.info("搜索结果:{}", response.getSourceAsString());
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }
    }

}
