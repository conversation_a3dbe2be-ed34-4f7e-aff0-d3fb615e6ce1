package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.risk.console.TestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-5-9
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class) // SpringJUnit支持，由此引入Spring-Test框架支持！
@SpringBootTest(classes = TestApplication.class) // 指定我们SpringBoot工程的Application启动类
public class RecallJobTest {

    private final static Logger LOGGER = LoggerFactory.getLogger(RecallJobTest.class);

    @Autowired
    private KafkaProducer kafkaProducer;

    //任务编号
//    String jobNo = "4c047d875b7f447a989decbcf723bef6";
//    //1，验证失败。2，验证成功，任务执行失败。3，验证成功，任务执行成功。
//    int scene = 3;

    @Test
    public void batchTest(){
        commonTest("8118efc34cb64afca357a66008bf9f00",1,1);
        commonTest("db3749cb21b74398a648c0f51597ce32",2,1);
        commonTest("3ab14620b487446694b93023533422f9",3,1);
        commonTest("c56692e13bd24086b70295d24429b384",4,1);
        commonTest("d3d3b57b753c42a695b34c39c04a75ea",5,1);
        commonTest("891ed0792eb649f1b2a19606eac69257",6,1);




    }

    public void commonTest(String jobNo,int scene,int messageType) {

        Map<String,Object> requestMap = new HashMap<>();
        requestMap.put("appKey","sensitiveWords");
        requestMap.put("appJobId",jobNo);

        switch (scene){
            case 1:
                requestMap.put("code",8000);
                requestMap.put("message","验证成功");
                requestMap.put("messageType",messageType);
                String requestJSON01 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future01 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON01);
                future01.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON01),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON01, throwable));
            case 2:
                requestMap.put("code",8022);
                requestMap.put("message","当日请求任务过多被拦截");
                requestMap.put("messageType",messageType);
                String requestJSON21 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future02 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON21);
                future02.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON21),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON21, throwable));
            case 3:
                requestMap.put("code",8020);
                requestMap.put("message","参数不合法，验证失败");
                requestMap.put("messageType",messageType);
                String requestJSON22 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future22 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON22);
                future22.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON22),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON22, throwable));
                break;
            case 4:
                requestMap.put("code",8023);
                requestMap.put("message","任务异常");
                requestMap.put("messageType",messageType);
                String requestJSON222 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future222 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON222);
                future222.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON222),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON222, throwable));
                break;
            case 5:
                requestMap.put("code",8025);
                requestMap.put("message","敏感词过长");
                requestMap.put("messageType",messageType);
                String requestJSON = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON);
                future.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON, throwable));
                break;
            case 6:
                requestMap.put("code",8026);
                requestMap.put("message","敏感词为空");
                requestMap.put("messageType",messageType);
                String requestJSON31 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future31 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON31);
                future31.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON31),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON31, throwable));
            case 7:
                requestMap.put("code",8027);
                requestMap.put("message","场景为空");
                requestMap.put("messageType",messageType);
                String requestJSON32 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future32 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON32);
                future32.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON32),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON32, throwable));
                break;
            case 8:
                requestMap.put("code",8028);
                requestMap.put("message","执行态任务过多");
                requestMap.put("messageType",messageType);
                String requestJSON322 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future322 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON322);
                future322.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON322),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON322, throwable));
                break;
            case 9:
                requestMap.put("code",8021);
                requestMap.put("message","程序超时");
                requestMap.put("messageType",messageType);
                String requestJSON3322 = JSONObject.toJSONString(requestMap);
                ListenableFuture<SendResult<String, String>> future3322 = kafkaProducer.send("ypp_bigdata_sensitive_word_res", requestJSON3322);
                future3322.addCallback(
                        o -> LOGGER.info("测试消息发送成功，requestJSON={}",requestJSON3322),
                        throwable -> LOGGER.error("测试消息发送失败，requestJSON="+requestJSON3322, throwable));
                break;
        }
    }

    @Test
    public void magicTest() {
        String id = "plka2099";
        //场景1  昵称  Topic Name = ypp_bigdata_sensitive_word_user_complete_nickname
        String topic1 = "ypp_bigdata_sensitive_word_user_complete_nickname";
        //String requestJSON1 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"content\":\"老哈哈\",\"UserId\":\"1b07d7d5987e8d14756dc44f16077f81\"}";
        String requestJSON1 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"content\":\""+id+"昵称-共产党\",\"UserId\":null}";

        //场景2  签名  Topic Name = ypp_bigdata_sensitive_word_user_complete_signature
        String topic2 = "ypp_bigdata_sensitive_word_user_complete_signature";
        String requestJSON2 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"签名-共产党\"}";

        //场景3   比心动态  Topic Name = ypp_bigdata_sensitive_word_timeline_publish_text
        String topic3 = "ypp_bigdata_sensitive_word_timeline_publish_text";
        String requestJSON3 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"动态-共产党\",\"bizId\":\"1\"}";

        // 场景4  比心动态评论  Topic Name = ypp_bigdata_sensitive_word_timeline_comment
        String topic4 = "ypp_bigdata_sensitive_word_timeline_comment";
        String requestJSON4 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"比心动态评论-共产党\",\"dongtaiId\":\"1\",\"commentId\":\"1\"}";

        //场景5  小星球动态评论  Topic Name = ypp_bigdata_sensitive_word_dongtai_comment
        String topic5 = "ypp_bigdata_sensitive_word_dongtai_comment";
        String requestJSON5 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"小星球动态评论-共产党\",\"dongtaiId\":\"1\",\"commentId\":\"1\"}";


        //场景6  资质介绍修改  Topic Name = ypp_bigdata_sensitive_word_biggie_cert_apply
        String topic6 = "ypp_bigdata_sensitive_word_biggie_cert_apply";
        String requestJSON6 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"资质介绍-共产党\",\"bizId\":\"1\"}";

        //场景7  大神对用户的评论  Topic Name = ypp_bigdata_sensitive_word_play_order_biggie_comment
        String topic7 = "ypp_bigdata_sensitive_word_play_order_biggie_comment";
        String requestJSON7 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"订单评论（大神对用户）-共产党\",\"bizId\":\"1\"}";


        //场景8  用户对大神的评论  Topic Name = ypp_bigdata_sensitive_word_play_order_user_comment
        String topic8 = "ypp_bigdata_sensitive_word_play_order_user_comment";
        String requestJSON8 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"订单评论（用户对大神）-共产党\",\"bizId\":\"1\"}";

        //场景9  比心开车房间名  Topic Name = ypp_bigdata_sensitive_word_bx_room_create
        String topic9 = "ypp_bigdata_sensitive_word_bx_room_create";
        String requestJSON9 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"比心开车房间名-共产党\",\"bizId\":\"1\"}";

        //场景10  鱼耳动态评论  Topic Name = ypp_bigdata_sensitive_word_voice_comment
        String topic10 = "ypp_bigdata_sensitive_word_voice_comment";
        String requestJSON10 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"比心开车房间名-共产党\",\"voiceId\":\"1\",\"commentId\":\"1\"}";

        //场景11 鱼儿动态,不需要
        String topic11 = "ypp_bigdata_sensitive_word_user_voice_publish";
        String requestJSON11 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"8821193d12d49f07e9c379f968d27fbd\",\"title\":\"鱼耳动态标题-共产党\",\"bizId\":\"1\",\"content\":\""+id+"鱼耳动态内容-共产党\"}";

        //场景12  小星球动态  Topic Name = ypp_bigdata_sensitive_word_user_dongtai_publish
        String topic12 = "ypp_bigdata_sensitive_word_user_dongtai_publish";
        String requestJSON12 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"8821193d12d49f07e9c379f968d27fbd\",\"dongtaiId\":\"1\",\"content\":\""+id+"小星球动态-共产党\"}";

        //场景13  鱼耳聊天室房间名称  Topic Name = ypp_bigdata_sensitive_word_chat_room_title
        String topic13 = "ypp_bigdata_sensitive_word_chat_room_title";
        String requestJSON13 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"鱼耳聊天室房间名称-共产党\",\"bizId\":\"1\"}";

        //场景14  鱼耳聊天室房间公告标题文本  Topic Name = ypp_bigdata_sensitive_word_chat_room_notice_title
        String topic14 = "ypp_bigdata_sensitive_word_chat_room_notice_title";
        String requestJSON14 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"鱼耳聊天室房间公告标题-共产党\",\"bizId\":\"1\"}";

        //场景15  鱼耳聊天室房间公告内容文本  Topic Name = ypp_bigdata_sensitive_word_chat_room_notice_content
        String topic15 = "ypp_bigdata_sensitive_word_chat_room_notice_content";
        String requestJSON15 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"鱼耳聊天室房间公告内容-共产党\",\"bizId\":\"1\"}";

        //场景16  鱼耳聊天室欢迎语内容文本  Topic Name = ypp_bigdata_sensitive_word_chat_room_welcome
        String topic16 = "ypp_bigdata_sensitive_word_chat_room_welcome";
        String requestJSON16 = "{\"messageType\":1,\"message\":\"success\",\"code\":8000,\"appKey\":\"sensitiveWords\",\"UserId\":\"e2b605d1ab4813234122beb870e4d660\",\"content\":\""+id+"鱼耳聊天室欢迎语内容-共产党\",\"bizId\":\"1\"}";



        ListenableFuture<SendResult<String, String>> future1 = kafkaProducer.send( topic1,requestJSON1);
        future1.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON1),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON1, throwable));

        ListenableFuture<SendResult<String, String>> future2 = kafkaProducer.send( topic2,requestJSON2);
        future2.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON2),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON2, throwable));

        ListenableFuture<SendResult<String, String>> future3 = kafkaProducer.send( topic3,requestJSON3);
        future3.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON3),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON3, throwable));

        ListenableFuture<SendResult<String, String>> future4 = kafkaProducer.send( topic4,requestJSON4);
        future4.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON4),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON4, throwable));

        ListenableFuture<SendResult<String, String>> future5 = kafkaProducer.send( topic5,requestJSON5);
        future5.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON5),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON5, throwable));

        ListenableFuture<SendResult<String, String>> future6 = kafkaProducer.send( topic6,requestJSON6);
        future6.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON6),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON6, throwable));

        ListenableFuture<SendResult<String, String>> future7 = kafkaProducer.send( topic7,requestJSON7);
        future7.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON7),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON7, throwable));

        ListenableFuture<SendResult<String, String>> future8 = kafkaProducer.send( topic8,requestJSON8);
        future8.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON8),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON8, throwable));

        ListenableFuture<SendResult<String, String>> future9 = kafkaProducer.send( topic9,requestJSON9);
        future9.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON9),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON9, throwable));

        ListenableFuture<SendResult<String, String>> future10 = kafkaProducer.send( topic10,requestJSON10);
        future10.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON10),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON10, throwable));

        ListenableFuture<SendResult<String, String>> future11 = kafkaProducer.send( topic11,requestJSON11);
        future11.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON11),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON11, throwable));

        ListenableFuture<SendResult<String, String>> future12 = kafkaProducer.send( topic12,requestJSON12);
        future12.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON12),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON12, throwable));

        ListenableFuture<SendResult<String, String>> future13 = kafkaProducer.send( topic13,requestJSON13);
        future13.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON13),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON13, throwable));

        ListenableFuture<SendResult<String, String>> future14 = kafkaProducer.send( topic14,requestJSON14);
        future14.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON14),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON14, throwable));

        ListenableFuture<SendResult<String, String>> future15 = kafkaProducer.send( topic15,requestJSON15);
        future15.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON15),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON15, throwable));

        ListenableFuture<SendResult<String, String>> future16 = kafkaProducer.send( topic16,requestJSON16);
        future16.addCallback(o -> LOGGER.error("测试消息发送成功，requestJSON={}" , requestJSON16),throwable -> LOGGER.error("测试消息发送失败，requestJSON=" + requestJSON16, throwable));
    }
}
