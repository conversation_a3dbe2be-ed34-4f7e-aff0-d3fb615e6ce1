package com.yupaopao.risk.console.job;

import com.yupaopao.aries.client.JobExecutionContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class LoginNameListReflushRedisJobTest {


    @Autowired
    LoginNameListReflushRedisJob loginNameListReflushRedisJob;

    @Test
    public void execute() {
        JobExecutionContext jobExecutionContext = new JobExecutionContext();
        loginNameListReflushRedisJob.execute(jobExecutionContext);
    }
}
