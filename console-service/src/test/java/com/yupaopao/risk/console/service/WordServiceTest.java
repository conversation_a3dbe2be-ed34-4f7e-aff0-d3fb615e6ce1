package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Word;
import com.yupaopao.risk.console.TestApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = TestApplication.class)
public class WordServiceTest {

    @Autowired
    private WordService wordService;

    @Test
    public void search(){
        List<String> wordContents = new ArrayList<>();
        wordContents.add("傻逼");

        List<Word> words = wordService.batchSearch(wordContents);

        System.out.println("ok");
    }

}
