package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * <AUTHOR>
 * @date 2019/1/3 5:30 PM
 */
public class SpelTest {

    private final static Logger LOGGER = LoggerFactory.getLogger(SpelTest.class);

    @Test
    public void testParse() {
        String json = "{\n" +
                "  \"clientIpDetail\": {\n" +
                "    \"country\": \"0\",\n" +
                "    \"province\": \"0\",\n" +
                "    \"city\": \"内网IP\",\n" +
                "    \"isp\": \"内网IP\",\n" +
                "    \"cityId\": 0,\n" +
                "    \"region\": \"0\"\n" +
                "  },\n" +
                "  \"UserId\": \"4edd2a6f03aac2dd8e2737fb0f794fec\",\n" +
                "  \"DeviceId\": \"20181031121244bc0cfecedb1ef0059afab3754e36db8701678578c2819fa4\",\n" +
                "  \"Timeout\": \"2000\",\n" +
                "  \"clientIpIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"Event\": \"iap-charge\",\n" +
                "  \"RiskLevel\": \"PASS\",\n" +
                "  \"TraceId\": \"fa9781ab6f8c4300ad25ca92f1e37b30\",\n" +
                "  \"mobileIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"ClientIp\": \"*************\",\n" +
                "  \"Business\": \"payment\"\n" +
                "}";
        //表达式解析
        ExpressionParser expressionParser = new SpelExpressionParser();
        Expression expression = expressionParser.parseExpression("#Event=='iap-charge2' && #RiskLevel=='PASS'");
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariables(JSONObject.parseObject(json));
        LOGGER.info("[SpELTest - testParse ] {} ", expression.getValue(context));
    }

}
