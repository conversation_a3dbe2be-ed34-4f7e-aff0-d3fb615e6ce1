package com.yupaopao.risk.console.service;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.console.ElasticSearchTestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.replication.ReplicationResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHits;
import org.junit.Test;

import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/2/21 5:04 PM
 */
@Slf4j
public class SearchTest extends ElasticSearchTestCase {

    @Test
    public void index() {
        try {
            // 1、创建索引请求
            IndexRequest request = new IndexRequest(index, "log", "477ae7b23e8a4eaa9ad987b77dd0c391");

            // 2、准备文档数据
            // 方式一：直接给JSON串
            String json = "{\n" +
                    "\t\"traceId\": \"ac76654cdffc4aba88d3512be3ecc122\",\n" +
                    "\t\"safeDeviceCheck\": {\n" +
                    "\t\t\"reason\": \"无任何登录记录\",\n" +
                    "\t\t\"riskLevel\": \"REVIEW\"\n" +
                    "\t},\n" +
                    "\t\"level\": \"REVIEW\",\n" +
                    "\t\"DeviceId\": \"20181212110135a7c46eb0da6b4fe0dff74a417665bf08018ef31a1fbdc738\",\n" +
                    "\t\"Timeout\": \"3000\",\n" +
                    "\t\"clientIpIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"userIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"ClientIp\": \"*************\",\n" +
                    "\t\"Mobile\": \"15000000015\",\n" +
                    "\t\"mobileIsWhite\": {\n" +
                    "\t\t\"riskLevel\": \"REVIEW\"\n" +
                    "\t},\n" +
                    "\t\"returnMap\": {},\n" +
                    "\t\"eventCode\": \"user-login\",\n" +
                    "\t\"clientIpDetail\": {\n" +
                    "\t\t\"country\": \"0\",\n" +
                    "\t\t\"riskLevel\": \"PASS\",\n" +
                    "\t\t\"province\": \"0\",\n" +
                    "\t\t\"city\": \"内网IP\",\n" +
                    "\t\t\"isp\": \"内网IP\",\n" +
                    "\t\t\"cityId\": 0,\n" +
                    "\t\t\"region\": \"0\"\n" +
                    "\t},\n" +
                    "\t\"createdAt\": 1551167914804,\n" +
                    "\t\"UserId\": \"8a9c9c0467868af601679600d8c2000d\",\n" +
                    "\t\"success\": true,\n" +
                    "\t\"Event\": \"user-login\",\n" +
                    "\t\"TraceId\": \"ac76654cdffc4aba88d3512be3ecc122\",\n" +
                    "\t\"mobileIsBlack\": {\n" +
                    "\t\t\"riskLevel\": \"PASS\"\n" +
                    "\t},\n" +
                    "\t\"loginCheck\": {},\n" +
                    "\t\"Level\": \"REVIEW\",\n" +
                    "\t\"Business\": \"userService\"\n" +
                    "}";
            request.source(json, XContentType.JSON);

            // 方式二：以map对象来表示文档
            /*
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("user", "kimchy");
            jsonMap.put("postDate", new Date());
            jsonMap.put("message", "trying out Elasticsearch");
            request.source(jsonMap);
            */

            //3、其他的一些可选设置
//            request.routing("routing");  //设置routing值
//            request.timeout(TimeValue.timeValueSeconds(1));  //设置主分片等待时长
//            request.setRefreshPolicy("wait_for");  //设置重刷新策略
//            request.version(2);  //设置版本号
//            request.opType(DocWriteRequest.OpType.CREATE);  //操作类别

            //4、发送请求
            IndexResponse response = null;
            try {
                // 同步方式
                response = client.index(request);
            } catch (ElasticsearchException e) {
                // 捕获，并处理异常
                //判断是否版本冲突、create但文档已存在冲突
                if (e.status() == RestStatus.CONFLICT) {
                    log.error("冲突了，请在此写冲突处理逻辑！\n" + e.getDetailedMessage());
                }
                log.error("索引异常", e);
            }

            //5、处理响应
            if (response != null) {
                String index = response.getIndex();
                String type = response.getType();
                String id = response.getId();
                long version = response.getVersion();
                if (response.getResult() == DocWriteResponse.Result.CREATED) {
                    log.info("添加索引成功");
                } else if (response.getResult() == DocWriteResponse.Result.UPDATED) {
                    log.info("修改索引成功");
                }
                // 分片处理信息
                ReplicationResponse.ShardInfo shardInfo = response.getShardInfo();
                if (shardInfo.getTotal() != shardInfo.getSuccessful()) {

                }
                // 如果有分片副本失败，可以获得失败原因信息
                if (shardInfo.getFailed() > 0) {
                    for (ReplicationResponse.ShardInfo.Failure failure : shardInfo.getFailures()) {
                        log.error("副本失败原因:{}", failure.reason());
                    }
                }
            }
            //异步方式发送索引请求
            /*ActionListener<IndexResponse> listener = new ActionListener<IndexResponse>() {
                @Override
                public void onResponse(IndexResponse response) {

                }

                @Override
                public void onFailure(Exception e) {

                }
            };
            client.indexAsync(request, listener);
            */

        } catch (Exception e) {
            log.error("请求ES添加索引失败", e);
        }
    }

    @Test
    public void get() {
        GetRequest request = new GetRequest(index, "user-login", "477ae7b23e8a4eaa9ad987b77dd0c391");
        GetResponse response = null;
        try {
            response = client.get(request);// 同步请求
        } catch (Exception e) {
            log.error("获取文档异常", e);
        }

        //4、处理响应
        if (response != null) {
            String index = response.getIndex();
            String type = response.getType();
            String id = response.getId();
            if (response.isExists()) { // 文档存在
                log.info("index:" + index + "  type:" + type + "  id:" + id);
                log.info("文档:{}", response.getSource());
            } else {
                log.error("没有找到该id的文档");
            }
        }
    }

    @Test
    public void search() {
        SearchRequest request = new SearchRequest(index);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchAllQuery());
//        boolQuery.filter(QueryBuilders.termQuery("Mobile", "***********"));
        boolQuery.filter(QueryBuilders.rangeQuery("createdAt").gte(System.currentTimeMillis() - 1000 * 60 * 60 * 6));
        boolQuery.filter(QueryBuilders.termQuery("level", "REJECT"));
        boolQuery.filter(QueryBuilders.termQuery("Event", "wechat-h5-pay-pre"));
//        boolQuery.filter(QueryBuilders.termQuery("success", true));
//        boolQuery.filter(QueryBuilders.termQuery("Business", "payment"));
//        boolQuery.filter(QueryBuilders.termQuery("ClientIp", "************"));
//        boolQuery.filter(QueryBuilders.termQuery("rid", "20181218165933a29d3f86274853b140"));
//        boolQuery.filter(QueryBuilders.termQuery("avatar", "http://pic32.photophoto.cn/20140825/0036036889302739_b.jpg"));
        System.out.println(boolQuery.toString());
        request.source().query(boolQuery);
        request.source().from(0);
        request.source().size(5);
        request.source().timeout(new TimeValue(10, TimeUnit.SECONDS));
        SearchResponse response = null;
        try {
            response = client.search(request);
        } catch (Exception e) {
            log.error("搜索文档异常", e);
        }

        //4、处理响应
        if (response != null && response.status() == RestStatus.OK) {
            SearchHits hits = response.getHits();
            if (hits != null && hits.totalHits > 0) {
                log.info("搜索结果数量:{},耗时:{}", hits.totalHits, response.getTook());
                hits.forEach(hit -> log.info("命中记录:{}", hit.getSourceAsMap()));
            } else {
                log.info("无搜索结果");
            }
        } else {
            log.info("搜索失败");
        }
    }

    @Test
    public void test01() {
        String json = "{\n" +
                "  \"safeDeviceCheck\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"DeviceId\": \"20190113141033898e88730daff05fc38396b4d785d7c80168f59fbd59ee87\",\n" +
                "  \"Timeout\": \"3000\",\n" +
                "  \"clientIpIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"userIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"ClientIp\": \"**************\",\n" +
                "  \"Mobile\": \"13824676396\",\n" +
                "  \"mobileIsWhite\": {\n" +
                "    \"riskLevel\": \"REVIEW\"\n" +
                "  },\n" +
                "  \"clientIpDetail\": {\n" +
                "    \"country\": \"中国\",\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"province\": \"广东省\",\n" +
                "    \"city\": \"云浮市\",\n" +
                "    \"isp\": \"电信\",\n" +
                "    \"cityId\": 2273,\n" +
                "    \"region\": \"华南\"\n" +
                "  },\n" +
                "  \"UserId\": \"44020da70884420c98fb23814b46e703\",\n" +
                "  \"Event\": \"user-login\",\n" +
                "  \"TraceId\": \"4fa7a9f228d442fc84a78cf0d087b648\",\n" +
                "  \"mobileIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"loginCheck\": {\n" +
                "    \"score\": 0,\n" +
                "    \"code\": 1100,\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"requestId\": \"269255e5d3f61d44018fc6f1f2c5cccb\",\n" +
                "    \"detail\": {\n" +
                "      \"hits\": [],\n" +
                "      \"relatedItems\": [],\n" +
                "      \"description\": \"正常\",\n" +
                "      \"model\": \"M1000\"\n" +
                "    },\n" +
                "    \"message\": \"成功\"\n" +
                "  },\n" +
                "  \"Business\": \"userService\"\n" +
                "}";
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            HitResult result = JSONObject.parseObject(json, HitResult.class);
            json = JSONObject.toJSONString(result);
            result = JSONObject.parseObject(json, HitResult.class);
        }
        System.out.println(System.currentTimeMillis() - start);
    }

    @Test
    public void test02() {
        String json = "{\n" +
                "  \"safeDeviceCheck\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"DeviceId\": \"20190113141033898e88730daff05fc38396b4d785d7c80168f59fbd59ee87\",\n" +
                "  \"Timeout\": \"3000\",\n" +
                "  \"clientIpIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"userIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"ClientIp\": \"**************\",\n" +
                "  \"Mobile\": \"13824676396\",\n" +
                "  \"mobileIsWhite\": {\n" +
                "    \"riskLevel\": \"REVIEW\"\n" +
                "  },\n" +
                "  \"clientIpDetail\": {\n" +
                "    \"country\": \"中国\",\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"province\": \"广东省\",\n" +
                "    \"city\": \"云浮市\",\n" +
                "    \"isp\": \"电信\",\n" +
                "    \"cityId\": 2273,\n" +
                "    \"region\": \"华南\"\n" +
                "  },\n" +
                "  \"UserId\": \"44020da70884420c98fb23814b46e703\",\n" +
                "  \"Event\": \"user-login\",\n" +
                "  \"TraceId\": \"4fa7a9f228d442fc84a78cf0d087b648\",\n" +
                "  \"mobileIsBlack\": {\n" +
                "    \"riskLevel\": \"PASS\"\n" +
                "  },\n" +
                "  \"loginCheck\": {\n" +
                "    \"score\": 0,\n" +
                "    \"code\": 1100,\n" +
                "    \"riskLevel\": \"PASS\",\n" +
                "    \"requestId\": \"269255e5d3f61d44018fc6f1f2c5cccb\",\n" +
                "    \"detail\": {\n" +
                "      \"hits\": [],\n" +
                "      \"relatedItems\": [],\n" +
                "      \"description\": \"正常\",\n" +
                "      \"model\": \"M1000\"\n" +
                "    },\n" +
                "    \"message\": \"成功\"\n" +
                "  },\n" +
                "  \"Business\": \"userService\"\n" +
                "}";
        HitResult result = JSONObject.parseObject(json, HitResult.class);
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            try {
                PropertyUtils.describe(result);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
        }
        System.out.println(System.currentTimeMillis() - start);
    }

}
