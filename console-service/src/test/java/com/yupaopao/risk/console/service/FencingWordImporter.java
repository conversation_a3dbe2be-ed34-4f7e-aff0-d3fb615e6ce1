package com.yupaopao.risk.console.service;

import com.yupaopao.risk.common.model.Word;
import com.yupaopao.risk.common.model.WordScene;
import com.yupaopao.risk.common.model.WordTag;
import com.yupaopao.risk.console.TestApplication;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.*;

/**
 * 批量导入违禁词
 *
 * <AUTHOR>
 * @date 2018/10/27 2:50 PM
 */
@RunWith(SpringJUnit4ClassRunner.class) // SpringJUnit支持，由此引入Spring-Test框架支持！
@SpringBootTest(classes = TestApplication.class) // 指定我们SpringBoot工程的Application启动类
public class FencingWordImporter {

    private final static Logger LOGGER = LoggerFactory.getLogger(FencingWordImporter.class);

    @Autowired
    private WordService wordService;
    @Autowired
    private WordTagService wordTagService;
    @Autowired
    private WordSceneService wordSceneService;

    @Test
    public void import1() {
        doImport(loadData());
    }

    @Test
    public void import2() throws IOException {
        Map<String, Integer> map = new HashMap<>();
        map.put("主要领导人名单", 37);
        map.put("政治人物", 37);
        map.put("切词政治人物", 37);
        map.put("英烈名单", 37);
        map.put("涉政涉恐敏感词", 1);
        map.put("涉政同音名单", 1);
        map.put("严格涉政名单", 1);
        map.put("违禁刀具类敏感词", 30);
        map.put("网安敏感词", 38);
        map.put("违禁枪敏感词", 30);
        map.put("色情污秽", 4);
        map.put("淫秽信息", 4);
        map.put("全局涉黄涉政敏感词", 4);
        map.put("政治机构", 39);
        map.put("政治机构敏感词", 39);
        map.put("切词后淫秽敏感词名单", 4);
        map.put("色情淫秽类敏感词", 4);
        map.put("宗教邪教敏感词", 35);
        map.put("政治事件", 40);
        map.put("违禁化学品敏感词", 29);
        map.put("国家领导人敏感词", 37);
        map.put("广告导流黑名单", 6);
        map.put("网监名单", 38);
        map.put("毒品", 25);
        map.put("涉毒", 25);
        map.put("涉赌类敏感词", 26);
        map.put("涉毒类敏感词", 26);
        map.put("竞品名单", 9);
        map.put("涉恐", 2);
        map.put("涉爆类敏感词", 2);
        map.put("涉警类敏感词", 31);
        File file = new File("/data/shumei.txt");
        List<String> lines = FileUtils.readLines(file);
        System.out.println("总记录数量:" + lines.size());
        Map<Long, List<String>> tagWords = new HashMap<>();
        lines.forEach(line -> {
            String[] items = line.split("\t");
            String word = items[0];
            String tag = items[1];
            tag = tag.substring(1, tag.length() - 1);
            String[] array = tag.replaceAll(" ", "").split(",");
            for (int i = 0; i < array.length && StringUtils.isNotBlank(array[i]) && !"null".equals(array[i]); i++) {
                long tagId = map.containsKey(array[i]) ? map.get(array[i]) : 41;
                if (!tagWords.containsKey(tagId)) {
                    tagWords.put(tagId, new LinkedList<>());
                }
                tagWords.get(tagId).add(word);
            }
        });
        doImport(tagWords);
    }

    protected void doImport(Map<Long, List<String>> data) {
        data.forEach((tagId, lines) -> lines.forEach(word -> {
            if (StringUtils.isBlank(word)) {
                return;
            }
            try {
                Word record = new Word();
                record.setContent(word);
                if (wordService.selectCount(record) == 0) {
                    record.setAllowSkip(true);
                    record.setAllowWholeMatch(false);
                    record.setWeight(1F);
                    wordService.insertSelective(record);

                    WordScene wordScene = new WordScene();
                    wordScene.setSceneId(1L);
                    wordScene.setWordId(record.getId());
                    wordSceneService.insertSelective(wordScene);
                } else {
                    record = wordService.get(record);
                }
                WordTag wordTag = new WordTag();
                wordTag.setTagId(tagId);
                wordTag.setWordId(record.getId());
                if (wordTagService.selectCount(wordTag) == 0) {
                    wordTagService.insertSelective(wordTag);
                }
            } catch (Exception e) {
                LOGGER.warn("保存违禁词失败:" + word, e);
            }
        }));
    }

    protected static Map<Long, List<String>> loadData() {
        Map<Long, List<String>> data = new HashMap<>();
        try {
            Enumeration<URL> resources = FencingWordImporter.class.getClassLoader().getResources("import");
            if (resources.hasMoreElements()) {
                String path = resources.nextElement().getPath();
                String[] files = new File(path).list();
                if (files != null) {
                    for (String file : files) {
                        try {
                            Long tagId = Long.parseLong(file.split("\\.")[0]);
                            List<String> lines = FileUtils.readLines(new File(path + "/" + file), "UTF-8");
                            if (CollectionUtils.isNotEmpty(lines)) {
                                data.put(tagId, lines);
                            }
                        } catch (Exception e) {
                            LOGGER.warn("读取数据失败", e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.warn("读取数据失败", e);
        }
        return data;
    }

    public static void main(String[] args) {
        loadData();
    }

}
