package com.yupaopao.risk.console.service.impl;

import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.bean.CorrectionRequest;
import com.yupaopao.risk.console.bean.CorrectionResult;
import com.yupaopao.risk.console.bean.CorrectionType;
import com.yupaopao.risk.console.bean.ShumeiFeedBackType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

import static org.junit.Assert.*;

@SpringBootTest(classes = ConsoleConstants.class)
@RunWith(SpringRunner.class)
public class ThirdServiceImplTest {
    @Autowired
    ThirdServiceImpl thirdService;

    @Test
    public void correction() {
        CorrectionRequest request = new CorrectionRequest();
        request.setServiceId(ShumeiFeedBackType.LOGIN.getType());
        request.setTokenId("1833906645700027");
        request.setType(CorrectionType.ERROR.getCode());
        request.setRequestId(UUID.randomUUID().toString());
        request.setRiskLevel("PASS");
        request.setTimeStamp(String.valueOf(System.currentTimeMillis()));

        CorrectionResult correction = thirdService.correction(request);

    }
}