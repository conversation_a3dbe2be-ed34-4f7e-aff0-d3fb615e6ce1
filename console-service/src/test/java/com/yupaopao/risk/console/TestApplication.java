package com.yupaopao.risk.console;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.yupaopao.framework.spring.boot.datasource.annotation.EnableDataSourceConfiguration;
import com.yupaopao.framework.spring.boot.kafka.annotation.EnableKafkaConfiguration;
import com.yupaopao.framework.spring.boot.redis.annotation.EnableRedisConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 启动入口
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
@SpringBootApplication(scanBasePackages = {"com.yupaopao.risk.common", "com.yupaopao.risk.console.*"})
@EnableApolloConfig
@EnableDataSourceConfiguration("middleware.db-v2.risk")
@EnableRedisConfiguration("middleware.redis.risk")
@EnableKafkaConfiguration("middleware.kafka")
public class TestApplication {

    private final static Logger LOGGER = LoggerFactory.getLogger(TestApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }

}
