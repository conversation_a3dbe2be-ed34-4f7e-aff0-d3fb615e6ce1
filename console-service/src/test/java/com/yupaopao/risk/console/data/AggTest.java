package com.yupaopao.risk.console.data;

import com.yupaopao.risk.console.ElasticSearchTestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.support.ValueType;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/2/21 5:04 PM
 */
@Slf4j
public class AggTest extends ElasticSearchTestCase {

    private final String baseDir = "/data/risk-data/";

    @Test
    public void distinct() throws Exception {
        File file = new File(baseDir + "distinct/content.txt");
        List<String> lines = FileUtils.readLines(file);
        Set<String> set = new LinkedHashSet<>();
        lines.forEach(item -> {
            if (StringUtils.isNotBlank(item)) {
                set.add(item.trim());
            }
        });
        FileUtils.writeLines(file, set, false);
        set.forEach(item -> System.out.println(item));
    }

    @Test
    public void aggDeviceByUsers() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-userid.txt"));
        Map<String, Long> result = aggByTerms("userId", "deviceId", terms);
        result.keySet().forEach(item -> System.out.println(item));
    }

    @Test
    public void aggMobileByUsers() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-userid.txt"));
        Map<String, Long> result = aggByTerms("userId", "mobileNo", terms);
        result.keySet().forEach(item -> System.out.println(item));
    }

    @Test
    public void aggMobileByDevices() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-deviceid.txt"));
        Map<String, Long> result = aggByTerms("deviceId", "mobileNo", terms, true);
        result.keySet().forEach(item -> System.out.println(item));
    }

    @Test
    public void aggDeviceByUser() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-userid.txt"));
        terms.forEach(term -> {
            Map<String, Long> result = aggByTerms("userId", "deviceId", Arrays.asList(term));
            result.keySet().forEach(item -> System.out.println(term + "\t" + item));
            if (result.isEmpty()) {
                System.out.println(term);
            }
        });
    }

    @Test
    public void aggMobileByUser() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-userid.txt"));
        terms.forEach(term -> {
            Map<String, Long> result = aggByTerms("userId", "mobileNo", Arrays.asList(term));
            result.keySet().forEach(item -> System.out.println(term + "\t" + item));
            if (result.isEmpty()) {
                System.out.println(term);
            }
        });
    }

    @Test
    public void aggMobileByDevice() throws IOException {
        List<String> terms = FileUtils.readLines(new File(baseDir + "agg-terms/should-deviceid.txt"));
        terms.forEach(term -> {
            Map<String, Long> result = aggByTerms("deviceId", "mobileNo", Arrays.asList(term));
            result.keySet().forEach(item -> System.out.println(term + "\t" + item));
            if (result.isEmpty()) {
                System.out.println(term);
            }
        });
    }

    protected Map<String, Long> aggByTerms(String key, String aggKey, List<String> terms) {
        return aggByTerms(key, aggKey, terms, false);
    }

    protected Map<String, Long> aggByTerms(String key, String aggKey, List<String> terms, boolean excludeReject) {
        try {
            Map<String, Object> map = new HashMap<>();
            Date endTime = new Date();
            Date startTime = DateUtils.addDays(endTime, -30);
//            log.info("搜索请求:{}", map);
            SearchRequest request = RiskLogElasticSearchUtils.buildSearchRequest(map, startTime, endTime);
            BoolQueryBuilder query = (BoolQueryBuilder) request.source().query();

            BoolQueryBuilder should = QueryBuilders.boolQuery();
            for (String item : terms) {
                if (StringUtils.isNotBlank(item)) {
                    should.should(QueryBuilders.termQuery(key, item.trim()));
                }
            }
            query.must().add(should);
//            query.mustNot(QueryBuilders.termQuery("eventCode", "im-message"));
            if (excludeReject) {
                query.mustNot(QueryBuilders.termQuery("result.riskLevel", "REJECT"));
            }
            TermsAggregationBuilder agg = new TermsAggregationBuilder("distinct", ValueType.STRING);
            agg.field(aggKey);
            agg.size(200);
            request.source().aggregation(agg);
//            log.debug("聚合搜索参数:{}", request.source());
            return parseResponse(client.search(request));
        } catch (Throwable e) {
            log.error("ES聚合查询失败", e);
        }
        return Collections.EMPTY_MAP;
    }

    protected Map<String, Long> parseResponse(SearchResponse response) {
        Map<String, Long> result = new LinkedHashMap<>();
        try {
            if (response != null && response.status() == RestStatus.OK) {
                List<Aggregation> list = response.getAggregations().asList();
                for (Aggregation item : list) {
                    ParsedStringTerms terms = (ParsedStringTerms) item;
                    List<? extends Terms.Bucket> buckets = terms.getBuckets();
                    if (CollectionUtils.isEmpty(buckets)) {
                        continue;
                    }
//                    log.info("聚合结果:{}, 耗时:{}", buckets.size(), response.getTook());
                    buckets.forEach(bucket -> {
                        String k = bucket.getKeyAsString();
                        if (StringUtils.isNotBlank(k)) {
                            result.put(k, bucket.getDocCount());
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("解析聚合结果失败", e);
        }
        return result;
    }

}
