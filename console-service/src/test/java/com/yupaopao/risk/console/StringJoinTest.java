package com.yupaopao.risk.console;

import com.yupaopao.risk.common.model.Event;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/10/16 下午11:51
 */
public class StringJoinTest {

    public static void main(String[] args) {
        List<Event> events = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            Event event = new Event();
            event.setId(Long.valueOf(i));
            events.add(event);
        }
        Set<String> set = events.stream().map(event -> event.getId().toString()).collect(Collectors.toSet());
        System.out.println(String.join(",", set));

    }

}
