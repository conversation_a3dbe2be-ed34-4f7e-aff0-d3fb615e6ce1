package com.yupaopao.risk.console;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/4/22 11:25 AM
 */
public class TTT {

    public static void main(String[] args) throws Exception {
        String source = "/Users/<USER>/工作空间/IM用户黑名单.txt";
        String target = "/Users/<USER>/工作空间/risk-list/IM用户黑名单.txt";
        String dimension = "userid";
        String type = "BLACK";
        String content = FileUtils.readFileToString(new File(source));
        JSONArray list = JSONObject.parseObject(content).getJSONObject("data").getJSONArray("list");
        List<String> lines = new LinkedList<>();
        list.forEach(item -> {
            JSONObject object = (JSONObject) item;
            if (dimension.equalsIgnoreCase(object.getString("dimension")) && type.equalsIgnoreCase(object.getString("type"))) {
                lines.add(object.getString("value") + "\t" + object.getString("comment"));
            }
        });
        if (lines.isEmpty()) {
            System.out.println("没有有效名单数据");
            return;
        }
        FileUtils.writeLines(new File(target), lines, false);
        System.out.println("写入有效名单数据:" + lines.size());
    }

}
