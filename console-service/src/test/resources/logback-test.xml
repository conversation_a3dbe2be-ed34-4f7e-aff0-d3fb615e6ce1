<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{32}:%line - %msg%n"/>

    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>

    <logger name="com.yupaopao" level="DEBUG" additivity="false">
        <appender-ref ref="Console"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="Console"/>
    </root>
</configuration>
