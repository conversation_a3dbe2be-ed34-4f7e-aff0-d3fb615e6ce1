/*!
 ColReorder 1.5.3
 ©2010-2020 SpryMedia Ltd - datatables.net/license
*/
(function(e){"function"===typeof define&&define.amd?define(["jquery","datatables.net"],function(p){return e(p,window,document)}):"object"===typeof exports?module.exports=function(p,o){p||(p=window);if(!o||!o.fn.dataTable)o=require("datatables.net")(p,o).$;return e(o,p,p.document)}:e(jQuery,window,document)})(function(e,p,o,s){function r(a){for(var b=[],d=0,f=a.length;d<f;d++)b[a[d]]=d;return b}function q(a,b,d){b=a.splice(b,1)[0];a.splice(d,0,b)}function t(a,b,d){for(var f=[],e=0,c=a.childNodes.length;e<
c;e++)1==a.childNodes[e].nodeType&&f.push(a.childNodes[e]);b=f[b];null!==d?a.insertBefore(b,f[d]):a.appendChild(b)}var u=e.fn.dataTable;e.fn.dataTableExt.oApi.fnColReorder=function(a,b,d,f,h){var c,g,j,l,n,i=a.aoColumns.length,k;n=function(a,b,c){if(a[b]&&"function"!==typeof a[b]){var d=a[b].split("."),e=d.shift();isNaN(1*e)||(a[b]=c[1*e]+"."+d.join("."))}};if(b!=d)if(0>b||b>=i)this.oApi._fnLog(a,1,"ColReorder 'from' index is out of bounds: "+b);else if(0>d||d>=i)this.oApi._fnLog(a,1,"ColReorder 'to' index is out of bounds: "+
d);else{j=[];c=0;for(g=i;c<g;c++)j[c]=c;q(j,b,d);var m=r(j);c=0;for(g=a.aaSorting.length;c<g;c++)a.aaSorting[c][0]=m[a.aaSorting[c][0]];if(null!==a.aaSortingFixed){c=0;for(g=a.aaSortingFixed.length;c<g;c++)a.aaSortingFixed[c][0]=m[a.aaSortingFixed[c][0]]}c=0;for(g=i;c<g;c++){k=a.aoColumns[c];j=0;for(l=k.aDataSort.length;j<l;j++)k.aDataSort[j]=m[k.aDataSort[j]];k.idx=m[k.idx]}e.each(a.aLastSort,function(b,c){a.aLastSort[b].src=m[c.src]});c=0;for(g=i;c<g;c++)k=a.aoColumns[c],"number"==typeof k.mData?
k.mData=m[k.mData]:e.isPlainObject(k.mData)&&(n(k.mData,"_",m),n(k.mData,"filter",m),n(k.mData,"sort",m),n(k.mData,"type",m));if(a.aoColumns[b].bVisible){n=this.oApi._fnColumnIndexToVisible(a,b);l=null;for(c=d<b?d:d+1;null===l&&c<i;)l=this.oApi._fnColumnIndexToVisible(a,c),c++;j=a.nTHead.getElementsByTagName("tr");c=0;for(g=j.length;c<g;c++)t(j[c],n,l);if(null!==a.nTFoot){j=a.nTFoot.getElementsByTagName("tr");c=0;for(g=j.length;c<g;c++)t(j[c],n,l)}c=0;for(g=a.aoData.length;c<g;c++)null!==a.aoData[c].nTr&&
t(a.aoData[c].nTr,n,l)}q(a.aoColumns,b,d);c=0;for(g=i;c<g;c++)a.oApi._fnColumnOptions(a,c,{});q(a.aoPreSearchCols,b,d);c=0;for(g=a.aoData.length;c<g;c++){l=a.aoData[c];if(k=l.anCells){q(k,b,d);j=0;for(n=k.length;j<n;j++)k[j]&&k[j]._DT_CellIndex&&(k[j]._DT_CellIndex.column=j)}"dom"!==l.src&&Array.isArray(l._aData)&&q(l._aData,b,d)}c=0;for(g=a.aoHeader.length;c<g;c++)q(a.aoHeader[c],b,d);if(null!==a.aoFooter){c=0;for(g=a.aoFooter.length;c<g;c++)q(a.aoFooter[c],b,d)}(h||h===s)&&e.fn.dataTable.Api(a).rows().invalidate();
c=0;for(g=i;c<g;c++)e(a.aoColumns[c].nTh).off(".DT"),this.oApi._fnSortAttachListener(a,a.aoColumns[c].nTh,c);e(a.oInstance).trigger("column-reorder.dt",[a,{from:b,to:d,mapping:m,drop:f,iFrom:b,iTo:d,aiInvertMapping:m}])}};var i=function(a,b){var d=(new e.fn.dataTable.Api(a)).settings()[0];if(d._colReorder)return d._colReorder;!0===b&&(b={});var f=e.fn.dataTable.camelToHungarian;f&&(f(i.defaults,i.defaults,!0),f(i.defaults,b||{}));this.s={dt:null,enable:null,init:e.extend(!0,{},i.defaults,b),fixed:0,
fixedRight:0,reorderCallback:null,mouse:{startX:-1,startY:-1,offsetX:-1,offsetY:-1,target:-1,targetIndex:-1,fromIndex:-1},aoTargets:[]};this.dom={drag:null,pointer:null};this.s.enable=this.s.init.bEnable;this.s.dt=d;this.s.dt._colReorder=this;this._fnConstruct();return this};e.extend(i.prototype,{fnEnable:function(a){if(!1===a)return fnDisable();this.s.enable=!0},fnDisable:function(){this.s.enable=!1},fnReset:function(){this._fnOrderColumns(this.fnOrder());return this},fnGetCurrentOrder:function(){return this.fnOrder()},
fnOrder:function(a,b){var d=[],f,h,c=this.s.dt.aoColumns;if(a===s){f=0;for(h=c.length;f<h;f++)d.push(c[f]._ColReorder_iOrigCol);return d}if(b){c=this.fnOrder();f=0;for(h=a.length;f<h;f++)d.push(e.inArray(a[f],c));a=d}this._fnOrderColumns(r(a));return this},fnTranspose:function(a,b){b||(b="toCurrent");var d=this.fnOrder(),f=this.s.dt.aoColumns;return"toCurrent"===b?!Array.isArray(a)?e.inArray(a,d):e.map(a,function(a){return e.inArray(a,d)}):!Array.isArray(a)?f[a]._ColReorder_iOrigCol:e.map(a,function(a){return f[a]._ColReorder_iOrigCol})},
_fnConstruct:function(){var a=this,b=this.s.dt.aoColumns.length,d=this.s.dt.nTable,f;this.s.init.iFixedColumns&&(this.s.fixed=this.s.init.iFixedColumns);this.s.init.iFixedColumnsLeft&&(this.s.fixed=this.s.init.iFixedColumnsLeft);this.s.fixedRight=this.s.init.iFixedColumnsRight?this.s.init.iFixedColumnsRight:0;this.s.init.fnReorderCallback&&(this.s.reorderCallback=this.s.init.fnReorderCallback);for(f=0;f<b;f++)f>this.s.fixed-1&&f<b-this.s.fixedRight&&this._fnMouseListener(f,this.s.dt.aoColumns[f].nTh),
this.s.dt.aoColumns[f]._ColReorder_iOrigCol=f;this.s.dt.oApi._fnCallbackReg(this.s.dt,"aoStateSaveParams",function(b,c){a._fnStateSave.call(a,c)},"ColReorder_State");var h=null;this.s.init.aiOrder&&(h=this.s.init.aiOrder.slice());this.s.dt.oLoadedState&&("undefined"!=typeof this.s.dt.oLoadedState.ColReorder&&this.s.dt.oLoadedState.ColReorder.length==this.s.dt.aoColumns.length)&&(h=this.s.dt.oLoadedState.ColReorder);if(h)if(a.s.dt._bInitComplete)b=r(h),a._fnOrderColumns.call(a,b);else{var c=!1;e(d).on("draw.dt.colReorder",
function(){if(!a.s.dt._bInitComplete&&!c){c=true;var b=r(h);a._fnOrderColumns.call(a,b)}})}else this._fnSetColumnIndexes();e(d).on("destroy.dt.colReorder",function(){e(d).off("destroy.dt.colReorder draw.dt.colReorder");e.each(a.s.dt.aoColumns,function(a,b){e(b.nTh).off(".ColReorder");e(b.nTh).removeAttr("data-column-index")});a.s.dt._colReorder=null;a.s=null})},_fnOrderColumns:function(a){var b=!1;if(a.length!=this.s.dt.aoColumns.length)this.s.dt.oInstance.oApi._fnLog(this.s.dt,1,"ColReorder - array reorder does not match known number of columns. Skipping.");
else{for(var d=0,f=a.length;d<f;d++){var h=e.inArray(d,a);d!=h&&(q(a,h,d),this.s.dt.oInstance.fnColReorder(h,d,!0,!1),b=!0)}this._fnSetColumnIndexes();b&&(e.fn.dataTable.Api(this.s.dt).rows().invalidate(),(""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY)&&this.s.dt.oInstance.fnAdjustColumnSizing(!1),this.s.dt.oInstance.oApi._fnSaveState(this.s.dt),null!==this.s.reorderCallback&&this.s.reorderCallback.call(this))}},_fnStateSave:function(a){var b,d,f,h=this.s.dt.aoColumns;a.ColReorder=[];if(a.aaSorting){for(b=
0;b<a.aaSorting.length;b++)a.aaSorting[b][0]=h[a.aaSorting[b][0]]._ColReorder_iOrigCol;var c=e.extend(!0,[],a.aoSearchCols);b=0;for(d=h.length;b<d;b++)f=h[b]._ColReorder_iOrigCol,a.aoSearchCols[f]=c[b],a.abVisCols[f]=h[b].bVisible,a.ColReorder.push(f)}else if(a.order){for(b=0;b<a.order.length;b++)a.order[b][0]=h[a.order[b][0]]._ColReorder_iOrigCol;c=e.extend(!0,[],a.columns);b=0;for(d=h.length;b<d;b++)f=h[b]._ColReorder_iOrigCol,a.columns[f]=c[b],a.ColReorder.push(f)}},_fnMouseListener:function(a,
b){var d=this;e(b).on("mousedown.ColReorder",function(a){d.s.enable&&1===a.which&&d._fnMouseDown.call(d,a,b)}).on("touchstart.ColReorder",function(a){d.s.enable&&d._fnMouseDown.call(d,a,b)})},_fnMouseDown:function(a,b){var d=this,f=e(a.target).closest("th, td").offset(),h=parseInt(e(b).attr("data-column-index"),10);h!==s&&(this.s.mouse.startX=this._fnCursorPosition(a,"pageX"),this.s.mouse.startY=this._fnCursorPosition(a,"pageY"),this.s.mouse.offsetX=this._fnCursorPosition(a,"pageX")-f.left,this.s.mouse.offsetY=
this._fnCursorPosition(a,"pageY")-f.top,this.s.mouse.target=this.s.dt.aoColumns[h].nTh,this.s.mouse.targetIndex=h,this.s.mouse.fromIndex=h,this._fnRegions(),e(o).on("mousemove.ColReorder touchmove.ColReorder",function(a){d._fnMouseMove.call(d,a)}).on("mouseup.ColReorder touchend.ColReorder",function(a){d._fnMouseUp.call(d,a)}))},_fnMouseMove:function(a){var b=this;if(null===this.dom.drag){if(5>Math.pow(Math.pow(this._fnCursorPosition(a,"pageX")-this.s.mouse.startX,2)+Math.pow(this._fnCursorPosition(a,
"pageY")-this.s.mouse.startY,2),0.5))return;this._fnCreateDragNode()}this.dom.drag.css({left:this._fnCursorPosition(a,"pageX")-this.s.mouse.offsetX,top:this._fnCursorPosition(a,"pageY")-this.s.mouse.offsetY});for(var d,e=this.s.mouse.toIndex,a=this._fnCursorPosition(a,"pageX"),h=function(){for(var a=b.s.aoTargets.length-1;0<a;a--)if(b.s.aoTargets[a].x!==b.s.aoTargets[a-1].x)return b.s.aoTargets[a]},c=1;c<this.s.aoTargets.length;c++){var g;a:{for(g=c;0<=g;){g--;if(0>=g){g=null;break a}if(b.s.aoTargets[g+
1].x!==b.s.aoTargets[g].x){g=b.s.aoTargets[g];break a}}g=void 0}if(!g)a:{for(g=0;g<b.s.aoTargets.length-1;g++)if(b.s.aoTargets[g].x!==b.s.aoTargets[g+1].x){g=b.s.aoTargets[g];break a}g=void 0}var j=g.x+(this.s.aoTargets[c].x-g.x)/2;if(this._fnIsLtr()){if(a<j){d=g;break}}else if(a>j){d=g;break}}d?(this.dom.pointer.css("left",d.x),this.s.mouse.toIndex=d.to):(this.dom.pointer.css("left",h().x),this.s.mouse.toIndex=h().to);this.s.init.bRealtime&&e!==this.s.mouse.toIndex&&(this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,
this.s.mouse.toIndex),this.s.mouse.fromIndex=this.s.mouse.toIndex,(""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY)&&this.s.dt.oInstance.fnAdjustColumnSizing(!1),this._fnRegions())},_fnMouseUp:function(){e(o).off(".ColReorder");null!==this.dom.drag&&(this.dom.drag.remove(),this.dom.pointer.remove(),this.dom.drag=null,this.dom.pointer=null,this.s.dt.oInstance.fnColReorder(this.s.mouse.fromIndex,this.s.mouse.toIndex,!0),this._fnSetColumnIndexes(),(""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY)&&
this.s.dt.oInstance.fnAdjustColumnSizing(!1),this.s.dt.oInstance.oApi._fnSaveState(this.s.dt),null!==this.s.reorderCallback&&this.s.reorderCallback.call(this))},_fnRegions:function(){var a=this.s.dt.aoColumns,b=this._fnIsLtr();this.s.aoTargets.splice(0,this.s.aoTargets.length);var d=e(this.s.dt.nTable).offset().left,f=[];e.each(a,function(a,c){if(c.bVisible&&"none"!==c.nTh.style.display){var h=e(c.nTh),i=h.offset().left;b&&(i+=h.outerWidth());f.push({index:a,bound:i});d=i}else f.push({index:a,bound:d})});
var h=f[0],a=e(a[h.index].nTh).outerWidth();this.s.aoTargets.push({to:0,x:h.bound-a});for(h=0;h<f.length;h++){var a=f[h],c=a.index;a.index<this.s.mouse.fromIndex&&c++;this.s.aoTargets.push({to:c,x:a.bound})}0!==this.s.fixedRight&&this.s.aoTargets.splice(this.s.aoTargets.length-this.s.fixedRight);0!==this.s.fixed&&this.s.aoTargets.splice(0,this.s.fixed)},_fnCreateDragNode:function(){var a=""!==this.s.dt.oScroll.sX||""!==this.s.dt.oScroll.sY,b=this.s.dt.aoColumns[this.s.mouse.targetIndex].nTh,d=b.parentNode,
f=d.parentNode,h=f.parentNode,c=e(b).clone();this.dom.drag=e(h.cloneNode(!1)).addClass("DTCR_clonedTable").append(e(f.cloneNode(!1)).append(e(d.cloneNode(!1)).append(c[0]))).css({position:"absolute",top:0,left:0,width:e(b).outerWidth(),height:e(b).outerHeight()}).appendTo("body");this.dom.pointer=e("<div></div>").addClass("DTCR_pointer").css({position:"absolute",top:a?e("div.dataTables_scroll",this.s.dt.nTableWrapper).offset().top:e(this.s.dt.nTable).offset().top,height:a?e("div.dataTables_scroll",
this.s.dt.nTableWrapper).height():e(this.s.dt.nTable).height()}).appendTo("body")},_fnSetColumnIndexes:function(){e.each(this.s.dt.aoColumns,function(a,b){e(b.nTh).attr("data-column-index",a)})},_fnCursorPosition:function(a,b){return-1!==a.type.indexOf("touch")?a.originalEvent.touches[0][b]:a[b]},_fnIsLtr:function(){return"rtl"!==e(this.s.dt.nTable).css("direction")}});i.defaults={aiOrder:null,bEnable:!0,bRealtime:!0,iFixedColumnsLeft:0,iFixedColumnsRight:0,fnReorderCallback:null};i.version="1.5.3";
e.fn.dataTable.ColReorder=i;e.fn.DataTable.ColReorder=i;"function"==typeof e.fn.dataTable&&"function"==typeof e.fn.dataTableExt.fnVersionCheck&&e.fn.dataTableExt.fnVersionCheck("1.10.8")?e.fn.dataTableExt.aoFeatures.push({fnInit:function(a){var b=a.oInstance;a._colReorder?b.oApi._fnLog(a,1,"ColReorder attempted to initialise twice. Ignoring second"):(b=a.oInit,new i(a,b.colReorder||b.oColReorder||{}));return null},cFeature:"R",sFeature:"ColReorder"}):alert("Warning: ColReorder requires DataTables 1.10.8 or greater - www.datatables.net/download");
e(o).on("preInit.dt.colReorder",function(a,b){if("dt"===a.namespace){var d=b.oInit.colReorder,f=u.defaults.colReorder;if(d||f)f=e.extend({},d,f),!1!==d&&new i(b,f)}});e.fn.dataTable.Api.register("colReorder.reset()",function(){return this.iterator("table",function(a){a._colReorder.fnReset()})});e.fn.dataTable.Api.register("colReorder.order()",function(a,b){return a?this.iterator("table",function(d){d._colReorder.fnOrder(a,b)}):this.context.length?this.context[0]._colReorder.fnOrder():null});e.fn.dataTable.Api.register("colReorder.transpose()",
function(a,b){return this.context.length&&this.context[0]._colReorder?this.context[0]._colReorder.fnTranspose(a,b):a});e.fn.dataTable.Api.register("colReorder.move()",function(a,b,d,e){this.context.length&&(this.context[0]._colReorder.s.dt.oInstance.fnColReorder(a,b,d,e),this.context[0]._colReorder._fnSetColumnIndexes());return this});e.fn.dataTable.Api.register("colReorder.enable()",function(a){return this.iterator("table",function(b){b._colReorder&&b._colReorder.fnEnable(a)})});e.fn.dataTable.Api.register("colReorder.disable()",
function(){return this.iterator("table",function(a){a._colReorder&&a._colReorder.fnDisable()})});return i});
