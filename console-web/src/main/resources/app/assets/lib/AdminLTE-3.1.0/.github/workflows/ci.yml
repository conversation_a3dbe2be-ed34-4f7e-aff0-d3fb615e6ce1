name: CI

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - "**"

env:
  FORCE_COLOR: 2

jobs:
  run:
    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        node:
          - "10.x"
          - "12.x"
          - "14.x"
        os:
          - "ubuntu-latest"
          - "macos-latest"
          - "windows-latest"

    steps:
      - name: Clone repository
        uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node }}

      - name: Get npm cache directory
        id: npm-cache
        run: |
          echo "::set-output name=dir::$(npm config get cache)"

      - name: Set up npm cache
        uses: actions/cache@v2
        with:
          path: ${{ steps.npm-cache.outputs.dir }}
          key: ${{ runner.os }}-node-v${{ matrix.node }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}}
          restore-keys: |
            ${{ runner.os }}-node-v${{ matrix.node }}-${{ hashFiles('package.json') }}-${{ hashFiles('package-lock.json') }}
            ${{ runner.os }}-node-v${{ matrix.node }}-

      - name: Install npm dependencies
        run: npm ci

      - name: Build files
        run: npm run compile

      - name: Run bundlewatch
        run: npm run bundlewatch
        if: startsWith(matrix.os, 'ubuntu') && startsWith(matrix.node, '14')
        env:
          BUNDLEWATCH_GITHUB_TOKEN: "${{ secrets.BUNDLEWATCH_GITHUB_TOKEN }}"
