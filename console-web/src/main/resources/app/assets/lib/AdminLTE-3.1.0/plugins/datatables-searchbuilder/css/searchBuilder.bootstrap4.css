div.dt-datetime {
  position: absolute;
  background-color: white;
  z-index: 2050;
  border: 1px solid #ccc;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.5);
  padding: 0 20px 6px 20px;
  width: 275px;
}
div.dt-datetime.inline {
  position: relative;
  box-shadow: none;
}
div.dt-datetime div.dt-datetime-title {
  text-align: center;
  padding: 5px 0px 3px;
}
div.dt-datetime table {
  border-spacing: 0;
  margin: 12px 0;
  width: 100%;
}
div.dt-datetime table.dt-datetime-table-nospace {
  margin-top: -12px;
}
div.dt-datetime table th {
  font-size: 0.8em;
  color: #777;
  font-weight: normal;
  width: 14.285714286%;
  padding: 0 0 4px 0;
  text-align: center;
}
div.dt-datetime table td {
  font-size: 0.9em;
  color: #444;
  padding: 0;
}
div.dt-datetime table td.selectable {
  text-align: center;
  background: #f5f5f5;
}
div.dt-datetime table td.selectable.disabled {
  color: #aaa;
  background: white;
}
div.dt-datetime table td.selectable.disabled button:hover {
  color: #aaa;
  background: white;
}
div.dt-datetime table td.selectable.now {
  background-color: #ddd;
}
div.dt-datetime table td.selectable.now button {
  font-weight: bold;
}
div.dt-datetime table td.selectable.selected button {
  background: #4E6CA3;
  color: white;
  border-radius: 2px;
}
div.dt-datetime table td.selectable button:hover {
  background: #ff8000;
  color: white;
  border-radius: 2px;
}
div.dt-datetime table td.dt-datetime-week {
  font-size: 0.7em;
}
div.dt-datetime table button {
  width: 100%;
  box-sizing: border-box;
  border: none;
  background: transparent;
  font-size: inherit;
  color: inherit;
  text-align: center;
  padding: 4px 0;
  cursor: pointer;
  margin: 0;
}
div.dt-datetime table button span {
  display: inline-block;
  min-width: 14px;
  text-align: right;
}
div.dt-datetime table.weekNumber th {
  width: 12.5%;
}
div.dt-datetime div.dt-datetime-calendar table {
  margin-top: 0;
}
div.dt-datetime div.dt-datetime-label {
  position: relative;
  display: inline-block;
  height: 30px;
  padding: 5px 6px;
  border: 1px solid transparent;
  box-sizing: border-box;
  cursor: pointer;
}
div.dt-datetime div.dt-datetime-label:hover {
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #f5f5f5;
}
div.dt-datetime div.dt-datetime-label select {
  position: absolute;
  top: 6px;
  left: 0;
  cursor: pointer;
  opacity: 0;
}
div.dt-datetime.horizontal {
  width: 550px;
}
div.dt-datetime.horizontal div.dt-datetime-date,
div.dt-datetime.horizontal div.dt-datetime-time {
  width: 48%;
}
div.dt-datetime.horizontal div.dt-datetime-time {
  margin-left: 4%;
}
div.dt-datetime div.dt-datetime-date {
  position: relative;
  float: left;
  width: 100%;
}
div.dt-datetime div.dt-datetime-time {
  position: relative;
  float: left;
  width: 100%;
  text-align: center;
}
div.dt-datetime div.dt-datetime-time > span {
  vertical-align: middle;
}
div.dt-datetime div.dt-datetime-time th {
  text-align: left;
}
div.dt-datetime div.dt-datetime-time div.dt-datetime-timeblock {
  display: inline-block;
  vertical-align: middle;
}
div.dt-datetime div.dt-datetime-iconLeft,
div.dt-datetime div.dt-datetime-iconRight,
div.dt-datetime div.dt-datetime-iconUp,
div.dt-datetime div.dt-datetime-iconDown {
  width: 30px;
  height: 30px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
  overflow: hidden;
  box-sizing: border-box;
}
div.dt-datetime div.dt-datetime-iconLeft:hover,
div.dt-datetime div.dt-datetime-iconRight:hover,
div.dt-datetime div.dt-datetime-iconUp:hover,
div.dt-datetime div.dt-datetime-iconDown:hover {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #f0f0f0;
  opacity: 0.6;
}
div.dt-datetime div.dt-datetime-iconLeft button,
div.dt-datetime div.dt-datetime-iconRight button,
div.dt-datetime div.dt-datetime-iconUp button,
div.dt-datetime div.dt-datetime-iconDown button {
  border: none;
  background: transparent;
  text-indent: 30px;
  height: 100%;
  width: 100%;
  cursor: pointer;
}
div.dt-datetime div.dt-datetime-iconLeft {
  position: absolute;
  top: 5px;
  left: 5px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==");
}
div.dt-datetime div.dt-datetime-iconRight {
  position: absolute;
  top: 5px;
  right: 5px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=");
}
div.dt-datetime div.dt-datetime-iconUp {
  height: 20px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAALCAMAAABf9c24AAAAFVBMVEX///99fX1+fn57e3t6enoAAAAAAAC73bqPAAAABnRSTlMAYmJkZt92bnysAAAAL0lEQVR4AWOgJmBhxCvLyopHnpmVjY2VCadeoCxIHrcsWJ4RlyxCHlMWCTBRJxwAjrIBDMWSiM0AAAAASUVORK5CYII=");
}
div.dt-datetime div.dt-datetime-iconDown {
  height: 20px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAALCAMAAABf9c24AAAAFVBMVEX///99fX1+fn57e3t6enoAAAAAAAC73bqPAAAABnRSTlMAYmJkZt92bnysAAAAMElEQVR4AWOgDmBiRQIsmPKMrGxQgJDFlEfIYpoPk8Utz8qM232MYFfhkQfKUg8AANefAQxecJ58AAAAAElFTkSuQmCC");
}

div.dt-datetime-error {
  clear: both;
  padding: 0 1em;
  max-width: 240px;
  font-size: 11px;
  line-height: 1.25em;
  text-align: center;
  color: #b11f1f;
}

div.dt-button-collection {
  overflow: visible !important;
}

.dtsb-greyscale {
  border: 1px solid #cecece !important;
}

div.dtsb-logicContainer .dtsb-greyscale {
  border: none !important;
}

div.dtsb-searchBuilder {
  justify-content: space-evenly;
  cursor: default;
  margin-bottom: 1em;
  text-align: left;
}
div.dtsb-searchBuilder button.dtsb-button,
div.dtsb-searchBuilder select {
  font-size: 1em;
}
div.dtsb-searchBuilder div.dtsb-titleRow {
  justify-content: space-evenly;
  margin-bottom: 0.5em;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  display: inline-block;
  padding-top: 6px;
}
div.dtsb-searchBuilder div.dtsb-titleRow button.dtsb-clearAll {
  float: right;
  margin-bottom: 0.333em;
}
div.dtsb-searchBuilder div.dtsb-vertical .dtsb-value, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-data, div.dtsb-searchBuilder div.dtsb-vertical .dtsb-condition {
  display: block;
}
div.dtsb-searchBuilder div.dtsb-group {
  position: relative;
  clear: both;
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin: 2px;
  text-align: center;
  padding: 0;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  position: absolute;
  margin-top: 0.8em;
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria {
  margin-bottom: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input {
  padding: 0.4em;
  margin-right: 0.8em;
  max-width: 20em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-dropDown option.dtsb-notItalic,
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-input option.dtsb-notItalic {
  font-style: normal;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-italic {
  font-style: italic;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer {
  float: right;
  display: inline-block;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left {
  margin-right: 0.8em;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-delete:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-right:last-child, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria div.dtsb-buttonContainer button.dtsb-left:last-child {
  margin-right: 0;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria span.dtsp-joiner {
  margin-right: 0.8em;
}

div.dtsb-searchBuilder div.dtsb-titleRow {
  height: 40px;
}
div.dtsb-searchBuilder div.dtsb-titleRow div.dtsb-title {
  padding-top: 10px;
}
div.dtsb-searchBuilder div.dtsb-group button.dtsb-clearGroup {
  margin-right: 8px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria .form-control {
  width: auto;
  display: inline-block;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-condition {
  border-color: #28a745;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-data {
  border-color: #dc3545;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria select.dtsb-value, div.dtsb-searchBuilder div.dtsb-group div.dtsb-criteria input.dtsb-value {
  border-color: #007bff;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer {
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
  margin-top: 10px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-logic {
  border: none;
  border-radius: 0px;
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: 40px;
  margin: 0px;
}
div.dtsb-searchBuilder div.dtsb-group div.dtsb-logicContainer button.dtsb-clearGroup {
  border: none;
  border-radius: 0px;
  width: 30px;
  margin: 0px;
}
