(function(){var x=function(a,b,d,c){var e=b.moment?b.moment:b.dayjs?b.dayjs:null,i=function(b,d){this.c=a.extend(!0,{},i.defaults,d);var c=this.c.classPrefix,g=this.c.i18n;if(!e&&"YYYY-MM-DD"!==this.c.format)throw"DateTime: Without momentjs or dayjs only the format 'YYYY-MM-DD' can be used";"string"===typeof this.c.minDate&&(this.c.minDate=new Date(this.c.minDate));"string"===typeof this.c.maxDate&&(this.c.maxDate=new Date(this.c.maxDate));g=a('<div class="'+c+'"><div class="'+c+'-date"><div class="'+
c+'-title"><div class="'+c+'-iconLeft"><button>'+g.previous+'</button></div><div class="'+c+'-iconRight"><button>'+g.next+'</button></div><div class="'+c+'-label"><span></span><select class="'+c+'-month"></select></div><div class="'+c+'-label"><span></span><select class="'+c+'-year"></select></div></div><div class="'+c+'-calendar"></div></div><div class="'+c+'-time"><div class="'+c+'-hours"></div><div class="'+c+'-minutes"></div><div class="'+c+'-seconds"></div></div><div class="'+c+'-error"></div></div>');
this.dom={container:g,date:g.find("."+c+"-date"),title:g.find("."+c+"-title"),calendar:g.find("."+c+"-calendar"),time:g.find("."+c+"-time"),error:g.find("."+c+"-error"),input:a(b)};this.s={d:null,display:null,minutesRange:null,secondsRange:null,namespace:"dateime-"+i._instance++,parts:{date:null!==this.c.format.match(/[YMD]|L(?!T)|l/),time:null!==this.c.format.match(/[Hhm]|LT|LTS/),seconds:-1!==this.c.format.indexOf("s"),hours12:null!==this.c.format.match(/[haA]/)}};this.dom.container.append(this.dom.date).append(this.dom.time).append(this.dom.error);
this.dom.date.append(this.dom.title).append(this.dom.calendar);this._constructor()};a.extend(i.prototype,{destroy:function(){this._hide(!0);this.dom.container.off().empty();this.dom.input.off(".datetime")},errorMsg:function(a){var b=this.dom.error;a?b.html(a):b.empty();return this},hide:function(){this._hide();return this},max:function(a){this.c.maxDate="string"===typeof a?new Date(a):a;this._optionsTitle();this._setCalander();return this},min:function(a){this.c.minDate="string"===typeof a?new Date(a):
a;this._optionsTitle();this._setCalander();return this},owns:function(b){return 0<a(b).parents().filter(this.dom.container).length},val:function(a,b){if(a===c)return this.s.d;if(a instanceof Date)this.s.d=this._dateToUtc(a);else if(null===a||""===a)this.s.d=null;else if("--now"===a)this.s.d=new Date;else if("string"===typeof a)if(e){var d=e.utc(a,this.c.format,this.c.locale,this.c.strict);this.s.d=d.isValid()?d.toDate():null}else d=a.match(/(\d{4})\-(\d{2})\-(\d{2})/),this.s.d=d?new Date(Date.UTC(d[1],
d[2]-1,d[3])):null;if(b||b===c)this.s.d?this._writeOutput():this.dom.input.val(a);this.s.d||(this.s.d=this._dateToUtc(new Date));this.s.display=new Date(this.s.d.toString());this.s.display.setUTCDate(1);this._setTitle();this._setCalander();this._setTime();return this},_constructor:function(){var d=this,c=this.c.classPrefix,g=function(){d.c.onChange.call(d,d.dom.input.val(),d.s.d,d.dom.input)};this.s.parts.date||this.dom.date.css("display","none");this.s.parts.time||this.dom.time.css("display","none");
this.s.parts.seconds||(this.dom.time.children("div."+c+"-seconds").remove(),this.dom.time.children("span").eq(1).remove());this._optionsTitle();b.allan=this;"hidden"===this.dom.input.attr("type")&&(this.dom.container.addClass("inline"),this.c.attachTo="input",this.val(this.dom.input.val(),!1),this._show());this.dom.input.attr("autocomplete","off").on("focus.datetime click.datetime",function(){if(!d.dom.container.is(":visible")&&!d.dom.input.is(":disabled")){d.val(d.dom.input.val(),false);d._show()}}).on("keyup.datetime",
function(){d.dom.container.is(":visible")&&d.val(d.dom.input.val(),false)});this.dom.container.on("change","select",function(){var b=a(this),e=b.val();if(b.hasClass(c+"-month")){d._correctMonth(d.s.display,e);d._setTitle();d._setCalander()}else if(b.hasClass(c+"-year")){d.s.display.setUTCFullYear(e);d._setTitle();d._setCalander()}else if(b.hasClass(c+"-hours")||b.hasClass(c+"-ampm")){if(d.s.parts.hours12){b=a(d.dom.container).find("."+c+"-hours").val()*1;e=a(d.dom.container).find("."+c+"-ampm").val()===
"pm";d.s.d.setUTCHours(b===12&&!e?0:e&&b!==12?b+12:b)}else d.s.d.setUTCHours(e);d._setTime();d._writeOutput(true);g()}else if(b.hasClass(c+"-minutes")){d.s.d.setUTCMinutes(e);d._setTime();d._writeOutput(true);g()}else if(b.hasClass(c+"-seconds")){d.s.d.setSeconds(e);d._setTime();d._writeOutput(true);g()}d.dom.input.focus();d._position()}).on("click",function(b){var e=d.s.d,i=b.target.nodeName.toLowerCase(),h=i==="span"?b.target.parentNode:b.target,i=h.nodeName.toLowerCase();if(i!=="select"){b.stopPropagation();
if(i==="button"){h=a(h);b=h.parent();if(b.hasClass("disabled")&&!b.hasClass("range"))h.blur();else if(b.hasClass(c+"-iconLeft")){d.s.display.setUTCMonth(d.s.display.getUTCMonth()-1);d._setTitle();d._setCalander();d.dom.input.focus()}else if(b.hasClass(c+"-iconRight")){d._correctMonth(d.s.display,d.s.display.getUTCMonth()+1);d._setTitle();d._setCalander();d.dom.input.focus()}else{if(h.parents("."+c+"-time").length){i=h.data("value");h=h.data("unit");if(h==="minutes"){if(b.hasClass("disabled")&&b.hasClass("range")){d.s.minutesRange=
i;d._setTime();return}d.s.minutesRange=null}if(h==="seconds"){if(b.hasClass("disabled")&&b.hasClass("range")){d.s.secondsRange=i;d._setTime();return}d.s.secondsRange=null}if(i==="am")if(e.getUTCHours()>=12)i=e.getUTCHours()-12;else return;else if(i==="pm")if(e.getUTCHours()<12)i=e.getUTCHours()+12;else return;e[h==="hours"?"setUTCHours":h==="minutes"?"setUTCMinutes":"setSeconds"](i);d._setTime();d._writeOutput(true)}else{e||(e=d._dateToUtc(new Date));e.setUTCDate(1);e.setUTCFullYear(h.data("year"));
e.setUTCMonth(h.data("month"));e.setUTCDate(h.data("day"));d._writeOutput(true);d.s.parts.time?d._setCalander():setTimeout(function(){d._hide()},10)}g()}}else d.dom.input.focus()}})},_compareDates:function(a,b){return this._dateToUtcString(a)===this._dateToUtcString(b)},_correctMonth:function(a,b){var d=this._daysInMonth(a.getUTCFullYear(),b),c=a.getUTCDate()>d;a.setUTCMonth(b);c&&(a.setUTCDate(d),a.setUTCMonth(b))},_daysInMonth:function(a,b){return[31,0===a%4&&(0!==a%100||0===a%400)?29:28,31,30,
31,30,31,31,30,31,30,31][b]},_dateToUtc:function(a){return new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds()))},_dateToUtcString:function(a){return a.getUTCFullYear()+"-"+this._pad(a.getUTCMonth()+1)+"-"+this._pad(a.getUTCDate())},_hide:function(c){if(c||"hidden"!==this.dom.input.attr("type"))c=this.s.namespace,this.dom.container.detach(),a(b).off("."+c),a(d).off("keydown."+c),a("div.dataTables_scrollBody").off("scroll."+c),a("div.DTE_Body_Content").off("scroll."+
c),a("body").off("click."+c)},_hours24To12:function(a){return 0===a?12:12<a?a-12:a},_htmlDay:function(a){if(a.empty)return'<td class="empty"></td>';var b=["selectable"],d=this.c.classPrefix;a.disabled&&b.push("disabled");a.today&&b.push("now");a.selected&&b.push("selected");return'<td data-day="'+a.day+'" class="'+b.join(" ")+'"><button class="'+d+"-button "+d+'-day" type="button" data-year="'+a.year+'" data-month="'+a.month+'" data-day="'+a.day+'"><span>'+a.day+"</span></button></td>"},_htmlMonth:function(b,
d){var c=this._dateToUtc(new Date),g=this._daysInMonth(b,d),e=(new Date(Date.UTC(b,d,1))).getUTCDay(),i=[],h=[];0<this.c.firstDay&&(e-=this.c.firstDay,0>e&&(e+=7));for(var f=g+e,j=f;7<j;)j-=7;var f=f+(7-j),k=this.c.minDate,j=this.c.maxDate;k&&(k.setUTCHours(0),k.setUTCMinutes(0),k.setSeconds(0));j&&(j.setUTCHours(23),j.setUTCMinutes(59),j.setSeconds(59));for(var l=0,n=0;l<f;l++){var m=new Date(Date.UTC(b,d,1+(l-e))),o=this.s.d?this._compareDates(m,this.s.d):!1,z=this._compareDates(m,c),q=l<e||l>=
g+e,w=k&&m<k||j&&m>j,t=this.c.disableDays;Array.isArray(t)&&-1!==a.inArray(m.getUTCDay(),t)?w=!0:"function"===typeof t&&!0===t(m)&&(w=!0);h.push(this._htmlDay({day:1+(l-e),month:d,year:b,selected:o,today:z,disabled:w,empty:q}));7===++n&&(this.c.showWeekNumber&&h.unshift(this._htmlWeekOfYear(l-e,d,b)),i.push("<tr>"+h.join("")+"</tr>"),h=[],n=0)}c=this.c.classPrefix;g=c+"-table";this.c.showWeekNumber&&(g+=" weekNumber");k&&(k=k>=new Date(Date.UTC(b,d,1,0,0,0)),this.dom.title.find("div."+c+"-iconLeft").css("display",
k?"none":"block"));j&&(j=j<new Date(Date.UTC(b,d+1,1,0,0,0)),this.dom.title.find("div."+c+"-iconRight").css("display",j?"none":"block"));return'<table class="'+g+'"><thead>'+this._htmlMonthHead()+"</thead><tbody>"+i.join("")+"</tbody></table>"},_htmlMonthHead:function(){var a=[],b=this.c.firstDay,d=this.c.i18n,c=function(a){for(a+=b;7<=a;)a-=7;return d.weekdays[a]};this.c.showWeekNumber&&a.push("<th></th>");for(var g=0;7>g;g++)a.push("<th>"+c(g)+"</th>");return a.join("")},_htmlWeekOfYear:function(a,
b,d){a=new Date(d,b,a,0,0,0,0);a.setDate(a.getDate()+4-(a.getDay()||7));d=Math.ceil(((a-new Date(d,0,1))/864E5+1)/7);return'<td class="'+this.c.classPrefix+'-week">'+d+"</td>"},_options:function(a,b,d){d||(d=b);a=this.dom.container.find("select."+this.c.classPrefix+"-"+a);a.empty();for(var c=0,g=b.length;c<g;c++)a.append('<option value="'+b[c]+'">'+d[c]+"</option>")},_optionSet:function(a,b){var d=this.dom.container.find("select."+this.c.classPrefix+"-"+a),c=d.parent().children("span");d.val(b);d=
d.find("option:selected");c.html(0!==d.length?d.text():this.c.i18n.unknown)},_optionsTime:function(b,d,c,g,e){var i=this.c.classPrefix,h=this.dom.container.find("div."+i+"-"+b),f,j=12===d?function(a){return a}:this._pad,i=this.c.classPrefix,k=i+"-table",l=this.c.i18n;if(h.length){var n="";f=10;var m=function(e,h,f){12===d&&"number"===typeof e&&(12<=c&&(e+=12),12==e?e=0:24==e&&(e=12));var j=c===e||"am"===e&&12>c||"pm"===e&&12<=c?"selected":"";g&&-1===a.inArray(e,g)&&(j+=" disabled");f&&(j+=" "+f);
return'<td class="selectable '+j+'"><button class="'+i+"-button "+i+'-day" type="button" data-unit="'+b+'" data-value="'+e+'"><span>'+h+"</span></button></td>"};if(12===d){n+="<tr>";for(e=1;6>=e;e++)n+=m(e,j(e));n+=m("am",l.amPm[0]);n+="</tr><tr>";for(e=7;12>=e;e++)n+=m(e,j(e));n+=m("pm",l.amPm[1]);n+="</tr>";f=7}else{if(24===d){var o=0;for(f=0;4>f;f++){n+="<tr>";for(e=0;6>e;e++)n+=m(o,j(o)),o++;n+="</tr>"}}else{n+="<tr>";for(f=0;60>f;f+=10)n+=m(f,j(f),"range");e=null!==e?e:10*Math.floor(c/10);n=
n+"</tr>"+('</tbody></thead><table class="'+k+" "+k+'-nospace"><tbody>')+"<tr>";for(f=e+1;f<e+10;f++)n+=m(f,j(f));n+="</tr>"}f=6}h.empty().append('<table class="'+k+'"><thead><tr><th colspan="'+f+'">'+l[b]+"</th></tr></thead><tbody>"+n+"</tbody></table>")}},_optionsTitle:function(){var a=this.c.i18n,b=this.c.minDate,d=this.c.maxDate,b=b?b.getFullYear():null,d=d?d.getFullYear():null,b=null!==b?b:(new Date).getFullYear()-this.c.yearRange,d=null!==d?d:(new Date).getFullYear()+this.c.yearRange;this._options("month",
this._range(0,11),a.months);this._options("year",this._range(b,d))},_pad:function(a){return 10>a?"0"+a:a},_position:function(){var d="input"===this.c.attachTo?this.dom.input.position():this.dom.input.offset(),c=this.dom.container,e=this.dom.input.outerHeight();if(c.hasClass("inline"))c.insertAfter(this.dom.input);else{this.s.parts.date&&this.s.parts.time&&550<a(b).width()?c.addClass("horizontal"):c.removeClass("horizontal");"input"===this.c.attachTo?c.css({top:d.top+e,left:d.left}).insertAfter(this.dom.input):
c.css({top:d.top+e,left:d.left}).appendTo("body");var g=c.outerHeight(),i=c.outerWidth(),h=a(b).scrollTop();d.top+e+g-h>a(b).height()&&(e=d.top-g,c.css("top",0>e?0:e));i+d.left>a(b).width()&&(d=a(b).width()-i,"input"===this.c.attachTo&&(d-=a(c).offsetParent().offset().left),c.css("left",0>d?0:d))}},_range:function(a,b,d){var c=[];for(d||(d=1);a<=b;a+=d)c.push(a);return c},_setCalander:function(){this.s.display&&this.dom.calendar.empty().append(this._htmlMonth(this.s.display.getUTCFullYear(),this.s.display.getUTCMonth()))},
_setTitle:function(){this._optionSet("month",this.s.display.getUTCMonth());this._optionSet("year",this.s.display.getUTCFullYear())},_setTime:function(){var a=this,b=this.s.d,d=b?b.getUTCHours():0,c=function(b){return a.c[b+"Available"]?a.c[b+"Available"]:a._range(0,59,a.c[b+"Increment"])};this._optionsTime("hours",this.s.parts.hours12?12:24,d,this.c.hoursAvailable);this._optionsTime("minutes",60,b?b.getUTCMinutes():0,c("minutes"),this.s.minutesRange);this._optionsTime("seconds",60,b?b.getSeconds():
0,c("seconds"),this.s.secondsRange)},_show:function(){var c=this,e=this.s.namespace;this._position();a(b).on("scroll."+e+" resize."+e,function(){c._hide()});a("div.DTE_Body_Content").on("scroll."+e,function(){c._hide()});a("div.dataTables_scrollBody").on("scroll."+e,function(){c._hide()});var g=this.dom.input[0].offsetParent;if(g!==d.body)a(g).on("scroll."+e,function(){c._hide()});a(d).on("keydown."+e,function(a){(9===a.keyCode||27===a.keyCode||13===a.keyCode)&&c._hide()});setTimeout(function(){a("body").on("click."+
e,function(b){!a(b.target).parents().filter(c.dom.container).length&&b.target!==c.dom.input[0]&&c._hide()})},10)},_writeOutput:function(a){var b=this.s.d,d=e?e.utc(b,c,this.c.locale,this.c.strict).format(this.c.format):b.getUTCFullYear()+"-"+this._pad(b.getUTCMonth()+1)+"-"+this._pad(b.getUTCDate());this.dom.input.val(d).trigger("change",{write:b});"hidden"===this.dom.input.attr("type")&&this.val(d,!1);a&&this.dom.input.focus()}});i.use=function(a){e=a};i._instance=0;i.defaults={attachTo:"body",classPrefix:"dt-datetime",
disableDays:null,firstDay:1,format:"YYYY-MM-DD",hoursAvailable:null,i18n:{previous:"Previous",next:"Next",months:"January February March April May June July August September October November December".split(" "),weekdays:"Sun Mon Tue Wed Thu Fri Sat".split(" "),amPm:["am","pm"],hours:"Hour",minutes:"Minute",seconds:"Second",unknown:"-"},maxDate:null,minDate:null,minutesAvailable:null,minutesIncrement:1,strict:!0,locale:"en",onChange:function(){},secondsAvailable:null,secondsIncrement:1,showWeekNumber:!1,
yearRange:25};i.version="1.0.1";b.DateTime||(b.DateTime=i);a.fn.dtDateTime=function(a){return this.each(function(){new i(this,a)})};a.fn.dataTable&&(a.fn.dataTable.DateTime=i,a.fn.DataTable.DateTime=i);return i};"function"===typeof define&&define.amd?define(["jquery"],function(a){return x(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);return x(b,a,a.document)}:x(jQuery,window,document);var e,q,o=window.moment,l,c=function(a,b,d,g,h){var i=this;void 0===g&&
(g=0);void 0===h&&(h=1);if(!q||!q.versionCheck||!q.versionCheck("1.10.0"))throw Error("SearchPane requires DataTables 1.10 or newer");this.classes=e.extend(!0,{},c.classes);this.c=e.extend(!0,{},c.defaults,e.fn.dataTable.ext.searchBuilder,b);b=this.c.i18n;this.s={condition:void 0,conditions:{},data:void 0,dataIdx:-1,dataPoints:[],depth:h,dt:a,filled:!1,index:g,momentFormat:!1,topGroup:d,type:"",value:[]};this.dom={buttons:e("<div/>").addClass(this.classes.buttonContainer),condition:e("<select disabled/>").addClass(this.classes.condition).addClass(this.classes.dropDown).addClass(this.classes.italic).attr("autocomplete",
"hacking"),conditionTitle:e('<option value="" disabled selected hidden/>').text(this.s.dt.i18n("searchBuilder.condition",b.condition)),container:e("<div/>").addClass(this.classes.container),data:e("<select/>").addClass(this.classes.data).addClass(this.classes.dropDown).addClass(this.classes.italic),dataTitle:e('<option value="" disabled selected hidden/>').text(this.s.dt.i18n("searchBuilder.data",b.data)),defaultValue:e("<select disabled/>").addClass(this.classes.value).addClass(this.classes.dropDown),
"delete":e("<button>&times</button>").addClass(this.classes["delete"]).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.deleteTitle",b.deleteTitle)).attr("type","button"),left:e("<button><</button>").addClass(this.classes.left).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.leftTitle",b.leftTitle)).attr("type","button"),right:e("<button>></button>").addClass(this.classes.right).addClass(this.classes.button).attr("title",this.s.dt.i18n("searchBuilder.rightTitle",
b.rightTitle)).attr("type","button"),value:[e("<select disabled/>").addClass(this.classes.value).addClass(this.classes.dropDown).addClass(this.classes.italic)],valueTitle:e('<option value="--valueTitle--" selected/>').text(this.s.dt.i18n("searchBuilder.value",b.value))};if(this.c.greyscale){e(this.dom.data).addClass(this.classes.greyscale);e(this.dom.condition).addClass(this.classes.greyscale);e(this.dom.defaultValue).addClass(this.classes.greyscale);a=0;for(d=this.dom.value;a<d.length;a++)e(d[a]).addClass(this.classes.greyscale)}this.s.dt.on("draw.dtsp",
function(){i._adjustCriteria()});this.s.dt.on("buttons-action",function(){i._adjustCriteria()});e(window).on("resize.dtsp",q.util.throttle(function(){i._adjustCriteria()}));this._buildCriteria();return this};c.prototype.updateArrows=function(a,b){void 0===a&&(a=!1);void 0===b&&(b=!0);e(this.dom.container).empty().append(this.dom.data).append(this.dom.condition).append(this.dom.value[0]);e(this.dom.value[0]).trigger("dtsb-inserted");for(var d=1;d<this.dom.value.length;d++)e(this.dom.container).append(this.dom.value[d]),
e(this.dom.value[d]).trigger("dtsb-inserted");1<this.s.depth&&e(this.dom.buttons).append(this.dom.left);(!1===this.c.depthLimit||this.s.depth<this.c.depthLimit)&&a?e(this.dom.buttons).append(this.dom.right):e(this.dom.right).remove();e(this.dom.buttons).append(this.dom["delete"]);e(this.dom.container).append(this.dom.buttons);b&&this._adjustCriteria()};c.prototype.destroy=function(){e(this.dom.data).off(".dtsb");e(this.dom.condition).off(".dtsb");e(this.dom["delete"]).off(".dtsb");for(var a=0,b=this.dom.value;a<
b.length;a++)e(b[a]).off(".dtsb");e(this.dom.container).remove()};c.prototype.search=function(a,b){var d=this.s.conditions[this.s.condition];if(void 0!==this.s.condition&&void 0!==d){-1!==this.s.type.indexOf("num")&&""!==this.s.dt.settings()[0].oLanguage.sDecimal&&(a[this.s.dataIdx]=a[this.s.dataIdx].replace(this.s.dt.settings()[0].oLanguage.sDecimal,"."));var c=a[this.s.dataIdx];"search"!==this.c.orthogonal.search&&(c=this.s.dt.settings()[0],c=c.oApi._fnGetCellData(c,b,this.s.dataIdx,"string"===
typeof this.c.orthogonal?this.c.orthogonal:this.c.orthogonal.search));"array"===this.s.type&&(Array.isArray(c)||(c=[c]),c.sort());return d.search(c,this.s.value,this)}};c.prototype.getDetails=function(){var a=this.s.value;if(-1!==this.s.type.indexOf("num")&&""!==this.s.dt.settings()[0].oLanguage.sDecimal)for(var b=0;b<this.s.value.length;b++)-1!==this.s.value[b].indexOf(".")&&(a[b]=this.s.value[b].replace(".",this.s.dt.settings()[0].oLanguage.sDecimal));return{condition:this.s.condition,data:this.s.data,
value:a}};c.prototype.getNode=function(){return this.dom.container};c.prototype.populate=function(){this._populateData();-1!==this.s.dataIdx&&(this._populateCondition(),void 0!==this.s.condition&&this._populateValue())};c.prototype.rebuild=function(a){var b=!1,d;this._populateData();if(void 0!==a.data){var c=this.classes.italic,h=this.dom.data;e(this.dom.data).children("option").each(function(){e(this).text()===a.data&&(e(this).attr("selected",!0),e(h).removeClass(c),b=!0,d=e(this).val())})}if(b){this.s.data=
a.data;this.s.dataIdx=d;e(this.dom.dataTitle).remove();this._populateCondition();e(this.dom.conditionTitle).remove();var i;e(this.dom.condition).children("option").each(function(){void 0!==a.condition&&(e(this).val()===a.condition&&"string"===typeof a.condition)&&(e(this).attr("selected",!0),i=e(this).val())});this.s.condition=i;void 0!==this.s.condition?(e(this.dom.conditionTitle).remove(),e(this.dom.condition).removeClass(this.classes.italic),this._populateValue(a)):e(this.dom.conditionTitle).prependTo(this.dom.condition).attr("selected",
!0)}};c.prototype.setListeners=function(){var a=this;e(this.dom.data).unbind("input change").on("input change",function(){e(a.dom.dataTitle).attr("selected",!1);e(a.dom.data).removeClass(a.classes.italic);a.s.dataIdx=e(a.dom.data).children("option:selected").val();a.s.data=e(a.dom.data).children("option:selected").text();a.c.orthogonal=a._getOptions().orthogonal;a._clearCondition();a._clearValue();a._populateCondition();a.s.filled&&(a.s.filled=!1,a.s.dt.draw(),a.setListeners());a.s.dt.state.save()});
e(this.dom.condition).unbind("input change").on("input change",function(){e(a.dom.conditionTitle).attr("selected",!1);e(a.dom.condition).removeClass(a.classes.italic);for(var b=e(a.dom.condition).children("option:selected").val(),d=0,c=Object.keys(a.s.conditions);d<c.length;d++)if(c[d]===b){a.s.condition=b;break}a._clearValue();a._populateValue();b=0;for(d=a.dom.value;b<d.length;b++)c=d[b],a.s.filled&&0!==e(a.dom.container).has(c).length&&(a.s.filled=!1,a.s.dt.draw(),a.setListeners());a.s.dt.draw()})};
c.prototype._adjustCriteria=function(){if(0!==e(document).has(this.dom.container).length){var a,b;a=this.dom.value[this.dom.value.length-1];if(0!==e(this.dom.container).has(a).length){b=e(a).outerWidth(!0);a=e(a).offset().left+b;var d=e(this.dom.left).offset(),c=e(this.dom.right).offset(),h=e(this.dom["delete"]).offset(),i=0!==e(this.dom.container).has(this.dom.left).length,f=0!==e(this.dom.container).has(this.dom.right).length,j=i?d.left:f?c.left:h.left;15>j-a||i&&d.top!==h.top||f&&c.top!==h.top?
(e(this.dom.container).parent().addClass(this.classes.vertical),e(this.s.topGroup).trigger("dtsb-redrawContents")):15<j-(e(this.dom.data).offset().left+e(this.dom.data).outerWidth(!0)+e(this.dom.condition).outerWidth(!0)+b)&&(e(this.dom.container).parent().removeClass(this.classes.vertical),e(this.s.topGroup).trigger("dtsb-redrawContents"))}}};c.prototype._buildCriteria=function(){e(this.dom.data).append(this.dom.dataTitle);e(this.dom.condition).append(this.dom.conditionTitle);e(this.dom.container).append(this.dom.data).append(this.dom.condition);
for(var a=0,b=this.dom.value;a<b.length;a++){var d=b[a];e(d).append(this.dom.valueTitle);e(this.dom.container).append(d)}e(this.dom.container).append(this.dom["delete"]).append(this.dom.right);this.setListeners()};c.prototype._clearCondition=function(){e(this.dom.condition).empty();e(this.dom.conditionTitle).attr("selected",!0).attr("disabled",!0);e(this.dom.condition).prepend(this.dom.conditionTitle).prop("selectedIndex",0);this.s.conditions={};this.s.condition=void 0};c.prototype._clearValue=function(){if(void 0!==
this.s.condition){for(var a=0,b=this.dom.value;a<b.length;a++){var d=b[a];e(d).remove()}this.dom.value=[].concat(this.s.conditions[this.s.condition].init(this,c.updateListener));e(this.dom.value[0]).insertAfter(this.dom.condition).trigger("dtsb-inserted");for(d=1;d<this.dom.value.length;d++)e(this.dom.value[d]).insertAfter(this.dom.value[d-1]).trigger("dtsb-inserted")}else{a=0;for(b=this.dom.value;a<b.length;a++)d=b[a],e(d).remove();e(this.dom.valueTitle).attr("selected",!0);e(this.dom.defaultValue).append(this.dom.valueTitle).insertAfter(this.dom.condition)}this.s.value=
[]};c.prototype._getOptions=function(){return e.extend(!0,{},c.defaults,this.s.dt.settings()[0].aoColumns[this.s.dataIdx].searchBuilder)};c.prototype._populateCondition=function(){var a=[],b=Object.keys(this.s.conditions).length;if(0===b){b=e(this.dom.data).children("option:selected").val();this.s.type=this.s.dt.columns().type().toArray()[b];null===this.s.type&&(this.s.dt.draw(),this.setListeners(),this.s.type=this.s.dt.columns().type().toArray()[b]);e(this.dom.condition).attr("disabled",!1).empty().append(this.dom.conditionTitle).addClass(this.classes.italic);
e(this.dom.conditionTitle).attr("selected",!0);b=this.s.dt.settings()[0].oLanguage.sDecimal;""!==b&&this.s.type.indexOf(b)===this.s.type.length-b.length&&(-1!==this.s.type.indexOf("num-fmt")?this.s.type=this.s.type.replace(b,""):-1!==this.s.type.indexOf("num")&&(this.s.type=this.s.type.replace(b,"")));var d=void 0!==this.c.conditions[this.s.type]?this.c.conditions[this.s.type]:-1!==this.s.type.indexOf("moment")?this.c.conditions.moment:this.c.conditions.string;-1!==this.s.type.indexOf("moment")&&
(this.s.momentFormat=this.s.type.replace(/moment\-/g,""));for(var c=0,h=Object.keys(d);c<h.length;c++){var i=h[c];null!==d[i]&&(this.s.conditions[i]=d[i],b=d[i].conditionName,"function"===typeof b&&(b=b(this.s.dt,this.c.i18n)),a.push(e("<option>",{text:b,value:i}).addClass(this.classes.option).addClass(this.classes.notItalic)))}}else if(0<b){e(this.dom.condition).empty().attr("disabled",!1).addClass(this.classes.italic);d=0;for(c=Object.keys(this.s.conditions);d<c.length;d++)i=c[d],b=this.s.conditions[i].conditionName,
"function"===typeof b&&(b=b(this.s.dt,this.c.i18n)),i=e("<option>",{text:b,value:i}).addClass(this.classes.option).addClass(this.classes.notItalic),void 0!==this.s.condition&&this.s.condition===b&&(e(i).attr("selected",!0),e(this.dom.condition).removeClass(this.classes.italic)),a.push(i)}else{e(this.dom.condition).attr("disabled",!0).addClass(this.classes.italic);return}for(b=0;b<a.length;b++)i=a[b],e(this.dom.condition).append(i);e(this.dom.condition).prop("selectedIndex",0)};c.prototype._populateData=
function(){var a=this;e(this.dom.data).empty().append(this.dom.dataTitle);if(0===this.s.dataPoints.length)this.s.dt.columns().every(function(b){if(!0===a.c.columns||-1!==a.s.dt.columns(a.c.columns).indexes().toArray().indexOf(b)){for(var d=!1,c=0,g=a.s.dataPoints;c<g.length;c++)if(g[c].index===b){d=!0;break}d||(b={text:a.s.dt.settings()[0].aoColumns[b].sTitle,index:b},a.s.dataPoints.push(b),e(a.dom.data).append(e("<option>",{text:b.text,value:b.index}).addClass(a.classes.option).addClass(a.classes.notItalic)))}});
else for(var b=function(b){d.s.dt.columns().every(function(d){a.s.dt.settings()[0].aoColumns[d].sTitle===b.text&&(b.index=d)});var c=e("<option>",{text:b.text,value:b.index}).addClass(d.classes.option).addClass(d.classes.notItalic);d.s.data===b.text&&(d.s.dataIdx=b.index,e(c).attr("selected",!0),e(d.dom.data).removeClass(d.classes.italic));e(d.dom.data).append(c)},d=this,c=0,h=this.s.dataPoints;c<h.length;c++)b(h[c])};c.prototype._populateValue=function(a){var b=this,d=this.s.filled;this.s.filled=
!1;e(this.dom.defaultValue).remove();for(var g=0,h=this.dom.value;g<h.length;g++)e(h[g]).remove();g=e(this.dom.container).children();if(3<g.length)for(h=2;h<g.length-1;h++)e(g[h]).remove();void 0!==a&&this.s.dt.columns().every(function(d){b.s.dt.settings()[0].aoColumns[d].sTitle===a.data&&(b.s.dataIdx=d)});this.dom.value=[].concat(this.s.conditions[this.s.condition].init(this,c.updateListener,void 0!==a?a.value:void 0));void 0!==a&&void 0!==a.value&&(this.s.value=a.value);e(this.dom.value[0]).insertAfter(this.dom.condition).trigger("dtsb-inserted");
for(h=1;h<this.dom.value.length;h++)e(this.dom.value[h]).insertAfter(this.dom.value[h-1]).trigger("dtsb-inserted");this.s.filled=this.s.conditions[this.s.condition].isInputValid(this.dom.value,this);this.setListeners();d!==this.s.filled&&(this.s.dt.draw(),this.setListeners())};c.version="1.0.0";c.classes={button:"dtsb-button",buttonContainer:"dtsb-buttonContainer",condition:"dtsb-condition",container:"dtsb-criteria",data:"dtsb-data","delete":"dtsb-delete",dropDown:"dtsb-dropDown",greyscale:"dtsb-greyscale",
input:"dtsb-input",italic:"dtsb-italic",joiner:"dtsp-joiner",left:"dtsb-left",notItalic:"dtsb-notItalic",option:"dtsb-option",right:"dtsb-right",value:"dtsb-value",vertical:"dtsb-vertical"};c.initSelect=function(a,b,d,g){void 0===d&&(d=null);void 0===g&&(g=!1);var h=e(a.dom.data).children("option:selected").val(),i=a.s.dt.rows().indexes().toArray(),f=a.s.dt.settings()[0],j=e("<select/>").addClass(c.classes.value).addClass(c.classes.dropDown).addClass(c.classes.italic).append(a.dom.valueTitle).on("input change",
function(){e(this).removeClass(c.classes.italic);b(a,this)});a.c.greyscale&&e(j).addClass(c.classes.greyscale);for(var k=[],l=[],m=0;m<i.length;m++){var o=i[m],p=f.oApi._fnGetCellData(f,o,h,"string"===typeof a.c.orthogonal?a.c.orthogonal:a.c.orthogonal.search),p="string"===typeof p?p.replace(/[\r\n\u2028]/g," "):p,o=f.oApi._fnGetCellData(f,o,h,"string"===typeof a.c.orthogonal?a.c.orthogonal:a.c.orthogonal.display);"array"===a.s.type&&(p=!Array.isArray(p)?[p]:p=p.sort(),o=!Array.isArray(o)?[o]:o=o.sort());
var q=function(b,g){var h=e("<option>",{text:typeof g==="string"?g.replace(/(<([^>]+)>)/ig,""):g,type:Array.isArray(b)?"Array":"String",value:a.s.type.indexOf("html")!==-1&&b!==null&&typeof b==="string"?b.replace(/(<([^>]+)>)/ig,""):b}).addClass(a.classes.option).addClass(a.classes.notItalic),f=e(h).val();if(k.indexOf(f)===-1){k.push(f);l.push(h);d!==null&&Array.isArray(d[0])&&(d[0]=d[0].sort().join(","));if(d!==null&&h.val()===d[0]){h.attr("selected",true);e(j).removeClass(c.classes.italic)}}};if(g)for(var r=
0;r<p.length;r++)q(p[r],o[r]);else q(p,o)}l.sort(function(b,d){if("string"===a.s.type||"num"===a.s.type||"html"===a.s.type||"html-num"===a.s.type)return e(b).val()<e(d).val()?-1:e(b).val()<e(d).val()?1:0;if("num-fmt"===a.s.type||"html-num-fmt"===a.s.type)return+e(b).val().replace(/[^0-9.]/g,"")<+e(d).val().replace(/[^0-9.]/g,"")?-1:+e(b).val().replace(/[^0-9.]/g,"")<+e(d).val().replace(/[^0-9.]/g,"")?1:0});for(g=0;g<l.length;g++)h=l[g],e(j).append(h);return j};c.initSelectArray=function(a,b,d){void 0===
d&&(d=null);return c.initSelect(a,b,d,!0)};c.initInput=function(a,b,d){void 0===d&&(d=null);var g=e("<input/>").addClass(c.classes.value).addClass(c.classes.input).on("input",function(){b(a,this)});a.c.greyscale&&e(g).addClass(c.classes.greyscale);null!==d&&e(g).val(d[0]);return g};c.init2Input=function(a,b,d){void 0===d&&(d=null);var g=[e("<input/>").addClass(c.classes.value).addClass(c.classes.input).on("input",function(){b(a,this)}),e("<span>").addClass(a.classes.joiner).text(a.s.dt.i18n("searchBuilder.valueJoiner",
a.c.i18n.valueJoiner)),e("<input/>").addClass(c.classes.value).addClass(c.classes.input).on("input",function(){b(a,this)})];a.c.greyscale&&(e(g[0]).addClass(c.classes.greyscale),e(g[2]).addClass(c.classes.greyscale));null!==d&&(e(g[0]).val(d[0]),e(g[2]).val(d[1]));a.s.dt.off("draw");a.s.dt.one("draw",function(){e(a.s.topGroup).trigger("dtsb-redrawContents")});return g};c.initDate=function(a,b,d){void 0===d&&(d=null);var g=e("<input/>").addClass(c.classes.value).addClass(c.classes.input).dtDateTime({attachTo:"input",
format:a.s.momentFormat?a.s.momentFormat:void 0}).on("input change",function(){b(a,this)});a.c.greyscale&&e(g).addClass(c.classes.greyscale);null!==d&&e(g).val(d[0]);return g};c.initNoValue=function(a){a.s.dt.off("draw");a.s.dt.one("draw",function(){e(a.s.topGroup).trigger("dtsb-redrawContents")})};c.init2Date=function(a,b,d){void 0===d&&(d=null);var g=[e("<input/>").addClass(c.classes.value).addClass(c.classes.input).dtDateTime({attachTo:"input",format:a.s.momentFormat?a.s.momentFormat:void 0}).on("input change",
function(){b(a,this)}),e("<span>").addClass(a.classes.joiner).text(a.s.dt.i18n("searchBuilder.valueJoiner",a.c.i18n.valueJoiner)),e("<input/>").addClass(c.classes.value).addClass(c.classes.input).dtDateTime({attachTo:"input",format:a.s.momentFormat?a.s.momentFormat:void 0}).on("input change",function(){b(a,this)})];a.c.greyscale&&(e(g[0]).addClass(c.classes.greyscale),e(g[2]).addClass(c.classes.greyscale));null!==d&&0<d.length&&(e(g[0]).val(d[0]),e(g[2]).val(d[1]));a.s.dt.off("draw");a.s.dt.one("draw",
function(){e(a.s.topGroup).trigger("dtsb-redrawContents")});return g};c.isInputValidSelect=function(a){for(var b=!0,d=0;d<a.length;d++){var g=a[d];e(g).children("option:selected").length===e(g).children("option").length-e(g).children("option."+c.classes.notItalic).length&&(1===e(g).children("option:selected").length&&e(g).children("option:selected")[0]===e(g).children("option:hidden")[0])&&(b=!1)}return b};c.isInputValidInput=function(a){for(var b=!0,d=0;d<a.length;d++){var c=a[d];e(c).is("input")&&
0===e(c).val().length&&(b=!1)}return b};c.inputValueSelect=function(a){for(var b=[],d=0;d<a.length;d++){var c=a[d];if(e(c).is("select")){var h=e(c).children("option:selected").val();b.push("Array"===e(c).children("option:selected").attr("type")?h.split(",").sort():h)}}return b};c.inputValueInput=function(a){for(var b=[],d=0;d<a.length;d++){var c=a[d];e(c).is("input")&&b.push(e(c).val())}return b};c.updateListener=function(a,b){var d=a.s.conditions[a.s.condition];a.s.filled=d.isInputValid(a.dom.value,
a);a.s.value=d.inputValue(a.dom.value,a);Array.isArray(a.s.value)||(a.s.value=[a.s.value]);for(d=0;d<a.s.value.length;d++)Array.isArray(a.s.value[d])?a.s.value[d].sort():""!==a.s.dt.settings()[0].oLanguage.sDecimal&&(a.s.value[d]=a.s.value[d].replace(a.s.dt.settings()[0].oLanguage.sDecimal,"."));for(var c=null,h=null,d=0;d<a.dom.value.length;d++)b===a.dom.value[d][0]&&(c=d,void 0!==b.selectionStart&&(h=b.selectionStart));a.s.dt.draw();null!==c&&(e(a.dom.value[c]).removeClass(a.classes.italic),e(a.dom.value[c]).focus(),
null!==h&&e(a.dom.value[c])[0].setSelectionRange(h,h))};c.dateConditions={"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.equals",b.conditions.date.equals)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return a===b[0]}},"!=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.not",b.conditions.date.not)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return a!==b[0]}},"<":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.before",b.conditions.date.before)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return a<b[0]}},">":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.after",b.conditions.date.after)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return a>b[0]}},between:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.between",b.conditions.date.between)},init:c.init2Date,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return b[0]<b[1]?b[0]<=a&&a<=b[1]:b[1]<=a&&a<=b[0]}},"!between":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.notBetween",b.conditions.date.notBetween)},init:c.init2Date,
inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){a=a.replace(/(\/|\-|\,)/g,"-");return b[0]<b[1]?!(b[0]<=a&&a<=b[1]):!(b[1]<=a&&a<=b[0])}},"null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.empty",b.conditions.date.empty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.date.notEmpty",
b.conditions.date.notEmpty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return!(null===a||void 0===a||0===a.length)}}};c.momentDateConditions={"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.equals",b.conditions.moment.equals)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b,d){return o(a,d.s.momentFormat).valueOf()===o(b[0],d.s.momentFormat).valueOf()}},"!=":{conditionName:function(a,
b){return a.i18n("searchBuilder.conditions.moment.not",b.conditions.moment.not)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b,d){return o(a,d.s.momentFormat).valueOf()!==o(b[0],d.s.momentFormat).valueOf()}},"<":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.before",b.conditions.moment.before)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b,d){return o(a,d.s.momentFormat).valueOf()<
o(b[0],d.s.momentFormat).valueOf()}},">":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.after",b.conditions.moment.after)},init:c.initDate,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b,d){return o(a,d.s.momentFormat).valueOf()>o(b[0],d.s.momentFormat).valueOf()}},between:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.between",b.conditions.moment.between)},init:c.init2Date,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b,d){var a=o(a,d.s.momentFormat).valueOf(),c=o(b[0],d.s.momentFormat).valueOf(),b=o(b[1],d.s.momentFormat).valueOf();return c<b?c<=a&&a<=b:b<=a&&a<=c}},"!between":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.notBetween",b.conditions.moment.notBetween)},init:c.init2Date,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b,d){var a=o(a,d.s.momentFormat).valueOf(),c=o(b[0],d.s.momentFormat).valueOf(),b=o(b[1],d.s.momentFormat).valueOf();
return c<b?!(+c<=+a&&+a<=+b):!(+b<=+a&&+a<=+c)}},"null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.empty",b.conditions.moment.empty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.moment.notEmpty",b.conditions.moment.notEmpty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},
search:function(a){return!(null===a||void 0===a||0===a.length)}}};c.numConditions={"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.equals",b.conditions.number.equals)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){return+a===+b[0]}},"!=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.not",b.conditions.number.not)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,
search:function(a,b){return+a!==+b[0]}},"<":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.lt",b.conditions.number.lt)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+a<+b[0]}},"<=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.lte",b.conditions.number.lte)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+a<=+b[0]}},">=":{conditionName:function(a,
b){return a.i18n("searchBuilder.conditions.number.gte",b.conditions.number.gte)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+a>=+b[0]}},">":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.gt",b.conditions.number.gt)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+a>+b[0]}},between:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.between",
b.conditions.number.between)},init:c.init2Input,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+b[0]<+b[1]?+b[0]<=+a&&+a<=+b[1]:+b[1]<=+a&&+a<=+b[0]}},"!between":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.notBetween",b.conditions.number.notBetween)},init:c.init2Input,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return+b[0]<+b[1]?!(+b[0]<=+a&&+a<=+b[1]):!(+b[1]<=+a&&+a<=+b[0])}},"null":{conditionName:function(a,
b){return a.i18n("searchBuilder.conditions.number.empty",b.conditions.number.empty)},init:c.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.notEmpty",b.conditions.number.notEmpty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return!(null===a||void 0===a||0===a.length)}}};c.numFmtConditions=
{"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.equals",b.conditions.number.equals)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,"");return+d===+c}},"!=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.not",b.conditions.number.not)},
init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,"");return+d!==+c}},"<":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.lt",b.conditions.number.lt)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){var d=0===
a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,"");return+d<+c}},"<=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.lte",b.conditions.number.lte)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,
""):b[0].replace(/[^0-9.]/g,"");return+d<=+c}},">=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.gte",b.conditions.number.gte)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,"");return+d>=+c}},">":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.gt",
b.conditions.number.gt)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,"");return+d>+c}},between:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.between",b.conditions.number.between)},init:c.init2Input,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,""),e=0===b[1].indexOf("-")?"-"+b[1].replace(/[^0-9.]/g,""):b[1].replace(/[^0-9.]/g,"");return+c<+e?+c<=+d&&+d<=+e:+e<=+d&&+d<=+c}},"!between":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.notBetween",b.conditions.number.notBetween)},init:c.init2Input,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b){var d=0===a.indexOf("-")?"-"+a.replace(/[^0-9.]/g,""):a.replace(/[^0-9.]/g,""),c=0===b[0].indexOf("-")?"-"+b[0].replace(/[^0-9.]/g,""):b[0].replace(/[^0-9.]/g,""),e=0===b[1].indexOf("-")?"-"+b[1].replace(/[^0-9.]/g,""):b[1].replace(/[^0-9.]/g,"");return+c<+e?!(+c<=+d&&+d<=+e):!(+e<=+d&&+d<=+c)}},"null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.empty",b.conditions.number.empty)},init:c.initNoValue,inputValue:function(){},isInputValid:function(){return!0},
search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.number.notEmpty",b.conditions.number.notEmpty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return!(null===a||void 0===a||0===a.length)}}};c.stringConditions={"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.equals",b.conditions.string.equals)},init:c.initSelect,inputValue:c.inputValueSelect,
isInputValid:c.isInputValidSelect,search:function(a,b){return a===b[0]}},"!=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.not",b.conditions.string.not)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidInput,search:function(a,b){return a!==b[0]}},starts:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.startsWith",b.conditions.string.startsWith)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,
search:function(a,b){return 0===a.toLowerCase().indexOf(b[0].toLowerCase())}},contains:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.contains",b.conditions.string.contains)},init:c.initInput,inputValue:c.inputValueInput,isInputValid:c.isInputValidInput,search:function(a,b){return-1!==a.toLowerCase().indexOf(b[0].toLowerCase())}},ends:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.endsWith",b.conditions.string.endsWith)},init:c.initInput,inputValue:c.inputValueInput,
isInputValid:c.isInputValidInput,search:function(a,b){return a.toLowerCase().endsWith(b[0].toLowerCase())}},"null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.empty",b.conditions.string.empty)},init:c.initNoValue,inputValue:function(){},isInputValid:function(){return!0},search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.string.notEmpty",b.conditions.string.notEmpty)},isInputValid:function(){return!0},
init:c.initNoValue,inputValue:function(){},search:function(a){return!(null===a||void 0===a||0===a.length)}}};c.arrayConditions={contains:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.array.contains",b.conditions.array.contains)},init:c.initSelectArray,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){return-1!==a.indexOf(b[0])}},without:{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.array.without",b.conditions.array.without)},
init:c.initSelectArray,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){return-1===a.indexOf(b[0])}},"=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.array.equals",b.conditions.array.equals)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){if(a.length===b[0].length){for(var d=0;d<a.length;d++)if(a[d]!==b[0][d])return!1;return!0}return!1}},"!=":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.array.not",
b.conditions.array.not)},init:c.initSelect,inputValue:c.inputValueSelect,isInputValid:c.isInputValidSelect,search:function(a,b){if(a.length===b[0].length){for(var d=0;d<a.length;d++)if(a[d]!==b[0][d])return!0;return!1}return!0}},"null":{conditionName:function(a,b){return a.i18n("searchBuilder.conditions.array.empty",b.conditions.array.empty)},init:c.initNoValue,isInputValid:function(){return!0},inputValue:function(){},search:function(a){return null===a||void 0===a||0===a.length}},"!null":{conditionName:function(a,
b){return a.i18n("searchBuilder.conditions.array.notEmpty",b.conditions.array.notEmpty)},isInputValid:function(){return!0},init:c.initNoValue,inputValue:function(){},search:function(a){return null!==a&&void 0!==a&&0!==a.length}}};c.defaults={columns:!0,conditions:{array:c.arrayConditions,date:c.dateConditions,html:c.stringConditions,"html-num":c.numConditions,"html-num-fmt":c.numFmtConditions,moment:c.momentDateConditions,num:c.numConditions,"num-fmt":c.numFmtConditions,string:c.stringConditions},
depthLimit:!1,filterChanged:void 0,greyscale:!1,i18n:{add:"Add Condition",button:{"0":"Search Builder",_:"Search Builder (%d)"},clearAll:"Clear All",condition:"Condition",data:"Data",deleteTitle:"Delete filtering rule",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",rightTitle:"Indent criteria",title:{"0":"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1};l=c;var f,u,r,k=function(a,
b,d,c,e,i){void 0===c&&(c=0);void 0===e&&(e=!1);void 0===i&&(i=1);if(!u||!u.versionCheck||!u.versionCheck("1.10.0"))throw Error("SearchBuilder requires DataTables 1.10 or newer");this.classes=f.extend(!0,{},k.classes);this.c=f.extend(!0,{},k.defaults,b);this.s={criteria:[],depth:i,dt:a,index:c,isChild:e,logic:void 0,opts:b,toDrop:void 0,topGroup:d};this.dom={add:f("<button/>").addClass(this.classes.add).addClass(this.classes.button).attr("type","button"),clear:f("<button>&times</button>").addClass(this.classes.button).addClass(this.classes.clearGroup).attr("type",
"button"),container:f("<div/>").addClass(this.classes.group),logic:f("<button/>").addClass(this.classes.logic).addClass(this.classes.button).attr("type","button"),logicContainer:f("<div/>").addClass(this.classes.logicContainer)};void 0===this.s.topGroup&&(this.s.topGroup=this.dom.container);this._setup();return this};k.prototype.destroy=function(){f(this.dom.add).off(".dtsb");f(this.dom.logic).off(".dtsb");f(this.dom.container).trigger("dtsb-destroy").remove();this.s.criteria=[]};k.prototype.getDetails=
function(){if(0===this.s.criteria.length)return{};for(var a={criteria:[],logic:this.s.logic},b=0,d=this.s.criteria;b<d.length;b++)a.criteria.push(d[b].criteria.getDetails());return a};k.prototype.getNode=function(){return this.dom.container};k.prototype.rebuild=function(a){if(!(void 0===a.criteria||null===a.criteria||0===a.criteria.length)){this.s.logic=a.logic;f(this.dom.logic).text("OR"===this.s.logic?this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr):this.s.dt.i18n("searchBuilder.logicAnd",
this.c.i18n.logicAnd));for(var b=0,d=a.criteria;b<d.length;b++)a=d[b],void 0!==a.logic?this._addPrevGroup(a):void 0===a.logic&&this._addPrevCriteria(a);b=0;for(d=this.s.criteria;b<d.length;b++)a=d[b],a.criteria instanceof l&&(a.criteria.updateArrows(1<this.s.criteria.length,!1),this._setCriteriaListeners(a.criteria))}};k.prototype.redrawContents=function(){f(this.dom.container).empty().append(this.dom.logicContainer).append(this.dom.add);this.s.criteria.sort(function(a,b){return a.criteria.s.index<
b.criteria.s.index?-1:a.criteria.s.index>b.criteria.s.index?1:0});this.setListeners();for(var a=0;a<this.s.criteria.length;a++){var b=this.s.criteria[a].criteria;b instanceof l?(this.s.criteria[a].index=a,this.s.criteria[a].criteria.s.index=a,f(this.s.criteria[a].criteria.dom.container).insertBefore(this.dom.add),this._setCriteriaListeners(b),this.s.criteria[a].criteria.rebuild(this.s.criteria[a].criteria.getDetails())):b instanceof k&&0<b.s.criteria.length?(this.s.criteria[a].index=a,this.s.criteria[a].criteria.s.index=
a,f(this.s.criteria[a].criteria.dom.container).insertBefore(this.dom.add),b.redrawContents(),this._setGroupListeners(b)):(this.s.criteria.splice(a,1),a--)}this.setupLogic()};k.prototype.search=function(a,b){return"AND"===this.s.logic?this._andSearch(a,b):"OR"===this.s.logic?this._orSearch(a,b):!0};k.prototype.setupLogic=function(){f(this.dom.logicContainer).remove();f(this.dom.clear).remove();if(1>this.s.criteria.length)this.s.isChild||(f(this.dom.container).trigger("dtsb-destroy"),f(this.dom.container).css("margin-left",
0));else{var a=f(this.dom.container).height()-2;f(this.dom.clear).height("0px");f(this.dom.logicContainer).append(this.dom.clear).width(a);f(this.dom.container).prepend(this.dom.logicContainer);this._setLogicListener();f(this.dom.container).css("margin-left",f(this.dom.logicContainer).outerHeight(!0));var a=f(this.dom.logicContainer).offset(),b=a.left,d=f(this.dom.container).offset().left,b=b-(b-d)-f(this.dom.logicContainer).outerHeight(!0);f(this.dom.logicContainer).offset({left:b});b=f(this.dom.logicContainer).next();
a=a.top;b=f(b).offset().top;a-=a-b;f(this.dom.logicContainer).offset({top:a});f(this.dom.clear).outerHeight(f(this.dom.logicContainer).height());this._setClearListener()}};k.prototype.setListeners=function(){var a=this;f(this.dom.add).unbind("click");f(this.dom.add).on("click",function(){a.s.isChild||f(a.dom.container).prepend(a.dom.logicContainer);a.addCriteria();f(a.dom.container).trigger("dtsb-add");a.s.dt.state.save();return!1});for(var b=0,d=this.s.criteria;b<d.length;b++)d[b].criteria.setListeners();
this._setClearListener();this._setLogicListener()};k.prototype.addCriteria=function(a,b){void 0===a&&(a=null);void 0===b&&(b=!0);var d=null===a?this.s.criteria.length:a.s.index,c=new l(this.s.dt,this.s.opts,this.s.topGroup,d,this.s.depth);null!==a&&(c.c=a.c,c.s=a.s,c.s.depth=this.s.depth,c.classes=a.classes);c.populate();for(var e=!1,i=0;i<this.s.criteria.length;i++)0===i&&this.s.criteria[i].criteria.s.index>c.s.index?(f(c.getNode()).insertBefore(this.s.criteria[i].criteria.dom.container),e=!0):i<
this.s.criteria.length-1&&(this.s.criteria[i].criteria.s.index<c.s.index&&this.s.criteria[i+1].criteria.s.index>c.s.index)&&(f(c.getNode()).insertAfter(this.s.criteria[i].criteria.dom.container),e=!0);e||f(c.getNode()).insertBefore(this.dom.add);this.s.criteria.push({criteria:c,index:d});this.s.criteria=this.s.criteria.sort(function(a,b){return a.criteria.s.index-b.criteria.s.index});d=0;for(e=this.s.criteria;d<e.length;d++)i=e[d],i.criteria instanceof l&&i.criteria.updateArrows(1<this.s.criteria.length,
b);this._setCriteriaListeners(c);c.setListeners();this.setupLogic()};k.prototype.checkFilled=function(){for(var a=0,b=this.s.criteria;a<b.length;a++){var d=b[a];if(d.criteria instanceof l&&d.criteria.s.filled||d.criteria instanceof k&&d.criteria.checkFilled())return!0}return!1};k.prototype.count=function(){for(var a=0,b=0,d=this.s.criteria;b<d.length;b++){var c=d[b];c.criteria instanceof k?a+=c.criteria.count():a++}return a};k.prototype._addPrevGroup=function(a){var b=this.s.criteria.length,d=new k(this.s.dt,
this.c,this.s.topGroup,b,!0,this.s.depth+1);this.s.criteria.push({criteria:d,index:b,logic:d.s.logic});d.rebuild(a);this.s.criteria[b].criteria=d;f(this.s.topGroup).trigger("dtsb-redrawContents");this._setGroupListeners(d)};k.prototype._addPrevCriteria=function(a){var b=this.s.criteria.length,d=new l(this.s.dt,this.s.opts,this.s.topGroup,b,this.s.depth);d.populate();this.s.criteria.push({criteria:d,index:b});d.rebuild(a);this.s.criteria[b].criteria=d;f(this.s.topGroup).trigger("dtsb-redrawContents")};
k.prototype._andSearch=function(a,b){if(0===this.s.criteria.length)return!0;for(var d=0,c=this.s.criteria;d<c.length;d++){var e=c[d];if((!(e.criteria instanceof l)||e.criteria.s.filled)&&!e.criteria.search(a,b))return!1}return!0};k.prototype._orSearch=function(a,b){if(0===this.s.criteria.length)return!0;for(var d=!1,c=0,e=this.s.criteria;c<e.length;c++){var f=e[c];if(f.criteria instanceof l&&f.criteria.s.filled){if(d=!0,f.criteria.search(a,b))return!0}else if(f.criteria instanceof k&&f.criteria.checkFilled()&&
(d=!0,f.criteria.search(a,b)))return!0}return!d};k.prototype._removeCriteria=function(a,b){void 0===b&&(b=!1);if(1>=this.s.criteria.length&&this.s.isChild)this.destroy();else{for(var d=void 0,c=0;c<this.s.criteria.length;c++)if(this.s.criteria[c].index===a.s.index&&(!b||this.s.criteria[c].criteria instanceof k))d=c;void 0!==d&&this.s.criteria.splice(d,1);for(c=0;c<this.s.criteria.length;c++)this.s.criteria[c].index=c,this.s.criteria[c].criteria.s.index=c}};k.prototype._setCriteriaListeners=function(a){var b=
this;f(a.dom["delete"]).unbind("click").on("click",function(){b._removeCriteria(a);f(a.dom.container).remove();for(var c=0,e=b.s.criteria;c<e.length;c++){var h=e[c];h.criteria instanceof l&&h.criteria.updateArrows(1<b.s.criteria.length)}a.destroy();b.s.dt.draw();f(b.s.topGroup).trigger("dtsb-redrawContents");f(b.s.topGroup).trigger("dtsb-updateTitle");return!1});f(a.dom.right).unbind("click").on("click",function(){var c=a.s.index,e=new k(b.s.dt,b.s.opts,b.s.topGroup,a.s.index,!0,b.s.depth+1);e.addCriteria(a);
b.s.criteria[c].criteria=e;b.s.criteria[c].logic="AND";f(b.s.topGroup).trigger("dtsb-redrawContents");b._setGroupListeners(e);return!1});f(a.dom.left).unbind("click").on("click",function(){b.s.toDrop=new l(b.s.dt,b.s.opts,b.s.topGroup,a.s.index);b.s.toDrop.s=a.s;b.s.toDrop.c=a.c;b.s.toDrop.classes=a.classes;b.s.toDrop.populate();var c=b.s.toDrop.s.index;f(b.dom.container).trigger("dtsb-dropCriteria");a.s.index=c;b._removeCriteria(a);f(b.s.topGroup).trigger("dtsb-redrawContents");b.s.dt.draw();return!1})};
k.prototype._setClearListener=function(){var a=this;f(this.dom.clear).unbind("click").on("click",function(){if(!a.s.isChild)return f(a.dom.container).trigger("dtsb-clearContents"),!1;a.destroy();f(a.s.topGroup).trigger("dtsb-updateTitle");f(a.s.topGroup).trigger("dtsb-redrawContents");return!1})};k.prototype._setGroupListeners=function(a){var b=this;f(a.dom.add).unbind("click").on("click",function(){b.setupLogic();f(b.dom.container).trigger("dtsb-add");return!1});f(a.dom.container).unbind("dtsb-add").on("dtsb-add",
function(){b.setupLogic();f(b.dom.container).trigger("dtsb-add");return!1});f(a.dom.container).unbind("dtsb-destroy").on("dtsb-destroy",function(){b._removeCriteria(a,!0);f(a.dom.container).remove();b.setupLogic();return!1});f(a.dom.container).unbind("dtsb-dropCriteria").on("dtsb-dropCriteria",function(){var c=a.s.toDrop;c.s.index=a.s.index;c.updateArrows(1<b.s.criteria.length,!1);b.addCriteria(c,!1);return!1});a.setListeners()};k.prototype._setup=function(){this.setListeners();f(this.dom.add).text(this.s.dt.i18n("searchBuilder.add",
this.c.i18n.add));f(this.dom.logic).text("OR"===this.c.logic?this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr):this.s.dt.i18n("searchBuilder.logicAnd",this.c.i18n.logicAnd));this.s.logic="OR"===this.c.logic?"OR":"AND";this.c.greyscale&&f(this.dom.logic).addClass(this.classes.greyscale);f(this.dom.logicContainer).append(this.dom.logic).append(this.dom.clear);this.s.isChild&&f(this.dom.container).append(this.dom.logicContainer);f(this.dom.container).append(this.dom.add)};k.prototype._setLogicListener=
function(){var a=this;f(this.dom.logic).unbind("click").on("click",function(){a._toggleLogic();a.s.dt.draw();for(var b=0,c=a.s.criteria;b<c.length;b++)c[b].criteria.setListeners()})};k.prototype._toggleLogic=function(){"OR"===this.s.logic?(this.s.logic="AND",f(this.dom.logic).text(this.s.dt.i18n("searchBuilder.logicAnd",this.c.i18n.logicAnd))):"AND"===this.s.logic&&(this.s.logic="OR",f(this.dom.logic).text(this.s.dt.i18n("searchBuilder.logicOr",this.c.i18n.logicOr)))};k.version="1.0.0";k.classes=
{add:"dtsb-add",button:"dtsb-button",clearGroup:"dtsb-clearGroup",greyscale:"dtsb-greyscale",group:"dtsb-group",inputButton:"dtsb-iptbtn",logic:"dtsb-logic",logicContainer:"dtsb-logicContainer"};k.defaults={columns:!0,conditions:{date:l.dateConditions,html:l.stringConditions,"html-num":l.numConditions,"html-num-fmt":l.numFmtConditions,moment:l.momentDateConditions,num:l.numConditions,"num-fmt":l.numFmtConditions,string:l.stringConditions},depthLimit:!1,filterChanged:void 0,greyscale:!1,i18n:{add:"Add Condition",
button:{"0":"Search Builder",_:"Search Builder (%d)"},clearAll:"Clear All",condition:"Condition",data:"Data",deleteTitle:"Delete filtering rule",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",rightTitle:"Indent criteria",title:{"0":"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1};r=k;var j,s,v,m=function(a,b){var c=this;if(!s||!s.versionCheck||!s.versionCheck("1.10.0"))throw Error("SearchBuilder requires DataTables 1.10 or newer");
var e=new s.Api(a);this.classes=j.extend(!0,{},m.classes);this.c=j.extend(!0,{},m.defaults,b);this.dom={clearAll:j('<button type="button">'+e.i18n("searchBuilder.clearAll",this.c.i18n.clearAll)+"</button>").addClass(this.classes.clearAll).addClass(this.classes.button).attr("type","button"),container:j("<div/>").addClass(this.classes.container),title:j("<div/>").addClass(this.classes.title),titleRow:j("<div/>").addClass(this.classes.titleRow),topGroup:void 0};this.s={dt:e,opts:b,search:void 0,topGroup:void 0};
if(void 0===e.settings()[0]._searchBuilder){e.settings()[0]._searchBuilder=this;if(this.s.dt.settings()[0]._bInitComplete)this._setUp();else e.one("init.dt",function(){c._setUp()});return this}};m.prototype.getDetails=function(){return this.s.topGroup.getDetails()};m.prototype.getNode=function(){return this.dom.container};m.prototype.rebuild=function(a){j(this.dom.clearAll).click();if(void 0===a||null===a)return this;this.s.topGroup.rebuild(a);this.s.dt.draw();this.s.topGroup.setListeners();return this};
m.prototype._applyPreDefDefaults=function(a){var b=this;void 0!==a.criteria&&void 0===a.logic&&(a.logic="AND");for(var c=function(a){a.criteria!==void 0?a=e._applyPreDefDefaults(a):e.s.dt.columns().every(function(c){if(b.s.dt.settings()[0].aoColumns[c].sTitle===a.data)a.dataIdx=c})},e=this,f=0,i=a.criteria;f<i.length;f++)c(i[f]);return a};m.prototype._setUp=function(a){var b=this;void 0===a&&(a=!0);this.s.topGroup=new r(this.s.dt,this.c,void 0);this._setClearListener();this.s.dt.on("stateSaveParams",
function(a,c,e){e.searchBuilder=b.getDetails();e.page=b.s.dt.page()});this._build();a&&(a=this.s.dt.state.loaded(),null!==a&&void 0!==a.searchBuilder?(this.s.topGroup.rebuild(a.searchBuilder),j(this.s.topGroup.dom.container).trigger("dtsb-redrawContents"),this.s.dt.page(a.page).draw("page"),this.s.topGroup.setListeners()):!1!==this.c.preDefined&&(this.c.preDefined=this._applyPreDefDefaults(this.c.preDefined),this.rebuild(this.c.preDefined)));this._setEmptyListener();this.s.dt.state.save()};m.prototype._updateTitle=
function(a){j(this.dom.title).text(this.s.dt.i18n("searchBuilder.title",this.c.i18n.title,a))};m.prototype._build=function(){var a=this;j(this.dom.clearAll).remove();j(this.dom.container).empty();var b=this.s.topGroup.count();this._updateTitle(b);j(this.dom.titleRow).append(this.dom.title);j(this.dom.container).append(this.dom.titleRow);this.dom.topGroup=this.s.topGroup.getNode();j(this.dom.container).append(this.dom.topGroup);this._setRedrawListener();var c=this.s.dt.table(0).node();-1===j.fn.dataTable.ext.search.indexOf(this.s.search)&&
(this.s.search=function(b,e,f){return b.nTable!==c?!0:a.s.topGroup.search(e,f)},j.fn.dataTable.ext.search.push(this.s.search));j.fn.DataTable.Api.registerPlural("columns().type()","column().type()",function(){return this.iterator("column",function(a,b){return a.aoColumns[b].sType},1)});this.s.dt.on("destroy.dt",function(){j(a.dom.container).remove();j(a.dom.clearAll).remove();for(var b=j.fn.dataTable.ext.search.indexOf(a.s.search);b!==-1;){j.fn.dataTable.ext.search.splice(b,1);b=j.fn.dataTable.ext.search.indexOf(a.s.search)}})};
m.prototype._checkClear=function(){0<this.s.topGroup.s.criteria.length?(j(this.dom.clearAll).insertAfter(this.dom.title),this._setClearListener()):j(this.dom.clearAll).remove()};m.prototype._filterChanged=function(a){var b=this.c.filterChanged;"function"===typeof b&&b(a,this.s.dt.i18n("searchBuilder.button",this.c.i18n.button,a))};m.prototype._setClearListener=function(){var a=this;j(this.dom.clearAll).unbind("click");j(this.dom.clearAll).on("click",function(){a.s.topGroup=new r(a.s.dt,a.c,void 0);
a._build();a.s.dt.draw();a.s.topGroup.setListeners();j(a.dom.clearAll).remove();a._setEmptyListener();a._filterChanged(0);return!1})};m.prototype._setRedrawListener=function(){var a=this;j(this.s.topGroup.dom.container).unbind("dtsb-redrawContents");j(this.s.topGroup.dom.container).on("dtsb-redrawContents",function(){a._checkClear();a.s.topGroup.redrawContents();a.s.topGroup.setupLogic();a._setEmptyListener();var b=a.s.topGroup.count();a._updateTitle(b);a._filterChanged(b);a.s.dt.state.save()});j(this.s.topGroup.dom.container).unbind("dtsb-clearContents");
j(this.s.topGroup.dom.container).on("dtsb-clearContents",function(){a._setUp(!1);a._filterChanged(0);a.s.dt.draw()});j(this.s.topGroup.dom.container).on("dtsb-updateTitle",function(){var b=a.s.topGroup.count();a._updateTitle(b);a._filterChanged(b)})};m.prototype._setEmptyListener=function(){var a=this;j(this.s.topGroup.dom.add).on("click",function(){a._checkClear()});j(this.s.topGroup.dom.container).on("dtsb-destroy",function(){j(a.dom.clearAll).remove()})};m.version="1.0.1";m.classes={button:"dtsb-button",
clearAll:"dtsb-clearAll",container:"dtsb-searchBuilder",inputButton:"dtsb-iptbtn",title:"dtsb-title",titleRow:"dtsb-titleRow"};m.defaults={columns:!0,conditions:{date:l.dateConditions,html:l.stringConditions,"html-num":l.numConditions,"html-num-fmt":l.numFmtConditions,moment:l.momentDateConditions,num:l.numConditions,"num-fmt":l.numFmtConditions,string:l.stringConditions},depthLimit:!1,filterChanged:void 0,greyscale:!1,i18n:{add:"Add Condition",button:{"0":"Search Builder",_:"Search Builder (%d)"},
clearAll:"Clear All",condition:"Condition",conditions:{array:{contains:"Contains",empty:"Empty",equals:"Equals",not:"Not",notEmpty:"Not Empty",without:"Without"},date:{after:"After",before:"Before",between:"Between",empty:"Empty",equals:"Equals",not:"Not",notBetween:"Not Between",notEmpty:"Not Empty"},moment:{after:"After",before:"Before",between:"Between",empty:"Empty",equals:"Equals",not:"Not",notBetween:"Not Between",notEmpty:"Not Empty"},number:{between:"Between",empty:"Empty",equals:"Equals",
gt:"Greater Than",gte:"Greater Than Equal To",lt:"Less Than",lte:"Less Than Equal To",not:"Not",notBetween:"Not Between",notEmpty:"Not Empty"},string:{contains:"Contains",empty:"Empty",endsWith:"Ends With",equals:"Equals",not:"Not",notEmpty:"Not Empty",startsWith:"Starts With"}},data:"Data",deleteTitle:"Delete filtering rule",leftTitle:"Outdent criteria",logicAnd:"And",logicOr:"Or",rightTitle:"Indent criteria",title:{"0":"Custom Search Builder",_:"Custom Search Builder (%d)"},value:"Value",valueJoiner:"and"},
logic:"AND",orthogonal:{display:"display",search:"filter"},preDefined:!1};v=m;var y=function(a,b,c){function g(a,b){var c=new h.Api(a),d=b?b:c.init().searchBuilder||h.defaults.searchBuilder;return(new v(c,d)).getNode()}j=a;s=a.fn.DataTable;f=a;u=a.fn.dataTable;e=a;var h=q=a.fn.dataTable;a.fn.dataTable.SearchBuilder=v;a.fn.DataTable.SearchBuilder=v;a.fn.dataTable.Group=r;a.fn.DataTable.Group=r;a.fn.dataTable.Criteria=l;a.fn.DataTable.Criteria=l;b=a.fn.dataTable.Api.register;a.fn.dataTable.ext.searchBuilder=
{conditions:{}};a.fn.dataTable.ext.buttons.searchBuilder={action:function(a,b,c,d){a.stopPropagation();this.popover(d._searchBuilder.getNode(),{align:"dt-container"})},config:{},init:function(b,c,d){var e=new a.fn.dataTable.SearchBuilder(b,a.extend({filterChanged:function(a,d){b.button(c).text(d)}},d.config));b.button(c).text(d.text||b.i18n("searchBuilder.button",e.c.i18n.button,0));d._searchBuilder=e},text:null};b("searchBuilder.getDetails()",function(){return this.context[0]._searchBuilder.getDetails()});
b("searchBuilder.rebuild()",function(a){this.context[0]._searchBuilder.rebuild(a);return this});b("searchBuilder.container()",function(){return this.context[0]._searchBuilder.getNode()});a(c).on("preInit.dt.dtsp",function(a,b){if("dt"===a.namespace&&(b.oInit.searchBuilder||h.defaults.searchBuilder))b._searchBuilder||g(b)});h.ext.feature.push({cFeature:"Q",fnInit:g});h.ext.features&&h.ext.features.register("searchBuilder",g)};"function"===typeof define&&define.amd?define(["jquery","datatables.net"],
function(a){return y(a,window,document)}):"object"===typeof exports?module.exports=function(a,b){a||(a=window);if(!b||!b.fn.dataTable)b=require("datatables.net")(a,b).$;return y(b,a,a.document)}:y(window.jQuery,window,document)})();
