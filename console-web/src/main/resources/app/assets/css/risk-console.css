html, body {
    font-family: PingFang SC, Verdana, Helvetica Neue, Microsoft Yahei, Hiragino Sans GB, Microsoft Sans Serif, WenQuanYi Micro Hei, sans-serif;
    /*font-family: "微软雅黑","黑体" !important;*/
    font-weight: 400;
    font-size: 14px;
    background-color: #f1f1f1;
}

.content-wrapper, .right-side {
    background-color: #f1f1f1;
}

.w5c-form input:focus:focus.error,
.w5c-form textarea:focus:focus.error,
.w5c-form select:focus:focus.error {
    border-color: #b94a48;
    -webkit-box-shadow: 0 0 6px #f8b9b7;
    -moz-box-shadow: 0 0 6px #f8b9b7;
    box-shadow: 0 0 6px #f8b9b7;
}

.w5c-form input.error,
.w5c-form textarea.error,
.w5c-form select.error {
    border-color: #b94a48;
    -webkit-box-shadow: 0 0 6px #f8b9b7;
    -moz-box-shadow: 0 0 6px #f8b9b7;
    box-shadow: 0 0 6px #f8b9b7;
}

.w5c-form .w5c-error {
    color: #b94a48;
    display: block;
    margin-top: 5px;
}

form .col-sm-1, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9 {
    padding-left: 0;
}

.box-search {
    padding: 10px 10px 10px 10px;
}

.box-search:before, .box-search:after {
    display: table;
    content: " ";
    clear: both;
}

.ace_editor {
    min-height: 200px;
}

.logHover:hover {
    background: #EAE9E9;
}

.mybigModal > div {
    width: 90%;
}

.break-word {
    -ms-word-break: break-all;
    word-break: break-all;
    -ms-word-wrap: break-word;
    word-wrap: break-word;
}

.step-div {
    border: 1px solid #b2b2b2;
    background-image: -webkit-linear-gradient(#fff, #e5e5e5);
    margin: 0 0 0 0;
    padding: 0 0 0 10px;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;

}

ul {
    margin: 0;
    padding: 0;
}

li {
    margin: 0;
    padding: 0;
}

#myTab li {
    /*width:25%;*/
    float: left;
    height: 40px;
    list-style: none;
    margin: 0;
    padding: 0;
}

#myTab li img {
    float: left;
    height: 40px;
}

#myTab li a {
    float: left;
    color: white;
    text-align: center;
    position: relative;
    display: block;
    padding: 10px 15px;
}

.blue {
    background: #0f9af2;
}

.gray {
    background: #dfdfdf;
}

.tabPaneUl {
    width: 700px;
    margin: 0 auto;
    list-style: none;
}

.tabPaneUl li {
    height: 40px;
    line-height: 40px;
}

.tableTitle {
    width: 100px;
    font-weight: bold;
    text-align: right;
    font-size: 12px;
}

.contentHistory {
    padding: 0px 45px;
    color: #999;
    margin-bottom: 30px;
}

.contentHistory .log {
    padding: 0px 5px;
    margin: 10px 0px;
    font-size: 12px;
}

.ideal-tabs-tab-counter {
    display: inline-block;
    right: 9.5px;
    top: 50%;
    margin-top: -9.5px;
    height: 19px;
    width: 19px;
    line-height: 19px;
    text-align: center;
    font-size: 10.5px;
    font-style: normal;
    border-radius: 10em;
    margin-right: 1em;
}

.ideal-tabs-tab-counter-unactive {
    border: 1px solid #FFFFFF;
    color: #FFFFFF;
}

.ideal-tabs-tab-counter-active {
    border: 1px solid #d14848;
    color: #d14848;
}

.no-top {
    border-top: none;
}

.loading-css {
    position: absolute;
    top: 25%;
    left: 50%;
    margin-left: -15px;
    margin-top: -15px;
    color: #000;
    font-size: 30px;
}

.mid-label {
    padding: 0.6em 0.7em !important;
    display: inline-block !important;
    margin: 3px !important;
}

table tr td {
    word-break: break-all !important;
}

form span.required{
    color:red;
    font-size: smaller;
}

form span.tip{
    font-size: smaller;
    color: #666;
}

/*解决日期控件出来，黑色背景条问题,策略给予足够高度*/
.content-main{
    min-height:620px;
}
.tagtree {
    border: 1px solid #d7d7d7;
    background-color: #fcfcfc;
    padding: 10px;
}
.tagtree ul{
    margin-left:-20px;
}
.tagtree li {
    margin:0;
    padding:10px 5px 0 5px;
    position:relative;
    list-style-type:none;
}
.tagtree li::before, .tagtree li::after {
    content:'';
    left:-20px;
    position:absolute;
    right:auto
}
.tagtree li::before {
    border-left:1px dashed #795548;
    bottom:50px;
    height:100%;
    top:-4;
    width:1px
}
.tagtree li::after {
    border-top:1px dashed #795548;
    height:20px;
    top:25px;
    width:25px
}
.tagtree li span {
    position: relative;
    z-index: 1;
    background-color: #1ABC9C;
    height: 28px;
    display: inline-block;
    padding: 0 8 0 5;
    font-size: 14px;
    line-height: 28px;
    color: white;
}
.tagtree li span:before {
    content: "";
    position: absolute;
    z-index: -1;
    border-top: 14px solid transparent;
    border-right: 10px solid #1ABC9C;
    border-bottom: 14px solid transparent;
    left: -10px;
}
.tagtree li span {
    cursor:pointer
}
.tagtree>ul>li::before, .tagtree>ul>li::after {
    border:0
}
.tagtree li:last-child::before {
    height:30px
}
.tagtree li span:hover
{
    background:#16A085;
}
.tagtree li span:hover::before
{
    border-right: 10px solid #16A085;
}
.node-count {
    display: inline-block;
    font-size: 10px;
    margin-left: 3px;
    height: 16px;
    width: 16px;
    background-color: #8bc34a;
    text-align: center;
    line-height: 16px;
    color: white;
    border-radius: 8px;
}
.tagtree li span i
{
    margin: 0 1 0 6;
    color: #ECF0F1;
    font-size: 18px;
}
.tagtree li span i:hover
{
    color: #FFEB3B;
}
span .i-check
{
    display: inline-block;
    color: #FFEB3B !important;
}
