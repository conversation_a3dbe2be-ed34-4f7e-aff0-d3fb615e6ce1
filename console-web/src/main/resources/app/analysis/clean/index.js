(function () {
    var app = angular.module(app_name);

    app.controller('cleanCtrl', function ($scope, $state, $uibModal, Rest, modal, AuthService) {
        $scope.dataClean = {};
        if(!$scope.dataClean.createTime){
            // 填充 时间范围搜索框
            var endTime = new Date();
            endTime.setHours(23);
            endTime.setMinutes(59);
            endTime.setSeconds(59);
            endTime.setMilliseconds(999);

            var startTime = new Date(endTime.getTime() - 7*1000 * 60 * 60 * 24 * 1+1);

            $scope.dataClean.createTime = startTime;
            $scope.dataClean.updateTime = endTime;
        }else{
            var endTime = new Date($scope.dataClean.createTime);  // js 日期字符串 转 Date日期类型
            endTime.setHours(23);
            endTime.setMinutes(59);
            endTime.setSeconds(59);
            endTime.setMilliseconds(999);

            var startTime = new Date(endTime.getTime() - 1000 * 60 * 60 * 24 * 1+1);

            var endTime1 = new Date();
            endTime1.setHours(23);
            endTime1.setMinutes(59);
            endTime1.setSeconds(59);
            endTime1.setMilliseconds(999);

           $scope.dataClean.createTime = startTime;
           $scope.dataClean.updateTime = endTime;
        }

        $scope.changeDate = function(){
            if ($scope.dataClean.updateTime) {
                var d = new Date($scope.dataClean.updateTime)
                d.setHours(0),d.setMinutes(0),d.setSeconds(0);
                if (d.getTime()==new Date($scope.dataClean.updateTime).getTime()){
                    $scope.dataClean.updateTime = new Date($scope.dataClean.updateTime).getTime()+ 1000 * 60 * 60 * 24 - 1;
                }
            }
        };

        function go(page){
            if(!$scope.dataClean.createTime){
                 modal.confirm('请输入查找的时间范围开始时间!', function () {
                    return;
                 });

                 return;
            }

            if(!$scope.dataClean.updateTime){
                 modal.confirm('请输入查找的时间范围结束时间!', function () {
                    return;
                 });

                 return;
            }

            if($scope.dataClean.createTime>$scope.dataClean.updateTime){
                modal.confirm('查找的时间范围开始时间必须小于结束时间!', function () {
                    return;
                });

                return;
            }

            var startTime = new Date($scope.dataClean.createTime).getTime();
            var endTime = new Date($scope.dataClean.updateTime).getTime();
            if(endTime-startTime>30 * 1000 * 60 * 60 * 24){
                modal.confirm('查找的时间范围时间跨度不能大于30天！',function(){
                    return;
                });

                return;
            }

            $scope.page = page || $scope.page || 1;

            let objType = $scope.dataClean['objType'];
            if (!objType) {
                Messenger().post({type: 'error', message: '请选择目标ID类型!'});
                return;
            }

            let objId = $scope.dataClean['objId'];
            if ("deviceId" === objType) {
                Rest.all('/detail/cleanLog/deviceSearch?p=' + $scope.page).post($scope.dataClean).then(function (response) {
                    $scope.dataCleanList = response.list;
                    $scope.total = response.total;
                });
                return;
            }

            // 默认按UID逻辑查询
            let reg = /^[0-9]*$/;
            if (objId && !reg.test(objId)) {
                Messenger().post({type: 'error', message: '输入的用户uid不合法!'});
                return;
            }

            // copy objId to uid or deviceId
            let param = angular.copy($scope.dataClean) || {};
            param['uid'] = objId;
             Rest.all('/detail/cleanLog/advancedSearch?p=' + $scope.page).post(param).then(function(response){
                 Messenger().post({type: 'success', message: '数据加载完成!'});

                 $scope.dataCleanList = response.list;
                 $scope.total = response.total;

             });
        }

        $scope.search = function(){
            go(1);
        }

        $scope.search();

        //切换页面响应事件
        $scope.pageChanged = function () {
            go($scope.page);
        };

        $scope.showResult = function(code){
            if(code==0){
               return '未执行';
            }else if(code==1){
               return '成功';
            }else if(code==2){
               return '失败';
            }
        }

        $scope.detail = function(errorInfo){
           modal.confirm(errorInfo,function(){
                return;
           });

           return;
        }


    });

})();