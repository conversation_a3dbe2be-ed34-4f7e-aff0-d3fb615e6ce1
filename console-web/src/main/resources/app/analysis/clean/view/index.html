<div class="row">
    <div class="col-md-12">
        <div class="box box-primary" cg-busy="loading">
            <div class="box-header with-border">
                <h3 class="text-primary box-title"><i class="fa fa-user"></i>清洗记录</h3>
            </div>
            <div class="box-search">
                <form>
                    <div class="form-group col-sm-2">
                        <input ng-model="dataClean.objId" type="text" class="form-control input-md" placeholder="Uid/DeviceId">
                    </div>

                    <div class="form-group col-sm-2">
                        <select name="type" ng-model="dataClean.objType" ng-init="dataClean.objType='uid'" class="form-control input-md" required>
                            <option value="deviceId">设备ID</option>
                            <option value="uid">用户ID</option>
                        </select>
                    </div>

                    <div class="form-group col-sm-2">
                        <input ng-model="dataClean.operator" type="text" class="form-control input-md" placeholder="操作人模糊搜索">
                    </div>
                    <div class="form-group col-sm-2">
                        <input ng-model="dataClean.note" type="text" class="form-control input-md" placeholder="备注模糊搜索">
                    </div>
                    <div class="form-group col-sm-2">
                        <input type="text" name="createTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                               ng-model="dataClean.createTime" ng-click="popup1.opened=true"
                               is-open="popup1.opened" datepicker-options="dateOptions"
                               close-text="Close" showError="时间格式错误" placeholder="修改开始时间"/>
                    </div>
                    <div class="form-group col-sm-2">
                        <input type="text" name="updateTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                               ng-model="dataClean.updateTime" ng-click="popup2.opened=true"
                               ng-change="changeDate()"
                               is-open="popup2.opened" datepicker-options="dateOptions"
                               close-text="Close" showError="时间格式错误" placeholder="修改结束时间"/>
                    </div>
                    <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索
                    </button>
                </form>
            </div>
            <div class="box-body">
                <table class="table table-bordered">
                    <tr>
                        <th>对象ID</th>
                        <th>操作人</th>
                        <th>备注</th>
                        <th>操作时间</th>
                        <th>细分执行结果</th>
                        <th>执行异常信息</th>
                    </tr>
                    <tr ng-repeat="dataClean in dataCleanList">
                        <td>{{dataClean.uid||dataClean.deviceId}}</td>
                        <td>{{dataClean.operator}}</td>
                        <td>{{dataClean.note}}</td>
                        <td>
                            <span>{{dataClean.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</span>
                        </td>
                        <td>
                            <div ng-show="dataClean.objType !== 'deviceId'">
                                <span ng-model="dataClean.shumei" class="label label-primary mid-label">数美:{{showResult(dataClean.shumei)}}</span>
                                <span ng-model="dataClean.portrait" class="label label-primary mid-label">画像全量:{{showResult(dataClean.portrait)}}</span>
                                <span ng-model="dataClean.blackGray" class="label label-primary mid-label">黑名单:{{showResult(dataClean.blackGray)}}</span>
                                <span ng-model="dataClean.factor" class="label label-primary mid-label">累计因子:{{showResult(dataClean.factor)}}</span>
                            </div>
                            <div ng-show="dataClean.objType === 'deviceId'">
                                <span class="label label-primary mid-label">设备清洗: 成功</span>
                            </div>
                        </td>
                        <td>
                            <button uib-tooltip="查看" class="btn btn-flat btn-md btn-info" ng-if="dataClean.errorDetail" ng-click="detail(dataClean.errorDetail)">
                                <i class="fa fa-book"></i>
                            </button>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="box-footer">
                <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
                    first-text="首页"
                    last-text="尾页"
                    previous-text="前一页"
                    next-text="后一页"
                    total-items="total"
                    ng-model="page"
                    ng-change="pageChanged()"
                    items-per-page="size"
                    max-size="10"
                    rotate="false">
                </ul>
            </div>
        </div>
    </div>
</div>



