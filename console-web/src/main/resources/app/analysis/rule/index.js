(function () {
    let app = angular.module(app_name);

    app.filter("trustUrl", ['$sce', function ($sce) {
        return function (url) {
            return $sce.trustAsResourceUrl(url);
        };
    }]);

    app.controller('RuleDetailCtrl', function ($scope, $state, $stateParams, $uibModal, Rest, modal, $aside) {
        $scope.query = angular.extend({}, $stateParams);
        $scope.ruleList = [];
        $scope.queryTime = "6";
        $scope.page = 1;
        $scope.colorList = ["label-danger","label-success","label-info","label-warning","label-primary"]
        $scope.loading = true;
        $scope.loading = Rest.all('/detail/rule/getRules').post().then(function (result) {
            let data = result.data;
            let paramRule = null;
            for (let key in data) {
                let rule = {'id': key, 'name': data[key]};
                $scope.ruleList.push(rule);
                if($scope.query.value && key == $scope.query.value){
                    paramRule = rule;
                }
            }
            if(paramRule != null){
                $scope.query.rule = paramRule;
                $scope.choiceRule(paramRule);
            }
        });
        $scope.currentRule= null;
        $scope.business = null;
        $scope.grayGroups = [];
        $scope.initStatus = {};
        $scope.searchedList = {};
        $scope.riskLogParam = {};

        $scope.initTime = function () {
            let startTime = new Date();
            startTime.setHours(0);
            startTime.setMinutes(0);
            startTime.setSeconds(0);
            startTime.setMilliseconds(0);
            let endTime = new Date(startTime.getTime() + 1000 * 60 * 60 * 24 - 1);

            $scope.startTime = startTime
            $scope.endTime = endTime
        }
        $scope.initTime()

        $scope.getDaysCount = function (ruleId) {
            let days = parseInt(($scope.endTime.getTime() - $scope.startTime.getTime())/(1000 * 60 * 60 * 24))
            if(days < 0){
                Messenger().post({type: 'error', message: '开始时间必须小于结束时间！'});
            }else if (days > 30) {
                Messenger().post({type: 'error', message: '时间跨度不允许超过31天！'});
            } else {
                $scope.getRiskCount(ruleId, days)
                $scope.getRiskUserCount(ruleId, days)
                $scope.getRiskMobileCount(ruleId, days)
            }
        }

        $scope.choiceRule = function (rule) {
            $scope.currentRule = rule;
            $scope.business = null;
            $scope.grayGroups = [];
            $scope.ruleGroups = [];
            $scope.summary = {};
            $scope.initStatus = {};
            $scope.simpleLocalAttrs = null;
            $scope.loadedFlag = {};
            $scope.riskLogParam = {};
            $scope.page = 1;
            $scope.eventCount = null
            $scope.riskCount = null
            $scope.riskUserCount = null
            $scope.riskMobileCount = null
            $scope.dataViews = null
            $scope.dataTable = null
            $scope.initTime()
            if(rule){
                let ruleId = rule.id
                $scope.getEventCount(ruleId)
                $scope.getDaysCount(ruleId)
                $scope.getDataViews(ruleId);
                $scope.switchTabTable('card_table_rule') // 默认规则标签页
                $scope.searchRule() // 获取规则信息
            }
        }
        $scope.choiceDays = function(){
            if($scope.currentRule){
                $scope.getDaysCount($scope.currentRule.id)
            }
        }
        $scope.getEventCount = function(ruleId){
            $scope.loading = Rest.all('/detail/rule/getEventCount/'+ruleId).post().then(function (response) {
                $scope.eventCount = response;
            });
        }
        $scope.getRiskCount = function(ruleId, days){
            $scope.loading = Rest.all('/detail/rule/getRiskCount').post({"ruleId":ruleId, "days":days}).then(function (response) {
                $scope.riskCount = response;
            });
        }
        $scope.getRiskUserCount = function(ruleId, days){
            $scope.loading = Rest.all('/detail/rule/getRiskUserCount').post({"ruleId":ruleId, "days":days}).then(function (response) {
                $scope.riskUserCount = response;
            });
        }
        $scope.getRiskMobileCount = function(ruleId, days){
            $scope.loading = Rest.all('/detail/rule/getRiskMobileCount').post({"ruleId":ruleId, "days":days}).then(function (response) {
                $scope.riskMobileCount = response;
            });
        }

        // data view start
        let dataViewsOrigin = [
            {
                "key": "card_event_line_top_10",
                "title": "事件TOP10",
                "url": "https://insight-visual.yupaopao.com/public/question/4b727a06-6594-4eb0-91ec-417cb52a602e?createdAt=thisday&topv=10",
                "descption": ""
            },
            {
                "key": "card_event_pie_top_10",
                "title": "事件分布TOP10",
                "url": "https://insight-visual.yupaopao.com/public/question/96b5628b-3f4e-4180-b8d4-de2d8420b357?createdAt=thisday&topv=10",
                "descption": ""
            },
            {
                "key": "card_user_table_top_20",
                "title": "用户TOP20",
                "url": "https://insight-visual.yupaopao.com/public/question/a57c25d3-04e5-4772-9193-63c453295e2a?createdAt=thisday&topv=20",
                "descption": ""
            },
            {
                "key": "card_device_table_top_20",
                "title": "设备TOP20",
                "url": "https://insight-visual.yupaopao.com/public/question/9bb134ad-95d0-445c-a2e9-15781ac5427c?createdAt=thisday&topv=20",
                "descption": ""
            }
        ];

        $scope.getDataViews = function(ruleId) {
            $scope.tabIndexArr = [0]
            let views = []
            let origins = angular.copy(dataViewsOrigin);
            for(let i=0;i<origins.length;i++){
                let view = origins[i]
                view.url += "&result_rule=" + ruleId
                views.push(view)
            }
            $scope.dataViews = views
        }

        $scope.switchTabView = function (target, tabIndex) {
            $("#rule-detail-data-view li").removeClass("active");
            $("#rule-detail-data-view .tab-pane").removeClass("active");
            $("#rule-detail-data-view ." + target).addClass("active");
            if($scope.tabIndexArr.indexOf(tabIndex) == -1){
                $scope.tabIndexArr.push(tabIndex)
            }
        };
        // data view end

        // data table start
        $scope.switchTabTable = function (target) {
            $("#rule-detail-data-table .active").removeClass("active");
            $("#rule-detail-data-table ." + target).addClass("active");
            if ($scope.initStatus[target] == true) {
                return;
            }
            if('card_table_event' == target){
                $scope.searchEvent()
            } else if('card_table_rule_group' == target){
                $scope.searchRuleGroup();
            } else if('card_table_rule' == target){
                $scope.searchRule()
            } else if('card_table_inner_attr' == target){
                $scope.searchLocalAttr();
            } else if('card_table_factor' == target){
                $scope.searchFactor();
            } else if('card_table_outer_attr' == target){
                $scope.searchRemoteAttr();
            } else if('card_table_config_record' == target){
                $scope.searchRiskLog();
            }
            $scope.initStatus[target] = true;
        };

        $scope.searchEvent = function(resolve){
            $scope.events = [];
            $scope.loadingEvent = Rest.all('/detail/rule/searchEvent').post({"id":$scope.currentRule.id}).then(function (response) {
                $scope.events = response;
                $scope.loadedFlag.event = true;
                if(resolve){
                    resolve("event");
                }
            });
        }

        $scope.searchRuleGroup = function(resolve){
            $scope.ruleGroups = [];
            $scope.loadingRuleGroup = Rest.all('/detail/rule/searchRuleGroup').post({"id":$scope.currentRule.id}).then(function (response) {
                $scope.ruleGroups = response;
                $scope.loadedFlag.ruleGroup = true;
                if(resolve){
                    resolve("ruleGroup");
                }
            });
        }

        $scope.showRuleGroupDetail = function (ruleGroup) {
            $aside.open({
                templateUrl: 'analysis/rule/view/ruleGroupDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.ruleGroupDetail = ruleGroup;
                }
            });
        };

        $scope.searchRule = function(resolve){
            $scope.ruleAtoms = [];
            $scope.loadingRule = Rest.all('/detail/rule/detail').post({"id":$scope.currentRule.id}).then(function (response) {
                $scope.ruleAtomDetail = response;
                $scope.loadedFlag.ruleAtom = true;
                if(response){
                    $scope.ruleAtoms.push(response)
                }
                if(resolve){
                    resolve("ruleAtom");
                }
            });
        }

        $scope.searchLocalAttr = function(resolve){
            $scope.localAttrs = [];
            $scope.simpleLocalAttrs = [];
            $scope.loadingLocalAttr = Rest.all('/detail/rule/searchAttr').post({"ruleId":$scope.currentRule.id, "type":"LOCAL"}).then(function (response) {
                $scope.localAttrs = response;
                if($scope.localAttrs){
                    for(let i=0;i<$scope.localAttrs.length;i++){
                        let localAttr = $scope.localAttrs[i];
                        $scope.simpleLocalAttrs.push({"id":localAttr.id,"name":localAttr.name,"dependent":localAttr.dependent});
                    }
                }
                $scope.loadedFlag.localAttr = true;
                if(resolve){
                    resolve("localAttr");
                }
            });
        }

        $scope.searchFactor = function(resolve){
            $scope.factors = [];
            $scope.loadingFactor = Rest.all('/detail/rule/searchFactor').post({"ruleId":$scope.currentRule.id, "attributes":$scope.simpleLocalAttrs}).then(function (response) {
                $scope.factors = response;
                $scope.loadedFlag.factor = true;
                if(resolve){
                    resolve("factor");
                }
            });
        }

        $scope.searchRemoteAttr = function(resolve){
            $scope.reomteAttrs = [];
            $scope.loadingRemoteAttr = Rest.all('/detail/rule/searchAttr').post({"ruleId":$scope.currentRule.id, "type":"REMOTE"}).then(function (response) {
                $scope.reomteAttrs = response;
                $scope.loadedFlag.remoteAttr = true;
                if(resolve){
                    resolve("remoteAttr");
                }
            });
        }

        $scope.searchRiskLog = function(){
            $scope.logs = [];
            $scope.total = 0;
            let promiseArr = [];
            if(!$scope.loadedFlag.hasOwnProperty("event")){
                $scope.initStatus["event"] = true;
                let p1 = new Promise(function(resolve, reject){
                    $scope.searchEvent(resolve);
                });
                promiseArr.push(p1);
            }
            if(!$scope.loadedFlag.hasOwnProperty("ruleGroup")){
                $scope.initStatus["ruleGroup"] = true;
                let p2 = new Promise(function(resolve, reject){
                    $scope.searchRuleGroup(resolve);
                });
                promiseArr.push(p2);
            }
            if(!$scope.loadedFlag.hasOwnProperty("ruleAtom")){
                $scope.initStatus["ruleAtom"] = true;
                let p3 = new Promise(function(resolve, reject){
                    $scope.searchRule(resolve);
                });
                promiseArr.push(p3);
            }
            if(!$scope.loadedFlag.hasOwnProperty("localAttr")){
                $scope.initStatus["localAttr"] = true;
                let p4 = new Promise(function(resolve, reject){
                    $scope.searchLocalAttr(resolve);
                });
                promiseArr.push(p4);
            }
            if(!$scope.loadedFlag.hasOwnProperty("remoteAttr")){
                $scope.initStatus["remoteAttr"] = true;
                let p5 = new Promise(function(resolve, reject){
                    $scope.searchRemoteAttr(resolve);
                });
                promiseArr.push(p5);
            }
            if(!$scope.loadedFlag.hasOwnProperty("factor")){
                $scope.initStatus["factor"] = true;
                let p6 = new Promise(function(resolve, reject){
                    $scope.searchFactor(resolve);
                });
                promiseArr.push(p6);
            }
            if(promiseArr.length > 0){
                Promise.all(promiseArr).then(
                    (res) => {
                        initRiskLogParam();
                        $scope.pageChanged(1);
                    }
                    ,(err) => console.log(err)
                )
            }else{
                initRiskLogParam();
                $scope.pageChanged(1);
            }
        }

        $scope.goRule = function(){
            openUrlNew = $state.href('rule.atom', {"id":$scope.currentRule.id,"name":$scope.currentRule.name});
            window.open(openUrlNew, '_blank')
        }

        function initRiskLogParam(){
            $scope.riskLogParam.event = {"id":$scope.currentRule.id};
            if($scope.events){
                $scope.riskLogParam.events = [];
                for(let i=0;i<$scope.events.length;i++){
                    let event = $scope.events[i];
                    $scope.riskLogParam.events.push({"id":event.id});
                }
            }
            if($scope.ruleGroups){
                $scope.riskLogParam.ruleGroups = [];
                for(let i=0;i<$scope.ruleGroups.length;i++){
                    let ruleGroup = $scope.ruleGroups[i];
                    $scope.riskLogParam.ruleGroups.push({"id":ruleGroup.id});
                }
            }
            if($scope.ruleAtoms){
                $scope.riskLogParam.ruleAtoms = [];
                for(let i=0;i<$scope.ruleAtoms.length;i++){
                    let ruleAtom = $scope.ruleAtoms[i];
                    $scope.riskLogParam.ruleAtoms.push({"id":ruleAtom.id});
                }
            }
            if($scope.localAttrs){
                $scope.riskLogParam.localAttrs = [];
                for(let i=0;i<$scope.localAttrs.length;i++){
                    let localAttr = $scope.localAttrs[i];
                    $scope.riskLogParam.localAttrs.push({"id":localAttr.id});
                }
            }
            if($scope.reomteAttrs){
                $scope.riskLogParam.remoteAttrs = [];
                for(let i=0;i<$scope.reomteAttrs.length;i++){
                    let remoteAttr = $scope.reomteAttrs[i];
                    $scope.riskLogParam.remoteAttrs.push({"id":remoteAttr.id});
                }
            }
            if($scope.factors){
                $scope.riskLogParam.factors = [];
                for(let i=0;i<$scope.factors.length;i++){
                    let factor = $scope.factors[i];
                    $scope.riskLogParam.factors.push({"id":factor.id});
                }
            }
        }
        $scope.searchRiskLogInner = function(page){
            $scope.page = page || $scope.page || 1;
            $scope.loading = true;
            $scope.loadingRiskLog = Rest.all('detail/rule/searchRiskLog?p=' + $scope.page).post($scope.riskLogParam).then(function (response) {
                $scope.logs = response.list;
                $scope.total = response.total;
            });
        }
        $scope.pageChanged = function (page) {
            $scope.page = page;
            $scope.searchRiskLogInner($scope.page);
        };
        $scope.format = function (json) {
            let newJson = JSON.parse(json);
            if (newJson.riskConst) {
                newJson.riskConst = JSON.parse(newJson.riskConst);
            }
            return newJson;
        };
    });
})();