<div class="box-body" cg-busy="loadingFactor">
    <table class="table table-bordered">
        <tr>
            <th>ID</th>
            <th>分组字段</th>
            <th>聚合字段</th>
            <th>聚合方式</th>
            <th style="width: 25%">前置条件</th>
            <th style="width: 20%">备注</th>
            <th style="width: 15%">关联属性</th>
            <th style="width: 12%">最后修改时间</th>
            <th>操作</th>
        </tr>
        <tr ng-repeat="factor in factors">
            <td>{{factor.id}}</td>
            <td>{{factor.groupKey}}</td>
            <td>{{factor.aggKey}}</td>
            <td>{{factor.function}}:{{factor.timeSpan}}</td>
            <td>{{factor.condition}}</td>
            <td>{{factor.comment}}</td>
            <td>
                <div class="box box-widget widget-user-2" ng-if="factor.attributeList != null && factor.attributeList.length > 0" style="margin-bottom: 0">
                    <div class="box-footer no-padding" style="padding:0">
                        <ul class="nav nav-stacked" ng-repeat="attr in factor.attributeList">
                            <li style="padding:5px;">
                                <span>{{attr.name}}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <td>{{factor.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            <td>
                <a href="javascript:void(0)" target="_blank" ui-sref="core.factor({id:factor.id})">
                    <span>更多</span>
                </a>
            </td>
        </tr>
    </table>
</div>
