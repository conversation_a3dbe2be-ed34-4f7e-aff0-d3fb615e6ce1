<div class="box-body" cg-busy="loadingLocalAttr">
    <table class="table table-bordered">
        <tr>
            <th>ID</th>
            <th style="width: 20%">属性名</th>
            <th>依赖属性名</th>
            <th>函数</th>
            <th>关联规则</th>
            <th style="width: 12%">最后修改时间</th>
            <th>操作</th>
        </tr>
        <tr ng-repeat="localAttr in localAttrs">
            <td>{{localAttr.id}}</td>
            <td>{{localAttr.name}}</td>
            <td>{{localAttr.dependent}}</td>
            <td>{{localAttr.function}}</td>
            <td>
                <div class="box box-widget widget-user-2" ng-if="localAttr.atomRules != null && localAttr.atomRules.length > 0" style="margin-bottom: 0">
                    <div class="box-footer no-padding" style="padding:0">
                        <ul class="nav nav-stacked" ng-repeat="atomRule in localAttr.atomRules">
                            <li style="padding:5px;">
                                <span>{{atomRule.name}}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <td>{{localAttr.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            <td>
                <a href="javascript:void(0)" target="_blank" ui-sref="attribute.local({id:localAttr.id,name:localAttr.name})">
                    <span>更多</span>
                </a>
            </td>
        </tr>
    </table>
</div>
