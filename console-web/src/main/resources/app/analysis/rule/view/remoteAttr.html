<div class="box-body" cg-busy="loadingRemoteAttr">
    <table class="table table-bordered">
        <tr>
            <th>ID</th>
            <th>属性名</th>
            <th style="width: 25%">依赖属性名</th>
            <th>返回结果key</th>
            <th>函数</th>
            <th>关联规则</th>
            <th style="width: 12%">最后修改时间</th>
            <th>操作</th>
        </tr>
        <tr ng-repeat="reomteAttr in reomteAttrs">
            <td>{{reomteAttr.id}}</td>
            <td>{{reomteAttr.name}}</td>
            <td>{{reomteAttr.dependent}}</td>
            <td>{{reomteAttr.resultKey}}</td>
            <td>{{reomteAttr.function}}</td>
            <td>
                <div class="box box-widget widget-user-2" ng-if="reomteAttr.atomRules != null && reomteAttr.atomRules.length > 0" style="margin-bottom: 0">
                    <div class="box-footer no-padding" style="padding:0">
                        <ul class="nav nav-stacked" ng-repeat="atomRule in reomteAttr.atomRules">
                            <li style="padding:5px;">
                                <span>{{atomRule.name}}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <td>{{reomteAttr.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            <td>
                <a href="javascript:void(0)" target="_blank" ui-sref="attribute.remote({id:reomteAttr.id,name:reomteAttr.name})">
                    <span>更多</span>
                </a>
            </td>
        </tr>
    </table>
</div>
