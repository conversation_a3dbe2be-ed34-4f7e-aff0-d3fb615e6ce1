<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-gg"></i> 规则详情</h3>
    </div>
    <div class="row" style="margin:0">
        <div class="col-md-5" style="padding-left: 3px;padding-right: 3px;">
            <div class="box-search rule-detail-box">
                <div class="form-group col-sm-4" style="width:350px; margin: 7px 0">
                    <ui-select class="input-md" ng-model="query.rule" theme="bootstrap" ng-change="choiceRule($select.selected)" style="width:350px">
                        <ui-select-match placeholder="选择规则" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                        <ui-select-choices repeat="ruleObj in ruleList| filter: $select.search" refresh-delay="300">
                            <div uib-tooltip="{{ruleObj.name+'('+ruleObj.id+')'}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:320px;"  ng-bind-html="ruleObj.name+'('+ruleObj.id+')'"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="form-group col-sm-2" ng-show="currentRule" style="margin: 7px 0">
                    <button ng-click="goRule()" class="btn btn-flat btn-md btn-info">
                        <i class="fa fa-link"></i> 规则配置
                    </button>
                </div>
            </div>
        </div>
        <div class="col-md-7" style="padding-left: 3px;padding-right: 3px;">
            <div class="row rule-detail-box" style="margin-bottom:15px;margin-left:0;margin-right:0;">
                <div class="col-md-5">
                    <div class="row" style="margin:0">
                        <div class="form-group col-sm-6" style="margin: 7px 0">
                            <input type="text" name="startTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd"
                                   ng-model="startTime" ng-click="popup1.opened=true" readonly="true"
                                   is-open="popup1.opened" datepicker-options="dateOptions" ng-disabled="!currentRule"
                                   close-text="Close" showError="时间格式错误" ng-change="choiceDays()"/>
                        </div>
                        <div class="form-group col-sm-6" style="margin: 7px 0">
                            <input type="text" name="endTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd"
                                   ng-model="endTime" ng-click="popup2.opened=true" readonly="true"
                                   is-open="popup2.opened" datepicker-options="dateOptions" ng-disabled="!currentRule"
                                   close-text="Close" showError="时间格式错误" ng-change="choiceDays()"/>
                        </div>
                    </div>
                </div>
                <div class="col-md-7">
                    <div class="row" style="margin:0">
                        <div class="col-md-3 border-right">
                            <div class="description-block text-green" style="margin: 5px 0">
                                <h5 class="description-header">{{ eventCount || 0 }}</h5>
                                <span class="description-text">事件数</span>
                            </div>
                        </div>
                        <div class="col-md-3 border-right">
                            <div class="description-block text-yellow" style="margin: 5px 0">
                                <h5 class="description-header">{{ riskCount || 0 }}</h5>
                                <span class="description-text">次数</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="description-block text-red" style="margin: 5px 0">
                                <h5 class="description-header">{{ riskUserCount || 0 }}</h5>
                                <span class="description-text">用户数</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="description-block text-red" style="margin: 5px 0">
                                <h5 class="description-header">{{ riskMobileCount || 0 }}</h5>
                                <span class="description-text">手机号数</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="box-body" style="padding: 0">
    <!-- data view start -->
    <div class="nav-tabs-custom" id="rule-detail-data-view">
        <ul class="nav nav-tabs" ng-if="dataViews">
            <li ng-repeat="item in dataViews" ng-class="{active:$index == 0}" class="{{item.key}}">
                <a  href="javascript:;" ng-click="switchTabView(item.key, $index)" style="padding:10px 10px;">
                    <span class="text-primary box-title">{{item.title}}</span>
                </a>
            </li>
        </ul>
        <div class="tab-content" style="min-height: 650px;" ng-if="dataViews">
            <div ng-repeat="item in dataViews" ng-class="{active:$index == 0}" class="tab-pane {{item.key}}">
                <iframe ng-if="tabIndexArr.indexOf($index) != -1" ng-src="{{item.url | trustUrl}}" allowtransparency="true" frameborder="0" scrolling="0" width="100%" height="600"></iframe>
            </div>
        </div>
    </div>
    <!-- data view end -->

    <!-- data table start -->
    <div class="nav-tabs-custom" id="rule-detail-data-table" ng-show="currentRule">
        <ul class="nav nav-tabs">
            <li class="card_table_event"><a href="javascript:;" ng-click="switchTabTable('card_table_event')">事件</a></li>
            <li class="card_table_rule_group"><a href="javascript:;" ng-click="switchTabTable('card_table_rule_group')">规则组</a></li>
            <li class="card_table_rule active"><a href="javascript:;" ng-click="switchTabTable('card_table_rule')">规则</a></li>
            <li class="card_table_inner_attr"><a href="javascript:;" ng-click="switchTabTable('card_table_inner_attr')">内部属性</a></li>
            <li class="card_table_factor"><a href="javascript:;" ng-click="switchTabTable('card_table_factor')">累计因子</a></li>
            <li class="card_table_outer_attr"><a href="javascript:;" ng-click="switchTabTable('card_table_outer_attr')">外部属性</a></li>
            <li class="card_table_config_record"><a href="javascript:;" ng-click="switchTabTable('card_table_config_record')">配置记录</a></li>
        </ul>
        <div class="tab-content" style="min-height: 701px;">
            <div class="tab-pane card_table_event" ng-include="'analysis/rule/view/event.html'"/>
            <div class="tab-pane card_table_rule_group" ng-include="'analysis/rule/view/ruleGroup.html'"/>
            <div class="tab-pane card_table_rule active" ng-include="'analysis/rule/view/ruleAtomDetail.html'"/>
            <div class="tab-pane card_table_inner_attr" ng-include="'analysis/rule/view/localAttr.html'"/>
            <div class="tab-pane card_table_factor" ng-include="'analysis/rule/view/factor.html'"/>
            <div class="tab-pane card_table_outer_attr" ng-include="'analysis/rule/view/remoteAttr.html'"/>
            <div class="tab-pane card_table_config_record" ng-include="'analysis/rule/view/riskLog.html'"/>
        </div>
    </div>
    <!-- data table end -->
</div>
<style>
    .rule-detail-box{
        padding: 5px 5px;
        border: solid #00a7d0 1px;
        min-height:60px;
    }
</style>