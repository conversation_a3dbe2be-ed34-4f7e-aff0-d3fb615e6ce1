(function () {
    var app = angular.module(app_name);

    app.factory('CleanResultService', function ($uibModal) {
        return {
            detail: function (cleanResult, callback) {
                $uibModal.open({
                    backdrop: 'static',
                    templateUrl: 'analysis/user/view/cleanResultDetail.html',
                    controller: function (Rest, $scope, modal, $uibModalInstance) {
                        $scope.title = "清洗结果展示";
                        $scope.cleanResult = angular.copy(cleanResult);
                        if ($scope.cleanResult.errorDetail) {
                            $scope.cleanResult.errorDetail = JSON.stringify(JSON.parse($scope.cleanResult.errorDetail), null, "    ");
                        }

                        $scope.showResult = function (result) {
                            if (result == 0) {
                                return '未执行';
                            } else if (result == 1) {
                                return '成功';
                            } else if (result == 2) {
                                return '失败';
                            }
                        };

                        $scope.close = function () {
                            $uibModalInstance.close();
                        };
                    }
                });
            }
        }
    });

    //清洗确认框
    app.factory('CleanDataService', function ($uibModal) {
        return {
            detail: function (blackGrayList, CleanResultService, uid, callback) {
                $uibModal.open({
                    backdrop: 'static',
                    size: 'lg',
                    templateUrl: 'analysis/user/view/dataCleanDetail.html',
                    controller: function (Rest, $scope, modal, $uibModalInstance, $filter) {
                        $scope.submitDisabled = false;
                        $scope.title = "清洗详单确认";
                        $scope.grayList = angular.copy(blackGrayList);

                        $scope.param = {};

                        $scope.param.shumei = true;
                        $scope.param.portrait = true;
                        $scope.param.blackGray = false;
                        $scope.param.factor = false;
                        $scope.visible = false;

                        $scope.selectFactor = function () {
                            if ($scope.param.factor) {
                                $scope.visible = true;
                            } else {
                                $scope.visible = false;
                            }
                        };

                        $scope.confirm = function () {
//                                   $scope.submitDisabled = true;
                            if ($scope.param.comment == null) {
                                modal.confirm('请输入备注信息', function () {
                                    return;
                                });

                                return;
                            }

                            if ($scope.param.factor) {
                                if (!$scope.param.factorIds) {
                                    modal.confirm('清洗累计因子，必须输入累计因子id.需输入多个时，请用英文逗号隔开。', function () {
                                        return;
                                    });

                                    return;
                                } else {
                                    var reg = /^[0-9]*$/;
                                    var ids = $scope.param.factorIds.split(',');
                                    if (ids.length > 0) {
                                        for (var i = 0; i < ids.length; i++) {
                                            if (ids[i] && !reg.test(ids[i])) {
                                                modal.confirm('请输入合法的累计因子id，多个请用英文逗号隔开。', function () {
                                                    return;
                                                });

                                                return;
                                            }
                                        }
                                    }
                                }
                            } else {
                                $scope.param.factorIds = null;
                            }

                            $scope.param.blackGrayList = blackGrayList;
                            Rest.all('/detail/user/correction?uid=' + uid).post($scope.param).then(function (response) {
                                if (response) {

                                    CleanResultService.detail(response, function () {
                                    })

                                    Messenger().post({type: 'success', message: '清洗成功!'});
                                } else {
                                    Messenger().post({type: 'error', message: response.message});
                                }

                                callback();
                                $scope.submitDisabled = true;
                                $uibModalInstance.close();
                            });
                        };

                        $scope.close = function () {
                            $uibModalInstance.close();
                        };
                    }
                });
            }
        };
    });

    var sparkLineDefaultConfig = defaultOptions = {
        chart: {
            backgroundColor: null,
            borderWidth: 0,
            type: 'area',
            margin: [2, 0, 2, 0],
            height: 100,
            style: {
                overflow: 'visible'
            },
            // small optimalization, saves 1-2 ms each sparkline
            skipClone: true
        },
        title: {
            text: ''
        },
        credits: {
            enabled: false
        },
        xAxis: {
            labels: {
                enabled: false
            },
            title: {
                text: null
            },
            startOnTick: false,
            endOnTick: false,
            tickPositions: []
        },
        yAxis: {
            endOnTick: false,
            startOnTick: false,
            labels: {
                enabled: false
            },
            title: {
                text: null
            },
            tickPositions: [0]
        },
        legend: {
            enabled: false
        },
        tooltip: {
            hideDelay: 0,
            outside: true,
            shared: true
        },
        plotOptions: {
            series: {
                animation: false,
                lineWidth: 1,
                shadow: false,
                states: {
                    hover: {
                        lineWidth: 1
                    }
                },
                marker: {
                    radius: 1,
                    states: {
                        hover: {
                            radius: 2
                        }
                    }
                },
                fillOpacity: 0.25
            },
            column: {
                negativeColor: '#910000',
                borderColor: 'silver'
            }
        }
    };

    app.controller('UserDetailCtrl', function ($scope, $state, $stateParams, CleanDataService, CleanResultService, $uibModal, Rest, modal, $aside, ImService, AuthService, $sce, toastr) {
        $scope.param = angular.extend({}, $stateParams);
        var defaultDaysRange = localStorage.getItem("defaultDaysRange");
        defaultDaysRange = (defaultDaysRange != null) ? parseInt(defaultDaysRange) : 7;
        var endTime = new Date();
        endTime.setHours(23);
        endTime.setMinutes(59);
        endTime.setSeconds(59);
        endTime.setMilliseconds(999);
        var startTime = new Date(endTime.getTime() - 1000 * 60 * 60 * 24 * defaultDaysRange + 1);
        $scope.param.startTime = startTime;
        $scope.param.endTime = endTime;
        $scope.expType = {
            0: '爵位',
            1: 'VIP',
            2: '鱼耳直播用户',
            3: '鱼耳直播主播',
            4: '大神',
            5: '配音秀',
            6: '新VIP',
        }

        $scope.devicesDesc = "(最近7天+当前时间范围)";

        // $scope.demoData = [1, 3, 5, 3, 8, 1, 3, 5, 3, 8]

        // $scope.param.value = '193600653368020026'
        /*      $scope.wordCloud = {
                  "title": {"text": ""},
                  "chart": {"height": 280},
                  series: [{
                      type: 'wordcloud',
                      data: []
                  }],
                  credits: {
                      enabled: false     //不显示LOGO
                  }
              };*/

        // $scope.param.value = '193600653368020026'

        // 轮播配置
        /*     $scope.interval = 5000;
             $scope.noWrapSlides = false;
             $scope.active = 0;
             $scope.slides = []*/

        // function initConfigPic(){
        $scope.wordCloud = {
            "title": {"text": ""},
            "chart": {"height": 280},
            series: [{
                type: 'wordcloud',
                maxFontSize: 30,//最大字体
                minFontSize: 16,//最小字体
                style: {
                    fontFamily: "微软雅黑",
                    fontWeight: '500'
                },
                data: []
            }],
            credits: {
                enabled: false     //不显示LOGO
            }
        };

        if ($scope.param.accountType == null) {
            $scope.param.accountType = '1';
        }
        $scope.accountTypes = [];
        $scope.accountTypeMap = {};

        //查询帐号体系
        function queryAccountTypes() {
            $scope.loading = Rest.all('/common/getAccountTypes').post().then(function (response) {
                $scope.accountTypes = response;
                if ($scope.accountTypes && $scope.accountTypes.length > 0) {
                    for (var accountType of $scope.accountTypes) {
                        $scope.accountTypeMap[accountType.type] = accountType;
                    }
                }
            });
        }

        queryAccountTypes();
        $scope.apps = [];

        //查询app列表
        function queryApps(accountType) {
            $scope.apps = $scope.accountTypeMap[accountType].appList;
        }

        $scope.activeChartConfig = angular.merge(angular.copy(sparkLineDefaultConfig), {
            series: [{
                data: []
            }],
            xAxis: {
                categories: []
            },
            chart: {
                type: 'area',
            },
            tooltip: {
                formatter: function () {
                    return '<b>' + this.x + '</b><br/>' + this.y + ' 次<br/>'
                }
            },
        });


        $scope.activeSceneChartConfig = angular.merge(angular.copy(sparkLineDefaultConfig), {
            series: [{
                data: []
            }],
            xAxis: {
                categories: []
            },
            chart: {
                type: 'column',
            },
            tooltip: {
                formatter: function () {
                    return '<b>' + this.x + '</b><br/>' + this.y + ' 次<br/>'
                }
            },
        });

        $scope.activeViolationSceneConfig = angular.merge(angular.copy(sparkLineDefaultConfig), {
            series: [{
                data: []
            }],
            xAxis: {
                categories: []
            },
            chart: {
                type: 'column',
            },
            tooltip: {
                formatter: function () {
                    return '<b>' + this.x + '</b><br/>' + this.y + ' 次<br/>'
                }
            },

        })
        //图片
        $scope.interval = 5000;
        $scope.noWrapSlides = false;
        $scope.active = 0;
        $scope.slides = []
        // }


        /*    $scope.chartConfig = {
                "chart": {"height": 500, "width": 500, "type": "line"},
                "plotOptions": {"series": {"stacking": ""}},
                "series": [{
                    "name": "My Super Column",
                    "data": [1, 1, 2, 3, 2],
                    "type": "column",
                    "id": "s4"
                }],
                "title": {"text": "近7日活跃", align: 'left'},
                credits: {
                    enabled: false     //不显示LOGO
                }
            };*/

        var initStatus = {};
        $scope.riskTip = false
        $scope.switchTab = function (target) {
            $("#user-detail .active").removeClass("active");
            $("#user-detail ." + target).addClass("active");
            $scope.riskTip = false
            if (target == 'order' || target == 'pay' || target == 'csd' || target == 'complain' || target == 'appeal' || target == 'punish' || target == 'search') {
                $scope.riskTip = true
            }
            if (initStatus[target] == true) {
                return;
            }
            if ('portrait' == target) { // 初始化IM聊天数据
                showPortrait($scope.userDetail.uid); // 加载用户关联数据
            } else if ('im' == target) { // 初始化IM聊天数据
                ImService.imInit({
                    userId: $scope.userDetail.uid,
                    uid: $scope.userDetail.uid,
                    accountType: $scope.param.accountType
                }, $scope);
            } else if ('xyx' == target) { // 初始化私聊数据
                ImService.xyxInit({userId: $scope.userDetail.uid, uid: $scope.userDetail.uid}, $scope);
            } else if ('imGroup' == target) { // 初始化私聊数据
                ImService.imGroupInit({userId: $scope.userDetail.uid, uid: $scope.userDetail.uid}, $scope);
            } else if ('cr' == target) { // 初始化私聊数据
                ImService.crInit({userId: $scope.userDetail.uid, uid: $scope.userDetail.uid}, $scope);
            } else if ('lr' == target) { // 初始化私直播间
                ImService.lrInit({userId: $scope.userDetail.uid, uid: $scope.userDetail.uid}, $scope);
            } else if ('order' == target) { // 初始化私聊数据
                queryOrder($scope.page)
            } else if ('pay' == target) {
                goCashJournal($scope.payPage)
            } else if ('csd' == target) { // 初始化客服数据
                queryCSDRecord($scope.KFPage)
            } else if ('complain' == target) {
                queryComplainRecord($scope.userDetail)
            } else if ('appeal' == target) {
                queryAppealRecord($scope.userDetail)
            } else if ('punish' == target) {
                queryPunishRecord($scope.userDetail)
            } else if ('risk' == target) {
                fetchRelation($scope.userDetail.uid);
            }else if('search' == target){
                querySearchRecord($scope.searchPage);
            }

            initStatus[target] = true;
        };

        var initUserInfoStatus = {"baseInfo": true};
        $scope.switchUserTab = function (target) {
            $("#user-info-tab .baseInfo").removeClass("active");
            $("#user-info-tab .dataCard").removeClass("active");
            $("#user-info-tab ." + target).addClass("active");
            if (initUserInfoStatus[target] == true) {
                return;
            }
            if ('dataCard' == target) { // 初始化资料卡数据
                queryDataCard();
            }
            initUserInfoStatus[target] = true;
        };
        $scope.switchDataCardTab = function (target) {
            $("#data-card-tab li").removeClass("active");
            $("#data-card-tab .tab-pane").removeClass("active");
            $("#data-card-tab ." + target).addClass("active");
        };
        $scope.showCompletePhone = function (userId) {
            Rest.all('detail/user/mobile?userId=' + userId).post().then(function (response) {
                if (response && response.mobile) {
                    modal.confirm("完整的电话号码(" + response.nationCode + ")" + response.mobile, function () {
                        return;
                    });
                }
            });
        };

        $scope.showRelatePhone = function (paramCopy, phone, value) {
            var param = {
                userId: paramCopy.userId,
                phone: phone,
                activeDegree: value,
                startTime: paramCopy.startTime,
                endTime: paramCopy.endTime
            };

            Rest.all('detail/user/relation/mobileInfo').post(param).then(function (response) {
                if (response) {
                    modal.confirm("关联的完整的电话号码" + response, function () {
                        return;
                    });
                }
            });

        };
        //直播视频播放事件
        $scope.videoUrlFun = function (url) {
            var urlFun = $sce.trustAsResourceUrl(url);
            return urlFun;
        };

        function noDataHighChart(tipText, heightValue) {
            return angular.merge(angular.copy(sparkLineDefaultConfig), {
                chart: {
                    backgroundColor: null,
                    borderWidth: 0,
                    type: 'area',
                    margin: [2, 0, 2, 0],
                    height: heightValue,
                    style: {
                        overflow: 'visible'
                    },
                    skipClone: true
                },
                title: {
                    text: tipText
                },
                series: [{
                    type: 'line',
                    name: 'Random data',
                    data: []
                }],
                lang: {
                    noData: tipText //真正显示的文本
                },
                noData: {
                    position: {},
                    attr: {},
                    style: {}
                }
            });
        }

        function flushHighChart(data, categories, type) {
            return angular.merge(angular.copy(sparkLineDefaultConfig), {
                series: [{
                    data: data,
                }],
                xAxis: {
                    categories: categories,
                },
                chart: {
                    type: type,
                },
                tooltip: {
                    formatter: function () {
                        return '<b>' + this.x + '</b><br/>' + this.y + ' 次<br/>'
                    }
                },
            });
        }

        function queryAnalysisUrl() {
            $scope.loading = Rest.all('detail/user/queryAnalysisUrl').post().then(function (response) {
                $scope.analysisUrl = response;
            });

        }

        /*       $scope.initEventMap = function (){
                   queryEvent()
               }
   */

        /*     function queryEvent(param) {
                 var code= param || 'all'
                 $scope.loading = Rest.all('detail/user/queryEvent?code='+code).post().then(function (response) {
                     $scope.eventCacheMap=response;
                     return $scope.eventCacheMap;
                 });
             }*/
        function getUserDetail() {
            // queryEvent('all')
            initStatus = {};
            initUserInfoStatus = {"baseInfo": true};
            $scope.userDetail = null;
            $scope.summary = null;
            $scope.riskLabel = null;
            $scope.expResult = null;
            // $scope.param.value='193600653368020026'
            $scope.loading = Rest.all('detail/user/info').post($scope.param).then(function (response) {

                if (response && response.uid != null) {
                    Messenger().post({type: 'success', message: '用户信息加载完成!'});
                    $scope.userDetail = response;
                    getUserExt(response); // 加载用户扩展信息
                    queryApps($scope.param.accountType);
                    if (response.uid) {
                        getChatroomTag(response.uid) // 获取聊天室标签
                        getLiveTag(response.uid) // 获取直播间标签
                        getRobot(response.uid) //获取是否是机器人
                        getActive(response); //加载用户活跃
                        // queryCSDDetail()
                        getActiveEventTop(response); //加载活跃前10

                        getActiveViolationEventTen(response);   //违规场景前10

                        getViolationText(response); //违规文本前50

                        getViolationImages(response); //违规图片前20

                        // fetchRelation(response.uid)

                        if (AuthService.hasPermission("system:detail:portrait")) {
                            showPortrait(response.uid)
                        } else {
                            fetchRelation(response.uid)
                        }
                        // showPortrait(response.uid)

                    }

                } else {
                    if ($scope.param.dimension != 'UID') {
                        Messenger().post({type: 'error', message: '加载用户信息失败! 注：非大陆手机号格式：(区号)手机号，如：(1)912875****'});
                    } else {
                        Messenger().post({type: 'error', message: '加载用户信息失败!'});
                    }
                }
            });
        };

        function getUserExt(user) {
            $scope.loading = Rest.all('detail/user/desensitizeMobile?userId=' + user.uid).post().then(function (response) {
                if (response && response.mobile) {
                    $scope.userDetail.userUid = user.uid;
                    $scope.userDetail.partialMobileNo = response.mobile;
                    $scope.userDetail.nationCode = response.nationCode;
                }
                $scope.loading = Rest.all('detail/user/grayList?uid=' + user.uid).post().then(function (res) { // 名单
                    $scope.grayList = res;
                });
            });
            $scope.loading = Rest.all('detail/user/associate?uid=' + user.uid + "&accountType=" + $scope.param.accountType).post().then(function (response) {
                if (response) {
                    if (response.qqUnionId) {
                        $scope.userDetail.qqUnionId = response.qqUnionId;
                    }
                    if (response.wechatUnionId) {
                        $scope.userDetail.wechatUnionId = response.wechatUnionId;
                    }
                }
            });
            $scope.loading = Rest.all('detail/user/exp?uid=' + user.uid).post().then(function (response) {
                var its = []
                for (var i = 0; i < response.length; i++) {
                    var it = response[i]
                    var type = it.vipType
                    if (type == 5 || type > 6) {
                        continue
                    }
                    if (!it.vipLevel) {
                        it.vipLevel = 0
                    }
                    its.push(it)
                }
                $scope.expResult = its
            });
            $scope.loading = Rest.all('detail/user/biggieInfo?uid=' + user.uid).post().then(function (response) {
                if (response) {
                    $scope.userDetail.biggieStatus = response.status;
                    $scope.userDetail.biggieFrozenReason = response.frozenReason;
                    $scope.userDetail.biggieFrozenEndTime = response.frozenEndTime;
                }
            });
            $scope.loading = Rest.all('detail/user/accountFreezeInfo?uid=' + user.uid + "&accountType=" + $scope.param.accountType).post().then(function (response) {
                $scope.userDetail.freezeInfos = response;
            });
            var param = {userId: user.uid, startTime: $scope.param.startTime, endTime: $scope.param.endTime};
            $scope.loading = Rest.all('detail/user/risk/summary').post(param).then(function (response) {
                $scope.summary = response;
            });
            $scope.loading = Rest.all('detail/user/relation/event').post(param).then(function (res) { // 风险标签
                initStatus['risk'] == true;
                $scope.riskLabel = res;
            });
        }

        // 获取聊天室标签
        function getChatroomTag(uid) {
            // 是否主持人
            $scope.loading = Rest.all('detail/user/isChatroomHost?uid=' + uid).post().then(function (response) {
                $scope.userDetail.isChatroomHost = response
            });

            // 是否官方房间、个人房间
            $scope.loading = Rest.all('detail/user/getChatroomRoomTag?uid=' + uid).post().then(function (response) {
                if (response) {
                    $scope.userDetail.isChatroomPerson = response.hasOwnProperty("person")
                    $scope.userDetail.isChatroomOfficial = response.hasOwnProperty("official")
                }
            });
        }

        // 获取直播间标签
        function getLiveTag(uid) {
            // 是否主播
            $scope.loading = Rest.all('detail/user/isAnchor?uid=' + uid).post().then(function (response) {
                $scope.userDetail.isAnchor = response
            });
        }

        // 获取是否是机器人
        function getRobot(uid) {
            // 是否机器人
            $scope.loading = Rest.all('detail/user/isRobot?uid=' + uid).post().then(function (response) {
                $scope.userDetail.liveRobot = response.liveRobot;
                $scope.userDetail.yuerRobot = response.yuerRobot;
            });
        }

        function fetchRelation(userId) {
            var param = {
                userId: userId,
                startTime: $scope.param.startTime,
                endTime: $scope.param.endTime
            }
            if (!AuthService.hasPermission("system:detail:portrait")) {
                $scope.loading = Rest.all('detail/user/relation/device').post(param).then(function (response) {
                    $scope.devices = response;
                    $scope.devicesDesc = "";
                })
            }

            $scope.loading = Rest.all('detail/user/relation/desensitizeMobile').post(param).then(function (response) {
                $scope.mobiles = response;
                $scope.paramCopy = param;
            });
            /*$scope.loading = Rest.all('detail/user/relation/ip').post(param).then(function (response) {
                $scope.ips = response;
            });*/
            $scope.loading = Rest.all('detail/user/relation/ip/list').post(param).then(function (response) {
                $scope.ips = response;
            });

        }

        function compare(count) {
            return function (a, b) {
                var value1 = a[count];
                var value2 = b[count];
                return value2 - value1;
            }
        }

        function emptyObject(obj) {
            for (var k in obj) {
                return false
            }
            return true
        }

        function showPortrait(uid) {
            //TODO 画像赋值
            $scope.devices = {}
            $scope.appVersions = {}
            // $scope.mobiles = []
            // $scope.ips = []
            var param = {
                userId: uid,
                startTime: $scope.param.startTime,
                endTime: $scope.param.endTime
            }
            // $scope.loading = Rest.all('detail/user/relation/device').post(param).then(function (response) {
            //     $scope.devices = [];
            $scope.showPortraitTag = false
            $scope.commonUseDevice = {"deviceId": "", "count": 0};
            $scope.ordinaryCity = {"city": "", "count": 0};
            //$scope.userDetail.uid
            $scope.portraitObj = null;
            $scope.loading = Rest.all('detail/user/portrait').post({
                "value": uid,
                "startTime": $scope.param.startTime,
                "endTime": $scope.param.endTime
            }).then(function (response) {
                var map = {};
                if (response && !emptyObject(response["portraitVO"])) {
                    //同一个type
                    $scope.portraitObj = response["portraitVO"]
                    var portraitVO = response["portraitVO"]
                    $scope.portraitObj = portraitVO[uid];

                    /*  if ($scope.portraitObj.userIdRelateMobileMap7d) {

                          var mobileTemp = JSON.parse($scope.portraitObj.userIdRelateMobileMap7d.value)
                          for (var mobileNo in mobileTemp) {
                              $scope.mobiles.push({"mobileNo": mobileNo, "count": mobileTemp[mobileNo]})
                          }
                          $scope.mobiles.sort(compare("count"))
                      }*/

                    if ($scope.portraitObj) {
                        //常用设备
                        var tempDeviceMap = $scope.portraitObj.userIdRelateDeviceIdMap7d
                        if (tempDeviceMap) {
                            var deviceValue = tempDeviceMap.value
                            if (deviceValue) {
                                var json = JSON.parse(deviceValue)
                                for (var dk in json) {
                                    if (json[dk] > $scope.commonUseDevice.count) {
                                        $scope.commonUseDevice.deviceId = dk
                                        $scope.commonUseDevice.count = json[dk]
                                    }
                                    // $scope.devices[dk] = {"count": json[dk], "tag": null, "devicePortrait": null}
                                }
                            }
                        }

                        //常住城市
                        var tempCityMap = $scope.portraitObj.userIdRelateIpCityMap7d
                        if (tempCityMap) {

                            var cityValue = tempCityMap.value
                            if (cityValue) {
                                var cityJson = JSON.parse(cityValue)
                                for (var ck in cityJson) {
                                    var strings = ck.split("$");
                                    if (strings && strings.length >= 2) {
                                        if (strings[0] == 0 || strings[0] == "0") {
                                            strings[0] = null;
                                        }
                                        // $scope.ips.push({"ip": strings[1], "city": strings[0], "count": cityJson[ck]})
                                    }
                                    if (cityJson[ck] > $scope.ordinaryCity.count) {
                                        $scope.ordinaryCity.city = strings[0]
                                        $scope.ordinaryCity.count = cityJson[ck]

                                    }

                                }
                            }


                        }

                        var tempAppVersionMap = $scope.portraitObj.userIdRelateAppVersionMap7d
                        if (tempAppVersionMap) {
                            var appVersionValue = tempAppVersionMap.value
                            if (appVersionValue) {
                                var json = JSON.parse(appVersionValue)
                                for (var ak in json) {
                                    var aks = ak.split("$");
                                    var detail = {};
                                    detail.platform = aks.length > 0 ? aks[0] : "";
                                    detail.app = aks.length > 1 ? aks[1] : "";
                                    detail.productId = aks.length > 2 ? aks[2] : "";
                                    detail.version = aks.length > 3 ? aks[3] : "";
                                    detail.count = json[ak];
                                    $scope.appVersions[ak] = detail;
                                }
                            }
                        }

                        /*  if ($scope.ips.length > 0) {
                              $scope.ips.sort(compare('count'))
                          }*/

                    }
                    var reg = /^[0-9]*$/;
                    for (var key in portraitVO) {

                        var tag = portraitVO[key]
                        if (key && !reg.test(key)) {

                            var deviceTag = {}
                            var subDeviceTag = {}
                            // {"count": json[dk], "tag": deviceTag, "devicePortrait": null}
                            for (var k in tag) {

                                if (k == 'count') {
                                    deviceTag['count'] = tag[k]
                                    continue
                                }

                                if (k == 'grayList') {
                                    var grayList = tag[k];

                                    var grays = new Array();
                                    for (var t in grayList) {
                                        var subGray = {};
                                        subGray.groupName = grayList[t].grayGroup.name;
                                        subGray.type = grayList[t].type;
                                        subGray.comment = grayList[t].comment;
                                        subGray.author = grayList[t].author;
                                        subGray.startTime = grayList[t].startTime;
                                        subGray.expireTime = grayList[t].expireTime;

                                        grays.push(subGray);
                                    }

                                    deviceTag['grayList'] = grays;
                                    continue;
                                }

                                var sub = tag[k]
                                var parentName = sub.typeName;
                                var subTag = {};
                                subTag.tagName = sub.tagName;
                                subTag.tagCode = k
                                subTag.updateTime = sub.updateTime;
                                subTag.value = sub.value;

                                if ('devicePortrait' != k) {
                                    if (sub.value == false || sub.value <= 0) {
                                        //过滤无效值标签
                                        continue
                                    }
                                }

                                if (subDeviceTag[parentName]) {
                                    var tmpList = subDeviceTag[parentName]
                                    tmpList.push(subTag);
                                    subDeviceTag[parentName] = tmpList;
                                } else {
                                    var tagList = new Array();
                                    tagList.push(subTag)
                                    subDeviceTag[parentName] = tagList
                                }
                                if ('devicePortrait' == k) {
                                    //设备评级
                                    deviceTag['devicePortrait'] = subTag
                                }
                            }
                            deviceTag['tag'] = subDeviceTag
                            $scope.devices[key] = deviceTag
                        }
                        for (var k in tag) {
                            if (k == 'userIdActiveDays7d' || k == 'userIdRelateDeviceIdCnt1d' ||
                                k == 'userIdRelateDeviceIdCnt7d' || k == 'userIdRelateIpCityCnt1d'
                                || k == 'userIdRelateDeviceIdMap7d' || k == 'userIdRelateIpCityMap7d' ||
                                k == 'userIdLoginCnt7d' || k == 'userIdLoginCnt1d' || 'devicePortrait' == k
                                || k == 'userIdRelateMobileMap7d' || k == 'userIdRelateIpCityCnt7d' || k == 'count'
                                || k == 'userIdLoginSuccessCnt7d' || k == 'userIdLoginSuccessCnt1d'
                            ) {
                                //过滤部分账号相关标签
                                continue
                            }
                            var sub = tag[k]
                            if ("设备频度相关" == sub.typeName || "设备关联相关" == sub.typeName || "设备通用标签" == sub.typeName || typeof(sub.typeName) == "undefined") {
                                continue
                            }

                            if (emptyObject(sub.value) == false && sub.typeName != "举报处理标签") {
                                continue
                            }
                            var parentName = sub.typeName;
                            var subTag = {};
                            subTag.tagName = sub.tagName;
                            subTag.tagCode = k
                            subTag.updateTime = sub.updateTime;
                            subTag.value = sub.value;
                            //过滤无效标签值
                            if (sub.value == false) {
                                continue
                            }
                            if (map[parentName]) {
                                var tmpList = map[parentName]
                                if (containTagCode(tmpList, k) == false) {
                                    map[parentName].push(subTag);
                                }
                            } else {
                                var tagList = new Array();
                                tagList.push(subTag)
                                map[parentName] = tagList
                            }
                        }

                    }
                    //1835212105700006
                    $scope.portraitTag = map
                    if (JSON.stringify($scope.portraitTag) == "{}") {
                        $scope.showPortraitTag = true;
                    }
                } else {
                    $scope.showPortraitTag = !emptyObject(response);
                }

            });
            // });
        };

        function containTagCode(list, code) {
            for (var i = 0; i < list.length; i++) {
                var tmpObj = list[i]
                if (tmpObj.tagCode == code) {
                    return true
                }
            }
            return false;
        }

        $scope.UpageChanged = function (page) {
            // $scope.page=page
            queryOrder(page)
        };
        $scope.payPageChanged = function (page) {
            $scope.payPage = page
            // $scope.page=page
            if ($scope.cashType == 'incomeJournal') {
                goIncomeJournal(page)
            } else if ($scope.cashType == 'diamondJournal') {
                goDiamondJournal(page)
            } else if ($scope.cashType == 'charmJournal') {
                goCharmJournal(page)
            } else if ($scope.cashType == 'starDiamondJournal') {
                goStarDiamondJournal(page)
            } else if ($scope.cashType == 'starJournal') {
                goStarJournal()
            } else {
                goCashJournal(page)
            }

        };
        $scope.KFPageChanged = function (page) {
            // $scope.page=page
            queryCSDRecord(page)
            // goCashJournal(page)
        };

        $scope.searchPageChanged = function(page){
            querySearchRecord(page);
        };

        $scope.download = function(){
            if ($scope.userDetail.uid) {

                const request = {};
                request.startTime =  $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid;

                $scope.loading = Rest.all('detail/user/download').post(request).then(function(response){
                    window.open("/common/download?fileName=" + response + "&delete=true", '_blank');
                });
            }
        };

        //查询订单记录
        function queryOrder(page) {

            if ($scope.userDetail.uid) {
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid;
                $scope.page = page || $scope.page || 1;
                // request.value = '192680946574710068';
                $scope.loading = Rest.all('detail/user/orderRecord?p=' + $scope.page).post(request).then(function (response) {
                    // $scope.orderData = response;
                    function getCoverValue(isAppeal) {
                        if (isAppeal == 0) {
                            return "否"
                        } else if (isAppeal == 1) {
                            return "是"
                        }
                        return undefined;
                    }

                    $scope.orderData = [];
                    $scope.total = 0;
                    $scope.total = 0;
                    if (response.list) {
                        for (var i = 0; i < response.list.length; i++) {
                            response.list[i].url = $scope.analysisUrl.orderUrl;
                        }
                        $scope.orderData = response.list;
                        $scope.total = response.total;

                    }
                });
            }
        }


        $scope.showOrderDetail = function (order) {
            $aside.open({
                templateUrl: 'analysis/user/view/orderDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.orderDetail = order;
                }
            });

        };
        $scope.showCustomerRecordDetail = function (csdVO) {
            // var detail = queryCSDDetail(csdVO)
            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = csdVO.taskId
            $scope.loading = Rest.all('detail/user/csdDetail').post(request).then(function (response) {
                $scope.csdVODetail = response.result;
                $scope.picturePropInterval = 5000;
                $scope.picturePropNoWrapSlides = false;
                $scope.picturePropActive = 0;
                $aside.open({
                    templateUrl: 'analysis/user/view/customerSRDetail.html',
                    placement: 'right',
                    size: 'md',
                    animation: false,
                    controller: function ($scope) {
                        $scope.csdVODetail = response.result;
                    }
                });
            });


        };

        $scope.appealInterval = 5000;
        $scope.appealNoWrapSlides = false;
        $scope.appealActive = 0;
        $scope.appealSlides = [];
        $scope.showAppealDetail = function (appeal) {
            $aside.open({
                templateUrl: 'analysis/user/view/appealDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.appealDetail = appeal;
                    $scope.appealInterval = 5000;
                    $scope.appealNoWrapSlides = false;
                    $scope.appealActive = 0;
                    $scope.appealSlides = appeal.images;
                }
            });

        };
        $scope.punishInterval = 5000;
        $scope.punishNoWrapSlides = false;
        $scope.punishActive = 0;
        $scope.punishSlides = [];
        $scope.showPunishDetail = function (punish) {
            $aside.open({
                templateUrl: 'analysis/user/view/punishDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.punishDetail = punish;
                    $scope.punishInterval = 5000;
                    $scope.punishNoWrapSlides = false;
                    $scope.punishActive = 0;
                    $scope.punishSlides = punish.evidences;
                }
            });

        }
        $scope.complainInterval = 5000;
        $scope.complainNoWrapSlides = false;
        $scope.complainActive = 0;
        $scope.complainSlides = [];
        $scope.showComplainDetail = function (complain) {
            $aside.open({
                templateUrl: 'analysis/user/view/complainDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.complainDetail = complain;
                    $scope.complainInterval = 5000;
                    $scope.complainNoWrapSlides = false;
                    $scope.complainActive = 0;
                    $scope.complainSlides = complain.picUrls;
                }
            });

        };

        $scope.showCashDetail = function (cash, cashTableDetailHeader) {
            $aside.open({
                templateUrl: 'analysis/user/view/cashDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.cashDetail = cash;
                    $scope.cashTableDetailHeader = cashTableDetailHeader;
                }
            });

        };

        //查询客服记录
        function queryCSDRecord(page) {
            if ($scope.userDetail.uid) {

                $scope.KFPage = page || $scope.KFPage || 1;
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                // request.value = $scope.userDetail.uid
                request.value = $scope.userDetail.showNo
                $scope.loading = Rest.all('detail/user/csdRecord?p=' + $scope.KFPage).post(request).then(function (response) {

                    if (response.list) {
                        for (var i = 0; i < response.list.length; i++) {
                            response.list[i].url = $scope.analysisUrl.cdsUrl
                        }
                    }
                    $scope.csdData = response.list;
                    $scope.KFtotal = response.total
                });
            }
        }

        //查询搜索明细
        function querySearchRecord(page){
            if ($scope.userDetail.uid) {
                $scope.searchPage = page || $scope.searchPage || 1;

                const request = {};
                request.startTime =  $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid;

                $scope.loading = Rest.all('detail/user/searchRecord?p=' + $scope.searchPage).post(request).then(function(response){

                    $scope.searchData = response.list;
                    $scope.searchTotal = response.total;
                });
            }
        }

        //查询申诉记录
        function queryAppealRecord(user) {
            // if (user.userId) {
            // var request = $scope.param;
            // 0=未知，10=比心，20=鱼耳语音，30=鱼耳直播

            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid;
            $scope.loading = Rest.all('detail/user/appealRecord').post(request).then(function (response) {
                for (var i = 0; i < response.length; i++) {
                    if (response[i].appId == 10) {
                        response[i].appId = '比心';
                    } else if (response[i].appId == 20) {
                        response[i].appId = '鱼耳语音';
                    } else if (response[i].appId == 30) {
                        response[i].appId = '鱼耳直播';
                    } else if (response[i].appId == 80) {
                        response[i].appId = '咪糖';
                    } else if (response.result[i].appId == 0) {
                        response[i].appId = '未知';
                    }
                }
                $scope.appealData = response;
            });
            // }
        }



        //查询资料卡数据
        function queryDataCard() {
            $scope.loading = Rest.all('detail/user/dataCard?uid=' + $scope.userDetail.uid).post().then(function (response) {
                $scope.dataCardDetail = response;
            });
        }

        //查询惩罚记录
        function queryPunishRecord(user) {
            if (user.uid) {
                // var request = $scope.param;

                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid;
                $scope.loading = Rest.all('detail/user/punishRecord').post(request).then(function (response) {
                    $scope.punishData = response.result;
                });
            }
        }

        //查询举报记录
        function queryComplainRecord(user) {
            // if (user.userId) {
            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.loading = Rest.all('detail/user/complainRecord').post(request).then(function (response) {
                $scope.complainData = response;
                for (var i = 0; i < response.length; i++) {
                    response[i].url = $scope.analysisUrl.hisComplainUrl
                }
            });
        }

        function goCashJournal(page) {
            // var request={};
            $scope.cashType = 'cashJournal';
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的比心币(余额)')
            $scope.cashTableHeader.push('变动的比心币(余额)')
            $scope.cashTableHeader.push('变动的赠送金')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的比心币(余额)'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的比心币(余额)'
            $scope.cashTableDetailHeader.userIdGive = '变动的赠送金'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []

            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryCashJournal?p=' + $scope.payPage).post(request).then(function (response) {

                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    if (!response.list[i].give) {
                        response.list[i].give = ' ';
                    }
                    // response.list[i].url='https://pay.yupaopao.com/account/details/balance-journal'
                    response.list[i].url = $scope.analysisUrl.cashJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total;

            });
        }

        $scope.queryCashJournal = function (page) {
            //比心币(余额)流水
            $scope.payPage = page
            goCashJournal(page);
        }

        $scope.queryIncomeJournal = function (page) {
            $scope.payPage = page
            goIncomeJournal(page)
        }

        //大神收入流水
        function goIncomeJournal(page) {
            $scope.cashType = 'incomeJournal';
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的大神收入')
            $scope.cashTableHeader.push('变动的大神收入')
            $scope.cashTableHeader.push('来源用户id')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的大神收入'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的大神收入'
            $scope.cashTableDetailHeader.fromUserId = '来源用户id'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []
            // if (user.userId) {

            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid;
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryIncomeJournal?p=' + $scope.payPage).post(request).then(function (response) {

                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    // response.list[i].url='http://test-pay.yupaopao.com/account/details/income-journal'
                    response.list[i].url = $scope.analysisUrl.incomeJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total
                $scope.payPage = page
            });
            // }
        }

        $scope.queryDiamondJournal = function (page) {
            $scope.payPage = page
            goDiamondJournal(page)
        }

        //钻石流水
        function goDiamondJournal(page) {
            $scope.cashType = 'diamondJournal';
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的钻石')
            $scope.cashTableHeader.push('变动的钻石')
            $scope.cashTableHeader.push('外部关联id')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的钻石'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的钻石'
            $scope.cashTableDetailHeader.fromUserId = '来源用户id'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []
            // if (user.userId) {
            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryDiamondJournal?p=' + $scope.payPage).post(request).then(function (response) {
                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    // response.list[i].url='http://test-pay.yupaopao.com/account/details/diamond-journal'
                    response.list[i].url = $scope.analysisUrl.diamondJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total
            });
            // }

        }

        $scope.queryCharmJournal = function (page) {
            $scope.payPage = page
            goCharmJournal(page)
        }

        //魅力值流水
        function goCharmJournal(page) {
            $scope.cashType = 'charmJournal';
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的魅力值')
            $scope.cashTableHeader.push('变动的魅力值')
            $scope.cashTableHeader.push('外部关联用户ID')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的魅力值'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的魅力值'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.fromUserId = '外部关联用户ID'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.productName = '关联产品名称'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []
            // if (user.userId) {

            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryCharmJournal?p=' + $scope.payPage).post(request).then(function (response) {
                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    // response.list[i].url='http://test-pay.yupaopao.com/account/details/charm-journal'
                    response.list[i].url = $scope.analysisUrl.charmJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total
            });
            // }
        }

        $scope.queryStarDiamondJournal = function (page) {
            $scope.payPage = page
            goStarDiamondJournal(page)
        }

        //星钻流水
        function goStarDiamondJournal(page) {
            $scope.cashType = 'starDiamondJournal';
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的星钻')
            $scope.cashTableHeader.push('变动的星钻')
            $scope.cashTableHeader.push('外部关联id')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的星钻'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的星钻'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []
            // if (user.userId) {
            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryStarDiamondJournal?p=' + $scope.payPage).post(request).then(function (response) {

                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    // response.list[i].url='http://test-pay.yupaopao.com/account/details/starDiamond-journal'
                    response.list[i].url = $scope.analysisUrl.starDiamondJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total
            });
            // }
        }

        $scope.queryStarJournal = function (page) {
            $scope.payPage = page
            goStarJournal(page)
        }

        //星动值流水
        function goStarJournal(page) {
            $scope.cashTableHeader = new Array();
            $scope.cashTableHeader.push('创建时间')
            $scope.cashTableHeader.push('流水类型')
            $scope.cashTableHeader.push('变动前的星动值')
            $scope.cashTableHeader.push('变动的星动值')
            $scope.cashTableHeader.push('外部关联id')
            $scope.cashTableHeader.push('备注')
            $scope.cashTableHeader.push('操作')

            $scope.cashTableDetailHeader = {}
            $scope.cashTableDetailHeader.appIdName = '应用id'
            $scope.cashTableDetailHeader.userIdName = '用户id'
            $scope.cashTableDetailHeader.userIdBXB = '变动的星动值'
            $scope.cashTableDetailHeader.userIdBXBPre = '变动前的星动值'
            $scope.cashTableDetailHeader.userIdUnionId = '外部关联id'
            $scope.cashTableDetailHeader.userIdType = '流水类型'
            $scope.cashTableDetailHeader.userIdJournalType = '流水业务类型'
            $scope.cashTableDetailHeader.userCreateTime = '创建时间'
            $scope.cashTableDetailHeader.userIdRemark = '备注'
            $scope.cashJournalData = []
            $scope.cashType = 'starJournal';
            // if (user.userId) {

            var request = {};
            request.startTime = $scope.param.startTime;
            request.endTime = $scope.param.endTime;
            request.value = $scope.userDetail.uid
            $scope.payPage = page || $scope.payPage || 1;
            $scope.loading = Rest.all('detail/user/queryStarJournal?=' + $scope.payPage).post(request).then(function (response) {
                for (var i = 0; i < response.list.length; i++) {
                    if (response.list[i].changeType == 'I') {
                        response.list[i].changeType = '收入'
                    } else if (response.list[i].changeType == 'O') {
                        response.list[i].changeType = '支出'
                    }
                    // response.list[i].url='http://test-pay.yupaopao.com/account/details/star-journal'
                    response.list[i].url = $scope.analysisUrl.starJournalUrl
                }
                $scope.cashJournalData = response.list;
                $scope.payTotal = response.total
            });
            // }
        }

        $scope.getOriginImg = function (content) {
            if (!content) {
                return "";
            }
            if (content.lastIndexOf('?') === -1) {
                return content;
            }
            return content.slice(0, content.lastIndexOf('?'));
        }

        $scope.getThumb = function (content) {
            var originSrc = $scope.getOriginImg(content);
            if (originSrc) {
                return originSrc + '?imageView2/2/w/300/h/200';
            }
        }

        $scope.getPic = function (content) {
            var originSrc = $scope.getOriginImg(content);
            if (originSrc) {
                return originSrc + '?imageView2/2/w/600/h/400';
            }
        }

        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            $scope.eventsMap = result.data;
        });

        $scope.code2Name = function (code) {
            return $scope.eventsMap[code];
        }

        $scope.search = function () {
            var id = $scope.param.value || '';
            var reg = /^[0-9]*$/;
            var regShowNo = /^[1-9]\d{0,8}$/;
            var len = id.length;
            if (regShowNo.test(id)) {
                console.log('目测输入值是ShowNo');
                $scope.param.dimension = 'YPPNO';
            } else if ((len < 11 && len > 6) || (len > 11 && len < 15)
                || (len == 11 && reg.test(id) && id.startsWith("1"))
                || (id.indexOf("(") > -1)) {
                console.log('目测输入值是Mobile');
                $scope.param.dimension = 'MOBILENO';
            } else if (len >= 15 && reg.test(id)) {
                console.log('目测输入值是UID');
                $scope.param.dimension = 'UID';
            } else {
                Messenger().post({type: 'error', message: '请输入用户标识，如UID或ShowNo或手机号码'});
                return false;
            }
            if (null == $scope.param.endTime || null == $scope.param.startTime) {
                Messenger().post({type: 'error', message: '搜索时间范围必选'});
                return false;
            }
            var timeSpan = ($scope.param.endTime.getTime() - $scope.param.startTime.getTime()) / 1000 / 60 / 60 / 24; // 时间跨度-天
            if (timeSpan > 30) {
                Messenger().post({type: 'error', message: '时间跨度不允许超过30天'});
                return false;
            }
            queryAnalysisUrl();
            getUserDetail();
        };


        function checkMobile(value) {

        }

        //活跃日志统计
        function getActive(user) {
            $scope.activeChartConfig = noDataHighChart('', 100)
            if ($scope.userDetail.uid) {
                $scope.activeFresh = true;
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid;
                $scope.loading = Rest.all('user/risk/log/active').post(request).then(function (response) {
                    $scope.activeData = new Array();
                    $scope.activeTimeList = new Array();
                    for (var i = 0; i < response.length; i++) {
                        $scope.activeData.push(response[i].count);
                        // activeO.push(response[i].count)
                        // activeT.push(response[i].createTime)
                        $scope.activeTimeList.push(response[i].createTime);
                    }
                    $scope.activeChartConfig = flushHighChart($scope.activeData, $scope.activeTimeList, 'area')
                    if ($scope.activeData.length <= 0) {
                        $scope.activeChartConfig = noDataHighChart('没有活跃日志', 100)
                    }
                    if ($scope.activeChartConfig) {
                        Highcharts.chart('activeChart', $scope.activeChartConfig)
                    }
                    $scope.activeFresh = false;
                });
            }

        }

        //活跃前10事件
        function getActiveEventTop(user) {
            $scope.activeSceneChartConfig = noDataHighChart('', 100)
            if ($scope.userDetail.uid) {
                $scope.activeSceneFresh = true;
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid
                $scope.loading = Rest.all('user/risk/log/activeSceneTop').post(request).then(function (response) {
                    $scope.activeTopData = new Array();
                    $scope.activeTopEvent = new Array();
                    for (var i = 0; i < response.length; i++) {
                        $scope.activeTopData[i] = response[i].count
                        var code = response[i].eventCode;

                        // if ($scope.eventCacheMap) {
                        var eventName = $scope.eventsMap[code]
                        if (eventName) {
                            $scope.activeTopEvent.push(eventName);
                        }
                        // }


                    }
                    $scope.activeSceneChartConfig = flushHighChart($scope.activeTopData, $scope.activeTopEvent, 'column')
                    if ($scope.activeTopData.length <= 0) {
                        $scope.activeSceneChartConfig = noDataHighChart('没有活跃场景', 100)
                    }
                    if ($scope.activeSceneChartConfig) {
                        Highcharts.chart('activeSceneChart', $scope.activeSceneChartConfig)
                    }

                    // }
                    $scope.activeSceneFresh = false;
                });

            }

        }

        //违规前十事件
        function getActiveViolationEventTen(user) {
            $scope.activeViolationSceneConfig = noDataHighChart('', 100)
            if ($scope.userDetail.uid) {
                $scope.violationFresh = true;
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid
                $scope.loading = Rest.all('user/risk/log/violationSceneTop').post(request).then(function (response) {
                        $scope.violationTopData = new Array();
                        $scope.violationTopEvent = new Array();
                        for (var i = 0; i < response.length; i++) {
                            $scope.violationTopData[i] = response[i].count
                            // $scope.violationTopEvent[i]=response[i].eventCode;
                            var code = response[i].eventCode;
                            // if ($scope.eventCacheMap){
                            var eventName = $scope.eventsMap[code]
                            if (eventName) {
                                $scope.violationTopEvent.push(eventName);
                            }
                            // }
                        }

                        $scope.activeViolationSceneConfig = flushHighChart($scope.violationTopData, $scope.violationTopEvent, 'column');
                        if ($scope.violationTopData.length <= 0) {
                            $scope.activeViolationSceneConfig = noDataHighChart('没有违规事件', 100)
                        }
                        if ($scope.activeViolationSceneConfig) {
                            Highcharts.chart('activeViolationSceneChart', $scope.activeViolationSceneConfig)
                        }
                        $scope.violationFresh = false;
                    }
                );
            }
        }

        //违规前50文本
        function getViolationText(user) {

            $scope.wordCloud = noDataHighChart('', 280)
            if ($scope.userDetail.uid) {
                $scope.wordCloudFresh = true
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid
                $scope.loading = Rest.all('user/risk/log/violationTextTop').post(request).then(function (response) {
                    $scope.violationTextTopData = new Array();
                    $scope.violationTextTopCount = new Array();

                    function checkZipNum(response) {
                        var minVal;
                        var maxVal;
                        for (var i = 0; i < response.length; i++) {
                            var count = response[i].weight
                            if (!minVal || minVal > count) {
                                minVal = count;
                            }
                            if (!maxVal || maxVal < count) {
                                maxVal = count;
                            }

                        }
                        if (maxVal - minVal > 8) {
                            return true;
                        }
                        return false;
                    }

                    if (response) {
                        var zipFlag = checkZipNum(response);
                        for (var i = 0; i < response.length; i++) {
                            var name = response[i].name
                            var count = response[i].weight
                            var abbrName = "";
                            if (name.length > 9) {
                                var abbrName = t + "..."
                            } else {
                                abbrName = name
                            }
                            var w = count;
                            if (zipFlag) {
                                w = Math.ceil(count / 8)
                            }
                            $scope.violationTextTopData.push({name: abbrName, weight: w, desc: name, count: count})
                        }
                    }
                    $scope.wordCloud = {
                        "title": {"text": ""},
                        "chart": {"height": 280},
                        series: [{
                            type: 'wordcloud',
                            data: $scope.violationTextTopData,
                        }],
                        xAxis: {
                            categories: $scope.violationTextTopData,
                        },
                        tooltip: {
                            formatter: function () {

                                var reallyVal = "";
                                var size = this.x.desc.length;
                                var splitNum = 18;
                                if (size > splitNum) {
                                    if (this.x.desc.length && this.x.desc.length > splitNum) {
                                        var rowNum = Math.ceil(this.x.desc.length / splitNum)
                                        var startIndex = 0;
                                        // var adjustFlag =false;
                                        for (var i = 0; i < rowNum; i++) {
                                            if (startIndex >= size) {
                                                continue
                                            }
                                            var sub = this.x.desc.substr(startIndex, splitNum)

                                            reallyVal = reallyVal + '<b>' + sub + "</b><br/>"
                                            startIndex = startIndex + splitNum
                                        }

                                    }
                                } else {
                                    reallyVal = '<b>' + this.x.desc + "</b><br/>"
                                }

                                //重新生成
                                var content = reallyVal
                                    + '<b style="font-size:10px">命中' + this.x.count + '次</b>';
                                return content;
                            }
                        },
                        credits: {
                            enabled: false     //不显示LOGO
                        }
                    };
                    if ($scope.violationTextTopData.length <= 0) {
                        $scope.wordCloud = noDataHighChart('没有发布违禁文本', 280)
                    }

                    if ($scope.violationTextTopData) {
                        Highcharts.chart('violationText', $scope.wordCloud)
                    }
                    $scope.wordCloudFresh = false
                });

            }
        }

        //违规前20图片
        function getViolationImages(user) {
            $scope.slides = new Array();
            if ($scope.userDetail.uid) {
                $scope.violationImageFresh = true;
                $scope.violationImageText = true;
                $scope.violationImageTextShow = false;
                //     var request = $scope.param;
                var request = {};
                request.startTime = $scope.param.startTime;
                request.endTime = $scope.param.endTime;
                request.value = $scope.userDetail.uid
                $scope.loading = Rest.all('user/risk/log/violationImageTop').post(request).then(function (response) {
                    for (var i = 0; i < response.length; i++) {
                        $scope.slides.push({id: i, image: response[i].data_images});
                    }
                    $scope.violationImageFresh = false
                    if ($scope.slides.length <= 0) {
                        $scope.violationImageText = true;
                        $scope.violationImageTextShow = true
                    } else {
                        $scope.violationImageText = false;
                        $scope.violationImageTextShow = false
                    }
                });

            }
        }

        if ($scope.param.value) {
            $scope.search();
        }

        $scope.correction = function () {
            if (!$scope.userDetail) {
                Messenger().post({type: 'error', message: '请对指定用户进行清洗！'});
                return;
            }

            Rest.all('detail/user/blackGray?uid=' + $scope.userDetail.uid).post().then(function (response) {
                CleanDataService.detail(response, CleanResultService, $scope.userDetail.uid, function () {
                });
            });

//            modal.confirm('确定进行清洗吗 ？', function () {
//                Rest.all('detail/user/correction?uid=' + $scope.userDetail.uid).post().then(function (response) {
//                    if (response.success) {
//                        Messenger().post({type: 'success', message: '清洗成功!'});
//                    } else {
//                        Messenger().post({type: 'error', message: response.message});
//                    }
//                })
//            });
        };

        $scope.his = function () {
            if (!$scope.userDetail) {
                Messenger().post({type: 'error', message: '请先获取用户信息！'});
                return;
            }
//            $state.go('history.log', {userId: $scope.userDetail.uid});
            openUrlNew = $state.href('history.log', {userId: $scope.userDetail.uid});
            window.open(openUrlNew, '_blank')
        };

        $scope.punishLog = function () {
            if (!$scope.userDetail) {
                Messenger().post({type: 'error', message: '请先获取用户信息！'});
                return;
            }
//            $state.go('history.log', {userId: $scope.userDetail.uid});
            openUrlNew = $state.href('riskPunish.abilityRecord', {objId: $scope.userDetail.uid, objType: "uid"});
            window.open(openUrlNew, '_blank')
        };

        $scope.punish = function () {
            if (!$scope.userDetail) {
                Messenger().post({type: 'error', message: '请先获取用户信息！'});
                return;
            }
            let uid = $scope.userDetail.uid;
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'analysis/user/view/punish.html',
                controller: function (rest, $scope, Upload, modal, $uibModalInstance) {
                    $scope.param = {
                        'uid': uid,
                        'proofImgUrl': '',
                        'deviceId': '',
                        'APPID': '',
                        'punishDevice': '0'
                    };

                    $scope.loading = Rest.all('punish/getPackagesAndObjectTypes').post({channel: 'REALTIME_RISK_PUNISH'}).then(function (response) {
                        $scope.packageIdList = response.packages;
                    });

                    Rest.all('punish/getRecentLoginDevice').post({uid: uid}).then(function (response) {
                        $scope.loginDevices = response;
                    });

                    $scope.toPunish = function () {
                        var punishDevice = angular.copy($scope.param.punishDevice);
                        if (punishDevice == '0') {
                            $scope.isPunishDevice = false;
                            $scope.param.deviceId = '';
                            $scope.param.APPID = '';
                        } else if (punishDevice == '1') {
                            $scope.isPunishDevice = true;
                        }
                    };

                    $scope.punishDeviceInfo = function (device) {
                        $scope.param.deviceId = device.deviceId;
                        $scope.param.APPID = device.APPID;
                    };

                    $scope.upload = function () {
                        $scope.log = '';
                        Upload.upload({
                            url: 'https://cdn.hibixin.com/cdn/backStage/upload',
                            fields: {
                                "bizType": "baseRisk",
                                "fileType": "4"
                            },
                            file: $scope.file
                        }).success(function (data, status, headers, config) {
                            if (data && ("8000" == data['code']) && data['result']) {
                                $scope.param.proofImgUrl = data['result']['domain'] + data['result']['url'];
                            } else {
                                Messenger().post({
                                    type: 'error',
                                    message: '上传图片接口出错! Response: ' + JSON.stringify(data)
                                });
                            }
                        });
                    };
                    $scope.$watch('file', function () {
                        if ($scope.file) {
                            $scope.upload();
                        }
                    });

                    $scope.confirm = function () {
                        if ($scope.isPunishDevice && $scope.param.deviceId == '') {
                            Messenger().post({type: 'error', message: '请选择需要惩罚的设备!'});
                            return;
                        }
                        if (!$scope.file) {
                            Messenger().post({type: 'error', message: '请添加图片凭证!'});
                            return;
                        }

                        let realParam = {
                            "objectIds": $scope.param.uid,
                            "objectType": "UID",
                            "packageId": $scope.param.packageId,
                            "internalReason": $scope.param.internalReason,
                            "externalReason": $scope.param.externalReason,
                            "proofImgUrl": $scope.param.proofImgUrl,
                            "deviceId": $scope.param.deviceId,
                            "appId": $scope.param.APPID,
                            "channel": "REALTIME_RISK_PUNISH"
                        };

                        $scope.loading = Rest.all('/punish/doPunish').post(realParam).then(function (res) {
                            if (!res.success) {
                                Messenger().post({type: 'error', message: res.message});
                            } else {
                                Messenger().post({type: 'success', message: '操作成功'});
                                $uibModalInstance.close();
                            }
                        });
                    };
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                }
            });
        };
        $scope.customTime = function () {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'analysis/user/view/customTime.html',
                controller: function (rest, $scope, modal, $uibModalInstance) {
                    $scope.defaultDaysRange = defaultDaysRange + "";
                    $scope.customTimes = [];
                    Rest.all('detail/user/getCustomTimes').post().then(function (response) {
                        $scope.customTimes = response;
                    });
                    $scope.confirm = function () {
                        localStorage.setItem("defaultDaysRange", $scope.defaultDaysRange);
                        $uibModalInstance.close();
                    };
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                }
            });
        };

        $scope.deviceCleaning = function () {
            let defaultDeviceId;
            if ($scope.userDetail && $scope.devices) {
                defaultDeviceId = Object.keys($scope.devices)[0];
            }

            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'analysis/user/view/deviceCleaning.html',
                controller: function (rest, $scope, Upload, modal, $uibModalInstance) {
                    $scope.deviceId = defaultDeviceId;
                    $scope.confirm = function () {
                        $scope.loading = Rest.all('/detail/user/deviceCleaning?deviceId=' + $scope.deviceId).post({}).then(function (res) {
                            Messenger().post({type: 'success', message: '设备画像清洗成功'});
                        });
                        $uibModalInstance.close();
                    };
                    $scope.close = function () {
                        $uibModalInstance.close();
                    }
                }
            });
        };

        $scope.hasPermission = function (permissionName) {
            //hasPermission('system:detail:portrait')
            var permission = AuthService.hasPermission(permissionName);
            // permission=false;
            if ("system:detail:portrait" == permissionName && permission) {
                $scope.portraitPermission = true
                $scope.riskClass = 'risk'
                $scope.tabRiskClass = 'tab-pane risk'
                $scope.portraitClass = 'active portrait'
                $scope.tabPortraitClass = 'tab-pane portrait active'
            } else if ("system:detail:portrait" == permissionName && !permission) {
                $scope.portraitPermission = false
                $scope.riskClass = 'active risk'
                $scope.tabRiskClass = 'tab-pane active risk'
                $scope.portraitClass = 'portrait'
                $scope.tabPortraitClass = 'tab-pane portrait'
            }
            return permission;
        };

    });


})();
