<div class="box box-primary" style="min-height: 200px;">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-info-circle"></i> 支付详情</h3>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.appIdName }}</td>
                <td>{{ cashDetail.app }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdName }}</td>
                <td>{{ cashDetail.uid }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdBXB }}</td>
                <td>{{ cashDetail.amount }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdBXBPre }}</td>
                <td>{{ cashDetail.origin }}</td>
            </tr>
            <tr ng-if="cashDetail.give!=null">
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdGive }}</td>
                <td>{{ cashDetail.give }}</td>
            </tr>
            <tr ng-if="cashDetail.fromUserId!=null">
                <td class="text-bold w250">{{ cashTableDetailHeader.fromUserId }}</td>
                <td>{{ cashDetail.fromUserId }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdType }}</td>
                <td>{{ cashDetail.changeType }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdUnionId }}</td>
                <td>{{ cashDetail.outId }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdJournalType }}</td>
                <td>{{ cashDetail.journalType }}</td>
            </tr>
            <tr ng-if="cashDetail.productName!=null">
                <td class="text-bold w250">{{ cashTableDetailHeader.productName }}</td>
                <td>{{ cashDetail.productName }}</td>
            </tr>

            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userCreateTime }}</td>
                <td>{{ cashDetail.createTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">{{ cashTableDetailHeader.userIdRemark }}</td>
                <td>{{ cashDetail.memo }}</td>
            </tr>
        </table>
    </div>
</div>
