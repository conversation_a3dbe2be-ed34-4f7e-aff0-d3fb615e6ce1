<div class="nav-tabs-custom" id="data-card-tab">
    <ul class="nav nav-tabs">
              <li ng-repeat="app in apps" ng-class="{active:$index == 0}" class="card_{{app.appId}}">
                  <a  href="javascript:;" ng-click="switchDataCardTab('card_'+app.appId)" style="padding:10px 10px;"><span class="text-primary box-title">{{app.appName}}</span></a>
              </li>
    </ul>
    <div class="tab-content" style="min-height: 550px;">
        <div class="tab-pane card_10 active">
            <table class="table table-bordered">
                <tr ng-if="dataCardDetail.card_10.data_5">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">个人语音</div>
                        <audio ng-repeat="url in dataCardDetail.card_10.data_5" src="{{videoUrlFun(url)}}" style="width: 100%;" controls=""></audio>
                    </td>
                </tr>
                <tr ng-if="dataCardDetail.card_10.data_1">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">形象照</div>
                        <img ng-repeat="userImage in dataCardDetail.card_10.data_1" ng-src="{{userImage}}" ng-if="dataCardDetail.card_10.data_1.length == 1" uib-tooltip-template="'pic3.html'" tooltip-class="customClass" tooltip-append-to-body="true" tooltip-placement="right"  style="width:100%;height:auto;max-height:270px; ">
                        <div class="overlay" ng-if="dataCardDetail.card_10.data_1.length > 1">
                            <div uib-carousel active="active" interval="interval" no-wrap="noWrapSlides">
                                <div uib-slide ng-repeat="userImage in dataCardDetail.card_10.data_1 track by $index" index="$index">
                                    <img ng-src="{{userImage}}" uib-tooltip-template="'pic3.html'" tooltip-class="customClass" tooltip-append-to-body="true" tooltip-placement="right"  style="width:100%;height:auto;max-height:270px; ">
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr ng-if="dataCardDetail.card_10.data_2">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">个人视频</div>
                        <video ng-repeat="url in dataCardDetail.card_10.data_2" src="{{videoUrlFun(url)}}" class="diff-content" style="width:100%;height:220px;" controls=""></video>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tab-pane card_30">
            <table class="table table-bordered">
                <tr ng-if="dataCardDetail.card_30.data_3">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">照片墙</div>
                        <img ng-repeat="userImage in dataCardDetail.card_30.data_3" ng-if="dataCardDetail.card_30.data_3.length == 1" ng-src="{{userImage}}" style="width:100%;height:auto;max-height:270px; ">
                        <div class="overlay" ng-if="dataCardDetail.card_30.data_3.length > 1">
                            <div uib-carousel active="active" interval="interval" no-wrap="noWrapSlides">
                                <div uib-slide ng-repeat="userImage in dataCardDetail.card_30.data_3 track by $index" index="$index">
                                    <img ng-src="{{userImage}}" uib-tooltip-template="'pic3.html'" tooltip-class="customClass" tooltip-append-to-body="true" tooltip-placement="right"  style="width:100%;height:auto;max-height:270px; ">
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr ng-if="dataCardDetail.card_30.data_4">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">个人视频</div>
                        <video ng-repeat="url in dataCardDetail.card_30.data_4" src="{{videoUrlFun(url)}}" class="diff-content" style="width:100%;height:220px;" controls=""></video>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tab-pane card_20">
            <table class="table table-bordered">
                <tr ng-if="dataCardDetail.card_20.data_9">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">个人语音</div>
                        <audio ng-repeat="url in dataCardDetail.card_20.data_9" src="{{videoUrlFun(url)}}" style="width: 100%;" controls=""></audio>
                    </td>
                </tr>
                <tr ng-if="dataCardDetail.card_20.data_8">
                    <td>
                        <div style="text-align: center;font-weight: bold;padding: 5px;">个人主页封面</div>
                        <img ng-repeat="userImage in dataCardDetail.card_20.data_8" ng-src="{{userImage}}" ng-if="dataCardDetail.card_20.data_8.length == 1" uib-tooltip-template="'pic3.html'" tooltip-class="customClass" tooltip-append-to-body="true" tooltip-placement="right"  style="width:100%;height:auto;max-height:270px; ">
                        <div class="overlay" ng-if="dataCardDetail.card_20.data_8.length > 1">
                            <div uib-carousel ng-if="dataCardDetail.card_20.data_8.length>0" active="active" interval="interval" no-wrap="noWrapSlides">
                                <div uib-slide ng-repeat="userImage in dataCardDetail.card_20.data_8 track by $index" index="$index">
                                    <img ng-src="{{userImage}}" uib-tooltip-template="'pic3.html'" tooltip-class="customClass" tooltip-append-to-body="true" tooltip-placement="right"  style="width:100%;height:auto;max-height:270px; ">
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
