<form class="form-horizontal w5c-form" name="punishForm" novalidate w5c-form-validate="">
    <div cg-busy="loading">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                    aria-hidden="true">&times;</span></button>
            <h3 class="modal-title">惩罚当前用户</h3>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="col-sm-3 control-label">用户id<span class="required">*</span>:</label>
                <div class="col-sm-8">
                    <input name="toUser" ng-model="param.uid"  type="text" class="form-control input-md"
                           ng-disabled="true"
                           required/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">设备<span class="required">*</span>:</label>
                <div class="col-sm-8">
                    <select ng-model="param.punishDevice" convert-to-number class="form-control input-md" ng-change="toPunish()" required>
                        <option value="0">不惩罚</option>
                        <option value="1">最近登录设备</option>
                    </select>
                </div>
            </div>
            <div class="form-group" ng-if="isPunishDevice">
                <label class="col-sm-3 control-label">设备列表:</label>
                <div class="col-sm-8">
                    <table class="table table-bordered" >
                        <tr>
                            <td></td>
                            <th>APPID</th>
                            <th>设备id</th>
                        </tr>
                        <tr ng-repeat="device in loginDevices">
                            <td><label>
                                <input type="radio" name="optionsRadios" ng-click="punishDeviceInfo(device)"/>
                            </label></td>
                            <td>{{device.APPID}}</td>
                            <td>{{device.deviceId}}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">惩罚包<span class="required">*</span>:</label>
                <div class="col-sm-8">
                    <ui-select class="input-md" ng-model="param.packageId" theme="bootstrap" required>
                        <ui-select-match placeholder="选择惩罚包" allow-clear="true">{{$select.selected.msg}}</ui-select-match>
                        <ui-select-choices repeat="t.code as t in packageIdList| filter: $select.search track by t.code" refresh-delay="300">
                            <div uib-tooltip="{{t.code}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="t.msg"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">对内原因<span class="required">*</span>:</label>
                <div class="col-sm-8">
                    <input name="source" ng-model="param.internalReason"  type="text" class="form-control input-md"
                           ng-maxlength="255"
                           required
                           maxlength="255"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">对外原因 :</label>
                <div class="col-sm-8">
                    <input name="source" ng-model="param.externalReason"  type="text" class="form-control input-md"
                           ng-maxlength="255"
                           maxlength="255"
                           placeholder="默认为违规类型对外原因"
                    />
                </div>
            </div>

            <div class="form-group">
                <!-- ref: https://angular-file-upload.appspot.com -->
                <!-- ref: https://github.com/danialfarid/ng-file-upload -->
                <label class="col-sm-3 control-label">违规凭证图片<span class="required">*</span>:</label>
                <div class="col-sm-7 drop-box" ngf-drop="" ng-model="file"
                     ngf-drag-over-class="dragover" ngf-model-options="{debounce:100}" ngf-pattern="'image/*'"
                     ngf-enable-firefox-paste="true" required>
                    <span>拖拽或粘贴到此处</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">预览:</label>
                <div class="col-sm-8 preview">
                    <img ngf-src="file">
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="col-sm-offset-3 col-sm-9">
                <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                <button type="submit" class="btn btn-success" w5c-form-submit="confirm();" ng-disabled="!punishForm.$valid">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 确认
                </button>
            </div>
        </div>
    </div>
</form>
<style>
    .drop-box {
        background: #F8F8F8;
        border: 5px dashed #DDD;
        padding: 15px;
        height: 65px;
        text-align: center;
    }
    .dragover {
        border: 5px dashed blue;
    }
    .preview img {
        max-width: 300px;
        max-height: 150px;
        float: left;
    }
</style>