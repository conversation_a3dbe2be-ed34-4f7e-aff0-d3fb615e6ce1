<div class="box box-primary">
    <div class="box-body">
        <!--<a class="btn btn-info" style="height: 30px">
            <i class="fa fa-info">
                共找到{{ orderData.length }}条订单记录
            </i>
        </a>-->
        <p class="text-black" align="right" style="">共找到<strong style="color: rgb(24,144,255);">{{ total }}</strong>条订单记录</p>
        <table class="table table-bordered">
            <tr>
                <th width="20%">ID</th>
                <th width="10%">品类</th>
                <th width="8%">状态</th>
                <th width="10%">类型</th>
                <th width="9%">单价(分)</th>
                <th width="20%">大神uid</th>
                <th width="14%">下单时间</th>
                <th width="10%">操作</th>
            </tr>
            <tr ng-repeat="order in orderData">
                <td>{{order.orderId}}</td>
                <td>{{order.spuDesc}}</td>
                <td>{{order.statusDesc}}</td>
                <td>{{order.orderTypeDesc}}</td>
                <td>{{order.price}}</td>
                <td>{{order.guid}}</td>
                <td>{{order.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>
                    <a href="javascript:void(0)" ng-click="showOrderDetail(order)">
                        <span>详情</span>
                    </a>
               <!--     <span  class="label label-success mid-label btn"
                          ng-click="showOrderDetail(order)">详情</span>-->
                    <a href="{{order.url}}?searchBy=0&searchKey={{order.orderId}}" target="_blank">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="UpageChanged(page)"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
