<table class="table table-bordered">
    <tr>
        <td class="text-bold w120">UID</td>
        <td>{{ userDetail.uid }}</td>
    </tr>
    <!--    <tr>
            <td class="text-bold w120">ID（弃用）</td>
            <td>{{ userDetail.userId }}</td>
        </tr>-->
    <tr>
        <td class="text-bold w120">手机号码</td>
        <td>({{ userDetail.nationCode }})&nbsp;{{ userDetail.partialMobileNo }}<button ng-click="showCompletePhone(userDetail.userUid)" ng-if="hasPermission('analysis:user:phone')">查看</button></td>
    </tr>
    <tr>
        <td class="text-bold w120">Show No</td>
        <td>{{ userDetail.showNo }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">微信标识</td>
        <td>{{ userDetail.wechatUnionId }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">QQ标识</td>
        <td>{{ userDetail.qqUnionId }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">用户来源</td>
        <td>{{ userDetail.sourceDesc }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">性别</td>
        <td>
            <span ng-if="userDetail.gender == 1">男</span>
            <span ng-if="userDetail.gender == 0">女</span>
            <span ng-if="userDetail.gender != 1 && userDetail.gender != 0">未知</span>
        </td>
    </tr>
    <tr>
        <td class="text-bold w120">省市</td>
        <td>{{ userDetail.province }} - {{ userDetail.city }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">职业</td>
        <td>{{ userDetail.profession }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">帐号是否冻结</td>
        <td>
            <ul>
                <li ng-repeat="freezeInfo in userDetail.freezeInfos">
                    <span ng-if="freezeInfo.status == 'NORMAL'">{{freezeInfo.appName}}: {{freezeInfo.statusName}}</span>
                    <span ng-if="freezeInfo.status != 'NORMAL'" uib-tooltip="{{freezeInfo.endTime | date:'yyyy-MM-dd HH:mm:ss'}}">{{freezeInfo.appName}} ：{{freezeInfo.statusName}}</span>
                </li>
            </ul>
        </td>
    </tr>
    <tr>
        <td class="text-bold w120">大神是否冻结</td>
        <td><span ng-if="(userDetail.biggieStatus == null ||  userDetail.biggieStatus == 1)">否</span>
            <span ng-if="(userDetail.biggieStatus != null &&  userDetail.biggieStatus == 0)"
                  uib-tooltip="{{userDetail.biggieFrozenReason}}">
                                是({{ userDetail.biggieFrozenEndTime | date:'yyyy-MM-dd HH:mm:ss' }})
                            </span></td>
    </tr>
    <tr>
        <td class="text-bold w120">鱼耳语音</td>
        <td>
            <div>主持人：{{userDetail.isChatroomHost?'是':'否'}}</div>
            <div>官方房间：{{userDetail.isChatroomOfficial?'是':'否'}}</div>
            <div>个人房间：{{userDetail.isChatroomPerson?'是':'否'}}</div>
        </td>
    </tr>
    <tr>
        <td class="text-bold w120">鱼耳直播主播</td>
        <td>{{userDetail.isAnchor?'是':'否'}}</td>
    </tr>
    <tr>
        <td class="text-bold w120">是否机器人</td>
        <td>
            <div>鱼耳直播：{{userDetail.liveRobot?'是':'否'}}</div>
            <div>鱼耳语音：{{userDetail.yuerRobot?'是':'否'}}</div>
        </td>
    </tr>
    <tr>
        <td class="text-bold w120">出生日期</td>
        <td>{{ userDetail.birthday }}</td>
    </tr>
    <tr>
        <td class="text-bold w120">注册时间</td>
        <td>{{ userDetail.createTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
    </tr>
</table>