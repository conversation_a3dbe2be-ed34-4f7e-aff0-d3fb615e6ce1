<!-- 手机 -->
<div class="box box-widget" ng-if="mobiles">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联手机号</h3>
    </div>
    <table class="table table-bordered">
        <tr>
            <th>手机号</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="(key,value) in mobiles" ng-if="key != null && key !=''">
            <td>{{key}}</td>
            <td>{{value}}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({mobileNo:key})">查看活跃行为</a>
            </td>
        </tr>
    </table>
</div>
<!-- IP -->
<div class="box box-widget" ng-if="ips">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联IP</h3>
    </div>
    <table class="table table-bordered">
        <tr>
            <th>活跃IP</th>
            <th>归属地</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="item in ips">
            <td>{{item.ip}}</td>
            <td>
                <span ng-if="item.detail!=null">
                    <span ng-if="item.detail.country!=null && item.detail.country!='' && item.detail.country!='0'">{{item.detail.country}}</span>
                    <span ng-if="item.detail['province']!=null && item.detail['province']!='' && item.detail['province']!='0'">{{item.detail['province']}}</span>
                    <span ng-if="item.detail.city!=null && item.detail.city!='' && item.detail.city!='0'">{{item.detail.city}}</span>
                </span>
            </td>
            <td>{{item.count}}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({clientIp:item.ip,userId:userDetail.uid})">查看活跃行为</a>
            </td>
        </tr>
    </table>
</div>
<!-- 设备 -->
<div class="box box-widget" ng-if="devices">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联设备</h3>
    </div>
    <table class="table table-bordered">
        <tr>
            <th>设备ID</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="(key,value) in devices" ng-if="key != null && key !=''">
            <td>{{key}}</td>
            <td>{{value}}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({deviceId:key,userId:userDetail.uid})">查看活跃行为</a>
            </td>
        </tr>
    </table>
</div>
