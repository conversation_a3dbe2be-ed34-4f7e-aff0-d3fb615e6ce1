<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-user"></i> 用户分析</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-3" style="width: 31%;">
                <input ng-model="param.value" type="text" name="search" class="form-control" ng-change="search()" placeholder="UID 或 ShowNo 或 手机号码(非大陆：(1)8888888)"/>
            </div>
            <div class="form-group col-sm-2" style="width: 10%">
                <select ng-model="param.accountType" class="form-control input-md">
                    <option value="{{accountType.type}}" ng-repeat="accountType in accountTypes">{{accountType.desc}}</option>
                </select>
            </div>
            <div class="form-group timeWidth" >
                <input type="text" name="startTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd"
                       ng-model="param.startTime" ng-click="popup1.opened=true"
                       is-open="popup1.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group timeWidth">
                <input type="text" name="endTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd"
                       ng-model="param.endTime" ng-click="popup2.opened=true"
                       is-open="popup2.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <!--<div class="form-group col-sm-2">
                <select ng-model="param.time" class="form-control input-md">
                    <option value="WEEK" selected="selected">7天</option>
                    <option value="HALFMONTH">15天</option>
                    <option value="MONTH">30天</option>
                </select>
            </div>-->
            <button ng-click="search()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-search"></i> 搜索
            </button>
            <button ng-click="customTime()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-calendar-check-o"></i> 自定义
            </button>
            <button ng-click="his()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-fire"></i> 风控历史
            </button>
            <button ng-click="punishLog()" class="btn btn-flat btn-md btn-info" ng-if="hasPermission('riskPunish:abilityRecord')">
                <i class="fa fa-list"></i> 惩罚记录
            </button>
            <div class="form-group col-sm-12" style="margin-bottom: 0px;">
                <button ng-click="correction()" class="btn btn-flat btn-md btn-info"
                        ng-if="hasPermission('system:detail:correction')">
                    <i class="fa fa-hourglass-2"></i> 清洗
                </button>
                <button ng-click="deviceCleaning()" class="btn btn-flat btn-md btn-info"
                        ng-if="hasPermission('system:detail:deviceCleaning')">
                    <i class="fa fa-hourglass-2"></i> 设备清洗
                </button>
                <button ng-click="punish()" ng-if="hasPermission('operation:punish:all')"
                        class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-gavel"></i> 惩罚
                </button>
            </div>
        </form>
    </div>
</div>

<div class="row" ng-if="userDetail">
    <div class="col-md-3">
        <div class="box box-widget widget-user">
            <div class="widget-user-header bg-aqua-active">
                <h3 class="widget-user-username" alt="昵称">{{ userDetail.nickname }}{{(userDetail.appId && userDetail.appId == 70) ? "（萤火）":""}}</h3>
                <h5 class="widget-user-desc" alt="签名">{{ userDetail.signature }}</h5>
            </div>
            <div class="widget-user-image">
                <img class="img-circle" src="{{ userDetail.avatar }}" style="height:90px;width:90px;" alt="头像">
            </div>
            <div class="box-footer" style="padding-bottom:0">
                <div class="row" style="margin-bottom:15px;">
                    <div class="col-sm-4 border-right">
                        <div class="description-block text-green">
                            <h5 class="description-header">{{ summary.PASS || 0 }}</h5>
                            <span class="description-text">安全</span>
                        </div>
                    </div>
                    <div class="col-sm-4 border-right">
                        <div class="description-block text-yellow">
                            <h5 class="description-header">{{ summary.REVIEW || 0 }}</h5>
                            <span class="description-text">可疑</span>
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <div class="description-block text-red">
                            <h5 class="description-header">{{ summary.REJECT || 0 }}</h5>
                            <span class="description-text">违规</span>
                        </div>
                    </div>
                </div>
                <div class="row" ng-if="expResult != null && expResult.length > 0" style="margin:0 -10px">
                    <ul class="exp-list clearfix">
                        <li ng-repeat="exp in expResult">
                            <span ng-if="exp.vipType == 4">{{userDetail.god?'大神'+exp.vipLevel:'非大神'}}</span>
                            <img ng-if="exp.vipType != 4" ng-show="exp.appIconUrl!=null && exp.appIconUrl!='' && exp.vipLevel>0" src="{{exp.appIconUrl}}"
                                 height="18px" uib-tooltip="{{expType[exp.vipType]}}">
                            <span ng-if="exp.vipType != 4" uib-tooltip="{{expType[exp.vipType]}}" ng-show="exp.appIconUrl==null || exp.appIconUrl=='' || exp.vipLevel<=0">
                                {{exp.vipName?exp.vipName:exp.vipLevel}}
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="alert alert-warning" style="margin:0 -10px"
                     ng-if="expResult != null && expResult.length == 0">
                    <i class="fa fa-warning"></i>&nbsp;&nbsp;当前用户没有任何权益!
                </div>
            </div>
        </div>
        <div class="row">
            <div class="nav-tabs-custom"  id="user-info-tab">
                <ul class="nav nav-tabs">
                    <li class="active baseInfo">
                        <a href="javascript:;" ng-click="switchUserTab('baseInfo')"><span class="text-primary box-title"><i class="fa fa-info-circle"></i> 基本信息</span></a>
                    </li>
                    <li class="dataCard">
                        <a href="javascript:;" ng-click="switchUserTab('dataCard')"><span class="text-primary box-title"><i class="fa fa-credit-card"></i> 资料卡</span></a>
                    </li>
                </ul>
                <div class="tab-content" style="min-height: 200px;">
                    <div class="tab-pane baseInfo active" ng-include="'analysis/user/view/baseInfo.html'"/>
                    <div class="tab-pane dataCard" ng-include="'analysis/user/view/dataCard.html'"/>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-9">
        <div class="row">
            <div class="col-md-4" id="activeHigh">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="text-primary box-title" style="font-size: 15px">近期活跃</h3>
                    </div>

                    <div class="box-body">
                        <div class="overlay">
                            <i class="fa fa-refresh fa-spin" ng-if="activeFresh">
                            </i>
                            <highchart id="activeChart" config="activeChartConfig">
                            </highchart>
                        </div>
                    </div>


                </div>
            </div>
            <div id="activeTopHigh" class="col-md-4">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="text-primary box-title" style="font-size: 15px">活跃场景Top10</h3>
                    </div>
                    <div class="box-body">
                        <div class="overlay">
                            <i class="fa fa-refresh fa-spin" ng-if="activeSceneFresh">
                            </i>
                            <highchart id="activeSceneChart" config="activeSceneChartConfig"></highchart>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4" id="activeViolationHigh">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="text-primary box-title" style="font-size: 15px">违规场景Top10</h3>
                    </div>
                    <div class="box-body">
                        <div class="overlay">
                            <i class="fa fa-refresh fa-spin" ng-if="violationFresh">
                            </i>
                            <highchart id="activeViolationSceneChart" config="activeViolationSceneConfig"></highchart>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="text-primary box-title" style="font-size: 15px">文本违规Top50</h3>
                    </div>

                    <div class="box-body">
                        <div class="overlay">
                            <i class="fa fa-refresh fa-spin" ng-if="wordCloudFresh">
                            </i>
                            <highchart id="violationText" config="wordCloud"></highchart>
                        </div>
                    </div>
                  <!--  <div class="content" ng-if="violationTextTopData==null||violationTextTopData.length<=0" style="margin:auto; height: 280px; width: 80%">
                        <h4>没有发布违禁文本</h4>
                    </div>-->
                </div>
            </div>
            <div class="col-md-6">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="text-primary box-title" style="font-size: 15px">图片违规Top20</h3>
                    </div>
                    <div class="box-body">
                        <div class="overlay">
                            <i class="fa fa-refresh fa-spin" ng-if="violationImageFresh">
                            </i>
                        <div uib-carousel ng-if="slides.length>0" active="active" interval="interval" no-wrap="noWrapSlides">
                            <div uib-slide ng-repeat="slide in slides track by slide.id" index="slide.id">
                                <img ng-src="{{getThumb(slide.image)}}"
                                     uib-tooltip-template="'pic2.html'" tooltip-placement="left"
                                     tooltip-class="customClass"
                                     tooltip-append-to-body="true"
                                     style="margin:auto; height: 280px; width: 80%">
                            </div>
                        </div>
                        <div ng-if="violationImageText && slides.length<=0" class="content" style="margin:auto; height: 280px; width: 80%;text-align: center">
                            <h4 ng-if="violationImageTextShow">没有发布违禁图片</h4>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="nav-tabs-custom" id="user-detail">
                <ul class="nav nav-tabs">
                    <!--                    <li class="active risk"><a href="javascript:;" ng-click="switchTab('risk')">风险概述</a></li>-->

                    <li class="{{ portraitClass}}" ng-if="hasPermission('system:detail:portrait')">
                        <a href="javascript:;" ng-click="switchTab('portrait')">用户画像</a>
                    </li>
                    <li class="{{ riskClass }}"><a href="javascript:;" ng-click="switchTab('risk')">关联信息</a></li>
                    <li class="im"><a href="javascript:;" ng-click="switchTab('im')">IM聊天</a></li>
                   <!-- <li class="pm"><a href="javascript:;" ng-click="switchTab('pm')">私聊</a></li>-->
                    <li class="xyx"><a href="javascript:;" ng-click="switchTab('xyx')">小游戏</a></li>
                    <li class="imGroup"><a href="javascript:;" ng-click="switchTab('imGroup')">群聊</a></li>
                    <li class="cr"><a href="javascript:;" ng-click="switchTab('cr')">聊天室</a></li>
                    <li class="lr"><a href="javascript:;" ng-click="switchTab('lr')">直播间</a></li>

                    <li class="order"><a href="javascript:;" ng-click="switchTab('order')">订单记录</a></li>
                    <li class="pay"><a href="javascript:;" ng-click="switchTab('pay')">交易记录</a></li>
                    <li class="csd"><a href="javascript:;" ng-click="switchTab('csd')">客服记录</a></li>
                    <li class="complain"><a href="javascript:;" ng-click="switchTab('complain')">举报记录</a></li>
                    <li class="appeal"><a href="javascript:;" ng-click="switchTab('appeal')">申诉记录</a></li>
                    <li class="punish"><a href="javascript:;" ng-click="switchTab('punish')">惩罚记录</a></li>
                    <li class="search"><a href="javascript:;" ng-click="switchTab('search')">搜索明细</a></li>
                    <li ng-show="riskTip" style="color:red;position: relative;top: 11px"><span class="glyphicon glyphicon-alert" aria-hidden="true"></span>&#160;数据敏感，注意保密</li>
                </ul>
                <div class="tab-content" style="min-height: 680px;">
                    <!--                    <div class="tab-pane risk active" ng-include="'analysis/user/view/risk.html'"/>-->
                    <div class="{{ tabPortraitClass }}" ng-if="hasPermission('system:detail:portrait')"
                         ng-include="'analysis/user/view/userPortrait.html'"/>
                    <div class="{{ tabRiskClass }}" ng-include="'analysis/user/view/risk.html'"/>
                    <div class="tab-pane order" ng-include="'analysis/user/view/orderRecord.html'"/>
                    <div class="tab-pane pay" ng-include="'analysis/user/view/payRecord.html'"/>

                    <div class="tab-pane csd" ng-include="'analysis/user/view/customerServiceRecord.html'"/>
                    <div class="tab-pane search" ng-include="'analysis/user/view/searchRecord.html'"/>
                    <div class="tab-pane complain" ng-include="'analysis/user/view/complainRecord.html'"/>
                    <div class="tab-pane appeal" ng-include="'analysis/user/view/appealRecord.html'"/>
                    <div class="tab-pane punish" ng-include="'analysis/user/view/punishRecord.html'"/>
                    <div class="tab-pane im" ng-include="'widget/im/index.html'"/>
                   <!-- <div class="tab-pane pm" ng-include="'widget/im/pmIndex.html'"/>-->
                    <div class="tab-pane xyx" ng-include="'widget/im/xyxIndex.html'"/>
                    <div class="tab-pane imGroup" ng-include="'widget/im/imGroupIndex.html'"/>
                    <div class="tab-pane cr" ng-include="'widget/im/crIndex.html'"/>
                    <div class="tab-pane lr" ng-include="'widget/im/lrIndex.html'"/>


                </div>
            </div>
        </div>
    </div>
</div>
<div class="row" ng-if="!userDetail">
    <div class="col-md-12">
        <div class="alert alert-info alert-dismissible"><i class="fa fa-info"></i>&nbsp;&nbsp;避免多次查询无果，请尽量填写准确！</div>
    </div>
</div>
<script type="text/ng-template" id="pic2.html">
    <img ng-src="{{getPic(slide.image)}}" style="height: 700px; width: 700px"/>
</script>
<script type="text/ng-template" id="pic3.html">
    <img ng-src="{{userImage}}" style="height: 500px; width: 500px"/>
</script>
<style>
    .w250 {
        width: 140px;
    }

    .w120 {
        width: 120px;
    }

    #user-detail {
        margin: 0px 15px 0px 15px;
    }

    #user-detail .tab-pane {
        min-height: 500px;
    }

    .label-risk {
        margin: 5px;
        padding: 5px 10px;
        font-size: 13px;
        display: inline-block;
        line-height: 18px;
    }

    .tooltip.customClass .tooltip-inner {
        max-width: fit-content;
        padding: 8px 8px;
        background-color: #ffffff;
        box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    }

    .tooltip.customClass .tooltip-arrow {
        display: none;
    }

    .tooltip.customClass.tooltip.in {
        opacity: 1;
    }

    .exp-list li {
        width: 33%;
        float: left;
        padding: 10px;
        text-align: center;
    }

    .timeWidth{
        padding-left:0;
        width: 11%;
        float:left;
        position:relative;
        min-height:1px;
        padding-right:15px;
    }
</style>
