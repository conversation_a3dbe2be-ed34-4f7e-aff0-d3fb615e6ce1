<div class="box box-primary">
    <div class="box-body">
        <p class="text-black" align="right" style="">共找到<strong style="color: rgb(24,144,255);">{{ complainData.length }}</strong>条举报记录</p>

        <table class="table table-bordered">
            <tr>
                <th width="15%">举报时间</th>
                <th width="13%">举报人uid</th>
                <th width="15%">处理状态</th>
                <th width="16%">处理结果</th>
                <th width="18%">举报来源描述</th>
                <th width="10%">举报原因描述</th>
                <th width="13%">操作</th>
            </tr>
            <tr ng-repeat="complain in complainData">
                <td>{{complain.complainTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{complain.fromUid}}</td>
                <td>{{complain.status}}</td>
                <td>{{complain.result}}</td>
                <td>{{complain.sourceDesc}}</td>
                <td>{{complain.reasonDesc}}</td>
                <td>
                    <a href="javascript:void(0)" ng-click="showComplainDetail(complain)">
                        <span>详情</span>
                    </a>
                    <!--     <span  class="label label-success mid-label btn"
                               ng-click="showOrderDetail(order)">详情</span>-->
                     <a href="{{complain.url}}" target="_blank">
                         <span>更多</span>
                     </a>
                </td>
            </tr>
        </table>
    </div>
 <!--   <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            max-size="10"
            rotate="false">
        </ul>
    </div>-->
</div>
