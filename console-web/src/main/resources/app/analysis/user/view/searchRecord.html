<div class="box box-primary">
    <div class="box-body">
        <div class="btn-group">
            <button type="button" class="btn btn-info" ng-click="download()">
                <i class="fa fa-download"></i>导出
            </button>
        </div>
        <table class="table table-bordered">
            <tr>
                <p class="text-black" align="right" style="">共找到<strong style="color: rgb(24,144,255);">{{ searchTotal }}</strong>条搜索明细记录</p>
            </tr>
            <tr>
                <th width="7%">搜索内容</th>
                <th width="9%">机审结果</th>
                <th width="9%">搜索时间(精确到秒)</th>
            </tr>
            <tr ng-repeat="search in searchData">
                <td>{{search.content}}</td>
                <td>{{search.riskLevel}}</td>
                <td>{{search.time | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
        </table>

<!--        <table class="table table-bordered">-->
<!--            <tr>-->
<!--                <th width="7%">搜索内容</th>-->
<!--                <th width="9%">机审结果</th>-->
<!--                <th width="9%">搜索时间(精确到秒)</th>-->
<!--            </tr>-->
<!--            <tr ng-repeat="search in searchData">-->
<!--                <td>{{search.content}}</td>-->
<!--                <td>{{search.riskLevel}}</td>-->
<!--                <td>{{search.time | date:'yyyy-MM-dd HH:mm:ss'}}</td>-->
<!--            </tr>-->
<!--        </table>-->
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="searchTotal"
            ng-model="searchPage"
            ng-change="searchPageChanged(searchPage)"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
