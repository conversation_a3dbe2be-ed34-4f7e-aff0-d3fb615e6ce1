<div class="box box-primary">
    <div class="box-body">
        <p class="text-black" align="right" style="">共找到<strong style="color: rgb(24,144,255);">{{ KFtotal }}</strong>条客服记录</p>
        <table class="table table-bordered">
            <tr>
                <th width="7%">任务接收组</th>
                <th width="9%">任务来源</th>
                <th width="9%">任务类型</th>
                <th width="5%">是否紧急</th>
                <th width="5%">任务状态</th>
                <th width="9%">创建人</th>
                <th width="13%">创建时间</th>
                <th width="9%">经办人</th>
                <th width="13%">分配时间</th>
                <th width="13%">完成时间</th>
                <th width="7%">操作</th>
            </tr>
            <tr ng-repeat="csdVO in csdData">
                <td>{{csdVO.taskGroup}}</td>
                <td>{{csdVO.taskSource}}</td>
                <td>{{csdVO.taskType}}</td>
                <td>{{csdVO.urgentLevel}}</td>
                <td>{{csdVO.taskStatus}}</td>
                <td>{{csdVO.taskCreator}}</td>
                <td>{{csdVO.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{csdVO.taskAssignee}}</td>
                <td>{{csdVO.assignTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{csdVO.finishTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>
                    <a href="javascript:void(0)" ng-click="showCustomerRecordDetail(csdVO)">
                        <span>详情</span>
                    </a>
                    <!--     <span  class="label label-success mid-label btn"
                               ng-click="showOrderDetail(order)">详情</span>-->
                    <a href="{{analysisUrl.cdsUrl}}{{csdVO.showNo}}" target="_blank">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="KFtotal"
            ng-model="KFPage"
            ng-change="KFPageChanged(KFPage)"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
