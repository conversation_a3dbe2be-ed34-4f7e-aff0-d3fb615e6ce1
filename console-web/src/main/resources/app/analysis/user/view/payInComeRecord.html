<div class="box box-primary">

    <div class="box-body">
        <div class="btn-group">
            <button type="button" class="btn btn-info" ng-click="queryCashJournal()">比心币(余额)流水</button>
            <button type="button" class="btn btn-info" ng-click="queryIncomeJournal(1)">星钻流水</button>
            <button type="button" class="btn btn-info" ng-click="queryDiamondJournal(1)">星动值流水</button>
            <button type="button" class="btn btn-info" ng-click="queryCharmJournal(1)">钻石流水</button>
            <button type="button" class="btn btn-info" ng-click="queryStarJournal(1)">魅力值流水</button>
            <button type="button" class="btn btn-info" ng-click="queryStarDiamondJournal(1)">大神收入流水</button>
        </div>
        <table class="table table-bordered">
            <tr>
                <th width="15%">创建时间</th>
                <th width="13%">流水类型</th>
                <th width="13%">变动前的比心币(余额)</th>
                <th width="10%">变动的比心币(余额)</th>
                <th width="13%">变动的赠送金</th>
                <th width="16%">备注</th>
                <th width="20%">操作</th>
            </tr>
            <tr ng-repeat="cash in cashJournalData">
                <td>{{cash.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{cash.changeType}}</td>
                <td>{{cash.origin}}</td>
                <td>{{cash.amount}}</td>
                <td>{{cash.give}}</td>
                <td>{{cash.memo}}</td>
                <td>
                    <a href="javascript:void(0)" ng-click="showCashDetail(cash)">
                        <span>详情</span>
                    </a>
                    <!--     <span  class="label label-success mid-label btn"
                               ng-click="showOrderDetail(order)">详情</span>-->
                    <a href="https://pay.yupaopao.com/account/details/balance-journal" target="_blank">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged(page)"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
