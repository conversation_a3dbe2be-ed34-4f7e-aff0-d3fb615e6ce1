        <form class="form-horizontal w5c-form" name="cleanDataForm" novalidate w5c-form-validate="">
        <div cg-busy="loading">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                        aria-hidden="true">&times;</span></button>
                <h3 class="modal-title">{{title}}</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-sm-3 control-label">数美清洗:</label>
                    <div class="col-sm-8" style="position: relatinive;top:7px;">
                        <input ng-model="param.shumei" type="checkbox" ng-true-value="true" ng-false-value="false"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">用户画像全量清洗:</label>
                    <div class="col-sm-8" style="position: relative;top:7px;">
                        <input ng-model="param.portrait" type="checkbox" ng-true-value="true" ng-false-value="false"/>
                    </div>
                </div>
                <div class="form-group" ng-if="grayList&&grayList.length>0">
                    <label class="col-sm-3 control-label">黑名单清洗:</label>
                    <div class="col-sm-8" style="position: relative;top:7px;">
                        <input ng-model="param.blackGray" type="checkbox" ng-true-value="true" ng-false-value="false"/>
                    </div>
                </div>
                <div class="form-group" ng-if="grayList&&grayList.length>0">
                    <label class="col-sm-3 control-label">黑名单列表</label>
                    <div class="col-sm-8">
                    </div>
                </div>
                <div class="form-group" ng-if="grayList&&grayList.length>0">
                    <label class="col-sm-1 control-label">&#160;</label>
                    <div class="col-sm-10">
                        <table class="table table-bordered" >
                            <tr>
                                <th>名单维度</th>
                                <th>数据</th>
                                <th>生效时间</th>
                                <th>过期时间</th>
                                <th>备注</th>
                                <th>创建人</th>
                            </tr>
                            <tr ng-repeat="g in grayList">
                                <td>{{g.dimension}}</td>
                                <td>{{g.value}}</td>
                                <td>{{g.startTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                                <td>{{g.expireTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                                <td>{{g.comment}}</td>
                                <td>{{g.author}}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">累计因子清洗:</label>
                    <div class="col-sm-8" style="position: relative;top:7px;">
                        <input ng-model="param.factor" type="checkbox" ng-true-value="true" ng-click="selectFactor()" ng-false-value="false"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">&#160;</label>
                    <div class="col-sm-8" style="position: relative;top:7px;">
                        <input ng-model="param.factorIds" name="comment" type="text" class="form-control input-md"
                               ng-if="visible"
                               placeholder="输入累积因子id，多个用英文逗号隔开(必填)" ng-maxlength="100" autocomplete="off"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注<span class="required">*</span>:</label>
                    <div class="col-sm-8">
                        <input ng-model="param.comment" name="comment" type="text" class="form-control input-md" placeholder="输入(必填)"
                               required ng-maxlength="100" autocomplete="off"/>
                        <span class="required" ng-show="cleanDataForm.comment.$dirty && cleanDataForm.comment.$invalid">
                            <span ng-show="cleanDataForm.comment.$error.required">备注是必须的</span>
                            <span ng-show="cleanDataForm.comment.$error.maxlength">长度限制100</span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-sm-offset-3 col-sm-9">
                    <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                    <button type="submit" class="btn btn-success" w5c-form-submit="confirm();" ng-disabled="!cleanDataForm.$valid || submitDisabled">
                        <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 确认
                    </button>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">备注:</label>
                    <div class="col-sm-8 control-label">
                        <div display="block" style="text-align:left">1. 用户画像全量清洗包括清除该用户对应的所有的画像标签和累计因子。</div>
                        <div display="block" style="text-align:left">2. 在用户分析页面对该用户的画像进行全量清洗后，预计五分钟内全部清洗完毕，届时可从洞察平台的【用户标签查询】页面和风控运营平台的【用户分析】页面查看用户清洗后的最新标签。</div>
                    </div>
                </div>
            </div>
        </div>
    </form>
