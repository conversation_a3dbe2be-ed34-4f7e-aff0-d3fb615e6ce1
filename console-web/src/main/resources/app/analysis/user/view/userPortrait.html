<div class="box box-widget">
    <div class="row">

        <div class="input-group margin">
          <!--  private JSONArray sTokenIdRelateIpCityInfoMap4w;
            private JSONArray sTokenIdRelateSmIdInfoMap4w;
-->
            <div class="input-group-btn" ng-if="ordinaryCity.city">
                <button type="button" class="btn btn-default dropdown-menu-left">常居城市</button>
            </div>
            <input type="text" class="form-control" readonly="readonly" ng-if="ordinaryCity.city" ng-model="ordinaryCity.city" style="width: 90%">
            <div class="input-group-btn">
                <button type="button" class="btn btn-default dropdown-menu-left" ng-if="commonUseDevice.deviceId">常用设备</button>
            </div>
            <input type="text" class="form-control" readonly="readonly" ng-if="commonUseDevice.deviceId" ng-model="commonUseDevice.deviceId">
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4 col-xs-5" ng-if="!showPortraitTag">
            <div class="box box-widget widget-user-2" ng-repeat="(key, value) in portraitTag">
                <div class="widget-user-header bg-orange">
                    <h5 class="widget-user-username" style="margin-left: auto;">{{ key }}</h5>
<!--                    <h7 class="widget-user-desc" style="margin-left: auto">{{val.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</h7>-->
                </div>
                <div class="box-footer no-padding">
                    <ul class="nav nav-stacked" ng-repeat="subVal in value">
                        <li>
                            <a href="javascript:void(0);" style="height: 35px">
                                <span class="pull-left" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width: 70%;"
                                uib-tooltip="{{subVal.tagName}}"
                                >{{subVal.tagName}}</span>
                                <span class="pull-right badge bg-orange" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width: 20%;"
                                      uib-tooltip-template="'tooltipTemplate'" ng-if="subVal.value>=0 || subVal.value=='true'||subVal.value=='false'"> {{subVal.value}}</span>
                            </a>
                        </li>

                    </ul>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-xs-5" ng-if="showPortraitTag">
            <div class="box-body">
                <strong>暂无标签</strong>
            </div>

        </div>
        <div class="col-lg-4 col-xs-5">
            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdActiveDays7d==null) ? 0 : portraitObj.userIdActiveDays7d.value }}</h3>
                    <p>账号近7天活跃时间</p>
                </div>
            </div>

            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>
                        {{(portraitObj.userIdLoginSuccessCnt1d.value==null)? 0:portraitObj.userIdLoginSuccessCnt1d.value}}
                        / {{ (portraitObj.userIdLoginCnt1d.value==null) ? 0: portraitObj.userIdLoginCnt1d.value }}</h3>
                    <p>账号1天内登录成功次数/尝试登录次数</p>
                </div>
            </div>

            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdRelateDeviceIdCnt1d==null) ? 0 : portraitObj.userIdRelateDeviceIdCnt1d.value }}</h3>
                    <p>账号1天内关联设备个数</p>
                </div>
            </div>

            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdRelateIpCityCnt1d==null) ? 0 : portraitObj.userIdRelateIpCityCnt1d.value }}</h3>
                    <p>账号1天内关联城市个数</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-xs-5">
         <!--   <div class="small-box bg-aqua">
                <div class="inner">
                    <h3 ng-if="portraitObj.iTokenIdActiveDays4w!=null">{{ portraitObj.iTokenIdActiveDays4w }}</h3>
                    <h3 ng-if="portraitObj.iTokenIdActiveDays4w==null">0</h3>
                    <p>账号近28天活跃天数</p>
                </div>
            </div>-->
            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdLoginSuccessCnt7d==null) ? 0 : portraitObj.userIdLoginSuccessCnt7d.value }} /
                        {{ (portraitObj.userIdLoginCnt7d==null) ? 0: portraitObj.userIdLoginCnt7d.value }}</h3>
                    <p>账号7天内登录成功次数/尝试登录次数</p>
                </div>
            </div>
            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdRelateDeviceIdCnt7d==null) ? 0 : portraitObj.userIdRelateDeviceIdCnt7d.value }}</h3>
                    <p>账号7天内关联设备个数</p>
                </div>
            </div>
            <div class="small-box bg-aqua">
                <div class="inner">
                    <h3>{{ (portraitObj.userIdRelateIpCityCnt7d==null) ? 0: portraitObj.userIdRelateIpCityCnt7d.value }}</h3>
                    <p>账号7天内关联城市个数</p>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/ng-template" id="tooltipTemplate">
    <div style="width: 300px; text-align: left;">
        更新时间：{{ subVal.updateTime| date:'yyyy-MM-dd HH:mm:ss'}}<br/>
        标签值：{{ subVal.value }}<br/>
        编码：{{subVal.tagCode}}<br/>
    </div>
</script>

<script type="text/ng-template" id="deviceTooltip">
    <div style="word-break: break-all">{{ portraitObj.currentIsmId }}</div>
</script>

