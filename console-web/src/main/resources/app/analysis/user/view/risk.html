<!--<div class="box box-widget">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-bolt"></i> 风险标签</h3>
    </div>
    <div class="box-body" ng-if="riskLabel">
        <span ng-repeat="(key, val) in riskLabel"
              class="label label-risk label-{{(val.REJECT || 0)>0 ? 'danger' : ((val.REVIEW || 0)>0?'warning':'success')}}">
            {{code2Name(key)}}
        </span>
    </div>
    <div class="box-body" ng-if="riskLabel == null" style="min-height: 200px;">
        暂无标签！
    </div>
</div>
<div class="box box-widget">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-bolt"></i> 引导话术</h3>
    </div>
    <div class="box-body" ng-if="riskLabel">
        <div ng-repeat="(key, val) in riskLabel" ng-if="(val.REJECT || 0)>0">
            <div class="callout callout-success" ng-if="key == 'user-register'">
                <h4>注册失败</h4>
                <p>
                    您的设备或网络存在安全风险，请更换设备或切换网络后重试，并确保：</br>
                    1. 不要越狱或ROOT</br>
                    2. 卸载危险软件</br>
                    3. 如有VPN请关闭</br>
                    4. 使用有效的SIM卡</br>
                </p>
            </div>
            <div class="callout callout-success" ng-if="key == 'user-login'">
                <h4>登陆失败</h4>
                <p>
                    您的设备或网络存在安全风险，请更换设备或切换网络后重试，并确保：</br>
                    1. 不要越狱或ROOT</br>
                    2. 卸载危险软件</br>
                    3. 如有VPN请关闭</br>
                    4. 使用有效的SIM卡</br>
                </p>
            </div>
            <div class="callout callout-success" ng-if="key == 'iap-charge'">
                <h4>IAP充值受限</h4>
                <p>
                    目前IAP充值仅支持部分国家和地区，敬请谅解，谢谢。
                </p>
            </div>
        </div>
        <div class="callout callout-success">
            <h4>通用话术</h4>
            <p>
                请确保您的行为或发表的内容符合国家法律法规及平台用户行为规范，或稍后重试，谢谢。
            </p>
        </div>
    </div>
    <div class="box-body" ng-if="riskLabel == null" style="min-height: 200px;">
        暂无建议！
    </div>
</div>-->
<div class="box box-widget">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-bolt"></i> 所属名单</h3>
    </div>
    <div class="box-body" ng-if="grayList && grayList.length>0">
        <table class="table table-bordered" >
            <tr>
                <th>名单组</th>
                <th>名单类型</th>
                <th>说明</th>
                <th>添加人</th>
                <th>生效时间</th>
                <th>过期时间</th>
            </tr>
            <tr ng-repeat="g in grayList">
                <td>{{g.grayGroup.name}}</td>
                <td><span class="label label-risk label-{{g.type == 'BLACK' ? 'danger' : 'success'}}">{{g.type == 'BLACK' ? '黑名单' : '白名单'}}</span></td>
                <td>{{g.comment}}</td>
                <td>{{g.author}}</td>
                <td>{{g.startTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{g.expireTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
        </table>
    </div>
    <div class="box-body" ng-if="grayList == null || grayList.length==0" style="min-height: 100px;">
        不在任何名单中
    </div>
</div>
<!-- 手机 -->
<div class="box box-widget" ng-if="mobiles">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联手机号</h3>
    </div>
    <table class="table table-bordered">
        <tr>
            <th>手机号</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="(key,value) in mobiles" ng-if="key != null && key !=''">
            <td>{{key}}<button ng-click="showRelatePhone(paramCopy,key,value)" ng-if="hasPermission('analysis:user:phone')">查看</button></td>
            <td>{{value}}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({mobileNo:key})">查看活跃行为</a>
            </td>
        </tr>
    </table>
</div>
<!-- IP -->
<div class="box box-widget" ng-if="ips">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联IP</h3>
    </div>
    <table class="table table-bordered">
        <tr>
            <th>活跃IP</th>
            <th>归属地</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="item in ips">
            <td>{{item.ip}}</td>
            <td>
                <span ng-if="item.detail!=null">
                    <span ng-if="item.detail.country!=null && item.detail.country!='' && item.detail.country!='0'">{{item.detail.country}}</span>
                    <span ng-if="item.detail['province']!=null && item.detail['province']!='' && item.detail['province']!='0'">{{item.detail['province']}}</span>
                    <span ng-if="item.detail.city!=null && item.detail.city!='' && item.detail.city!='0'">{{item.detail.city}}</span>
                </span>
            </td>
            <td>{{item.count}}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({clientIp:item.ip,userId:userDetail.uid})">查看活跃行为</a>
            </td>
        </tr>
    </table>
</div>
<!-- 设备 -->
<div class="box box-widget" ng-if="devices">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联设备{{devicesDesc}}</h3>
<!--        <input type="text" hidden ng-model="dynamicPopover.templateUrl" class="form-control">-->
    </div>
    <table class="table table-bordered">
        <tr>
            <th>设备ID</th>
            <th>名单组</th>
            <th class="w250" ng-if="hasPermission('system:detail:portrait')">设备评级</th>
            <th class="w250">活跃次数</th>
            <th class="w250">&nbsp;</th>
        </tr>
        <tr ng-repeat="(key,value) in devices" ng-if="key != null && key !=''">
            <td>{{key}} </br>
                <a ng-if="value.tag && hasPermission('system:detail:portrait')" popover-trigger="'mouseenter'" uib-popover-template="'myPopoverTemplate.html'" style="margin: 5px" popover-title="{{k}}" ng-repeat="(k, v) in value.tag" class="label label-danger">{{k}}</a>
            </td>
            <td>
                <ul>
                    <li ng-repeat="item in value.grayList" style="margin: 5px">
                        <a class="label label-default bg-green" ng-if="item.type=='WHITE'" uib-tooltip-template="'deviceGrayListTemplate'">{{item.groupName}}</a>
                        <a class="label label-danger" ng-if="item.type=='BLACK'" uib-tooltip-template="'deviceGrayListTemplate'">{{item.groupName}}</a>
                    </li><br/>
                </ul>
            </td>
            <td ng-if="hasPermission('system:detail:portrait')">
                <a class="label label-danger" ng-if="value.devicePortrait && value.devicePortrait.value==3" uib-tooltip-template="'devicePortraitTooltipTemplate'">高危</a>
                <a class="label label-warning" ng-if="value.devicePortrait && value.devicePortrait.value==2" uib-tooltip-template="'devicePortraitTooltipTemplate'">中危</a>
                <a class="label label-info" ng-if="value.devicePortrait && value.devicePortrait.value==1" uib-tooltip-template="'devicePortraitTooltipTemplate'">低危</a>
                <a class="label label-default bg-green" ng-if="value.devicePortrait && value.devicePortrait.value==0" uib-tooltip-template="'devicePortraitTooltipTemplate'">无风险</a>
                <a class="label label-default" ng-if="!value.devicePortrait">未评级</a>
            </td>

            <td>{{ value.count }}</td>
            <td>
                <a class="label label-success btn" target="_blank" ui-sref="history.log({deviceId:key,userId:userDetail.uid})">查看活跃行为</a>
                <br/>
                <a style="margin-top: 5px;display: inline-block;" ng-if="hasPermission('riskPunish:abilityRecord')" class="label label-success btn" target="_blank" ui-sref="riskPunish.abilityRecord({objId:key,objType:'deviceId'})">查看惩罚记录</a>
            </td>
        </tr>
    </table>
</div>

<!-- 设备 -->
<div class="box box-widget" ng-if="appVersions">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-cubes"></i> 关联版本(最近7天)</h3>
        <!--        <input type="text" hidden ng-model="dynamicPopover.templateUrl" class="form-control">-->
    </div>
    <table class="table table-bordered">
        <tr>
            <th>platform</th>
            <th>app</th>
            <th>productId</th>
            <th>version</th>
            <th class="w250">活跃次数</th>
        </tr>
        <tr ng-repeat="(key,value) in appVersions" ng-if="key != null && key !=''">
            <td>{{value.platform}}</td>
            <td>{{value.app}}</td>
            <td>{{value.productId}}</td>
            <td>{{value.version}}</td>
            <td>{{value.count}}</td>
        </tr>
    </table>
</div>

<script type="text/ng-template" id="devicePortraitTooltipTemplate">
        <div style="width: 300px; text-align: left;">
            更新时间：{{ value.devicePortrait.updateTime| date:'yyyy-MM-dd HH:mm:ss'}}<br/>
            标签值：{{ value.devicePortrait.value }}<br/>
            编码：{{ value.devicePortrait.tagCode}}<br/>
        </div>
</script>

<script type="text/ng-template" id="deviceGrayListTemplate">
        <div style="width:300px;text-align:left;">
            名单组:{{item.groupName}}<br/>
            名单类型:{{item.type == 'BLACK' ? '黑名单' : '白名单'}}<br/>
            说明:{{item.comment}}<br/>
            添加人:{{item.author}}<br/>
            生效时间:{{item.startTime| date:'yyyy-MM-dd HH:mm:ss'}}<br/>
            过期时间:{{item.expireTime| date:'yyyy-MM-dd HH:mm:ss'}}<br/>
        </div>
</script>

<script type="text/ng-template" id="myPopoverTemplate.html">
        <div class="form-group" style="max-width: 1400px">
            <table class="table table-bordered">
                <tr>
                    <th style="width: 20%">标签名</th>
                    <th style="width: 25%">编码</th>
                    <th style="width: 15%">标签值</th>
                    <th style="width: 40%">更新时间</th>
                </tr>
                <tr ng-repeat="val in v">
                    <td>{{ val.tagName }}</td>
                    <td>{{ val.tagCode }}</td>
                    <td>{{ val.value }}</td>
                    <td>{{ val.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                </tr>
            </table>
        </div>
</script>
<style>
    .popover{
        max-width:1400px;
    }
</style>
