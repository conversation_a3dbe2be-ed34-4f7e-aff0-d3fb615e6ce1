<div class="box box-primary">

    <div class="box-body">
        <div class="btn-group">
            <button type="button" class="btn btn-info" ng-click="queryCashJournal(1)">比心币(余额)流水</button>
            <button type="button" class="btn btn-info" ng-click="queryIncomeJournal(1)">大神收入流水</button>
            <button type="button" class="btn btn-info" ng-click="queryCharmJournal(1)">魅力值流水</button>
            <button type="button" class="btn btn-info" ng-click="queryStarDiamondJournal(1)">星钻流水</button>
            <button type="button" class="btn btn-info" ng-click="queryStarJournal(1)">星动值流水</button>
            <button type="button" class="btn btn-info" ng-click="queryDiamondJournal(1)">钻石流水</button>
        </div>
       <!-- <a class="btn btn-info" style="height: 30px">
            <i class="fa fa-info">
                共找到{{ payTotal }}条流水记录
            </i>fa-info-circle
        </a>-->


        <table class="table table-bordered">
            <tr>
<!--                <div class="row">-->
                <!--    <div class="" style="height: 30px;width: 100%; color: white;" align="right">
                        <span class="fa fa-info-circle">共找到<strong style="color: rgb(24,144,255);">{{ payTotal }}</strong>条流水记录</span>
                    </div>-->
                <p class="text-black" align="right" style="">共找到<strong style="color: rgb(24,144,255);">{{ payTotal }}</strong>条流水记录</p>
<!--                </div>-->
            </tr>
            <tr>
                <th width="13%" ng-repeat="headO in cashTableHeader">{{headO}}</th>
            </tr>
            <tr ng-repeat="cash in cashJournalData">
                <td>{{cash.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{cash.changeType}}</td>
                <td>{{cash.origin}}</td>
                <td>{{cash.amount}}</td>
                <td ng-if="cash.give">{{cash.give}}</td>
                <td ng-if="cash.fromUserId || cashType=='incomeJournal'">{{cash.fromUserId}}</td>
                <td ng-if="cashType!='cashJournal' && cashType!='incomeJournal' && cashType!='charmJournal'">{{cash.outId}}</td>
                <td ng-if="cash.relatedUserId || cash.relatedUserId===''">{{cash.relatedUserId}}</td>
                <td>{{cash.memo}}</td>
                <td>
                    <a href="javascript:void(0)" ng-click="showCashDetail(cash,cashTableDetailHeader)">
                        <span>详情</span>
                    </a>
                    <!--     <span  class="label label-success mid-label btn"
                               ng-click="showOrderDetail(order)">详情</span>-->
                    <a href="{{cash.url}}" target="_blank">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="payTotal"
            ng-model="payPage"
            ng-change="payPageChanged(payPage)"
            max-size="10"
            rotate="true">
        </ul>
    </div>
</div>
