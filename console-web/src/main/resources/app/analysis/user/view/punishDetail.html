<div class="box box-primary" style="min-height: 200px;">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-info-circle"></i> 处罚详情</h3>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <td class="text-bold w250">处罚时间</td>
                <td>{{ punishDetail.createTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">处罚来源</td>
                <td>{{ punishDetail.plateForm }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">处罚类型</td>
                <td>{{ punishDetail.freezeTypeName }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">处罚业务类型</td>
                <td>{{ punishDetail.type }}({{punishDetail.typeName}})</td>
            </tr>
            <tr>
                <td class="text-bold w250">对内展示原因</td>
                <td>{{ punishDetail.inReason }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">对外展示原因</td>
                <td>{{ punishDetail.reason }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">处罚场景</td>
                <td>{{ punishDetail.modeName }}</td>
            </tr>
            <tr>
                <td class="text-bold w250">处罚凭证</td>

                <td>
<!--                    {{ punishDetail.evidences}}-->
                    <div class="box-body">
                        <div uib-carousel active="punishActive" interval="punishInterval" no-wrap="punishNoWrapSlides">
                            <div uib-slide ng-repeat="picUrl in punishSlides track by picUrl.id" index="picUrl.id">
                                <img ng-src="{{ picUrl.image }}"
                                     uib-tooltip-template="'punishPic2.html'" tooltip-placement="left"
                                     tooltip-class="customClass"
                                     tooltip-append-to-body="true"
                                     style="margin:auto; height: 100px; width: 50%">
                            </div>
                        </div>
                    </div>
                </td>
            </tr>

        </table>
    </div>
</div>
<script type="text/ng-template" id="punishPic2.html">
    <img ng-src="{{ picUrl.image }}" style="height: 400px; width: 400px"/>
</script>
<style>

    .tooltip.customClass .tooltip-inner {
        max-width: fit-content;
        padding: 8px 8px;
        background-color: #ffffff;
        box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    }

    .tooltip.customClass .tooltip-arrow {
        display: none;
    }

    .tooltip.customClass.tooltip.in {
        opacity: 1;
    }
    .exp-list li{
        width: 33%;
        float: left;
        padding: 10px;
        text-align: center;
    }
</style>
