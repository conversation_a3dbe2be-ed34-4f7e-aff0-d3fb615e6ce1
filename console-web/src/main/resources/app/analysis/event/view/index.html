<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-gg"></i> 事件详情</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-4" >
                <ui-select class="input-md" ng-model="query.event" theme="bootstrap" ng-change="choiceEvent($select.selected)">
                    <ui-select-match placeholder="选择事件" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="eventObj in eventsList| filter: $select.search" refresh-delay="300">
                        <div uib-tooltip="{{eventObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="eventObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="form-group col-sm-2" id="daysComponent">
                <select ng-model="days" class="form-control input-md" ng-init="days = '6'" ng-change="choiceDays()">
                    <option value="6" selected="selected">7天</option>
                    <option value="14">15天</option>
                    <option value="29">30天</option>
                </select>
            </div>
            <div class="form-group col-sm-4">
                <button ng-click="his()" class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-search"></i> 风控历史
                </button>
            </div>

        </form>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="row" ng-show="currentEvent">
        <div class="col-md-3">
            <div class="box box-widget widget-user">
                <div class="widget-user-header bg-aqua-active">
                    <h3 class="widget-user-username" alt="事件名称" ng-click="goEvent()" style="cursor: pointer;">{{currentEvent.name}}</h3>
                    <h5 class="widget-user-desc" alt="事件code">{{currentEvent.code}}</h5>
                </div>
                <div class="box-footer" style="padding-bottom:0">
                    <div class="row" style="margin-bottom:15px;">
                        <div class="col-md-4 border-right">
                            <div class="description-block text-green">
                                <h5 class="description-header">{{ summary.PASS || 0 }}</h5>
                                <span class="description-text">安全</span>
                            </div>
                        </div>
                        <div class="col-md-4 border-right">
                            <div class="description-block text-yellow">
                                <h5 class="description-header">{{ summary.REVIEW || 0 }}</h5>
                                <span class="description-text">可疑</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="description-block text-red">
                                <h5 class="description-header">{{ summary.REJECT || 0 }}</h5>
                                <span class="description-text">违规</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="box box-primary" style="min-height: 200px;">
                <div class="box-header with-border">
                    <h3 class="box-title">事件详情</h3>
                </div>
                <!-- /.box-header -->
                <div class="box-body">
                    <strong><i class="fa fa-book margin-r-5"></i> 业务</strong>
                    <p>
                        <span class="label label-danger">{{business.name}}</span>
                        <span class="label label-success">{{business.code}}</span>
                        <span class="label label-info">{{business.topic}}</span>
                    </p>
                    <hr>
                    <strong><i class="fa fa-list-ul margin-r-5"></i> 名单组</strong>
                    <p>
                        <span style="margin-right:5px;" class="label {{colorList[$index%5]}}" ng-repeat="grayGroup in grayGroups">{{grayGroup.name}}</span>
                    </p>
                    <hr>
                    <strong><i class="fa fa-registered margin-r-5"></i> 返回Map</strong>
                    <p style="word-break:break-all;">{{currentEvent.returnJson}}</p>
                    <hr>
                    <strong><i class="fa fa-file-text-o margin-r-5"></i> 惩罚话术</strong>
                    <p>{{currentEvent.reply ? currentEvent.reply : '无'}}</p>
                    <ul class="list-group list-group-unbordered">
                        <li class="list-group-item">
                            <b>接入方</b> <span class="pull-right">{{currentEvent.accessBy}}</span>
                        </li>
                        <li class="list-group-item">
                            <b>是否保存规则结果</b> <span class="pull-right">{{currentEvent.saveRuleResult == 1 ? '是' : '否'}}</span>
                        </li>
                        <li class="list-group-item">
                            <b>最后修改人</b> <span class="pull-right">{{currentEvent.modifier}}</span>
                        </li>
                        <li class="list-group-item">
                            <b>最后修改时间</b> <span class="pull-right">{{currentEvent.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="box box-primary" >
                    <div class="box-header with-border">
                        <h3 class="box-title">每日统计</h3>

                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
                            </button>
                            <button type="button" class="btn btn-box-tool" data-widget="remove"><i class="fa fa-times"></i></button>
                        </div>
                    </div>
                    <div class="box-body">
                        <div id="eventDetailContainer" style="height:300px;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="nav-tabs-custom" id="event-detail">
                    <ul class="nav nav-tabs">
                        <li class="active ruleGroup"><a href="javascript:;" ng-click="switchTab('ruleGroup')">规则组</a></li>
                        <li class="ruleAtom"><a href="javascript:;" ng-click="switchTab('ruleAtom')">规则</a></li>
                        <li class="localAttr"><a href="javascript:;" ng-click="switchTab('localAttr')">内部属性</a></li>
                        <li class="factor"><a href="javascript:;" ng-click="switchTab('factor')">累计因子</a></li>
                        <li class="remoteAttr"><a href="javascript:;" ng-click="switchTab('remoteAttr')">外部属性</a></li>
                        <li class="riskLog"><a href="javascript:;" ng-click="switchTab('riskLog')">配置记录</a></li>
                    </ul>
                    <div class="tab-content" style="min-height: 701px;">
                        <div class="tab-pane ruleGroup active" ng-include="'analysis/event/view/ruleGroup.html'"/>
                        <div class="tab-pane ruleAtom" ng-include="'analysis/event/view/ruleAtom.html'"/>
                        <div class="tab-pane localAttr" ng-include="'analysis/event/view/localAttr.html'"/>
                        <div class="tab-pane factor" ng-include="'analysis/event/view/factor.html'"/>
                        <div class="tab-pane remoteAttr" ng-include="'analysis/event/view/remoteAttr.html'"/>
                        <div class="tab-pane riskLog" ng-include="'analysis/event/view/riskLog.html'"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<style>
    .list-group-item{
        border: 1px solid #eee;
    }
</style>