    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>ID</th>
                <th>名称</th>
                <th>优先级</th>
                <th>包含规则</th>
                <th style="width: 12%">最后修改时间</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="ruleGroup in ruleGroups">
                <td>{{ruleGroup.id}}</td>
                <td>{{ruleGroup.name}}</td>
                <td>{{ruleGroup.priority}}</td>
                <td>
                    <div class="box box-widget widget-user-2" ng-if="ruleGroup.atomRules != null && ruleGroup.atomRules.length > 0" style="margin-bottom: 0">
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" ng-repeat="atomRule in ruleGroup.atomRules">
                                <li style="padding:5px;">
                                    <span>{{atomRule.name}}</span>
                                    <small ng-if="atomRule.status=='DISABLE'" class="pull-right label bg-yellow">无效</small>
                                    <small ng-if="atomRule.status=='TEST'" class="pull-right label bg-blue">测试</small>
                                    <small ng-if="atomRule.status=='ENABLE'" class="pull-right label bg-green">有效</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td>{{ruleGroup.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>
                    <small ng-if="ruleGroup.status=='DISABLE'" class="label bg-yellow">无效</small>
                    <small ng-if="ruleGroup.status=='TEST'" class="label bg-blue">测试</small>
                    <small ng-if="ruleGroup.status=='ENABLE'" class="label bg-green">有效</small>
                </td>
                <td>
                    <a href="javascript:void(0)" ng-click="showRuleGroupDetail(ruleGroup)">
                        <span>详情</span>
                    </a>
                    <a href="javascript:void(0)" target="_blank" ui-sref="rule.group({id:ruleGroup.id,name:ruleGroup.name})">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
