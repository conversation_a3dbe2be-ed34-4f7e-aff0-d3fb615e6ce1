    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>ID</th>
                <th>名称</th>
                <th>优先级</th>
                <th>关联规则组</th>
                <th style="width: 12%">最后修改时间</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="ruleAtom in ruleAtoms">
                <td>{{ruleAtom.id}}</td>
                <td>{{ruleAtom.name}}</td>
                <td>{{ruleAtom.priority}}</td>
                <td>
                    <div class="box box-widget widget-user-2" ng-if="ruleAtom.groupRules != null && ruleAtom.groupRules.length > 0" style="margin-bottom: 0">
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" ng-repeat="groupRule in ruleAtom.groupRules">
                                <li style="padding:5px;">
                                    <span>{{groupRule.name}}</span>
                                    <small ng-if="groupRule.status=='DISABLE'" class="pull-right label bg-yellow">无效</small>
                                    <small ng-if="groupRule.status=='TEST'" class="pull-right label bg-blue">测试</small>
                                    <small ng-if="groupRule.status=='ENABLE'" class="pull-right label bg-green">有效</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td>{{ruleAtom.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>
                    <small ng-if="ruleAtom.status=='DISABLE'" class="label bg-yellow">无效</small>
                    <small ng-if="ruleAtom.status=='TEST'" class="label bg-blue">测试</small>
                    <small ng-if="ruleAtom.status=='ENABLE'" class="label bg-green">有效</small>
                </td>
                <td>
                    <a href="javascript:void(0)" ng-click="showRuleAtomDetail(ruleAtom)">
                        <span>详情</span>
                    </a>
                    <a href="javascript:void(0)" target="_blank" ui-sref="rule.atom({id:ruleAtom.id,name:ruleAtom.name})">
                        <span>更多</span>
                    </a>
                </td>
            </tr>
        </table>
    </div>
