<div class="box box-primary" style="min-height: 200px;">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-info-circle"></i> 规则详情</h3>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <td class="text-bold w20">id</td>
                <td>{{ ruleAtomDetail.id }}</td>
            </tr>
            <tr>
                <td class="text-bold w20">名称</td>
                <td>{{ ruleAtomDetail.name }}</td>
            </tr>
            <tr>
                <td class="text-bold w20">优先级</td>
                <td>{{ruleAtomDetail.priority}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">准确度</td>
                <td>{{ruleAtomDetail.accuracy}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">规则内容</td>
                <td>{{ruleAtomDetail.condition}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">依赖字段</td>
                <td>{{ruleAtomDetail.dependent}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">依赖常量</td>
                <td>{{ruleAtomDetail.riskConst}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">惩罚包</td>
                <td>
                    <div class="box box-widget widget-user-2" ng-if="ruleAtomDetail.punishPackages != null && ruleAtomDetail.punishPackages.length > 0" style="margin-bottom: 0">
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" ng-repeat="punishPackage in ruleAtomDetail.punishPackages">
                                <li>
                                    <a href="javascript:void(0)">
                                        <span>{{punishPackage.msg}}</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="text-bold w20">惩罚包属性</td>
                <td>{{ruleAtomDetail.punishPackageAttr}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">返回Map</td>
                <td>{{ruleAtomDetail.returnJson}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">惩罚话术</td>
                <td>{{ruleAtomDetail.reply}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">对内话术</td>
                <td>{{ruleAtomDetail.innerReason}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">对外话术</td>
                <td>{{ruleAtomDetail.outerReason}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">状态</td>
                <td>{{ruleAtomDetail.status == 'DISABLE' ? '无效' : (ruleAtomDetail.status == 'TEST' ? '测试' : '有效')}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">创建人</td>
                <td>{{ruleAtomDetail.author}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">创建时间</td>
                <td>{{ruleAtomDetail.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">最后修改人</td>
                <td>{{ruleAtomDetail.modifier}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">最后修改时间</td>
                <td>{{ruleAtomDetail.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
        </table>
    </div>
</div>
<style>
    .w20{
        width: 20%;
    }
</style>