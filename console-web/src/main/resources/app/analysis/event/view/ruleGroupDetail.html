<div class="box box-primary" style="min-height: 200px;">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-info-circle"></i> 规则组详情</h3>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <td class="text-bold w20">id</td>
                <td>{{ ruleGroupDetail.id }}</td>
            </tr>
            <tr>
                <td class="text-bold w20">名称</td>
                <td>{{ ruleGroupDetail.name }}</td>
            </tr>
            <tr>
                <td class="text-bold w20">优先级</td>
                <td>{{ruleGroupDetail.priority}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">前置条件</td>
                <td>{{ruleGroupDetail.condition}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">依赖字段</td>
                <td>{{ruleGroupDetail.dependent}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">返回Map</td>
                <td>{{ruleGroupDetail.comment}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">惩罚话术</td>
                <td>{{ruleGroupDetail.reply}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">状态</td>
                <td>{{ruleGroupDetail.status == 'DISABLE' ? '无效' : (ruleGroupDetail.status == 'TEST' ? '测试' : '有效')}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">创建人</td>
                <td>{{ruleGroupDetail.author}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">创建时间</td>
                <td>{{ruleGroupDetail.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">最后修改人</td>
                <td>{{ruleGroupDetail.modifier}}</td>
            </tr>
            <tr>
                <td class="text-bold w20">最后修改时间</td>
                <td>{{ruleGroupDetail.updatedAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
            </tr>
        </table>
    </div>
</div>
<style>
    .w20{
        width: 20%;
    }
</style>