    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>配置类型</th>
                <th>变更类型</th>
                <th>修改人</th>
                <th>修改时间</th>
                <th>配置内容</th>
                <th>关联关系</th>
            </tr>
            <tr ng-repeat="log in logs">
                <td>
                    <span ng-if="log.configType == 'risk_event'">事件</span>
                    <span ng-if="log.configType == 'risk_rule_atom'">规则</span>
                    <span ng-if="log.configType == 'risk_rule_group'">规则组</span>
                    <span ng-if="log.configType == 'risk_attribute'">属性</span>
                    <span ng-if="log.configType == 'risk_factor'">累计因子</span>
                    <span ng-if="log.configType == 'risk_fencing_word'">违禁词</span>
                    <span ng-if="log.configType == 'risk_fencing_scene'">违禁词场景</span>
                    <span ng-if="log.configType == 'risk_fencing_tag'">违禁词标签</span>
                    <span ng-if="log.configType == 'risk_gray_list'">名单</span>
                </td>
                <td>
                    <span ng-if="log.updateType == 'insert'" class="text-green">新增</span>
                    <span ng-if="log.updateType == 'update'" class="text-yellow">修改</span>
                    <span ng-if="log.updateType == 'delete'" class="text-red">删除</span>
                </td>
                <td>{{log.modifier}}</td>
                <td>{{log.createTime | date: 'yyyy-MM-dd HH:mm'}}</td>
                <td>
                    <div style="width:400px;overflow:auto">
                        <pre ng-if="log.updateType == 'insert'">{{format(log.after) | json}}</pre>
                        <pre ng-if="log.updateType == 'delete'">{{format(log.before) | json}}</pre>
                        <pre ng-if="log.updateType == 'update'">变更前：<br/>{{format(log.before) | json}}<br/><br/>变更后：<br/>{{format(log.after) | json}}</pre>
                    </div>
                </td>
                <td>
                    <pre ng-if="log.relation != null && log.relation != ''">{{format(log.relation) | json}}</pre>
                </td>
            </tr>
        </table>
        <div class="box-footer">
            <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
                first-text="首页"
                last-text="尾页"
                previous-text="前一页"
                next-text="后一页"
                total-items="total"
                ng-model="page"
                ng-change="pageChanged(page)"
                max-size="10"
                rotate="false">
            </ul>
        </div>
    </div>
