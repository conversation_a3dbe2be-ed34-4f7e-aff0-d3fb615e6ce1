(function () {
    let app = angular.module(app_name);

    app.controller('EventDetailCtrl', function ($scope, $state, $stateParams, $uibModal, Rest, modal, $aside) {

        $scope.query = angular.extend({}, $stateParams);
        $scope.eventsList = [];
        $scope.queryTime = "6";
        $scope.page = 1;
        $scope.colorList = ["label-danger","label-success","label-info","label-warning","label-primary"]
        $scope.loading = true;
        $scope.loading = Rest.all('/detail/event/getEvents').post().then(function (result) {
            let data = result.data;
            let paramEvent = null;
            for (let key in data) {
                let event = {'id': key, 'name': data[key]};
                $scope.eventsList.push(event);
                if($scope.query.value && key == $scope.query.value){
                    paramEvent = event;
                }
            }
            if(paramEvent != null){
                $scope.query.event = paramEvent;
                $scope.choiceEvent(paramEvent);
            }
        });
        $scope.currentEvent = null;
        $scope.business = null;
        $scope.grayGroups = [];
        $scope.initStatus = {};
        $scope.searchedList = {};
        $scope.riskLogParam = {};
        $scope.choiceEvent = function (event) {
            $scope.currentEvent = null;
            $scope.business = null;
            $scope.grayGroups = [];
            $scope.ruleGroups = [];
            $scope.summary = {};
            $scope.initStatus = {};
            $scope.simpleLocalAttrs = null;
            $scope.loadedFlag = {};
            $scope.riskLogParam = {};
            $scope.page = 1;
            $scope.searchEvent(event);
        }
        $scope.searchEvent = function(event){
            if(event){
                $scope.loading = Rest.all('/detail/event/detail').post({"code":event.id}).then(function (response) {
                    $scope.currentEvent = response.event;
                    if($scope.currentEvent){
                        $scope.business = response.business;
                        $scope.grayGroups = response.grayGroups;
                        $scope.switchTab("ruleGroup");
                        $scope.riskSummary();
                        $scope.searchRiskData();
                    }
                });
            }
        }
        $scope.choiceDays = function(){
            if($scope.currentEvent){
                $scope.riskSummary();
                $scope.searchRiskData();
            }
        }
        $scope.goEvent = function(){
            openUrlNew = $state.href('core.event', {"id":$scope.currentEvent.id,"code":$scope.currentEvent.code});
            window.open(openUrlNew, '_blank')
        }
        $scope.searchRuleGroup = function(resolve){
            $scope.ruleGroups = [];
            $scope.loading = Rest.all('/detail/event/searchRuleGroup').post({"ruleGroupId":$scope.currentEvent.ruleGroupId}).then(function (response) {
                $scope.ruleGroups = response;
                $scope.loadedFlag.ruleGroup = true;
                if(resolve){
                    resolve("ruleGroup");
                }
            });
        }
        $scope.searchRuleAtom = function(resolve){
            $scope.ruleAtoms = [];
            $scope.loading = Rest.all('/detail/event/searchRuleAtom').post({"ruleGroupId":$scope.currentEvent.ruleGroupId}).then(function (response) {
                $scope.ruleAtoms = response;
                $scope.loadedFlag.ruleAtom = true;
                if(resolve){
                    resolve("ruleAtom");
                }
            });
        }
        $scope.searchLocalAttr = function(resolve){
            $scope.localAttrs = [];
            $scope.simpleLocalAttrs = [];
            $scope.loading = Rest.all('/detail/event/searchAttr').post({"ruleGroupId":$scope.currentEvent.ruleGroupId,"type":"LOCAL"}).then(function (response) {
                $scope.localAttrs = response;
                if($scope.localAttrs){
                    for(let i=0;i<$scope.localAttrs.length;i++){
                        let localAttr = $scope.localAttrs[i];
                        $scope.simpleLocalAttrs.push({"id":localAttr.id,"name":localAttr.name,"dependent":localAttr.dependent});
                    }
                }
                $scope.loadedFlag.localAttr = true;
                if(resolve){
                    resolve("localAttr");
                }
            });
        }
        $scope.searchRemoteAttr = function(resolve){
            $scope.reomteAttrs = [];
            $scope.loading = Rest.all('/detail/event/searchAttr').post({"ruleGroupId":$scope.currentEvent.ruleGroupId,"type":"REMOTE"}).then(function (response) {
                $scope.reomteAttrs = response;
                $scope.loadedFlag.remoteAttr = true;
                if(resolve){
                    resolve("remoteAttr");
                }
            });
        }
        $scope.searchFactor = function(resolve){
            $scope.factors = [];
            $scope.loading = Rest.all('/detail/event/searchFactor').post({"ruleGroupId":$scope.currentEvent.ruleGroupId,"attributes":$scope.simpleLocalAttrs}).then(function (response) {
                $scope.factors = response;
                $scope.loadedFlag.factor = true;
                if(resolve){
                    resolve("factor");
                }
            });
        }
        $scope.riskSummary = function(){
            $scope.summary = {};
            $scope.loading = Rest.all('/detail/event/riskSummary').post({"eventCode":$scope.currentEvent.code,"days":$scope.days}).then(function (response) {
                $scope.summary = response;
            });
        }
        $scope.searchRiskLog = function(){
            $scope.logs = [];
            $scope.total = 0;
            let promiseArr = [];
            if(!$scope.loadedFlag.hasOwnProperty("ruleGroup")){
                $scope.initStatus["ruleGroup"] = true;
                let p1 = new Promise(function(resolve, reject){
                    $scope.searchRuleGroup(resolve);
                });
                promiseArr.push(p1);
            }
            if(!$scope.loadedFlag.hasOwnProperty("ruleAtom")){
                $scope.initStatus["ruleAtom"] = true;
                let p2 = new Promise(function(resolve, reject){
                    $scope.searchRuleAtom(resolve);
                });
                promiseArr.push(p2);
            }
            if(!$scope.loadedFlag.hasOwnProperty("localAttr")){
                $scope.initStatus["localAttr"] = true;
                let p3 = new Promise(function(resolve, reject){
                    $scope.searchLocalAttr(resolve);
                });
                promiseArr.push(p3);
            }
            if(!$scope.loadedFlag.hasOwnProperty("remoteAttr")){
                $scope.initStatus["remoteAttr"] = true;
                let p4 = new Promise(function(resolve, reject){
                    $scope.searchRemoteAttr(resolve);
                });
                promiseArr.push(p4);
            }
            if(!$scope.loadedFlag.hasOwnProperty("factor")){
                $scope.initStatus["factor"] = true;
                let p5 = new Promise(function(resolve, reject){
                    $scope.searchFactor(resolve);
                });
                promiseArr.push(p5);
            }
            if(promiseArr.length > 0){
                Promise.all(promiseArr).then(
                    (res) => {
                        initRiskLogParam();
                        $scope.pageChanged(1);
                    }
                    ,(err) => console.log(err)
                )
            }else{
                initRiskLogParam();
                $scope.pageChanged(1);
            }
        }
        function initRiskLogParam(){
            $scope.riskLogParam.event = {"id":$scope.currentEvent.id};
            if($scope.ruleGroups){
                $scope.riskLogParam.ruleGroups = [];
                for(let i=0;i<$scope.ruleGroups.length;i++){
                    let ruleGroup = $scope.ruleGroups[i];
                    $scope.riskLogParam.ruleGroups.push({"id":ruleGroup.id});
                }
            }
            if($scope.ruleAtoms){
                $scope.riskLogParam.ruleAtoms = [];
                for(let i=0;i<$scope.ruleAtoms.length;i++){
                    let ruleAtom = $scope.ruleAtoms[i];
                    $scope.riskLogParam.ruleAtoms.push({"id":ruleAtom.id});
                }
            }
            if($scope.localAttrs){
                $scope.riskLogParam.localAttrs = [];
                for(let i=0;i<$scope.localAttrs.length;i++){
                    let localAttr = $scope.localAttrs[i];
                    $scope.riskLogParam.localAttrs.push({"id":localAttr.id});
                }
            }
            if($scope.reomteAttrs){
                $scope.riskLogParam.remoteAttrs = [];
                for(let i=0;i<$scope.reomteAttrs.length;i++){
                    let remoteAttr = $scope.reomteAttrs[i];
                    $scope.riskLogParam.remoteAttrs.push({"id":remoteAttr.id});
                }
            }
            if($scope.factors){
                $scope.riskLogParam.factors = [];
                for(let i=0;i<$scope.factors.length;i++){
                    let factor = $scope.factors[i];
                    $scope.riskLogParam.factors.push({"id":factor.id});
                }
            }
        }
        $scope.searchRiskLogInner = function(page){
            $scope.page = page || $scope.page || 1;
            $scope.loading = true;
            $scope.loading = Rest.all('detail/event/searchRiskLog?p=' + $scope.page).post($scope.riskLogParam).then(function (response) {
                $scope.logs = response.list;
                $scope.total = response.total;
            });
        }
        $scope.searchRiskData = function(){
            $scope.riskData = [];
            $scope.loading = Rest.all('/detail/event/riskData').post({"eventCode":$scope.currentEvent.code,"days":$scope.days}).then(function (response) {
                $scope.riskData = response;
                if($scope.riskData){
                    let xAxisData = [];
                    let passSeriesData = [];
                    let reviewSeriesData = [];
                    let rejectSeriesData = [];
                    for(let i=0;i<$scope.riskData.length;i++){
                        let data = $scope.riskData[i];
                        let createTime = data.createTime;
                        createTime = createTime.substring(5);
                        createTime = createTime.replace("-","/");
                        xAxisData.push(createTime);
                        passSeriesData.push(data.PASS);
                        reviewSeriesData.push(data.REVIEW);
                        rejectSeriesData.push(data.REJECT);
                    }
                    option.xAxis.data = xAxisData;
                    option.series[0].data = passSeriesData;
                    option.series[1].data = reviewSeriesData;
                    option.series[2].data = rejectSeriesData;
                    if (option && typeof option === "object") {
                        myChart.setOption(option, true);
                    }
                }
            });
        }
        $scope.switchTab = function (target) {
            $("#event-detail .active").removeClass("active");
            $("#event-detail ." + target).addClass("active");
            if ($scope.initStatus[target] == true) {
                return;
            }
            if ('ruleGroup' == target) { // 初始化规则组数据
                $scope.searchRuleGroup();
            }else if('ruleAtom' == target){
                $scope.searchRuleAtom();
            }else if('localAttr' == target){
                $scope.searchLocalAttr();
            }else if('remoteAttr' == target){
                $scope.searchRemoteAttr();
            }else if('factor' == target){
                $scope.searchFactor();
            }else if('riskLog' == target){
                $scope.searchRiskLog();
            }
            $scope.initStatus[target] = true;
        };
        $scope.showRuleGroupDetail = function (ruleGroup) {
            $aside.open({
                templateUrl: 'analysis/event/view/ruleGroupDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.ruleGroupDetail = ruleGroup;
                }
            });
        };
        $scope.showRuleAtomDetail = function (ruleAtom) {
            $aside.open({
                templateUrl: 'analysis/event/view/ruleAtomDetail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.ruleAtomDetail = ruleAtom;
                }
            });
        };
        $scope.his = function () {
            if (!$scope.currentEvent) {
                Messenger().post({type: 'error', message: '请先选择事件！'});
                return;
            }
            openUrlNew = $state.href('history.log', {eventCode: $scope.currentEvent.code,eventName: $scope.currentEvent.name});
            window.open(openUrlNew, '_blank')
        };
        $scope.pageChanged = function (page) {
            $scope.page = page;
            $scope.searchRiskLogInner($scope.page);
        };
        $scope.format = function (json) {
            let newJson = JSON.parse(json);
            if (newJson.riskConst) {
                newJson.riskConst = JSON.parse(newJson.riskConst);
            }
            return newJson;
        };
        $("#eventDetailContainer").css('width',$("#daysComponent").width()*4.5);
        let dom = document.getElementById("eventDetailContainer");
        let myChart = echarts.init(dom);
        let app = {};
        let option = null;
        option = {
            color: ['#00a65a', '#f39c12', '#dd4b39'],
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['安全', '可疑', '违规']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '安全',
                    type: 'line'
                },
                {
                    name: '可疑',
                    type: 'line'
                },
                {
                    name: '违规',
                    type: 'line'
                }
            ]
        };
    });
})();