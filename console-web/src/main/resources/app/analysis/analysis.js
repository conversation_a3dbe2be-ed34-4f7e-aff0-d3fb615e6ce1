(function () {
    var app = angular.module(app_name);

    app.config(function ($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.when('/detail', '/detail/user');
        $stateProvider.state('detail', {
            parent: 'home',
            url: '/detail',
            templateUrl: 'analysis/index.html'
        });

        $stateProvider.state('detail.user', {
            url: '/user?value&accountType',
            templateUrl: 'analysis/user/view/index.html',
            controller: 'UserDetailCtrl'
        });

        $stateProvider.state('detail.event', {
            url: '/event?value',
            templateUrl: 'analysis/event/view/index.html',
            controller: 'EventDetailCtrl'
        });

        $stateProvider.state('detail.clean', {
            url: '/cleanLog',
            templateUrl: 'analysis/clean/view/index.html',
            controller: 'cleanCtrl'
        });

        $stateProvider.state('detail.rule', {
            url: '/rule?value',
            templateUrl: 'analysis/rule/view/index.html',
            controller: 'RuleDetailCtrl'
        });

    });
})();
