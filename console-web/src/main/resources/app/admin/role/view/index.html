<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/> 角色列表</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-2">
                <input ng-model="name" type="text" class="form-control input-md" placeholder="角色名">
            </div>
            <div class="form-group col-sm-2">
                <input ng-model="description" type="text" class="form-control input-md" placeholder="角色描述">
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索</button>
            <button ng-click="add()" class="btn btn-flat btn-md btn-info"><i class="fa fa-pencil fa-fw"></i>新增</button>
        </form>

    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>角色名</th>
                <th>角色描述</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="role in roles" ng-if="user.username != 'admin'">
                <td>{{role.name}}</td>
                <td>{{role.description}}</td>
                <td>
                    <button uib-tooltip="编辑" class="btn btn-flat btn-md btn-info" ng-click="edit(role)" ng-if="role.name != 'ADMIN'">
                        <i class="fa fa-pencil fa-fw"></i>
                    </button>
                    <button uib-tooltip="权限" class="btn btn-flat btn-md btn-info" ng-click="permission(role)" ng-if="role.name != 'ADMIN'">
                        <i class="fa fa-cog fa-fw"/>
                    </button>
                    <button class="btn btn-flat btn-md btn-danger" ng-click="delete(role)" ng-if="role.name != 'ADMIN'">
                        <i class="fa fa-remove fa-fw"/>
                    </button>
                </td>
            </tr>

        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
