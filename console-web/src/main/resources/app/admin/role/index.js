(function () {
    var app = angular.module(app_name);

    app.controller('roleCtrl', function ($scope, $state, $uibModal, modal, Rest, UserService) {
        $scope.username = '';
        go();

        function go(page) {
            $scope.page = page || $scope.page || 1;
            $scope.loading = Rest.all('role/search?p=' + $scope.page).post({name: $scope.name,description:$scope.description}).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.roles = response.list;
                $scope.total = response.total;
            });
        }

        $scope.search = function () {
            go(1);
        };

        $scope.pageChanged = function () {
            go($scope.page);
        };

        $scope.add = function () {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/role/view/add.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {

                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        var role = angular.copy($scope.role);
                        $scope.loading = Rest.service('role/add').post(role).then(function () {
                            Messenger().post({type: 'success', message: '保存成功'});
                            go();
                            $uibModalInstance.close();
                        });
                    };
                }
            });
        };

        // 编辑
        $scope.edit = function (role) {
            $scope.editRole = angular.copy(role);
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/role/view/edit.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {
                    $scope.role = angular.copy(role);
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        var role = {};
                        role.id = $scope.role.id;
                        role.name = $scope.role.name;
                        role.description = $scope.role.description;

                        $scope.loading = Rest.service('role/update/' + role.id).post(role).then(function () {
                            Messenger().post({type: 'success', message: '更新成功'});
                            go();
                            $uibModalInstance.close();
                        });
                    };

                }
            });
        };

        var $permissionZTree;
        // 配置权限
        $scope.permission = function (role) {
            $permissionZTree = null; // 清除tree
            $scope.permissionRole = angular.copy(role);
            var modalInstance = $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/role/view/permission.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {
                    $scope.role = angular.copy(role);
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        var role = {};
                        role.id = $scope.role.id;
                        // console.log('0--0');
                        if($permissionZTree){
                            // 获取权限数据
                            var items = $permissionZTree .getCheckedNodes(true);
                            // console.log('items:'+JSON.stringify(items));
                            var permissions = new Array();
                            for(var i=0;i<items.length;i++){
                                permissions.push(items[i].id);
                            }
                            role.permissions = permissions;
                            // console.log('-=-=-=-:'+JSON.stringify(role));

                            $scope.loading = Rest.service('role/update/' + role.id+"/permissions").post(role).then(function () {
                                Messenger().post({type: 'success', message: '更新成功'});
                                // go();
                                $uibModalInstance.close();
                            });
                        }

                    };

                }
            });

            // 初始化tree
            modalInstance.rendered.then(function () {
                $scope.loading = Rest.all('role/'+$scope.permissionRole.id+'/permissions').post().then(function (permissions) {
                    // console.log("permissions:"+JSON.stringify(permissions));
                    var setting = {
                        check: {
                            enable: true
                        },
                        data: {
                            simpleData: {
                                enable: true
                            }
                        }
                    };
                    // for(var i=0;i<permissions.length;i++){
                    //     var item = permissions[i];
                    //     item.pId = item.pid;
                    // }
                    // console.log("permissions222:"+JSON.stringify(permissions));
                    $permissionZTree = $.fn.zTree.init($("#permissionZTree"), setting, permissions);
                    $permissionZTree.expandAll(true); // 默认全部展开
                });

            });

        };

        // 删除
        $scope.delete = function (role) {
            modal.confirm('确定删除1 [ ' + role.name + ' ] ?', function () {
                Rest.all('role/delete/' + role.id).post().then(function () {
                    Messenger().post('删除成功!');
                    go();
                });
            });
        };

    });

})();