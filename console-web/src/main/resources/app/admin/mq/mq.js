(function () {
    var app = angular.module(app_name);

    app.controller('MQCtrl', function ($scope, $state, $uibModal, Rest) {
        $scope.sql = '';
        $scope.execute = function () {
            $scope.loading = Rest.all('mq/execute').post({
                topic: $scope.topic,
                message: $scope.message,
                kafkaType: $scope.kafkaType
            }).then(function (response) {
                Messenger().post({type: 'success', message: 'MQ发送完成!'});
                $scope.result = response || '0';
            });
        };
    });
})();