<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/> 自定义MQ</h3>
        <button ng-click="execute()" class="btn btn-flat btn-md btn-info pull-right"><i class="fa fa-play"></i> 执行
        </button>
    </div>
    <div class="box-body">
        <div class="form-group col-sm-2">
            <select ng-model="kafkaType" class="form-control input-md" ng-init="kafkaType = '1'">
                <option value="1" selected="selected">风控集群</option>
                <option value="2" >核心集群</option>
                <option value="3" >大数据集群</option>
            </select>
        </div>
        <div class="form-group col-sm-10">
        <input required="" ng-model="topic" type="text" class="form-control" placeholder="MQ Topic">
        </div>
        <br/>
        <textarea name="value" ng-model="message" type="text" class="form-control input-md"
                  placeholder="MQ消息体，JSON格式，多条消息以换行隔开" required autocomplete="off" rows="20"/>
        <br/>
        <div>发送结果:&nbsp;<span class="text-green">{{result}}</span>&nbsp;</div>
    </div>
</div>
