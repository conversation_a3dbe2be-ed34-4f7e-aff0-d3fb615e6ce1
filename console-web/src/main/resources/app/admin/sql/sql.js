(function () {
    var app = angular.module(app_name);

    app.controller('SQLCtrl', function ($scope, $state, $uibModal, Rest) {
        $scope.sql = '';
        $scope.execute = function () {
            $scope.loading = Rest.all('sql/execute').post({sql: $scope.sql}).then(function (response) {
                Messenger().post({type: 'success', message: 'SQL执行完成!'});
                $scope.result = response || '0';
            });
        };
    });
})();