<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/> 自定义SQL</h3>
        <button ng-click="execute()" class="btn btn-flat btn-md btn-info pull-right"><i class="fa fa-play"></i> 执行
        </button>
    </div>
    <div class="box-body">
        <textarea name="value" ng-model="sql" type="text" class="form-control input-md"
                  placeholder="SQL，仅限delete、update、insert、批量insert（用;隔开） 操作" required autocomplete="off" rows="20"/>
        <br/>
        <div>SQL执行结果:&nbsp;<span class="text-green">{{result}}</span>&nbsp;</div>
    </div>
</div>
