<div class="row" style="margin:0">
    <div class="box-search">
        <form>
            <div class="col-md-12" style="padding:0">
                <div class="form-group col-sm-2">
                    <input ng-model="traceId" type="text" class="col-md-6" style="width: 100%;height: 34px"
                           placeholder="Trace ID">
                </div>
                <div class="form-group col-sm-2">
                    <ui-select class="input-md" ng-model="query.event" theme="bootstrap" ng-change="choiceEvent($select.selected)">
                        <ui-select-match placeholder="选择事件" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                        <ui-select-choices repeat="eventObj in eventsList| filter: $select.search" refresh-delay="300">
                            <div uib-tooltip="{{eventObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="eventObj.name"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
                <div class="form-group col-sm-2">
                    <select ng-model="mode" class="form-control input-md"
                            ng-options="item.value for item in modes">
                        <option value="" selected="selected">-模式(默认单条)-</option>
                    </select>
                </div>
                <button ng-click="execute()" class="btn btn-flat btn-md btn-info pull-right"><i class="fa fa-play"></i> 执行
                </button>
                <button ng-click="process()" class="btn btn-flat btn-md btn-info pull-right"><i class="fa fa-code"></i>JSON检测
                </button>
                <button ng-click="extractSampleData()" class="btn btn-flat btn-md btn-success pull-right"><i class="fa fa-play"></i> 抽取样本数据
                </button>
            </div>
        </form>
    </div>
    <div class="col-md-12" style="padding:0">
        <textarea name="content" ng-model="content" type="text" class="form-control input-md"
                  placeholder="JSON请求参数" required autocomplete="off" rows="20"/>
        <br/>
        <textarea name="result" ng-model="result" type="text" class="form-control input-md"
                  placeholder="执行结果" required autocomplete="off" rows="10" readonly/>
        <br/>
    </div>
</div>
