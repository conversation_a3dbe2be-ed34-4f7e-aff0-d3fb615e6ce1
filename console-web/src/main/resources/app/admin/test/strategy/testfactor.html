<div class="row" style="margin:0">
    <div class="col-md-12" style="padding:0">
        <div class="col-md-6" style="padding:0">
            <div class="col-md-6" style="padding:0 15px 0 0">
                <input ng-model="traceId" type="text" class="col-md-6" style="width: 100%;height: 34px"
                       placeholder="Trace ID">
            </div>
            <button ng-click="searchHitLog()" class="btn btn-flat btn-md btn-info"><i class="fa fa-code"></i>查询风控数据
            </button>
            <div ui-ace="{useWrapMode : true,showGutter: true,theme:'sqlserver',mode: 'json',firstLineNumber: 1,onLoad: aceLoaded,onChange: aceChanged}"
                 ng-model="content" style="height: 390px;width: 100%;"></div>
            <br/>
        </div>
        <div class="col-md-6" style="padding:0 15px 0 0">
            <div class="col-md-6" style="padding:0 15px 0 0;width: 50%" >
                <ui-select class="input-md" ng-model="factorObj" theme="bootstrap" ng-change="factorChange($select.selected)">
                    <ui-select-match placeholder="选择因子" allow-clear="true">{{$select.selected.comment}}</ui-select-match>
                    <ui-select-choices repeat="tag in factorList| filter: $select.search" refresh-delay="300">
                        <div uib-tooltip="{{tag.comment}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="tag.comment"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <button ng-click="testFactor()" class="btn btn-flat btn-md btn-info"><i class="fa fa-play"></i> 执行
            </button>
            <button ng-click="updateFactor(factorObj)" class="btn btn-flat btn-md btn-warning pull-right">
                <i class="fa fa-pencil fa-fw"></i>提交修改
            </button>
            <div ui-ace="{useWrapMode : true,showGutter: true,theme:'twilight',mode: 'groovy',firstLineNumber: 1,onLoad: aceLoaded,onChange: aceChanged}"
                 ng-model="factorObj.condition" style="height: 415px;width: 100%;"></div>
        </div>

        <div class="col-md-12" style="padding:0">
                <textarea name="result" ng-model="result" type="text" class="form-control input-md"
                          placeholder="执行结果" required autocomplete="off" rows="10" readonly/>
            <br/>
        </div>
    </div>

</div>
