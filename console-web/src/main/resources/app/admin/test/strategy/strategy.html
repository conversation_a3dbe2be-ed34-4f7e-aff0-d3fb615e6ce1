<div class="row" style="margin:0">
    <div class="col-md-12" style="padding:0">

        <div class="col-md-5" style="padding:0">
            <div class="col-md-6" style="padding:0 15px 0 0">
                <input ng-model="traceId" type="text" class="col-md-6" style="width: 100%;height: 34px"
                       placeholder="Trace ID">
            </div>
            <button ng-click="searchHitLog()" class="btn btn-flat btn-md btn-info"><i class="fa fa-code"></i>查询风控数据
            </button>
            <div ui-ace="{useWrapMode : true,showGutter: true,theme:'sqlserver',mode: 'json',firstLineNumber: 1,onLoad: aceLoaded,onChange: aceChanged}"
                 ng-model="content" style="height: 390px;width: 100%;"></div>
            <br/>
        </div>
        <div class="col-md-1" style="padding:0"></div>

        <div class="col-md-6" style="padding:0 15px 0 0">

            <div class="col-md-6" style="padding:0 15px 0 0">
                <ui-select class="input-md" ng-model="ruleObj" theme="bootstrap" ng-change="ruleChange($select.selected)">
                    <ui-select-match placeholder="选择规则" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="rule in atomRules| filter: $select.search" refresh-delay="300">
                        <div ng-bind-html="rule.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <button ng-click="testRule()" class="btn btn-flat btn-md btn-info"><i class="fa fa-play"></i> 执行
            </button>
            <button ng-click="updateRule(ruleObj)" class="btn btn-flat btn-md btn-warning pull-right">
                <i class="fa fa-pencil fa-fw"></i>提交修改
            </button>
            <div ui-ace="{useWrapMode : true,showGutter: true,theme:'twilight',mode: 'groovy',firstLineNumber: 1,onLoad: aceLoaded,onChange: aceChanged}"
                 ng-model="ruleObj.condition" style="height: 64px;width: 100%;" ng-mouseleave="parseCondition()"></div>
            <br />
            <input ng-model="ruleObj.dependent" type="text" class="form-control input-md"
                   placeholder="规则内容依赖字段(可编辑)，多个用英文逗号分割" name="dependent" style="height: 40px"
                   ng-maxlength="500" autocomplete="off" ng-readonly="isReadOnly">
            <br/>
            <textarea ng-model="ruleObj.riskConst" type="text" class="form-control"
                      placeholder="若外部属性存在依赖字段时，才可编辑" name="riskConst" style="resize:none; height: 108px"
                      ng-readonly="!ruleObj.riskConst" ng-blur="riskConstChange()"></textarea>
            <span class="required" ng-show="ruleObj.dependent.$dirty && ruleObj.dependent.$invalid">
                        <span ng-show="ruleObj.dependent.$error.maxlength">最长为500位</span>
                    </span>
            <br/>
        </div>

        <div ng-hide="results" class="col-md-12" style="padding:0">
                <textarea name="result" ng-model="result" type="text" class="form-control input-md"
                          placeholder="执行结果" required autocomplete="off" rows="10" readonly/>
            <br/>
        </div>

        <div ng-show="results" class="box-body">
            <table class="table table-bordered">
                <tr>
                    <th style="width: 10%">属性名称</th>
                    <th style="width: 10%">执行结果</th>
                    <th style="width: 80%">详情</th>
                </tr>
                <tr ng-repeat="result in results" >
                    <td style="width: 100px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden">{{result.name}}</td>
                    <td style="width: 20px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden">
                        <span ng-if="result.level == 'PASS'"
                              class="label label-success mid-label btn">{{result.level}}</span>

                        <span ng-if="result.level == 'REVIEW'" class="label label-warning mid-label btn">{{result.level}}</span>

                        <span ng-if="result.level == 'REJECT'"
                              class="label label-danger mid-label btn">{{result.level}}</span>

                        <span ng-if="result.name=='规则结果' && result.level != 'REVIEW'&&result.level != 'REJECT'&&result.level != 'PASS'"
                              class="label label-info mid-label btn">{{result.level}}</span>
                    </td>
                    <td>
                        {{result.detail}}
                    </td>
                </tr>

            </table>
        </div>
    </div>

</div>
