(function () {
    var app = angular.module(app_name);

    var modes = [
        {key: 0, value: "单条"},
        {key: 1, value: "批量"}
    ];

  /*  app.directive('uiAce',[function () {
        return {
            restrict : 'EA',
            require: '?ngModel',
            link: function ($scope,$element,$attributes,ngModel) {
                if (angular.isUndefined(window.ace)) {
                    throw new Error('ui-ace need ace to work...');
                }
                var acee = window.ace.edit($element[0]);
                window.ace.edit.session.setMode()
                var session = acee.getSession();

                if(ngModel){
                    ngModel.$formatters.push(function (value) {
                        if(angular.isUndefined(value) || value ==null){
                            return '';
                        }else {
                            return value;
                        }
                    });
                    ngModel.$render = function () {
                        var text = JSON.stringify(ngModel.$viewValue, null, '\t');
                        session.setValue(text);
                    };
                }
            }
        }
    }]);*/

    app.config(function ($stateProvider, $urlRouterProvider) {

        $urlRouterProvider.when('/admin', '/test/view');
        $stateProvider.state('test', {
            parent: 'home',
            url: '/test',
            templateUrl: 'admin/test/testHome.html',
        });

        $stateProvider.state('test.all', {
            url: '/view',
            templateUrl: 'admin/test/view/index.html',
            controller: 'testCtrl'
        });

        $stateProvider.state('test.rule', {
            url: '/rule',
            templateUrl: 'admin/test/strategy/strategy.html',
            controller: 'testStrategyCtrl'
        });

        $stateProvider.state('test.factor', {
            url: '/factor',
            templateUrl: 'admin/test/strategy/testfactor.html',
            controller: 'testFactorCtrl'
        });
    });

    app.controller('testStrategyCtrl', function ($scope, $state, $uibModal, Rest, modal) {
        $scope.ruleObj ="";
        function go() {
            Rest.all('rule/atom/all').post({name: name}).then(function (response) {
                $scope.atomRules = response;
            });
        }
        go();
        $scope.ruleChange = function (rule) {
            $scope.ruleObj = rule;
            if ($scope.ruleObj && $scope.ruleObj.riskConst) {
                $scope.ruleObj.riskConst = JSON.stringify(JSON.parse($scope.ruleObj.riskConst), null, "    ");
            }
        };

        $scope.searchHitLog = function(){
            var param = {};
            var query = {};
            query.traceId = $scope.traceId;
            param.query= query
            $scope.loading = Rest.all('log/search/hit').post(param).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.results = null;
                $scope.ruleObj = null;
                $scope.content = JSON.stringify(response.list[0]);
                $scope.process()
                $scope.total = response.total;
            });
        }

        $scope.updateRule = function (atomRule) {
            $scope.loading = Rest.all('rule/atom/update/' + atomRule.id).post(atomRule).then(function (response) {
                $uibModalInstance.close();
            })
        };
        $scope.content = '';
        $scope.result = '';
        $scope.testRule = function () {
            if (!$scope.ruleObj){
                Messenger().post({type: 'error', message: '请选择规则!'});
                return;
            }
            if(!$scope.process()){
                Messenger().post({type: 'error', message: '未通过监测，错误详情请查看执行结果!'});
                return;
            }

            if($scope.ruleObj.riskConst && !$scope.riskConstChange()){
                Messenger().post({type: 'error', message: '未通过监测，错误详情请查看执行结果!'});
                return;
            }
            var requestMap = {};
            requestMap.ruleObj = angular.copy($scope.ruleObj);
            requestMap.content = angular.copy($scope.content);
            $scope.loading = Rest.all('risk/test/rule/execute').post(requestMap).then(function (response) {
                Messenger().post({type: 'success', message: '执行完成!'});
                try{
                    var res = response;
                    $scope.results = res.attributeResult.strategyVOList
                }catch (e) {
                    $scope.results = response.result;
                }
            });
        };

        $scope.riskConstChange = function () {
            $scope.results = null; // 上次结果清空
            try{
                if(!$scope.ruleObj.riskConst){
                    $scope.result = "未检测到外部常量内容";
                    return false;
                }
                //json-schema.js
                var result = jsl.parser.parse($scope.ruleObj.riskConst);
                if(result) {
                    $scope.result = '外部属性的JSON格式正常';
                    $scope.ruleObj.riskConst = JSON.stringify(JSON.parse($scope.ruleObj.riskConst), null, "    ");
                    return true;
                } else {
                    $scope.result = "外部属性的JSON数据解析失败";
                }
            }catch(e){
                $scope.result = "JSON数据格式不正确:\n"+e.message;
            }
            return false;
        };

        $scope.process = function () {
            var result;
            try{
                if(!$scope.content){
                    $scope.result = "未检测到内容";
                    return false;
                }
                //json-schema.js
                result = jsl.parser.parse($scope.content);
                if(result) {
                    $scope.result = '正确的JSON格式';
                    $scope.content = JSON.stringify(JSON.parse($scope.content), null, "    ");
                    return true;
                } else {
                    $scope.result = "JSON数据解析失败";
                }
            }catch(e){
                try {
                    $scope.content = jsl.format.formatJson($scope.content);
                    result = jsl.parser.parse($scope.content)
                } catch(e) {
                    parseException = e
                }
                $scope.result = "JSON数据格式不正确:\n"+e.message;
            }
            return false;
        };

        $scope.parseCondition = function () {
            if ($scope.ruleObj.condition) {
                Rest.all('groovy/parseCondition').post({
                    ruleDetail: $scope.ruleObj.condition
                }).then(function (re) {
                    if (re.success) {
                        if ($scope.ruleObj.dependent) {
                            var t = $scope.ruleObj.dependent.split(",");
                            var k = _.union(t, re.set);
                            $scope.ruleObj.dependent = k.join(",");
                        } else {
                            $scope.ruleObj.dependent = re.set.join(",");
                        }

                    }
                    else {
                        $uibModal.open({
                            templateUrl: 'core/rule/lg_alert.html',
                            size: 'lg',
                            controller: function ($scope, $uibModalInstance) {
                                $scope.title = "错误信息";
                                $scope.getMsg = function () {
                                    $('#alert_desc').html(re.msg.replace(/\r\n/g, '<br>').replace(/\s\s/g, '&nbsp;&nbsp;&nbsp;'))
                                }
                                $scope.confirm = function () {
                                    $uibModalInstance.close();
                                };
                            }
                        });

                    }
                });
            }
        };
    });

    app.controller('testFactorCtrl', function ($scope, $state, $uibModal, Rest, modal) {

        $scope.choiceFactor = function (comment) {
            Rest.all('factor/all').post({comment: comment}).then(function (response) {
                $scope.factorList = response;
            });
        };
        $scope.factorObj ="";

        $scope.factorChange = function (factor) {
            $scope.factorObj =factor;
        };
        function go() {
            Rest.all('factor/all').post({}).then(function (response) {
                $scope.factorList = response;
            });
        }
        go();
        $scope.searchHitLog = function(){
            var param = {};
            var query = {};
            query.traceId = $scope.traceId;
            param.query= query
            $scope.loading = Rest.all('log/search/hit').post(param).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.results = response.list;
                $scope.content = JSON.stringify(response.list[0]);
                $scope.process()
                $scope.total = response.total;
            });
        }
        // choiceFactor();
        $scope.updateFactor = function (factor) {
            $scope.loading = Rest.all('factor/update/' + factor.id).post(factor).then(function () {
                Messenger().post({type: 'success', message: '更新因子完成!'});
            });
        }
        $scope.content = '';
        $scope.result = '';
        $scope.testFactor = function () {
            if (!$scope.factorObj){
                Messenger().post({type: 'error', message: '请选择因子!'});
                return;
            }
            if(!$scope.process()){
                Messenger().post({type: 'error', message: '未通过监测，错误详情请查看执行结果!'});
                return;
            }
            var requestMap = {};
            if (!$scope.factorObj){
                Messenger().post({type: 'error', message: '请选择测试因子'});
                return;
            }
            requestMap.factor = angular.copy($scope.factorObj);
            requestMap.content = angular.copy($scope.content);
            $scope.loading = Rest.all('risk/test/attribute/execute').post(requestMap).then(function (response) {
                Messenger().post({type: 'success', message: '执行完成!'});
                try{
                    var res = response;
                    $scope.result = JSON.stringify(JSON.parse(res), undefined, 2);
                }catch (e) {
                    $scope.result = response;
                }
            });
        };
        $scope.process = function () {
            var result;
            try{
                if(!$scope.content){
                    $scope.result = "未检测到内容";
                    return false;
                }
                //json-schema.js
                result = jsl.parser.parse($scope.content);
                if(result) {
                    $scope.result = '正确的JSON格式';
                    $scope.content = JSON.stringify(JSON.parse($scope.content), null, "    ");
                    return true;
                } else {
                    $scope.result = "JSON数据解析失败";
                }
            }catch(e){
                try {
                    $scope.content = jsl.format.formatJson($scope.content);
                    result = jsl.parser.parse($scope.content)
                } catch(e) {
                    parseException = e
                }
                $scope.result = "JSON数据格式不正确:\n"+e.message;
            }
            return false;
        };

    });

    app.controller('testCtrl', function ($scope, $state, $uibModal, Rest, modal) {
        $scope.modes = modes;
        $scope.mode = {};
        $scope.query = {};
        $scope.content = '';
        $scope.result = '';
        $scope.eventsList = [];
        $scope.loading = true;
        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            var data = result.data;
            for (var key in data) {
                $scope.eventsList.push({'id': key, 'name': data[key]})
            }
        });

        $scope.execute = function () {
            if(!$scope.process()){
                Messenger().post({type: 'error', message: '未通过监测，错误详情请查看执行结果!'});
                return;
            }
            var url = '';
            if($scope.mode && $scope.mode.key){
                url = 'risk/test/batch/execute';
            }else{
                url = 'risk/test/execute';
            }
            $scope.loading = Rest.all(url).post({content: $scope.content}).then(function (response) {
                Messenger().post({type: 'success', message: '执行完成!'});
                try{
                    $scope.result = JSON.stringify(JSON.parse(response), undefined, 2);
                }catch (e) {
                    $scope.result = response;
                }
            });
        };

        $scope.process = function () {
            var result;
            try{
                if(!$scope.content){
                    $scope.result = "未检测到内容";
                    return false;
                }
                //json-schema.js
                result = jsl.parser.parse($scope.content);
                if(result) {
                    $scope.result = '正确的JSON格式';
                    $scope.content = JSON.stringify(JSON.parse($scope.content), null, "    ");
                    return true;
                } else {
                    $scope.result = "JSON数据解析失败";
                }
            }catch(e){
                try {
                    $scope.content = jsl.format.formatJson($scope.content);
                    result = jsl.parser.parse($scope.content)
                } catch(e) {
                    parseException = e
                }
                $scope.result = "JSON数据格式不正确:\n"+e.message;
            }
            return false;
        };

//        $scope.choiceEvent = function (event) {
//            $scope.query.event = event;
//            if (!$scope.query.event) {
//                var query = {};
//                query.userId = $scope.query.userId
//                query.deviceId = $scope.query.deviceId
//                query.mobileNo = $scope.query.mobileNo
//                query.traceId = $scope.query.traceId
//                query.clientIp = $scope.query.clientIp
//                query.eventCode = undefined
//                $scope.query = query;
//                $scope.param = {
//                    query: $scope.query,
//                    startTime: startTime,
//                    endTime: endTime
//                };
//            }
//
//        }

        $scope.extractSampleData = function(){
            var param={};
            param.query={};
            param.query.event = $scope.query.event||'';
            if(param.query.event){
                param.query.eventCode = $scope.query.event.id||'';
            }

            if($scope.traceId==''){
               param.query.traceId = null;
            }else{
               param.query.traceId = $scope.traceId;
            }

            Rest.all('/risk/test/getRequestParameter').post(param).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.content = response;
                $scope.process();
            });
        };

    });
})();
