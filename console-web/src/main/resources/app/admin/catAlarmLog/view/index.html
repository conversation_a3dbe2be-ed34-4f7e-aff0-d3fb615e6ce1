<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-list-alt"></i> 灾备告警</h3>
    </div>
    <div class="box-body">
        <form class="w5c-form" name="queryForm" novalidate w5c-form-validate="">
            <div class="form-group col-sm-2">
                <input ng-model="query.name" type="text" class="form-control input-md" placeholder="告警名称">
            </div>
            <div class="form-group col-sm-1" style="width:12%;">
                <select ng-model="query.checkType" class="form-control input-md">
                    <option value="" selected="selected">--检测类型--</option>
                    <option value="{{checkType.code}}" ng-repeat="checkType in checkTypes">{{checkType.name}}</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <input ng-model="query.bizTypeCode" type="text" class="form-control input-md" placeholder="业务类型code">
            </div>
            <div class="form-group col-sm-1" style="width:12%;">
                <select ng-model="query.channel" class="form-control input-md">
                    <option value="" selected="selected">--三方标识--</option>
                    <option value="{{channel}}" ng-repeat="channel in channels">{{channel}}</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="query.alarmStartTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="query.alarmStartTime" ng-click="popup1.opened=true"
                       is-open="popup1.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="query.alarmEndTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="query.alarmEndTime" ng-click="popup2.opened=true"
                       is-open="popup2.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <button type="buttom" class="btn btn-flat btn-md btn-info" w5c-form-submit="search();">
                <i class="fa fa-search"></i>搜索
            </button>
            <br/>
        </form>
    </div>
</div>
<div class="box box-primary">
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>告警名称</th>
                <th>告警类型</th>
                <th>项目名</th>
                <th>检测类型</th>
                <th>业务类型</th>
                <th>三方标识</th>
                <th>是否切换</th>
                <th>备注</th>
                <th>告警时间</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="log in catAlarmLogs">
                <td>{{log.name}}</td>
                <td>{{log.type}}</td>
                <td>{{log.domain}}</td>
                <td>{{getCheckTypeByCode(log.checkType).name}}</td>
                <td>{{log.bizTypeCode}}</td>
                <td>{{log.channel}}</td>
                <td>{{log.switchFlag == true ? '是' : '否'}}</td>
                <td>{{log.memo}}</td>
                <td>{{log.alarmTime}}</td>
                <td>
                    <button ng-click="showDetail(log)" class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-stack-overflow"></i> 详情
                </button>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>