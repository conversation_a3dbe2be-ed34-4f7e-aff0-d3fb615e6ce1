(function () {
    var app = angular.module(app_name);
    app.controller('catAlarmLogCtrl', function ($scope, $state, $uibModal, $stateParams, modal, Rest,$aside) {
        $scope.query = {};
        var endTime = new Date();
        endTime.setHours(23);
        endTime.setMinutes(59);
        endTime.setSeconds(59);
        endTime.setMilliseconds(999);
        var startTime = new Date(endTime.getTime() - 1000 * 60 * 60 * 24 * 7 + 1);
        $scope.query.alarmStartTime = startTime;
        $scope.query.alarmEndTime = endTime;

        $scope.checkTypes = [];
        $scope.channels = [];
        $scope.checkTypeMap = {};
        $scope.loading = Rest.all('common/getCheckTypes').post({}).then(function (response) {
            $scope.checkTypes = response;
            if($scope.checkTypes && $scope.checkTypes.length>0){
                for(var checkType of $scope.checkTypes){
                    var channels = checkType.channels;
                    if(channels && channels.length>0){
                        for(var channel of channels){
                            if($scope.channels.indexOf(channel.code) == -1){
                                $scope.channels.push(channel.code);
                            }
                        }
                    }
                    $scope.checkTypeMap[checkType.code] = checkType;
                }
            }
        });
        $scope.getCheckTypeByCode = function(code){
            return $scope.checkTypeMap[code] ? $scope.checkTypeMap[code] : {};
        }
        $scope.page = 1;
        $scope.catAlarmLogs = [];
        function go() {
            var param = $scope.query || {};
            $scope.loading = Rest.all('/catAlarmLog/search?p=' + $scope.page).post(param).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.catAlarmLogs = response.list;
                $scope.total = response.total;
            });
        }

        $scope.search = function () {
            go();
        };
        $scope.search();
        $scope.pageChanged = function () {
            go();
        };
        $scope.showDetail = function (log) {
            $aside.open({
                templateUrl: 'admin/catAlarmLog/view/detail.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.log = angular.copy(log);
                    if($scope.log.detail){
                        $scope.log.detail = JSON.parse($scope.log.detail);
                    }
                }
            });
        };
    });
})();