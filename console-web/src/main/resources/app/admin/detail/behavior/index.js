(function () {
    var app = angular.module(app_name);

    app.controller('userBehaviorCtrl', function ($scope, $state, $uibModal, Rest, modal,$aside) {
        // 搜索条件
        $scope.query = {
            userId: '',
            time:'WEEK'
        };

        $scope.loading = true;

        function go() {
            if (preCheck()) {
                getTop();
                getHit();
            }
        };

        // 事件排行
        function getTop(){
            $scope.loading = Rest.all('detail/user/top').post($scope.query).then(function (res) {
                $scope.top = res&&res.data;
            });
        }

        // 风控记录
        function getHit(){
            $scope.loading = Rest.all('detail/user/hit').post($scope.query).then(function (response) {
                handleHit(response);
            });
        }

        $scope.search = function () {
            go();
        };

        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            $scope.eventsMap = result.data;
        });

        $scope.code2Name = function (code) {
            return $scope.eventsMap[code];
        }

         // 处理风控记录
         function handleHit(result){
            $scope.loginResult = null;
            $scope.devices = null;
            $scope.ips = null;
            $scope.mobiles = null;
            if(result&&result.length>0){
                $scope.loginResult = result;
                $scope.colSize = $scope.colSizeHalf;
                var devices = {count:0,data:{}};
                var ips = {count:0,data:{}};
                var mobiles = {count:0,data:{}};
                result.forEach(function (obj) {
                    if(obj.deviceId){
                        if(!devices.data.hasOwnProperty(obj.deviceId)){
                            devices.data[obj.deviceId] = {
                                data:obj,
                                count:0
                            };
                        }
                        devices.data[obj.deviceId].count++;
                        devices.count++;
                    }

                    if(obj.clientIp&&obj.clientIp!='0.0.0.0'){
                        if(!ips.data.hasOwnProperty(obj.clientIp)){
                            ips.data[obj.clientIp] = {
                                data:obj,
                                count:0
                            };
                        }
                        ips.data[obj.clientIp].count++;
                        ips.count++;
                    }

                    if(obj.mobileNo){
                        if(!mobiles.data.hasOwnProperty(obj.mobileNo)){
                            mobiles.data[obj.mobileNo] = {
                                data:obj,
                                count:0
                            };
                        }
                        mobiles.data[obj.mobileNo].count++;
                        mobiles.count++;
                    }
                });
                $scope.devices = devices;
                $scope.ips = ips;
                $scope.mobiles = mobiles;
            }else{
                $scope.colSize = $scope.colSizeDefault;
            }
        }

        // 搜索条件检查
        function preCheck() {
            if (isEmpty($scope.query.userId)) {
                Messenger().post({type: 'error', message: '请输入用户ID'});
                return false;
            }
            return true;
        }

        // 空判断
        function isEmpty(obj){
            if(typeof obj == "undefined" || obj == null || obj.trim() == ""){
                return true;
            }else{
                return false;
            }
        }
    });
})();