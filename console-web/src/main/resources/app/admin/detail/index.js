(function () {
    var app = angular.module(app_name);

    app.config(function ($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.when('/detail', '/detail');
        $stateProvider.state('detail', {
            parent: 'home',
            url: '/detail',
            templateUrl: 'admin/detail/index.html'
        });

        $stateProvider.state('detail.user', {
            url: '/user',
            templateUrl: 'admin/detail/user/view/index.html',
            controller: 'userDetailCtrl'
        });

        $stateProvider.state('detail.im', {
            url: '/im',
            templateUrl: 'admin/detail/im/view/index.html',
            controller: 'userImCtrl'
        });

        $stateProvider.state('detail.behavior', {
            url: '/behavior',
            templateUrl: 'admin/detail/behavior/view/index.html',
            controller: 'userBehaviorCtrl'
        });

    });

})();
