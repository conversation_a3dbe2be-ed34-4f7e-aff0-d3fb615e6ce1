(function () {
    var app = angular.module(app_name);

    app.controller('userDetailCtrl', function ($scope, $state, $uibModal, Rest, modal,$aside) {
        $scope.dimensions = ["MOBILENO", "USERID", "UID","YPPNO"]; // 维度数组

        // 维度描述
        $scope.dimensionDesc = {
            'MOBILENO':'手机号',
            'USERID':'USERID',
            'UID':'UID',
            'YPPNO':'鱼泡泡No'
        }

        // 搜索条件
        $scope.query = {
            dimension: 'MOBILENO',
            value: '',
            time:'WEEK'
        };

        // 检验检查
        $scope.preCheck = function () {
            if ($scope.query.value === '') {
                Messenger().post({type: 'error', message: '请输入'+$scope.dimensionDesc[$scope.query.dimension]});
                return false;
            }
            return true;
        }

        function go() {
            if (!$scope.preCheck()) {
                return;
            }
            $scope.loading = Rest.all('detail/user/info').post($scope.query).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                var info = response;
                $scope.rawResult = info;
                goMore(info);
            });
        };

        function goMore(info){
            getMobile(info);
            getExp(info);
            getHit(info);
        }

        function getMobile(info){
            if(info&&info.uid){
                $scope.loading = Rest.all('detail/user/mobile?userId='+info.uid).post().then(function (response) {
                    if(response&&response.mobile){
                        $scope.rawResult.mobileNo = response.mobile;
                    }
                });
            }
        }

        function getExp(info){
            if(info&&info.uid){
                $scope.loading = Rest.all('detail/user/exp?uid='+info.uid).post().then(function (response) {
                    $scope.expResult = response;
                });
            }
        }

        function getHit(info){
            if(info&&info.uid){
                var param = {
                    userId:info.uid,
                    time: $scope.query.time
                }
                $scope.loading = Rest.all('detail/user/hit').post(param).then(function (response) {
                    $scope.loginOverview(response);
                });
            }
        }

        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            $scope.eventsMap = result.data;
        });

        $scope.code2Name = function (code) {
            return $scope.eventsMap[code];
        }

        $scope.search = function () {
            go();
        };

        $scope.loading = true;
        $scope.colSizeDefault = 12; // 栅格大小默认值
        $scope.colSizeHalf = 6; // 栅格大小半值
        $scope.colSize = $scope.colSizeDefault; // 栅格大小
        $scope.moreSize = 10; // 记录数达到此值，显示更多详情按钮

        $scope.titles = {
            'mobile':'活跃手机号',
            'ip':'活跃客户端IP',
            'device':'活跃设备号'
        }

        $scope.showDetail = function (type,data,title) {
            var $$scope = $scope;
            $aside.open({
                templateUrl: 'admin/detail/user/view/template.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.others = {
                        title: $$scope.titles[type]
                    }
                    if(data){
                        // 详情
                        if(title.length>20){
                            title = title.substring(0,20)+'...';
                        }
                        $scope.others.title += '【'+title+'】';
                        $scope.others.result = data;
                    }else{
                        // 更多详情
                        if(type=='mobile'){
                            $scope.others.result = $$scope.loginDetail('mobileNo');
                        }else if(type=='ip'){
                            $scope.others.result = $$scope.loginDetail('clientIp');
                        }else if(type=='device'){
                            $scope.others.result = $$scope.loginDetail('deviceId');
                        }
                    }
                }
            });
        };

        // 登录指定维度详细
        $scope.loginDetail = function(field){
            var arr = [];
            if($scope.loginResult&&$scope.loginResult.length>0) {
                $scope.loginResult.forEach(function(obj){
                    if(obj&&obj[field]){
                        arr.push(obj);
                    }
                })
            }
            return arr;
        }

        $scope.loginOverview = function(result){
            $scope.loginResult = null;
            $scope.devices = null;
            $scope.ips = null;
            $scope.mobiles = null;
            if(result&&result.length>0){
                $scope.loginResult = result;
                $scope.colSize = $scope.colSizeHalf;
                var devices = {count:0,data:{}};
                var ips = {count:0,data:{}};
                var mobiles = {count:0,data:{}};
                result.forEach(function (obj) {
                    if(obj.deviceId){
                        if(!devices.data.hasOwnProperty(obj.deviceId)){
                            devices.data[obj.deviceId] = {
                                data:obj,
                                count:0
                            };
                        }
                        devices.data[obj.deviceId].count++;
                        devices.count++;
                    }

                    if(obj.clientIp&&obj.clientIp!='0.0.0.0'){
                        if(!ips.data.hasOwnProperty(obj.clientIp)){
                            ips.data[obj.clientIp] = {
                                data:obj,
                                count:0
                            };
                        }
                        ips.data[obj.clientIp].count++;
                        ips.count++;
                    }

                    if(obj.mobileNo){
                        if(!mobiles.data.hasOwnProperty(obj.mobileNo)){
                            mobiles.data[obj.mobileNo] = {
                                data:obj,
                                count:0
                            };
                        }
                        mobiles.data[obj.mobileNo].count++;
                        mobiles.count++;
                    }
                });
                $scope.devices = devices;
                $scope.ips = ips;
                $scope.mobiles = mobiles;
            }else{
                $scope.colSize = $scope.colSizeDefault;
            }
        }

    });
})();