<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-user"></i> 用户详情</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-2">
                <select ng-model="query.dimension" class="form-control input-md">
                    <option ng-repeat="item in dimensions" value="{{item}}">{{dimensionDesc[item]}}</option>
                </select>
            </div>
            <div class="form-group col-sm-6">
                <input ng-model="query.value" type="text" name="search" class="form-control" placeholder="请输入{{dimensionDesc[query.dimension]}}">
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.time" class="form-control input-md">
                    <option value="WEEK">最近一周</option>
                    <option value="DAY">最近一天</option>
                    <option value="MONTH">最近一个月</option>
                </select>
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-search"></i> 搜索
            </button>
        </form>
    </div>
</div>

<div ng-if="rawResult">
    <div class="row">
        <!-- 基本信息、经验等级开始 -->
        <div class="col-md-{{colSize}}">
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-info-circle"></i> 基本信息</h3>
                        </div>
                        <pre class="ng-binding">{{rawResult|json}}</pre>
                    </div>
                </div>
            </div>

            <div class="row" ng-if="expResult&&expResult.length>0">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-rebel"></i> 经验等级信息</h3>
                        </div>
                        <pre class="ng-binding" ng-if="expResult.length==1">{{expResult[0]|json}}</pre>
                        <pre class="ng-binding" ng-if="expResult.length>1">{{expResult|json}}</pre>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息、经验等级结束 -->

        <!-- 行为分析开始 -->
        <div class="col-md-{{colSize}}" ng-if="loginResult">
            <!-- 手机 -->
            <div class="row" ng-if="mobiles&&mobiles.count>0">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-mobile"></i> {{titles['mobile']}}</h3>
                        </div>
                        <div class="loginResult">
                            <p ng-if="mobiles.length>moreSize">
                                <span class="label label-success mid-label btn" ng-click="showDetail('mobile')">更多详情</span>
                            </p>
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w60">操作</th>
                                    <th>手机号</th>
                                    <th class="w60">次数</th>
                                    <th class="w140">最近记录时间</th>
                                </tr>
                                <tr ng-repeat="(key,value) in mobiles.data">
                                    <td><span class="label label-success mid-label btn" ng-click="showDetail('mobile',value.data,key)">详情</span></td>
                                    <td>{{key}}</td>
                                    <td>{{value.count}}</td>
                                    <td>{{value.data.createdAt | date:'MM-dd HH:mm:ss'}}</td>
                                </tr>
                            </table>
                            <p>
                                <span class="label label-success mid-label btn" ng-click="showDetail('mobile')">更多详情</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- IP -->
            <div class="row" ng-if="ips&&ips.count>0">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-mobile"></i> {{titles['ip']}}</h3>
                        </div>
                        <div class="loginResult">
                            <p ng-if="ips.length>moreSize">
                                <span class="label label-success mid-label btn" ng-click="showDetail('device')">更多详情</span>
                            </p>
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w60">操作</th>
                                    <th>客户端IP</th>
                                    <th class="w60">次数</th>
                                    <th class="w140">最近记录时间</th>
                                </tr>
                                <tr ng-repeat="(key,value) in ips.data">
                                    <td><span class="label label-success mid-label btn" ng-click="showDetail('ip',value.data,key)">详情</span></td>
                                    <td>{{key}}</td>
                                    <td>{{value.count}}</td>
                                    <td>{{value.data.createdAt | date:'MM-dd HH:mm:ss'}}</td>
                                </tr>
                            </table>
                            <p>
                                <span class="label label-success mid-label btn" ng-click="showDetail('ip')">更多详情</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备 -->
            <div class="row" ng-if="devices&&devices.count>0">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-mobile"></i> {{titles['device']}}</h3>
                        </div>
                        <div class="loginResult">
                            <p ng-if="devices.length>moreSize">
                                <span class="label label-success mid-label btn" ng-click="showDetail('device')">更多详情</span>
                            </p>
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w60">操作</th>
                                    <th>设备号</th>
                                    <th class="w60">次数</th>
                                    <th class="w140">最近记录时间</th>
                                </tr>
                                <tr ng-repeat="(key,value) in devices.data">
                                    <td><span class="label label-success mid-label btn" ng-click="showDetail('device',value.data,key)">详情</span></td>
                                    <td>{{key}}</td>
                                    <td>{{value.count}}</td>
                                    <td>{{value.data.createdAt | date:'MM-dd HH:mm:ss'}}</td>
                                </tr>
                            </table>
                            <p>
                                <span class="label label-success mid-label btn" ng-click="showDetail('device')">更多详情</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 行为分析结束 -->
    </div>
</div>
<div ng-if="!rawResult" style="padding:10px;">避免多次查询无果，请尽量填写准确！</div>

<style>
    .loginResult{
        padding:20px;
    }
    .w60{
        width:60px;
    }
    .w140{
        width:140px;
    }
</style>

