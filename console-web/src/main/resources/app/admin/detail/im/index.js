(function () {
    var app = angular.module(app_name);

    app.directive('errSrc', [function() {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                element.bind('error', function() {
                    if (attrs.src != attrs.url) {
                        attrs.$set('src', attrs.url);
                    }
                });
            }
        }
    }])

    // 复制
    app.directive('ngCopyable', function($document) {
        return {
            restrict: 'A',
            scope: {
                copyText: '='
            },
            link: function (scope, element, attrs) {
                //点击事件
                element.bind('click', function ($event) {
                    $event.stopPropagation();
                    //创建将被复制的内容
                    $document.find('body').eq(0).append('<div id="ngCopyableId">' + scope.copyText + '</div>');
                    var newElem = angular.element(document.getElementById('ngCopyableId'))[0];

                    var range = document.createRange();
                    range.selectNode(newElem);
                    window.getSelection().removeAllRanges();
                    window.getSelection().addRange(range);
                    var successful = document.execCommand('copy');

                    //执行完毕删除
                    var oldElem = document.getElementById('ngCopyableId');
                    oldElem.parentNode.removeChild(oldElem);
                    window.getSelection().removeAllRanges();

                    //提示
                    if (successful) {
                        var text = scope.copyText;
                        if(text&&text.length>10){
                            text = text.substring(0,10)+'...';
                        }
                        Messenger().post({type: 'success', message: '复制【'+text+'】成功!'});
                        // alert('已成功复制：' + scope.copyText);
                    } else {
                        // alert('浏览器不支持复制');
                        Messenger().post({type: 'success', message: '浏览器不支持复制'});
                    }

                });
            }

        };
    });

    /*图片点击放大再点击还原*/
    app.directive('enlargePic',function(){//enlargePic指令名称，写在需要用到的地方img中即可实现放大图片
        return{
            restrict: "AE",
            link: function(scope,elem){
                elem.bind('click',function($event){
                    var img = $event.srcElement || $event.target;
                    angular.element(document.querySelector(".diff-mask"))[0].style.display = "block";
                    angular.element(document.querySelector(".diff-bigPic"))[0].src = img.src;
                })
            }
        }
    })
    .directive('closePic',function(){
        return{
            restrict: "AE",
            link: function(scope,elem){
                elem.bind('click',function($event){
                    angular.element(document.querySelector(".diff-mask"))[0].style.display = "none";
                })
            }
        }
    });

    app.directive('backImg', function(){
        return function(scope, element, attrs){
            attrs.$observe('backImg', function(value) {
                var img = new Image();
                img.src = value;
                var rw = this.width;
                var rh = this.height;
                // var time = new Date().getTime();
                var check = function(){
                    // console.log('-------------------------------------------------')
                    if(img.width>0 || img.height>0){
                        clearInterval(interval);
                        // console.log("time;"+(new Date().getTime()-time));
                        rw = img.width;
                        rh = img.height;
                        var css = {
                            'background-image': 'url(' + value +')',
                            'background-position' : 'center'
                        }
                        if(rw<=600&&rh<=400){
                            css['background-repeat'] = 'no-repeat';
                        }else{
                            css['background-size'] = 'cover';
                        }
                        element.css(css);
                    }
                }
                var interval = setInterval(check,40);

                /*$("<img/>").attr("src", value).load(function() {
                    var rw = this.width;
                    var rh = this.height;
                    var css = {
                        'background-image': 'url(' + value +')',
                        'background-position' : 'center'
                    }
                    if(rw<=600&&rh<=400){
                        css['background-repeat'] = 'no-repeat';
                    }else{
                        css['background-size'] = 'cover';
                    }
                    element.css(css);
                });*/
                /*element.css({
                    'background-image': 'url(' + value +')',
                    //'background-size' : 'cover',
                    'background-position' : 'center'
                });*/
            });
        };
    })

    app.filter("trustUrl", ['$sce', function ($sce) {
        return function (recordingUrl) {
            return $sce.trustAsResourceUrl(recordingUrl);
        };
    }]);

    app.controller('userImCtrl', function ($scope, $state, $uibModal, Rest, modal,$aside) {
        var startTime = new Date();
        startTime.setHours(0);
        startTime.setMinutes(0);
        startTime.setSeconds(0);
        startTime.setMilliseconds(0);
        var endTime = new Date(startTime.getTime() + 1000 * 60 * 60 * 24 - 1);
        startTime = new Date(startTime.getTime() - 1000 * 60 * 60 * 24*6)

        $scope.selectedIndex = null; // 用户选中高亮显示

        $scope.query = {};
        $scope.query.eventCode = 'im-message';

        $scope.userParam = {
            query: $scope.query,
            startTime: startTime,
            endTime: endTime
        };

        $scope.page = 1;
        // $scope.pageSize = 3; // 每页显示记录数
        $scope.pageSize = 200; // 每页显示记录数

        $scope.loading = true;

        $scope.contentParam = {
            query: $scope.query,
            startTime: startTime,
            endTime: endTime,
            size: $scope.pageSize
        };

        // 用户信息
        $scope.user = null; // 对方
        $scope.currUser = null; // 本方
        $scope.currUserId = null; // 本方userId

        // 搜索用户列表信息
        $scope.searchUser = function(){
            if(preCheck()){
                // 清除数据
                $scope.selectedIndex = null;
                $scope.users = [];
                $scope.list = [];
                $scope.currUser = null;

                    // 设置搜索聊天内容时间范围，为了不受界面上时间改变影响
                $scope.contentParam.startTime = $scope.userParam.startTime;
                $scope.contentParam.endTime = $scope.userParam.endTime

                var query = angular.copy($scope.query);
                for (var key in query) {
                    if (query[key] == '') {
                        delete query[key];
                    }
                }
                $scope.loading = Rest.all('detail/im/users').post($scope.userParam).then(function (result) {
                    if(result&&result.length>0){
                        $scope.currUserId = query.userId; // 设置当前搜索用户ID且是有效的
                        for(var i=0;i<result.length;i++){
                            var item = result[i];
                            if(item.userId === $scope.query.userId){
                                $scope.currUser = item;
                                result.splice(i,1);
                                break;
                            }
                        }

                        // 添加自己
                        result.unshift({
                            self: true,
                            userId: $scope.currUserId
                        });

                        $scope.users = result;

                        // 当前用户信息无设置userId,当前主要是聊天室用
                        if(!$scope.currUser){
                            $scope.currUser = {
                                userId: $scope.currUserId
                            };
                        }
                    }else{
                        $scope.users = [];
                    }
                });
            }
        }

        // 搜索聊天内容
        $scope.searchContent = function (user,index) {
            $scope.selectedIndex = index;
            $scope.user = user;
            getContent(1);
        }

        // 切换分页
        $scope.pageChanged = function () {
            getContent($scope.page);
        }

        // 显示详细
        $scope.showDetail = function (key) {
            showDetail($scope.originMap[key]);
        }

        // 获取聊天内容
        function getContent(page) {
            $scope.contentParam.userId = $scope.currUser&&$scope.currUser.userId; // IM单聊、私聊
            if($scope.user){
                $scope.contentParam.targetUserId = $scope.user.userId; // IM单聊、私聊
                $scope.contentParam.roomId = $scope.user.userId; // 聊天室
                $scope.contentParam.self = $scope.user.self?true:false;
            }
            $scope.page = page || $scope.page || 1;
            var query = angular.copy($scope.query);
            for (var key in query) {
                if (query[key] == '') {
                    delete query[key];
                }
            }
            $scope.contentParam.query = query;
            $scope.contentParam.page = $scope.page;
            $scope.loading = Rest.all('detail/im/content').post($scope.contentParam).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                if(response&&response.list){
                    var originMap = {};
                    var results = [];
                    var meUserId = $scope.currUserId||$scope.query.userId;
                    response.list.forEach(function(item){
                        if(item.traceId&&item.userId){
                            originMap[item.traceId] = item;
                            var words,avatar,name,image,audio,vedio;
                            if(item.data){
                                words = getWords(item.data);
                                avatar = item.data.ext&&item.data.ext.avatar
                                name = item.data.ext&&item.data.ext.name
                                image = item.data.images;
                                audio = item.data.audio;
                                video = item.data.video;
                            }

                            var positionClass = 'left'; // 左侧
                            if(item.userId == meUserId){
                                positionClass = 'right'; // 右侧,自己
                            }

                            // 头像图片不存在，设置默认值
                            if(!avatar){
                                avatar = '../../../assets/images/im-'+positionClass+'.png';
                            }

                            results.push({
                                traceId: item.traceId,
                                reason: item.reason,
                                full_text: item.full_text,
                                level: item.level,
                                words: words,
                                createdAt: item.createdAt,
                                avatar: avatar,
                                name: name,
                                userId: item.userId,
                                positionClass: positionClass,
                                image: image,
                                audio: audio,
                                video: video

                                // 测试使用
                                // image:'https://yphoto.eryufm.cn/upload/98674118-BC96-499A-9FDD-6D358CD906D2.jpg',
                                // audio:'https://yvideo.eryufm.cn/audio/95259878-1852-4fa1-a499-83ba3918e61f.mp3',
                                // video:'https://yvideo.eryufm.cn/video/2877ad8d-5c5c-427e-9a58-01f2dc7f56a2.mp4.mp4'
                            });
                        }
                    })

                    $scope.originMap = originMap;
                    $scope.list = results;
                    $scope.total = response.total;
                }
            });
        }

        // 获取拦截违禁词
        function getWords(data){
            var words = null;
            for(var key in data){
                if(key.lastIndexOf("TextCheck")!==-1){
                    words = data[key].reason; // 原因就是命中的违禁词
                    break;
                }
            }
            return words;
        }

        // 记录详细
        function showDetail(result){
            var title = result&&result.full_text;
            if(title&&title.length>20){
                title = title.substring(0,20)+'...';
            }
            $aside.open({
                templateUrl: 'admin/detail/im/view/template.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.others = {
                        title: title,
                        result: result
                    };
                }
            })
        }

        // 搜索条件检查
        function preCheck() {
            if (isEmpty($scope.query.userId)) {
                Messenger().post({type: 'error', message: '请输入用户ID'});
                return false;
            }

            if (isEmpty($scope.query.eventCode)) {
                Messenger().post({type: 'error', message: '请选择事件场景'});
                return false;
            }

            var timeSpan = ($scope.userParam.endTime.getTime() - $scope.userParam.startTime.getTime()) / 1000 / 60 / 60 / 24; // 时间跨度-天
            if (timeSpan > 14) {
                Messenger().post({type: 'error', message: '时间跨度不允许超过14天'});
                return false;
            }
            return true;
        }

        // 空判断
        function isEmpty(obj){
            if(typeof obj == "undefined" || obj == null || obj.trim() == ""){
                return true;
            }else{
                return false;
            }
        }

    });
})();