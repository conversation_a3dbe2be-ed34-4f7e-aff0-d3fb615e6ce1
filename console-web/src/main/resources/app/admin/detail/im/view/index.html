<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="text-primary box-title"><i class="fa fa-comment"></i> IM详情</h3>
    </div>
    <!-- 搜索条件开始 -->
    <div class="box-search">
        <form>
            <div class="form-group col-sm-3">
                <input ng-model="query.userId" type="text" class="form-control input-md" placeholder="用户ID">
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.eventCode" class="form-control input-md">
                    <option value="">-事件场景-</option>
                    <option value="im-message">IM单聊</option>
                    <option value="private-chat">私聊</option>
                    <option value="chat-room">聊天室</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.level" class="form-control input-md">
                    <option value="">-风险级别-</option>
                    <option value="REJECT">拒绝</option>
                    <option value="REVIEW">审核</option>
                    <option value="PASS">通过</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="startTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="userParam.startTime" ng-click="popup1.opened=true"
                       is-open="popup1.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="endTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="userParam.endTime" ng-click="popup2.opened=true"
                       is-open="popup2.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <button ng-click="searchUser()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-search"></i> 搜索
            </button>
        </form>
    </div>
    <!-- 搜索条件结束 -->
    <div class="row">
        <!-- 对聊userId列表开始 -->
        <div class="col-md-3 col-xs-3">
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-user"></i> 对话用户列表</h3>
                        </div>
                        <div class="chat im-chat" style="min-height:350px;">
                            <div ng-repeat="user in users">
                                <div class="im-user-id" ng-click="searchContent(user,$index)" ng-class="{'im-user-id-hover': selectedIndex === $index}">
                                    <i class="fa fa-files-o" ng-copyable copy-text="user.userId" class="copy-text"></i>&#160;
                                    {{user.userId === currUserId?'自己(右侧)':user.userId}}
                                </div>
                            </div>
                            <div ng-if="!(users&&users.length>0)" style="padding:10px;">避免查询无果，请填写准确！</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 对聊userId列表结束 -->

        <!-- 对聊内容开始 -->
        <div class="col-md-9 col-xs-9">
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-primary">
                        <div class="box-header with-border">
                            <h3 class="text-primary box-title"><i class="fa fa-weixin"></i> 聊天内容</h3>
                        </div>
                        <div data-role="content" class="chat im-chat" role="main" style="min-height:300px;">
                            <ul class="content-reply-box mg10" ng-if="list&&list.length>0">
                                <li class="{{item.positionClass}}" ng-repeat="item in list">
                                    <a class="user user-left" href="javascript:;">
                                        <img class="img-responsive avatar_" ng-src="{{item.avatar}}" alt="" err-src url="this.src='../../../assets/images/im-{{item.positionClass}}.png">
                                        <span class="user-name">{{item.name||item.userId}}</span>
                                    </a>
                                    <div class="box-right box-left">
                                        <div class="box-right box-left">
                                            <span class="reply-time right left">{{item.createdAt | date:'MM-dd HH:mm:ss'}}</span>
                                            <span class="reply-time right left">&#160;
                                                <span ng-if="item.level == 'PASS'" class="label label-success mid-label btn"
                                                      ng-click="showDetail(item.traceId)">通过</span>
                                                <span ng-if="item.level == 'REVIEW'" class="label label-warning mid-label btn"
                                                      ng-click="showDetail(item.traceId)">审核</span>
                                                <span ng-if="item.level == 'REJECT'" class="label label-danger mid-label btn"
                                                      ng-click="showDetail(item.traceId)">拒绝</span>
                                            </span>
                                            <span class="reply-time right left">&#160;{{item.reason}}</span>
                                            <span class="reply-time right left">&#160;{{item.words}}</span>
                                            <span style="clear:both;"></span>
                                        </div>
                                        <div style="clear:both;"></div>
                                        <div class="box-right box-left">
                                            <div ng-if="item.full_text" class="reply-content pr left right"><span class="arrow">&nbsp;</span>{{item.full_text}}</div>
                                            <div style="clear:both"></div>
                                            <div ng-if="item.image" class="left right">
                                                <!--<img src="{{item.image}}" class="diff-content diff-pointer" enlarge-pic>-->
                                                <img ng-src="{{item.image}}"  id="{{item.traceId}}" uib-tooltip-template="'pic.html'" tooltip-placement="{{item.positionClass==='left'?'right':'left'}}" tooltip-class="customClass" class="diff-content diff-content-image">
                                            </div>
                                            <div style="clear:both"></div>
                                            <div ng-if="item.audio" class="left right">
                                                <audio src="{{item.audio | trustUrl}}" class="diff-content-audio diff-w350" controls></audio>
                                            </div>
                                            <div style="clear:both"></div>
                                            <div ng-if="item.video" class="left right">
                                                <video src="{{item.video | trustUrl}}" class="diff-content diff-w350 diff-h200" controls></video>
                                            </div>
                                            <div style="clear:both"></div>
                                        </div>
                                        <div style="clear:both;"></div>
                                    </div>

                                    <div style="clear:both;"></div>

                                </li>
                            </ul>
                            <div ng-if="!(list&&list.length>0)" style="padding:10px;">避免查询无果，请填写准确！</div>
                        </div>
                        <div class="box-footer">
                            <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
                                first-text="首页"
                                last-text="尾页"
                                previous-text="前一页"
                                next-text="后一页"
                                total-items="total"
                                ng-model="page"
                                ng-change="pageChanged()"
                                items-per-page="pageSize"
                                max-size="10"
                                rotate="false">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 对聊内容结束 -->

        <!-- 图片放大遮罩层 -->
        <div class="diff-mask" close-Pic>
            <div class="diff-mask-box"></div>
            <div class="diff-big-pic-wrap">
                <img src="" alt="" class="diff-bigPic" />
                <span class="diff-close-pic"><i class="fa fa-close"></i></span>
            </div>
        </div>
    </div>

</div>

<!--<script type="text/ng-template" id="pic.html">
    <img ng-src="{{item.image}}" style="height: 400px; width: 600px">
</script>-->
<script type="text/ng-template" id="pic.html">
    <div class="back-img" back-img="{{item.image}}" style="height: 400px; width: 600px;" ></div>
</script>

<style>
    .im-chat{background-color:#f2f2f2;}
    .box-footer{background-color:#f2f2f2;}
    ul{padding:0;margin:0;}
    li{list-style:none;}
    .menu a{color:#333;padding:1em 0;font-size:1em;display:block;}
    .menu a:hover{text-decoration:none;background-color:#ffffff;}
    .ui-loader h1{display:none;}
    .avatar_{width:40px;height:40px;border-radius:50%;margin-top:5px;opacity:1;filter:alpha(opacity=100);transition:all 0.3s ease-in 0s;-moz-transition:all 0.3s ease-in 0s;-webkit-transition:all 0.3s ease-in 0s;-o-transition:all 0.3s ease-in 0s;}
    .user:hover .avatar_{border-radius:0;transition:all 0.3s ease-in 0s;-moz-transition:all 0.3s ease-in 0s;-webkit-transition:all 0.3s ease-in 0s;-o-transition:all 0.3s ease-in 0s;opacity:.8;filter:alpha(opacity=80);}
    .content-reply-box{width:100%;overflow:hidden;}
    .content-reply-box li{width:100%;margin-bottom:1em;}
    .content-reply-box li.left .user{float:left;margin-right:10px;}
    .content-reply-box li.right .user{float:right;margin-left:10px;}
    .content-reply-box li.right .box-right{float:right;}
    .content-reply-box li.right .box-right .right{float:right;}
    .content-reply-box li.left .box-left{float:left;}
    .content-reply-box li.left .box-left .left{float:left;}
    .user-name{color:#999;margin-top:5px;display:block;text-align:center;width:50px;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow: hidden;}
    .reply-time{color:#e1912d;font-size:.85em;}
    .content-reply-box li.left .reply-time{text-align:left;}
    .content-reply-box li.right .reply-time{text-align:right;}
    .reply-content{border:1px solid #ccc;padding:.3em;background-color:#fcfcfc;border-radius:4px;box-shadow:0 0 5px #ccc;word-break: break-all;min-height: 30px;}
    .content-reply-box li.left .arrow{width:0;height:0;line-height:0;font-size:0;border-color:transparent #fff transparent transparent;border-width:6px;border-style:dashed solid dashed dashed;display:block;position:absolute;top:8px;left:-12px;z-index:999;}
    .content-reply-box li.right .arrow{width:0;height:0;line-height:0;font-size:0;border-color:transparent transparent transparent #fff;border-width:6px;border-style:dashed dashed dashed solid;display:block;position:absolute;top:8px;right:-12px;z-index:999;}
    .pr{position:relative;}
    .mg10 {margin:10px 0;}
    .im-user-id{cursor:pointer;padding:10px 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
    .im-user-id-hover{color:#00a65a;}
    .copy-text{color:#5d9c0a;cursor:pointer;}
    .diff-w350{width:350px;}
    .diff-pointer{cursor: pointer;}
    .diff-content{margin:5px 0;display: block;background:#000;}
    .diff-h200{height:200px;}
    .diff-content-image{width:200px;height:100px;}
    .diff-mask{display: none;}
    .diff-mask-box{position:absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 1080;opacity: 0.5;background: #000; }
    .diff-big-pic-wrap{position:fixed;top:50%;left:50%;margin-left: -460px;margin-top: -300px;width:920px;height:620px;padding:10px;z-index:1090;background:#fff;}
    .diff-bigPic{width:900px;height:600px;}
    /*关闭大图按钮*/
    .diff-close-pic{position:absolute;top:-5px;right:-5px;display:inline-block;width: 35px;height: 35px;cursor:pointer;border-radius:50% !important;background: #393A3C;text-align: center;line-height: 40px;}
    .diff-close-pic:hover{background: #D43F27;}
    .diff-close-pic>i{font-size: 25px;color:#fff;}

    .tooltip.customClass .tooltip-inner {max-width: fit-content;padding: 8px 8px;background-color: #ffffff;box-shadow: 0 6px 12px rgba(0,0,0,.175);}
    .tooltip.customClass .tooltip-arrow {display: none;}
    .tooltip.customClass.tooltip.in {opacity: 1;}
</style>