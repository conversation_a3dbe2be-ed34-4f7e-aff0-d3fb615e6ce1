(function () {
    var app = angular.module(app_name);
    app.factory('DropUserService', function ($uibModal) {
        return {
           detail: function(dropResult,callback){
               $uibModal.open({
                   backdrop: 'static',
                   templateUrl: 'admin/drop/view/detail.html',
                   controller: function (Rest, $scope, modal, $uibModalInstance) {
                       $scope.title = "踢出用户结果";
                       $scope.dropResult = angular.copy(dropResult);
                       $scope.close = function () {
                           $uibModalInstance.close();
                       };
                   }
               });
           }
        }
    });

    app.controller('dropCtrl', function ($scope, $state, $uibModal, modal, DropUserService,Rest) {

        Rest.all("/drop/listPackages").post().then(function(response){
            $scope.packages = response;
            $scope.pkgCode = $scope.packages[0].code;
        });

        $scope.drop = function () {
             if(!$scope.pkgCode){
                  modal.confirm('请选择惩罚包！', function () {
                     return;
                  });
                  return;
             }
            if($scope.uids == null){
                 modal.confirm('请输入想要踢出的用户uid！', function () {
                    return;
                 });
                return;
            }
            // 校验输入的用户uid的个数不能超过1万
            if($scope.uids.trim().split("\n").length>10000){
                modal.confirm('输入想要踢出的用户uid总数不能超过1万个！', function () {
                   return;
                });
                return;
            }
            var length = $scope.uids.trim().split("\n").length;
            var showStr = '确定要踢出'+length+'个用户吗?';
            modal.confirm(showStr, function () {
                  $scope.param = {};
                  $scope.param.pkgCode = $scope.pkgCode;
                  $scope.param.uids = $scope.uids;

                  $scope.loading = Rest.all('/drop/dropUsers').post($scope.param).then(function (res) {
                     DropUserService.detail(res,function(){
                     })
                  });
           });

        };
    });
})();