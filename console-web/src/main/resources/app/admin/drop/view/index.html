<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/>踢出用户</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-5">
                <select ng-model="pkgCode" class="form-control input-md" ng-options="p.code as p.msg for p in packages">
                </select>
            </div>
            <button ng-click="drop()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>踢出</button>
        </form>
    </div>
    <div class="box-body">
        <div class="form-group">
            <label class="col-sm-3 control-label">输入待踢出用户uid:</label>
            <textarea name="value" ng-model="uids" type="text" class="form-control input-md col-sm-8" style="padding-left:5px;" placeholder="输入用户uid,每个用户uid一行" laceholder="输入待踢出用户uid" required rows="24"/>
        </div>
    </div>
</div>
