<form class="form-horizontal w5c-form" name="addForm" novalidate w5c-form-validate="">
    <div cg-busy="loading">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                    aria-hidden="true">&times;</span></button>
            <h3 class="modal-title">新增用户</h3>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="col-sm-3 control-label">用户名*:</label>
                <div class="col-sm-8">
                    <input ng-model="user.name" type="text" class="form-control input-sm">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">部门:</label>
                <div class="col-sm-8">
                    <input ng-model="user.department" type="text" class="form-control input-sm">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">电话:</label>
                <div class="col-sm-8">
                    <input ng-model="user.mobile" type="text" class="form-control input-sm">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">中文名:</label>
                <div class="col-sm-8">
                    <input ng-model="user.chineseName" type="text" class="form-control input-sm">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">角色*:</label>
                <div class="col-sm-8">
                    <ui-select class="form-control" multiple ng-model="user.roles" theme="bootstrap">
                        <ui-select-match placeholder="选择角色">{{$item.description}}</ui-select-match>
                        <ui-select-choices
                                repeat="r in roles|filter: $select.search track by r.description">
                            <div>{{r.description}}</div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">隶属于*:</label>
                <div class="col-sm-8">
                    <ui-select class="" style="width:100%" ng-model="user.toSystem"
                               theme="bootstrap" title="所属系统">
                        <ui-select-match placeholder="隶属于">{{$select.selected.msg}}</ui-select-match>
                        <ui-select-choices repeat="toSystem in toSystemTypes" refresh="refreshToSystemType($select.search)"
                                           refresh-delay="300">
                            <div ng-bind-html="toSystem.msg"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="col-sm-offset-3 col-sm-9">
                <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                <button type="submit" class="btn btn-success" w5c-form-submit="confirm();">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 确认
                </button>
            </div>
        </div>
    </div>
</form>