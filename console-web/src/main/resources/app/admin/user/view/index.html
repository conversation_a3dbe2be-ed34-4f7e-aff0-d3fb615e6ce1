<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/> 用户列表</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-6 col-md-4 col-lg-3">
                <input ng-model="username" type="text" class="form-control input-md" placeholder="用户名">
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="role" class="form-control input-md" ng-options="item.description for item in roles">
                    <option value="" selected="selected">-角色-</option>
                </select>
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索</button>
            <button ng-click="add()" class="btn btn-flat btn-md btn-info"><i class="fa fa-plus"></i>新增用户</button>
        </form>

    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>用户名</th>
                <th>中文名</th>
                <th>电话</th>
                <th>部门</th>
                <th>角色</th>
                <th>隶属于</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="user in users" ng-if="user.username != 'admin'">
                <td>{{user.name}}</td>
                <td>{{user.chineseName}}</td>
                <td>{{user.mobile}}</td>
                <td>{{user.department}}</td>
                <td>{{transRoles(user.roles)}}</td>
                <td>{{toSystemTypeName(user.toSystem)}}</td>
                <td>
                    <button uib-tooltip="编辑" class="btn btn-flat btn-md btn-info" ng-click="edit(user)">
                        <i class="fa fa-pencil fa-fw"></i>
                    </button>
                </td>
            </tr>

        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
