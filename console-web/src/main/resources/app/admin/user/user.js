(function () {
    var app = angular.module(app_name);

    app.controller('UserCtrl', function ($scope, $state, $uibModal, Rest, UserService) {
        $scope.username = '';
        $scope.role = {};

        $scope.loading = true;
        $scope.loading = Rest.all('user/getRoles').post().then(function (response) {
            $scope.roles = response;
        });

        go();

        function go(page) {
            $scope.page = page || $scope.page || 1;
            var roles = [];
            if ($scope.role) {
                roles.push($scope.role);
            }
            $scope.loading = Rest.all('user/search?p=' + $scope.page).post({
                name: $scope.username,
                roles: roles
            }).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.users = response.list;
                $scope.total = response.total;
            });
        }

        $scope.search = function () {
            go(1);
        };

        $scope.pageChanged = function () {
            go($scope.page);
        };

        $scope.add = function () {
            UserService.add(go);
        };

        $scope.edit = function (user) {
            UserService.edit(user, go);
        };

        $scope.transRoles = function (roles) {
            var roleNames = [];
            if (roles) {
                roles.forEach(function (role) {
                    roleNames.push(role.description);
                })
            }
            return roleNames.join(', ');
        }

        $scope.toSystemTypes = {};
        $scope.toSystemTypeMap = {};
        Rest.all('user/getToSystemTypeList').post().then(function (response) {
            $scope.toSystemTypes = response.list;
            if (response && response.length > 0) {
                for (var i = 0; i < response.length; i++) {
                    var t = response[i];
                    $scope.toSystemTypeMap[t.code] = t.msg;
                }
            }
        });

        $scope.toSystemTypeName = function (toSystem) {
            return $scope.toSystemTypeMap[toSystem];
        }

    });

    app.factory('UserService', function ($uibModal, Rest) {
        var eventService = {};

        eventService.add = function (callback) {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/user/view/add.html',
                controller: function (rest, $scope, modal, $uibModalInstance, GroupRuleService) {
                    $scope.user = {};
                    $scope.toSystemTypes = {};
                    $scope.toSystemTypeMap = {};
                    $scope.roles = {};
                    Rest.all('user/getToSystemTypeList').post().then(function (response) {
                        $scope.toSystemTypes = response.list;
                        if (response && response.length > 0) {
                            for (var i = 0; i < response.length; i++) {
                                var t = response[i];
                                $scope.toSystemTypeMap[t.code] = t.msg;
                            }
                        }
                    });
                    $scope.refreshToSystemType = function (toSystem) {
                        Rest.all('user/getToSystemTypeList').post().then(function (response) {
                            $scope.toSystemTypes = response;
                            for (var t in response) {
                                if ($scope.user.toSystem == response[t].code) {
                                    $scope.user.toSystem = response[t];
                                }
                            }
                        });
                    };
                    $scope.loading = Rest.all('user/getRoles').post().then(function (response) {
                        $scope.roles = response;
                        $scope.user = angular.copy(user);
                    });
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        if (!$scope.user.roles || $scope.user.roles.length == 0 || !$scope.user.toSystem || !$scope.user.name) {
                            Messenger().post({type: 'info', message: '请检查必填参数!'});
                            return;
                        }
                        Rest.all('user/existName').post({name: $scope.user.name}).then(function (response) {
                            if (response && (response.length > 0)) {
                                Messenger().post({type: 'info', message: '该用户名已存在!'});
                                return;
                            }
                            var user = angular.copy($scope.user);
                            user.toSystem = user.toSystem.code;
                            $scope.loading = Rest.all('user/add').post(user).then(function () {
                                Messenger().post({type: 'success', message: '名单数据新增成功!'});
                                callback();
                                $uibModalInstance.close();
                            });
                        });
                    };
                }
            });
        };

        eventService.edit = function (user, callback) {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/user/view/edit.html',
                controller: function (rest, $scope, modal, $uibModalInstance, GroupRuleService) {
                    $scope.toSystemTypes = {};
                    $scope.toSystemTypeMap = {};
                    Rest.all('user/getToSystemTypeList').post().then(function (response) {
                        $scope.toSystemTypes = response.list;
                        if (response && response.length > 0) {
                            for (var i = 0; i < response.length; i++) {
                                var t = response[i];
                                $scope.toSystemTypeMap[t.code] = t.msg;
                            }
                        }
                    });

                    $scope.refreshToSystemType = function (toSystem) {
                        Rest.all('user/getToSystemTypeList').post().then(function (response) {
                            $scope.toSystemTypes = response;
                            for (var t in response) {
                                if ($scope.user.toSystem == response[t].code) {
                                    $scope.user.toSystem = response[t];
                                }
                            }
                        });
                    }

                    $scope.loading = Rest.all('user/getRoles').post().then(function (response) {
                        $scope.roles = response;
                        $scope.user = angular.copy(user);
                    });

                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        var user = angular.copy($scope.user);
                        user.toSystem = user.toSystem ? (user.toSystem.code == 0 ? null : user.toSystem.code) : null;
                        $scope.loading = Rest.all('user/updateRole').post(user).then(function () {
                            callback();
                            $scope.close();
                        });
                    };
                }
            });
        };
        return eventService;
    });
})();