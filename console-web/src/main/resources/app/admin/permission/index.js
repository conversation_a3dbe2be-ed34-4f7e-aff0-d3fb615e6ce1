(function () {
    var app = angular.module(app_name);
    app.controller('permissionCtrl', function ($scope, $state, $uibModal, Rest, UserService, modal) {
        $scope.total = 0;
        $scope.list = [];
        $scope.size = 10;
        $scope.page = 1;
        $scope.param = {
            name: '',
            parent: ''
        };

        function go(page) {
            $scope.loading = true;
            $scope.page = page || $scope.page || 1;
            $scope.loading = Rest.all('permission/search?s=' + $scope.size +'&p=' + $scope.page).post($scope.param).then(function (response) {
                //Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.list = response.list;
                $scope.total = response.total;
            });
        };
        go($scope.page);

        $scope.search = function () {
            go(1);
        };

        $scope.pageChanged = function () {
            go($scope.page);
        };

        $scope.delete = function (permission) {
            modal.confirm('确定删除权限 [ ' + permission.description + ' ] ?', function () {
                Rest.all('permission/delete/' + permission.id).post().then(function (response) {
                    go();
                })
            });
        };

        $scope.edit = function(edit){
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/permission/view/edit.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {
                    $scope.chooseList = [];
                    $scope.choose = {};
                    $scope.permission = angular.copy(edit);
                    /*if(null != edit.parentId){
                        Rest.all('permission/get', edit.parentId).post().then(function(response){
                            if(null != response){
                                $scope.chooseList = [];
                                $scope.chooseList.push(response);
                                $scope.choose.permission = response;
                            }
                        })
                    }*/

                    Rest.all('permission/hint?s=200&p=1').post({
                        delFlag: false
                    }).then(function(response){
                        $scope.chooseList = response;
                        // 设置所属父权限信息
                        if(edit.parentId&&response&&response.length>0){
                            for(var i=0;i<response.length;i++){
                                var item = response[i];
                                var id = item.id;
                                if(id&&id==edit.parentId){
                                    $scope.choose.permission = item;
                                    break;
                                }
                            }
                        }
                    });

                    $scope.close = function () {
                        $uibModalInstance.close();
                    };

                    $scope.load = function(keyword){
                        Rest.all('permission/hint?s=200&p=1').post({
                            description: keyword,
                            delFlag: false
                        }).then(function(response){
                            $scope.chooseList = response;
                        });
                    };

                    $scope.confirm = function () {
                        var permission = angular.copy($scope.permission);
//                        console.log("权限："+JSON.stringify(permission))
//                        permission.parentId = $scope.choose.permission.id;
                        if(null != $scope.choose.permission){
                            //用来更新父菜单权限
                            permission.parentDesc = permission.parentId
                            permission.parentId = $scope.choose.permission.id;
                        }else {
                            permission.parentId = 0;
                        }

                        // console.log(JSON.stringify(permission));
                        $scope.loading = Rest.service('permission/update/' + permission.id).post(permission).then(function () {
                            Messenger().post({type: 'success', message: '保存成功'});
                            go();
                            $uibModalInstance.close();
                        });
                    };
                }
            });
        };

        $scope.add = function () {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/permission/view/add.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {
                    $scope.chooseList = [];
                    $scope.choose = {};
                    $scope.permission = {type : 1};

                    $scope.close = function () {
                        $uibModalInstance.close();
                    };

                    $scope.load = function(keyword){
                        Rest.all('permission/hint?s=200&p=1').post({
                            description: keyword,
                            delFlag: false
                        }).then(function(response){
                           $scope.chooseList = response;
                        });
                    };

                    $scope.confirm = function () {
                        var permission = angular.copy($scope.permission);
                        if(null != $scope.choose.permission){
                            permission.parentId = $scope.choose.permission.id;
                        }
                        //console.log(JSON.stringify(permission));
                        $scope.loading = Rest.service('permission/add').post(permission).then(function () {
                            Messenger().post({type: 'success', message: '保存成功'});
                            go();
                            $uibModalInstance.close();
                        });
                    };
                }
            });
        };

    });
})();
