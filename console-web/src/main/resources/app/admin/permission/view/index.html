<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-user"/> 权限列表</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-3">
                <input ng-model="param.name" type="text" class="form-control input-md" placeholder="按标识匹配">
            </div>
            <div class="form-group col-sm-3">
                <input ng-model="param.description" type="text" class="form-control input-md" placeholder="按描述匹配">
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索</button>
            <button ng-click="add()" class="btn btn-flat btn-md btn-info"><i class="fa fa-pencil fa-fw"></i>新增</button>
        </form>

    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>标识</th>
                <th>描述</th>
                <th>父权限</th>
                <th>类型</th>
                <th>排序</th>
                <th style="width: 15%;">补充信息</th>
                <th>创建人</th>
                <th>创建时间</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="perm in list">
                <td>{{perm.name}}</td>
                <td>{{perm.description}}</td>
                <td>{{perm.parentDesc}}</td>
                <td>{{perm.type | display: 'permissionType'}}</td>
                <td>{{perm.orderNum}}</td>
                <td>{{perm.supplement}}</td>
                <td>{{perm.author}}</td>
                <td>{{perm.createdAt | date: 'yyyy-MM-dd HH:mm'}}</td>
                <td>{{perm.updatedAt | date: 'yyyy-MM-dd HH:mm'}}</td>
                <td>
                    <button uib-tooltip="编辑" class="btn btn-flat btn-md btn-info" ng-click="edit(perm)">
                        <i class="fa fa-pencil fa-fw"></i>
                    </button>
                    <button class="btn btn-flat btn-md btn-danger" ng-click="delete(perm)">
                        <i class="fa fa-remove fa-fw"/>
                    </button>
                </td>
            </tr>

        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
