<form class="form-horizontal w5c-form" name="addForm" novalidate w5c-form-validate="">
    <div cg-busy="loading">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                    aria-hidden="true">&times;</span></button>
            <h3 class="modal-title">修改权限</h3>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="col-sm-3 control-label">权限标识</label>
                <div class="col-sm-8">
                    <input ng-model="permission.name" name="name" type="text" class="form-control input-md"
                           ng-maxlength="128"
                           ng-minlength="1"
                           autocomplete="off"
                           ng-disabled="disabled"
                           required
                           maxlength="128" 　/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">权限描述</label>
                <div class="col-sm-8">
                    <input ng-model="permission.description" name="description" type="text" class="form-control input-md"
                           ng-maxlength="128"
                           ng-minlength="1"
                           autocomplete="off"
                           ng-disabled="disabled"
                           required
                           maxlength="128" 　/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label">所属父权限</label>
                <div class="col-sm-8">
                    <ui-select ng-model="choose.permission" name="parent" theme="bootstrap">
                        <ui-select-match allow-clear="true" placeholder="选择父权限">{{$select.selected.description}}</ui-select-match>
                        <ui-select-choices repeat="perm in chooseList |filter: $select.search track by perm.id"
                                           refresh="load($select.search)" refresh-delay="300">
                            <div ng-bind-html="perm.description"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">权限类型</label>
                <div class="col-sm-8">
                    <select name="type" ng-model="permission.type" convert-to-number class="form-control input-md" required>
                        <option value="1">菜单</option>
                        <option value="2">按钮</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">排序编号</label>
                <div class="col-sm-8">
                    <input ng-model="permission.orderNum" type="number" name="orderNum" class="form-control input-md"
                           ng-maxlength="11"
                           autocomplete="off"
                           required
                           ng-disabled="disabled"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">权限补充信息</label>
                <div class="col-sm-8">
                    <textarea cols="30" rows="5" ng-model="permission.supplement" name="supplement"
                        class="form-control input-md" ng-maxlength="768"
                              ng-disabled="disabled"></textarea>
                </div>
            </div>

        </div>
        <div class="modal-footer">
            <div class="col-sm-offset-3 col-sm-9">
                <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                <button type="submit" class="btn btn-success" w5c-form-submit="confirm();">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 确认
                </button>
            </div>
        </div>
    </div>
</form>