<div class="row">
    <div class="col-md-12">
        <div class="box box-primary" cg-busy="loading">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-bars"/> 缓存管理</h3>
            </div>
            <div class="box-search">
                <form class="w5c-form" novalidate w5c-form-validate="" name="form">
                    <div class="form-group col-sm-1">
                        <select ng-model="cache.hint" class="form-control input-md" ng-options="item.name for item in hints" ng-change="hintChange()">
                            <option value="" selected="selected">-提示-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-1">
                        <select ng-model="cache.operation" class="form-control input-md" ng-options="item.name for item in operations">
                            <option value="" selected="selected">-操作-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-1">
                        <select ng-model="cache.type" class="form-control input-md" ng-options="item.name for item in types">
                            <option value="" selected="selected">-数据类型-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-3">
                        <input ng-model="cache.key" type="text" class="form-control input-md" placeholder="{{keyPlaceHolder}}">
                    </div>
                    <div class="form-group col-sm-3">
                        <input ng-model="cache.field" type="text" class="form-control input-md" placeholder="{{fieldPlaceHolder}}">
                    </div>
                    <div class="form-group col-sm-2">
                        <input ng-model="cache.value" type="text" class="form-control input-md" placeholder="{{valuePlaceHolder}}">
                    </div>
                    <button type="button" class="btn btn-flat btn-md btn-info" w5c-form-submit="execute()">
                        <i class="fa fa-cog"></i>执行
                    </button>
                </form>
            </div>
            <div class="box-body">
                <pre>{{format(result)}}</pre>
            </div>
        </div>
    </div>
</div>