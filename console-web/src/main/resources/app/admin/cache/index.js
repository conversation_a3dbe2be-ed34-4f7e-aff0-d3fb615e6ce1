(function () {
    var app = angular.module(app_name);

    var operations = [
        {id: 'GET', name: "GET"},
        {id: 'SET', name: "SET"},
        {id: 'DEL', name: "DEL"}
    ];
    var types = [
        {id: 'STRING', name: "STRING"},
        {id: 'LIST', name: "LIST"},
        {id: 'HASH', name: "HASH"},
        {id: 'SET', name: "SET"},
        {id: 'ZSET', name: "ZSET"}
    ];

    app.controller('cacheCtrl', function ($scope, $state, $uibModal, Rest) {

        $scope.cache = {};
        $scope.operations = operations;
        $scope.types = types;

        $scope.keyPlaceHolder = "Key";
        $scope.fieldPlaceHolder = "Field";
        $scope.valuePlaceHolder = "Value";

        $scope.loading = true;
        $scope.loading = Rest.all('cache/hints').post().then(function (response) {
            $scope.hints = response;
        });

        $scope.format = function (json) {
            if (!json) {
                return null;
            }
            return JSON.stringify(json, undefined, 2);
        };

        $scope.hintChange = function () {
            if (!$scope.cache.hint) {
                $scope.cache = {};
                $scope.operations = operations;
                $scope.types = types;
                $scope.keyPlaceHolder = "Key";
                $scope.fieldPlaceHolder = "Field";
                $scope.valuePlaceHolder = "Value";
                $scope.result = "";
                return;
            }

            // operation
            var hintOps = $scope.cache.hint.operations;
            var avaOps = [];
            for (var idx in hintOps) {
                var ops = hintOps[idx];
                var tmp = {};
                tmp['id'] = ops;
                tmp['name'] = ops;
                avaOps.push(tmp);
            }
            $scope.operations = avaOps;
            $scope.cache.operation = avaOps[0];

            // type
            var hintType = $scope.cache.hint.type;
            var avaType = [];
            var tmp = {};
            tmp['id'] = hintType;
            tmp['name'] = hintType;
            avaType.push(tmp);

            $scope.types = avaType;
            $scope.cache.type = avaType[0];

            // key
            $scope.keyPlaceHolder = $scope.cache.hint.key;
            $scope.cache.key = '';
            // field
            $scope.fieldPlaceHolder = $scope.cache.hint.field;
            $scope.cache.field = '';
            // value
            $scope.valuePlaceHolder = $scope.cache.hint.value;
            $scope.cache.value = '';
        };

        $scope.execute = function () {
            if (!$scope.preCheck()) {
                return;
            }
            $scope.loading = true;
            var params = {};
            params.type = ($scope.cache.type && $scope.cache.type.id) || ''
            params.operation = ($scope.cache.operation && $scope.cache.operation.id) || ''
            params.key = $scope.cache.key || ''
            if ($scope.cache.hint.name == "图片检测") {
                //图片检测防止url过长，需做md5处理
                params.key = params.key.slice(0, 13) + hex_md5(params.key.slice(13));
            }
            params.field = $scope.cache.field || ''
            params.value = $scope.cache.value || ''
            $scope.loading = Rest.all('cache/execute').post(params).then(function (response) {
                Messenger().post({type: 'success', message: '执行完成'});
                $scope.result = response;
            });
        };

        $scope.preCheck = function () {
            $scope.result = null;
            if (!$scope.cache.operation) {
                Messenger().post({type: 'error', message: '请选择操作类型'});
                return false;
            }
            if (!$scope.cache.type) {
                Messenger().post({type: 'error', message: '请选择数据类型'});
                return false;
            }
            if (!$scope.cache.key) {
                Messenger().post({type: 'error', message: '请输入Key'});
                return false;
            }
            if ($scope.cache.type.id == 'HASH') {
                if (!$scope.cache.field) {
                    Messenger().post({type: 'error', message: 'HASH类型需指定Field'});
                    return false;
                }
            }

            return true;
        };

    });
})();