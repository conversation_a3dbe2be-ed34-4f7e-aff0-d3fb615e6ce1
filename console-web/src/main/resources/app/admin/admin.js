(function () {
    var app = angular.module(app_name);

    app.config(function ($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.when('/admin', '/admin/user');
        $stateProvider.state('admin', {
            parent: 'home',
            url: '/admin',
            templateUrl: 'admin/index.html'
        });

        $stateProvider.state('admin.user', {
            url: '/user',
            templateUrl: 'admin/user/view/index.html',
            controller: 'UserCtrl'
        });

        $stateProvider.state('admin.role', {
            url: '/role',
            templateUrl: 'admin/role/view/index.html',
            controller: 'roleCtrl'
        });

        $stateProvider.state('admin.permission', {
            url: '/permission',
            templateUrl: 'admin/permission/view/index.html',
            controller: 'permissionCtrl'
        });

        $stateProvider.state('admin.sql', {
            url: '/sql',
            templateUrl: 'admin/sql/view/index.html',
            controller: 'SQLCtrl'
        });

        $stateProvider.state('admin.mq', {
            url: '/mq',
            templateUrl: 'admin/mq/view/index.html',
            controller: 'MQCtrl'
        });

/*        $stateProvider.state('admin.test', {
            url: '/test',
            templateUrl: 'admin/test/testHome.html',
            controller: 'testCtrl'
        });*/

        $stateProvider.state('admin.cache', {
            url: '/cache',
            templateUrl: 'admin/cache/view/index.html',
            controller: 'cacheCtrl'
        });

        $stateProvider.state('admin.log', {
            url: '/log',
            templateUrl: 'admin/log/view/index.html',
            controller: 'riskLogCtrl'
        });

        $stateProvider.state('admin.reissue', {
            url: '/reissue',
            templateUrl: 'admin/reissue/view/index.html',
            controller: 'reissueCtrl'
        });

        $stateProvider.state('admin.drop', {
            url: '/drop',
            templateUrl: 'admin/drop/view/index.html',
            controller: 'dropCtrl'
        });

        $stateProvider.state('admin.catAlarmLog', {
            url: '/catAlarmLog',
            templateUrl: 'admin/catAlarmLog/view/index.html',
            controller: 'catAlarmLogCtrl'
        });

    });
})();
