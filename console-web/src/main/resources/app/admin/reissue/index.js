(function () {
    var app = angular.module(app_name);

    var reissueTypes = [
        {key: 0, value: "送审记录"},
        {key: 1, value: "机审通知"},
        {key: 2, value: "人审通知"}
    ];
    var taskTypes = [
        {key: 0, value: "手动单条"},
        {key: 1, value: "手动批量"},
        {key: 2, value: "定时任务"}
    ];
    var taskStatus = [
        {key: 0, value: "进行中"},
        {key: 1, value: "完成"},
        {key: 2, value: "失败"}
    ];

    app.controller('reissueCtrl', function ($scope, $state, $uibModal, Rest, reissueService, modal) {
        $scope.reissue = {};
        $scope.reissueType = {};
        $scope.taskType = {};
        $scope.status = {};
        //业务/补偿类型
        $scope.reissueTypes = reissueTypes;
        //任务类型
        $scope.taskTypes = taskTypes;
        //任务状态
        $scope.taskStatus = taskStatus;
        //时间范围
        $scope.reissue.updateTime = new Date();
        $scope.reissue.updateTime.setHours(23);
        $scope.reissue.updateTime.setMinutes(59);
        $scope.reissue.updateTime.setSeconds(59);
        $scope.reissue.updateTime.setMilliseconds(999);
        $scope.reissue.createTime = new Date($scope.reissue.updateTime.getTime() - 1000 * 60 * 60 * 24 * 7 + 1);

        go();

        function go(page) {
            var timeSpan = ($scope.reissue.updateTime.getTime() - $scope.reissue.createTime.getTime()) / 1000 / 60 / 60 / 24; // 时间跨度-天
            if (timeSpan > 30) {
                modal.alert("时间跨度不允许超过30天");
                return;
            }
            $scope.reissue.reissueType = $scope.reissueType ? $scope.reissueType.key : null;
            $scope.reissue.taskType = $scope.taskType.key ? $scope.taskType.key : null;
            $scope.reissue.status = $scope.status.key ? $scope.status.key : null;
            $scope.loading = true;
            $scope.page = page || $scope.page || 1;
            $scope.loading = Rest.all('reissue/search?p=' + $scope.page).post($scope.reissue).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.records = response.list;
                $scope.total = response.total;
            });
        };
        $scope.search = function () {
            go(1);
        };
        $scope.pageChanged = function () {
            go($scope.page);
        };

        //需送审的events
        $scope.auditEvents = [];
        $scope.loading = Rest.all('reissue/getAuditEvents').post().then(function (result) {
            $scope.auditEvents = result;
        });
        $scope.isAuditEvent = function (eventCode) {
            for (var i = 0; i < $scope.auditEvents.length; i++) {
                if ($scope.auditEvents[i] === eventCode) {
                    return true;
                }
            }
            return false;
        };

        //风控事件
        $scope.eventsMap = {};
        $scope.events = [];
        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            var data = result.data;
            for (var key in data) {
                $scope.events.push({'id': key, 'name': data[key]});
            }
            $scope.eventsMap = data;
        });
        $scope.code2Name = function (code) {
            return $scope.eventsMap[code];
        };

        $scope.execute = function () {
            if (!$scope.preCheck()) {
                return;
            }
            Rest.all('reissue/auditBatch').post($scope.reissue).then(function (response) {
                if (response) {
                    Messenger().post({type: 'success', message: '送审成功!'});
                } else {
                    Messenger().post({type: 'error', message: "送审失败!"});
                }
            })
        };

        $scope.preCheck = function () {
            if (!$scope.reissue.eventCode) {
                Messenger().post({type: 'error', message: '请输入事件code！'});
                return false;
            }
            if ($scope.reissue.createTime >= $scope.reissue.updateTime) {
                Messenger().post({type: 'error', message: '开始时间必须小于结束时间！'});
                return false;
            }
            if (!isAuditEvent()) {
                Messenger().post({type: 'error', message: '请输入正确的需人工审核事件！'});
                return false;
            }
            return true;
        };

        function isAuditEvent() {
            for (var i = 0; i < $scope.auditEvents.length; i++) {
                if ($scope.auditEvents[i] === $scope.reissue.eventCode) {
                    return true;
                }
            }
            return false;
        };

        $scope.add = function () {
            reissueService.add(go);
        };
    });

    app.factory('reissueService', function ($uibModal, Rest, Session) {
        var reissueService = {};

        reissueService.add = function (callback) {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'admin/reissue/view/add.html',
                controller: function (Rest, $scope, modal, $uibModalInstance) {
                    $scope.reissue = {};
                    //业务/补偿类型
                    $scope.reissueTypes = reissueTypes;
                    //风控事件
                    $scope.eventsList = [];
                    $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
                        var data = result.data;
                        for (var key in data) {
                            $scope.eventsList.push({'id': key, 'name': data[key]});
                        }
                    });

                    //需送审的events
                    $scope.auditEvents = [];
                    $scope.loading = Rest.all('reissue/getAuditEvents').post().then(function (result) {
                        $scope.auditEvents = result;
                    });
                    $scope.isAuditEvent = function () {
                        for (var i = 0; i < $scope.auditEvents.length; i++) {
                            if ($scope.auditEvents[i] === $scope.reissue.event.id) {
                                console.log("$scope.auditEvents[i]");
                                console.log($scope.auditEvents[i]);
                                console.log("$scope.reissue.event.id");
                                console.log($scope.reissue.event.id);
                                return true;
                            }
                        }
                        return false;
                    };

                    $scope.preCheck = function () {
                        if (!$scope.reissue.reissueType) {
                            Messenger().post({type: 'error', message: '请选择业务类型！'});
                            return false;
                        }
                        if (!$scope.reissue.event) {
                            Messenger().post({type: 'error', message: '请选择事件！'});
                            return false;
                        }
                        if (!$scope.reissue.startTime) {
                            Messenger().post({type: 'error', message: '请选择事件开始时间！'});
                            return false;
                        }
                        if (!$scope.reissue.endTime) {
                            Messenger().post({type: 'error', message: '请选择事件结束时间！'});
                            return false;
                        }
                        if ($scope.reissue.startTime >= $scope.reissue.endTime) {
                            Messenger().post({type: 'error', message: '开始时间必须小于结束时间！'});
                            return false;
                        }
                        console.log("$scope.reissue.reissueType.key");
                        console.log($scope.reissue.reissueType.key);
                        if ($scope.reissue.reissueType.key === 0) {
                            if (!$scope.isAuditEvent()) {
                                Messenger().post({type: 'error', message: '该事件无需人审！'});
                                return false;
                            }
                        }
                        var timeSpan = ($scope.reissue.endTime.getTime() - $scope.reissue.startTime.getTime()) / 1000 / 60 / 60 / 24; // 时间跨度-天
                        if (timeSpan > 3) {
                            Messenger().post({type: 'error', message: '时间跨度不允许超过3天！'});
                            return false;
                        }
                        return true;
                    };

                    $scope.count = function () {
                        if (!$scope.preCheck()) {
                            return;
                        }
                        var param = angular.copy($scope.reissue);
                        param.reissueType = $scope.reissue.reissueType ? $scope.reissue.reissueType.key : null;
                        param.eventCode = $scope.reissue.event ? $scope.reissue.event.id : null;
                        //初始化为空
                        $scope.reissue.count = "";
                        $scope.loading = Rest.all('reissue/previewBatch').post(param).then(function (response) {
                            if (!response) {
                                $scope.reissue.count = 0;
                            } else {
                                $scope.reissue.count = response;
                            }
                        })
                    };

                    $scope.close = function () {
                        $uibModalInstance.close();
                    };

                    $scope.confirm = function () {
                        if (!$scope.preCheck()) {
                            return;
                        }
                        var param = angular.copy($scope.reissue);
                        param.reissueType = $scope.reissue.reissueType ? $scope.reissue.reissueType.key : null;
                        param.eventCode = $scope.reissue.event ? $scope.reissue.event.id : null;
                        $uibModalInstance.close();
                        callback();
                        Rest.all('reissue/batch').post(param).then(function () {
                            callback();
                        });
                    };
                }
            });
        };
        return reissueService;
    });
})();