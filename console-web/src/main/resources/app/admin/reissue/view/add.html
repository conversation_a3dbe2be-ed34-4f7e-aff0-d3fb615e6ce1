<form class="form-horizontal w5c-form" name="addForm" novalidate w5c-form-validate="">
    <div cg-busy="loading">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                    aria-hidden="true">&times;</span></button>
            <h3 class="modal-title">新增补偿数据</h3>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="col-sm-3 control-label">业务类型*:</label>
                <div class="col-sm-8">
                    <ui-select ng-model="reissue.reissueType" theme="bootstrap">
                        <ui-select-match placeholder="业务类型">{{$select.selected.value}}</ui-select-match>
                        <ui-select-choices
                                repeat="item in reissueTypes|filter: $select.search track by item.value">
                            <div ng-bind-html="item.value"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">事件*:</label>
                <div class="col-sm-8">
                    <ui-select ng-model="reissue.event" theme="bootstrap">
                        <ui-select-match placeholder="事件">{{$select.selected.name}}</ui-select-match>
                        <ui-select-choices
                                repeat="item in eventsList|filter: $select.search">
                            <div ng-bind-html="item.name"></div>
                        </ui-select-choices>
                    </ui-select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">起始时间*:</label>
                <div class="col-sm-8">
                    <input type="text" name="startTime" class="form-control" placeholder="起始时间"
                           uib-datepicker-popup="yyyy-MM-dd HH:mm:ss"
                           ng-model="reissue.startTime" ng-click="popup1.opened=true"
                           is-open="popup1.opened" datepicker-options="dateOptions"
                           close-text="Close" showError="时间格式错误"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">截止时间*:</label>
                <div class="col-sm-8">
                    <input type="text" name="endTime" class="form-control" placeholder="截止时间"
                           uib-datepicker-popup="yyyy-MM-dd HH:mm:ss"
                           ng-model="reissue.endTime" ng-click="popup2.opened=true"
                           is-open="popup2.opened" datepicker-options="dateOptions"
                           close-text="Close" showError="时间格式错误"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">补偿间隔(分钟):</label>
                <div class="col-sm-8">
                    <input name="interval" ng-model="reissue.interval" type="text" class="form-control input-md"
                           placeholder="时间分片，防止一次查询数据过多，默认30min">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">风控级别:</label>
                <div class="col-sm-8">
                    <input name="levels" ng-model="reissue.levels" type="text" class="form-control input-md"
                           placeholder="例：PASS,REVIEW">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">高级过滤:</label>
                <div class="col-sm-8">
                    <textarea name="condition" ng-model="reissue.condition" type="text" class="form-control input-md"
                              placeholder='新鸟与菜鸟慎用，例：[{"key":"data.UserId","value":"uid"},{"expression":"!=","key":"traceId","value":"abc"}]' autocomplete="off" rows="3"/>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">预览数量:</label>
                <div class="col-sm-8">
                    <input name="count" ng-model="reissue.count" type="text" readonly
                           class="form-control input-md" placeholder="预览数量(批量检测同BatchId算一笔)"></div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="col-sm-offset-3 col-sm-9">
                <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                <button type="submit" class="btn btn-success" w5c-form-submit="count();">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 预览数量
                </button>
                <button type="submit" class="btn btn-success" w5c-form-submit="confirm();">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 保存
                </button>
            </div>
        </div>
    </div>
</form>