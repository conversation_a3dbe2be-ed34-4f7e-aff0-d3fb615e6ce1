<div class="row">
    <div class="col-md-12">
        <div class="box box-primary" cg-busy="loading">
            <div class="box-header with-border">
                <h3 class="box-title"><i class="fa fa-fire"/>补偿记录</h3>
            </div>
            <div class="box-search">
                <form>
                    <div class="form-group col-sm-2">
                        <select ng-model="reissueType" class="form-control input-md"
                                ng-options="item.value for item in reissueTypes">
                            <option value="" selected="selected">-业务类型-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-2">
                        <select ng-model="taskType" class="form-control input-md"
                                ng-options="item.value for item in taskTypes">
                            <option value="" selected="selected">-任务类型-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-2">
                        <select ng-model="status" class="form-control input-md"
                                ng-options="item.value for item in taskStatus">
                            <option value="" selected="selected">-任务状态-</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-2">
                        <input type="text" name="startTime" class="form-control"
                               uib-datepicker-popup="yyyy-MM-dd HH:mm:ss"
                               ng-model="reissue.createTime" ng-click="popup1.opened=true"
                               is-open="popup1.opened" datepicker-options="dateOptions"
                               close-text="Close" showError="时间格式错误"/>
                    </div>
                    <div class="form-group col-sm-2">
                        <input type="text" name="endTime" class="form-control"
                               uib-datepicker-popup="yyyy-MM-dd HH:mm:ss"
                               ng-model="reissue.updateTime" ng-click="popup2.opened=true"
                               is-open="popup2.opened" datepicker-options="dateOptions"
                               close-text="Close" showError="时间格式错误"/>
                    </div>
                    <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索
                    </button>
                    <button ng-if="!readonly" class="btn btn-flat btn-md btn-info" ng-click="add()">
                        <i class="fa fa-pencil fa-fw"></i>&nbsp;新增
                    </button>
                </form>
            </div>

            <div class="box-body">
                <table class="table table-bordered">
                    <tr>
                        <th>业务类型</th>
                        <th>任务类型</th>
                        <th>执行时间</th>
                        <th>事件</th>
                        <th>操作人</th>
                        <th>补偿条数</th>
                        <th>任务状态</th>
                    </tr>
                    <tr ng-repeat="record in records">
                        <td>
                            <span ng-if="record.reissueType == '0'">送审记录</span>
                            <span ng-if="record.reissueType == '1'">机审通知</span>
                            <span ng-if="record.reissueType == '2'">人审通知</span>
                        </td>
                        <td>
                            <span ng-if="record.taskType == '0'">手动单条</span>
                            <span ng-if="record.taskType == '1'">手动批量</span>
                            <span ng-if="record.taskType == '2'">定时任务</span>
                        </td>
                        <td>
                            {{record.createTime | date: 'yyyy-MM-dd HH:mm:ss'}}
                        </td>
                        <td>
                            {{code2Name(record.eventCode)}}
                        </td>
                        <td>
                            {{record.modifier}}
                        </td>
                        <td>
                            <span ng-if="record.successCount > '0'">成功: {{record.successCount}}</span>
                            <br/>
                            <span ng-if="record.failCount > '0'">失败: {{record.failCount}}</span>
                        </td>
                        <td>
                            <span ng-if="record.taskStatus == '0'">进行中</span>
                            <span ng-if="record.taskStatus == '1'">完成</span>
                            <span ng-if="record.taskStatus == '2'">失败</span>
                        </td>
                    </tr>

                </table>
            </div>
            <div class="box-footer">
                <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
                    first-text="首页"
                    last-text="尾页"
                    previous-text="前一页"
                    next-text="后一页"
                    total-items="total"
                    ng-model="page"
                    ng-change="pageChanged()"
                    items-per-page="size"
                    max-size="10"
                    rotate="false">
                </ul>
            </div>
        </div>
    </div>
</div>