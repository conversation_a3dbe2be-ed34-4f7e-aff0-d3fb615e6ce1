<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-fire"/> 配置变更列表</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-2">
                <select ng-model="log.configType" class="form-control input-md"
                        ng-options="item.value for item in configTypes">
                    <option value="" selected="selected">-配置类型-</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="log.updateType" class="form-control input-md"
                        ng-options="item.value for item in updateTypes">
                    <option value="" selected="selected">-变更类型-</option>
                </select>
            </div>
            <div class="form-group col-sm-6 col-md-4 col-lg-3">
                <input ng-model="log.modifier" type="text" class="form-control input-md" placeholder="用户名">
            </div>
            <div class="form-group col-sm-6 col-md-4 col-lg-3">
                <input ng-model="log.content" type="text" class="form-control input-md" placeholder="配置内容">
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="startTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="log.startTime" ng-click="popup1.opened=true"
                       is-open="popup1.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="endTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="log.endTime" ng-click="popup2.opened=true"
                       is-open="popup2.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info"><i class="fa fa-search"></i>搜索</button>
        </form>

    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>配置类型</th>
                <th>变更类型</th>
                <th>修改人</th>
                <th>修改时间</th>
                <th>配置内容</th>
                <th>关联关系</th>
            </tr>
            <tr ng-repeat="log in logs">
                <td class="text-bold" style="width:100px">
                    <span ng-if="log.configType == 'risk_event'">事件</span>
                    <span ng-if="log.configType == 'risk_rule_atom'">规则</span>
                    <span ng-if="log.configType == 'risk_rule_group'">规则组</span>
                    <span ng-if="log.configType == 'risk_attribute'">属性</span>
                    <span ng-if="log.configType == 'risk_factor'">累计因子</span>
                    <span ng-if="log.configType == 'risk_fencing_word'">违禁词</span>
                    <span ng-if="log.configType == 'risk_fencing_letter'">字</span>
                    <span ng-if="log.configType == 'risk_fencing_scene'">违禁词场景</span>
                    <span ng-if="log.configType == 'risk_fencing_tag'">违禁词标签</span>
                    <span ng-if="log.configType == 'risk_gray_list'">名单</span>
                    <span ng-if="log.configType == 'risk_patrol_rule'">异步规则</span>
                    <span ng-if="log.configType == 'temp_third_feedback'">三方反馈</span>
                    <span ng-if="log.configType == 't_punish_violation'">违规类型</span>
                    <span ng-if="log.configType == 't_punish_subscribe_pkg'">订阅惩罚包</span>
                    <span ng-if="log.configType == 't_punish_subscribe_violation'">订阅违规类型</span>
                    <span ng-if="log.configType == 't_punish_ability'">惩罚项</span>
                    <span ng-if="log.configType == 't_punish_pkg'">惩罚包</span>
                    <span ng-if="log.configType == 't_punish_violation_reason'">违规原因</span>
                    <span ng-if="log.configType == 'risk_biz_type'">业务类型</span>
                    <span ng-if="log.configType == 'risk_third_channel'">三方通道</span>
                </td>
                <td class="text-bold" style="width:100px">
                    <span ng-if="log.updateType == 'insert'" class="text-green">新增</span>
                    <span ng-if="log.updateType == 'update'" class="text-yellow">修改</span>
                    <span ng-if="log.updateType == 'delete'" class="text-red">删除</span>
                </td>
                <td class="text-bold" style="width:100px">
                    {{log.modifier}}
                </td>
                <td class="text-bold" style="width:150px">{{log.createTime | date: 'yyyy-MM-dd HH:mm'}}</td>
                <td class="text-bold">
                    <div style="width:400px;overflow:auto">
                        <pre ng-if="log.updateType == 'insert'">{{format(log.after) | json}}</pre>
                        <pre ng-if="log.updateType == 'delete'">{{format(log.before) | json}}</pre>
                        <pre ng-if="log.updateType == 'update'">变更前：<br/>{{format(log.before) | json}}<br/><br/>变更后：<br/>{{format(log.after) | json}}</pre>
                    </div>
                </td>
                <td class="text-bold">
                    <pre ng-if="log.relation != null && log.relation != ''">{{format(log.relation) | json}}</pre>
                </td>
            </tr>

        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
