(function () {
    var app = angular.module(app_name);

    var configTypes = [
        {key: 'risk_event', value: "事件"},
        {key: 'risk_rule_atom', value: "规则"},
        {key: 'risk_rule_group', value: "规则组"},
        {key: 'risk_attribute', value: "属性"},
        {key: 'risk_factor', value: "累计因子"},
        {key: 'risk_fencing_letter', value: "字"},
        {key: 'risk_fencing_word', value: "违禁词"},
        {key: 'risk_fencing_scene', value: "违禁词场景"},
        {key: 'risk_fencing_tag', value: "违禁词标签"},
        {key: 'risk_gray_list', value: "名单"},
        {key: 'risk_patrol_rule', value: "异步规则"},
        {key: 'temp_third_feedback', value: "三方反馈"},
        {key: 't_punish_violation', value: "违规类型"},
        {key: 't_punish_subscribe_pkg', value: "订阅惩罚包"},
        {key: 't_punish_subscribe_violation', value: "订阅违规类型"},
        {key: 't_punish_ability', value: "惩罚项"},
        {key: 't_punish_pkg', value: "惩罚包"},
        {key: 't_punish_violation_reason', value: "违规原因"},
        {key: 'risk_biz_type', value: "业务类型"},
        {key: 'risk_third_channel', value: "三方通道"}

    ];

    var updateTypes = [
        {key: 'insert', value: "新增"},
        {key: 'update', value: "修改"},
        {key: 'delete', value: "删除"}
    ];

    app.controller('riskLogCtrl', function ($scope, $state, $uibModal, Rest, $aside) {
        $scope.log = {};
        //配置类型
        $scope.configTypes = configTypes;
        //变更类型
        $scope.updateTypes = updateTypes;
        //时间范围
        var endTime = new Date();
        endTime.setHours(23);
        endTime.setMinutes(59);
        endTime.setSeconds(59);
        endTime.setMilliseconds(999);
        var startTime = new Date(endTime.getTime() - 1000 * 60 * 60 * 24 * 7 + 1);
        $scope.log.startTime = startTime;
        $scope.log.endTime = endTime;

        // 查询参数
        function getParam() {
            var param = {};
            param.configType = ($scope.log.configType && $scope.log.configType.key) ? $scope.log.configType.key : null;
            param.updateType = ($scope.log.updateType && $scope.log.updateType.key) ? $scope.log.updateType.key : null;
            param.modifier = $scope.log.modifier || null;
            param.after = $scope.log.content || null;
            param.createTime = $scope.log.startTime;
            param.updateTime = $scope.log.endTime;
            return param
        }

        go();

        function go(page) {
            $scope.loading = true;
            $scope.page = page || $scope.page || 1;
            $scope.loading = Rest.all('log/search?p=' + $scope.page).post(getParam()).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.logs = response.list;
                $scope.total = response.total;
            });
        };

        $scope.search = function () {
            go(1);
        };

        $scope.pageChanged = function () {
            go($scope.page);
        };

        $scope.format = function (json) {
            var json = JSON.parse(json);
            if (json.riskConst) {
                json.riskConst = JSON.parse(json.riskConst);
            }
            return json;
        };

        $scope.showDetail = function (content) {
            var json = {};
            try {
                json = JSON.parse(content);
            } catch (e) {
            }
            console.log(json);
            $aside.open({
                templateUrl: 'admin/log/view/template.html',
                placement: 'right',
                size: 'md',
                animation: false,
                controller: function ($scope) {
                    $scope.result = json;
                }
            });
        };

    });
})();