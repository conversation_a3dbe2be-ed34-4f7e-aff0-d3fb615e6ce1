(function () {
    var app = angular.module(app_name);

    app.config(function ($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.when('/alarmTab', '/alarmTab');
        $stateProvider.state('alarmTab', {
            parent: 'home',
            url: '/alarmTab',
            templateUrl: 'alarm/alarm/index.html',
            controller: 'alarmTabCtrl'
        });
        $stateProvider.state('alarmTab.alarmMain', {
            url: '/alarmMain',
            templateUrl: 'alarm/alarm/alarmMain/view/index.html',
            controller: 'alarmCtrl'
        });
        $stateProvider.state('alarmTab.fuse', {
            url: '/fuse',
            templateUrl: 'alarm/alarm/fuse/view/index.html',
            controller: 'alarmFuseCtrl'
        });
    });

    app.controller('alarmTabCtrl', function ($scope, $rootScope, $state, $stateParams, $uibModal, Rest, modal, $aside, AuthService) {
        $scope.hasPermission = function (permissionName) {
            return AuthService.hasPermission(permissionName);
        };
    });

})();
