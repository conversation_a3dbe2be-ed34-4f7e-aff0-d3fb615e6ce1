(function () {
    var app = angular.module(app_name);

    app.controller('alarmCtrl', function ($scope, $rootScope, $state, $stateParams, $uibModal, Rest, modal,$filter) {
        $scope.eventsList = [];
        $scope.atomRuleList = [];
        $scope.configs = [];
        $scope.page = 1;
        $scope.query = {};
        $scope.alarmRuleTypes = [{"code":"0","name":"--请选择类型--"}];
        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            var data = result.data;
            $scope.eventsList.push({'code': 'ALL', 'name': '全部事件'});
            for (var key in data) {
                $scope.eventsList.push({'code': key, 'name': data[key]});
            }
        });
        $scope.loading = Rest.all('/alarm/getAlarmRuleTypes').post().then(function(response){
            $scope.alarmRuleTypes = $scope.alarmRuleTypes.concat(response);
        });
        $scope.loadRule = function(){
            Rest.all('/rule/atom/all').post({}).then(function(response){
                if(response){
                   $scope.atomRuleList.push({'id': '0', 'name': '全部规则'});
                  for(var key in response){
                    $scope.atomRuleList.push({'id':response[key].id,'name':response[key].name});
                  }
                }
            });
        };
        $scope.loadRule();
        $scope.loadConfigs = function(){
            $scope.loading = Rest.all('alarm/config/getAlarmConfigs').post().then(function (result) {
                $scope.configs = result;
            });
        };
        $scope.loadConfigs();

        $scope.search = function(){
            $scope.query.eventCodes = $scope.query.event ? $scope.query.event.code:null;
            $scope.query.ruleIds = $scope.query.rule ? $scope.query.rule.id:null;

            $scope.loading = Rest.all('alarm/search?p=' + $scope.page).post($scope.query).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.alarms = response.list;
                $scope.total = response.total;
            });
        }
        $scope.search();
        $scope.pageChanged = function () {
            $scope.search();
        };

        $scope.upStatus = function(state,alarm){
            var msg = state == 1 ? "启用" : (state == 2 ? "停用" : "删除");
            modal.confirm('确定'+msg+' [ ' + alarm.name + ' ] ?', function () {
                $scope.loading = Rest.all('alarm/updateStatus').post({"id":alarm.id,"state":state}).then(function (response) {
                    $scope.search();
                });
            });
        }
        $scope.generateTemplate = function(alarm){
            modal.confirm('确定使用 [ ' + alarm.name + ' ] 生成模板?', function () {
                $scope.loading = Rest.all('alarm/generateTemplate').post({"id":alarm.id}).then(function (response) {
                    $scope.search();
                    $scope.loadConfigs();
                });
            });
        }
        $scope.add = function () {
            var addScope = $rootScope.$new();
            addScope.configs = angular.copy($scope.configs);
            addScope.search = $scope.search;
            addScope.alarmRuleTypes = $scope.alarmRuleTypes;
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'alarm/alarm/alarmMain/view/add.html',
                scope: addScope,
                controller: function (rest, $scope, modal, $uibModalInstance) {
                    var defaultConfig = {"id": 0,"type":2, "name":"无","countType":1,"relatedPersons":"","ruleGroup":[{"rules":[{"type":0,"threshold":""}]}]};
                    $scope.countTypes = [{"code":1,"name":"分钟"},{"code":2,"name":"小时"},{"code":3,"name":"天"},{"code":4,"name":"周"}];

                    var timeRegExp = new RegExp("^([0-1][0-9]|2[0-3]):([0-5][0-9])-(([0-1][0-9]|2[0-3]):([0-5][0-9])|24:00)$");
                    var numberRegExp = new RegExp("^([1-9][0-9]*|0)$");
                    $scope.configs.splice(0,0,defaultConfig);
                    $scope.alarm = {"type":"1","ruleType":"0","riskResult":"ALL","actionType":1,"config":defaultConfig};
                    $scope.preConfig = null;
                    $scope.allAtomRules = [];
                    $scope.atomRuleList = [];
                    $scope.allEvents = [];
                    $scope.eventsList = [];

                    $scope.loadAtomRules = function(){
                        Rest.all('rule/atom/all').post({type:$scope.alarm.ruleType == "0" ? null : $scope.alarm.ruleType}).then(function (response) {
                            $scope.allAtomRules = [{'id': '0', 'name': '全部规则'}];
                            if(response){
                                $scope.allAtomRules = $scope.allAtomRules.concat(response);
                            }
                            $scope.atomRuleList = angular.copy($scope.allAtomRules);
                        });
                    }
                    $scope.loadAtomRules();

                    $scope.loadEvents = function(){
                        Rest.all('event/getEvents').post().then(function (result) {
                            var data = result.data;
                            $scope.allEvents = [{'code': 'ALL', 'name': '全部事件'}];
                            for (var key in data) {
                                $scope.allEvents.push({'code': key, 'name': data[key]});
                            }
                            $scope.eventsList = angular.copy($scope.allEvents);
                        });
                    }
                    $scope.loadEvents();
                    $scope.changeRuleType = function(){
                        $scope.alarm.atomRules = null;
                        $scope.loadAtomRules();
                    }

                    $scope.refreshRules = function (name) {
                        $scope.atomRuleList = $filter('filter')($scope.getSelectAtomRules(),name);
                    };

                    $scope.refreshEvents = function (name) {
                        $scope.eventsList = $filter('filter')($scope.getSelectEvents(),name);
                    };

                    $scope.getChoiceAtomRuleType = function(){
                        if($scope.alarm.atomRules == null || $scope.alarm.atomRules.length == 0){
                            return 0;
                        }
                        for(var atomRule of $scope.alarm.atomRules){
                            if(atomRule.id == "0"){
                                return 1;
                            }
                        }
                        return 2;
                    }

                    $scope.getChoiceEventType = function(){
                        if($scope.alarm.events == null || $scope.alarm.events.length == 0){
                            return 0;
                        }
                        for(var event of $scope.alarm.events){
                            if(event.code == "ALL"){
                                return 1;
                            }
                        }
                        return 2;
                    }

                    $scope.getSelectAtomRules = function(){
                        var type = $scope.getChoiceAtomRuleType();
                        if(type == 1){
                            return [{'id': '0', 'name': '全部规则'}];
                        }
                        var tempList = angular.copy($scope.allAtomRules);
                        if(type == 2){
                            tempList.splice(0,1);
                        }
                        return tempList;
                    }

                    $scope.choiceAtomRule = function(atomRule){
                        $scope.atomRuleList = $scope.getSelectAtomRules();
                    }

                    $scope.getSelectEvents = function(){
                        var type = $scope.getChoiceEventType();
                        if(type == 1){
                            return [{'code': 'ALL', 'name': '全部事件'}];
                        }
                        var tempList = angular.copy($scope.allEvents);
                        if(type == 2){
                            tempList.splice(0,1);
                        }
                        return tempList;
                    }

                    $scope.choiceEvent = function(event){
                        $scope.eventsList = $scope.getSelectEvents();
                    }

                    $scope.choiceConfig = function(config){
                        if(config.id > 0){
                            $scope.loading = Rest.all('alarm/config/get/'+config.id).post({}).then(function (response) {
                                $scope.alarm.config = response;
                                $scope.preConfig = response;
                            });
                        }else{
                            if($scope.preConfig != null){
                                $scope.alarm.config = angular.copy($scope.preConfig);
                                $scope.alarm.config.id = 0;
                                $scope.alarm.config.name = "无";
                                $scope.alarm.config.type = 2;
                            }
                        }
                    }
                    $scope.addRuleGroup = function(){
                        $scope.alarm.config.ruleGroup.push({"rules":[{"type":0,"threshold":""}]});
                    }
                    $scope.removeRuleGroup = function(index){
                        if($scope.alarm.config.ruleGroup.length<=1){
                            Messenger().post({type: 'error', message: '规则组必须有1个!'});
                            return;
                        }
                        $scope.alarm.config.ruleGroup.splice(index, 1);
                    }
                    $scope.addRule = function(parentIndex){
                        $scope.alarm.config.ruleGroup[parentIndex].rules.push({"type":0,"threshold":""});
                    }
                    $scope.removeRule = function(parentIndex,index){
                        if($scope.alarm.config.ruleGroup[parentIndex].rules.length<=1){
                            Messenger().post({type: 'error', message: '规则必须有1个!'});
                            return;
                        }
                        $scope.alarm.config.ruleGroup[parentIndex].rules.splice(index, 1);
                    }
                    $scope.validType = function(rule,parentIndex,index){
                        var isTypeErr = false;
                        for(var j=0;j<$scope.alarm.config.ruleGroup[parentIndex].rules.length;j++){
                            var itemRule = $scope.alarm.config.ruleGroup[parentIndex].rules[j];
                            if(j != index){
                                if(itemRule.type != 0 && itemRule.type == rule.type){
                                    isTypeErr = true;
                                    break;
                                }
                            }
                        }
                        rule.isTypeErr = isTypeErr;
                        if(!isTypeErr){
                            if(rule.type == 5 && rule.threshold == ""){
                                rule.threshold = "00:00-24:00";
                                rule.isErr = false;
                                return;
                            }
                        }
                        $scope.validThreshold(rule);
                    }
                    $scope.validThreshold = function(rule){
                        if(rule.threshold == ""){
                            rule.isErr = false;
                            return;
                        }
                        if(rule.type == 5){
                            if(!timeRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }else{
                            if(!numberRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }
                    }
                    $scope.validAllThreshold = function(){
                        for(var i=0;i<$scope.alarm.config.ruleGroup.length;i++){
                            for(var j=0;j<$scope.alarm.config.ruleGroup[i].rules.length;j++){
                                var rule = $scope.alarm.config.ruleGroup[i].rules[j];
                                if(rule.isErr || rule.isTypeErr || rule.type==0 || rule.threshold == ""){
                                    return false;
                                }
                            }
                        }
                        return true;
                    }
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        if(!$scope.validAllThreshold()){
                            return;
                        }
                        if($scope.alarm.events == null || $scope.alarm.events.length == 0){
                            return;
                        }
                        if($scope.alarm.type == 1){
                            $scope.alarm.ruleIds = null;
                        }else if($scope.alarm.type == 2){
                            if($scope.alarm.atomRules == null || $scope.alarm.atomRules.length == 0){
                                return;
                            }
                            var atomRuleIds = [];
                            for(var atomRule of $scope.alarm.atomRules){
                                atomRuleIds.push(atomRule.id);
                            }
                            $scope.alarm.ruleIds = atomRuleIds.join(",");
                        }
                        var eventCodes = [];
                        for(var event of $scope.alarm.events){
                            eventCodes.push(event.code);
                        }
                        $scope.alarm.eventCodes = eventCodes.join(",");
                        $scope.alarm.configId = $scope.alarm.config.id;
                        $scope.loading = Rest.all('alarm/add').post($scope.alarm).then(function () {
                            Messenger().post({type: 'success', message: '数据保存成功!'});
                            $uibModalInstance.close();
                            $scope.search();
                        });
                    };
                }
            });
        };
        $scope.edit = function (alarm) {
            var addScope = $rootScope.$new();
            addScope.configs = angular.copy($scope.configs);
            addScope.search = $scope.search;
            addScope.alarmRuleTypes = $scope.alarmRuleTypes;
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'alarm/alarm/alarmMain/view/edit.html',
                scope: addScope,
                controller: function (rest, $scope, modal, $uibModalInstance) {
                    var defaultConfig = {"id": 0,"type":2, "name":"无","countType":1,"relatedPersons":"","ruleGroup":[{"rules":[{"type":0,"threshold":""}]}]};
                    $scope.countTypes = [{"code":1,"name":"分钟"},{"code":2,"name":"小时"},{"code":3,"name":"天"},{"code":4,"name":"周"}];

                    var timeRegExp = new RegExp("^([0-1][0-9]|2[0-3]):([0-5][0-9])-(([0-1][0-9]|2[0-3]):([0-5][0-9])|24:00)$");
                    var numberRegExp = new RegExp("^([1-9][0-9]*|0)$");
                    $scope.preConfig = null;
                    $scope.alarm = {};
                    $scope.alarm.config = {};
                    $scope.allAtomRules = [];
                    $scope.atomRuleList = [];
                    $scope.allEvents = [];
                    $scope.eventsList = [];

                    $scope.getChoiceAtomRuleType = function(){
                        if($scope.alarm.atomRules == null || $scope.alarm.atomRules.length == 0){
                            return 0;
                        }
                        for(var atomRule of $scope.alarm.atomRules){
                            if(atomRule.id == "0"){
                                return 1;
                            }
                        }
                        return 2;
                    }

                    $scope.getChoiceEventType = function(){
                        if($scope.alarm.events == null || $scope.alarm.events.length == 0){
                            return 0;
                        }
                        for(var event of $scope.alarm.events){
                            if(event.code == "ALL"){
                                return 1;
                            }
                        }
                        return 2;
                    }

                    $scope.getSelectAtomRules = function(){
                        var type = $scope.getChoiceAtomRuleType();
                        if(type == 1){
                            return [{'id': '0', 'name': '全部规则'}];
                        }
                        var tempList = angular.copy($scope.allAtomRules);
                        if(type == 2){
                            tempList.splice(0,1);
                        }
                        return tempList;
                    }

                    $scope.choiceAtomRule = function(atomRule){
                        $scope.atomRuleList = $scope.getSelectAtomRules();
                    }

                    $scope.getSelectEvents = function(){
                        var type = $scope.getChoiceEventType();
                        if(type == 1){
                            return [{'code': 'ALL', 'name': '全部事件'}];
                        }
                        var tempList = angular.copy($scope.allEvents);
                        if(type == 2){
                            tempList.splice(0,1);
                        }
                        return tempList;
                    }

                    $scope.choiceEvent = function(event){
                        $scope.eventsList = $scope.getSelectEvents();
                    }

                    $scope.loadAtomRules = function(){
                        Rest.all('rule/atom/all').post({type:$scope.alarm.ruleType == "0" ? null : $scope.alarm.ruleType}).then(function (response) {
                            $scope.allAtomRules = [{'id': '0', 'name': '全部规则'}];
                            if(response){
                                $scope.allAtomRules = $scope.allAtomRules.concat(response);
                            }
                            $scope.atomRuleList = angular.copy($scope.allAtomRules);
                            $scope.choiceAtomRule(null);
                        });
                    }

                    $scope.loadEvents = function(){
                        Rest.all('event/getEvents').post().then(function (result) {
                            var data = result.data;
                            $scope.allEvents = [{'code': 'ALL', 'name': '全部事件'}];
                            for (var key in data) {
                                $scope.allEvents.push({'code': key, 'name': data[key]});
                            }
                            $scope.eventsList = angular.copy($scope.allEvents);
                            $scope.choiceEvent(null);
                        });
                    }

                    $scope.loading =  Rest.all('alarmFuse/get/'+alarm.id).post({}).then(function (response) {
                            $scope.alarm = response;
                            $scope.alarm.ruleType = $scope.alarm.ruleType+"";
                            if($scope.alarm.events == null){
                                $scope.alarm.events = [{'code': 'ALL', 'name': '全部事件'}];
                            }else{
                                var events = [];
                                for(var event of $scope.alarm.events){
                                    events.push({'code': event.code, 'name': event.name});
                                }
                                $scope.alarm.events = events;
                            }
                            if($scope.alarm.atomRules == null){
                                $scope.alarm.atomRules = [{'id': '0', 'name': '全部规则'}];
                            }
                            if($scope.alarm.relatedTemplateCode == 2){
                                $scope.alarm.config.name = "无";
                                $scope.configs.splice(0,0,$scope.alarm.config);
                            }else{
                                $scope.preConfig = $scope.alarm.config;
                                $scope.configs.splice(0,0,defaultConfig);
                            }
                            $scope.loadAtomRules();
                            $scope.loadEvents();
                        });

                    $scope.changeRuleType = function(){
                        $scope.alarm.atomRules = null;
                        $scope.loadAtomRules();
                    }

                    $scope.refreshRules = function (name) {
                        $scope.atomRuleList = $filter('filter')($scope.getSelectAtomRules(),name);
                    };

                    $scope.refreshEvents = function (name) {
                        $scope.eventsList = $filter('filter')($scope.getSelectEvents(),name);
                    };

                    $scope.choiceConfig = function(config){
                        if(config.type == 1){
                            $scope.loading = Rest.all('alarm/config/get/'+config.id).post({}).then(function (response) {
                                $scope.alarm.config = response;
                                $scope.preConfig = response;
                            });
                        }else if(config.id == 0 && $scope.preConfig != null){
                            $scope.alarm.config = angular.copy($scope.preConfig);
                            $scope.alarm.config.id = 0;
                            $scope.alarm.config.name = "无";
                            $scope.alarm.config.type = 2;
                        }
                    }
                    $scope.addRuleGroup = function(){
                        $scope.alarm.config.ruleGroup.push({"rules":[{"type":0,"threshold":""}]});
                    }
                    $scope.removeRuleGroup = function(index){
                        if($scope.alarm.config.ruleGroup.length<=1){
                            Messenger().post({type: 'error', message: '规则组必须有1个!'});
                            return;
                        }
                        $scope.alarm.config.ruleGroup.splice(index, 1);
                    }
                    $scope.addRule = function(parentIndex){
                        $scope.alarm.config.ruleGroup[parentIndex].rules.push({"type":0,"threshold":""});
                    }
                    $scope.removeRule = function(parentIndex,index){
                        if($scope.alarm.config.ruleGroup[parentIndex].rules.length<=1){
                            Messenger().post({type: 'error', message: '规则必须有1个!'});
                            return;
                        }
                        $scope.alarm.config.ruleGroup[parentIndex].rules.splice(index, 1);
                    }
                    $scope.validType = function(rule,parentIndex,index){
                        var isTypeErr = false;
                        for(var j=0;j<$scope.alarm.config.ruleGroup[parentIndex].rules.length;j++){
                            var itemRule = $scope.alarm.config.ruleGroup[parentIndex].rules[j];
                            if(j != index){
                                if(itemRule.type != 0 && itemRule.type == rule.type){
                                    isTypeErr = true;
                                    break;
                                }
                            }
                        }
                        rule.isTypeErr = isTypeErr;
                        if(!isTypeErr){
                            if(rule.type == 5 && rule.threshold == ""){
                                rule.threshold = "00:00-24:00";
                                rule.isErr = false;
                                return;
                            }
                        }
                        $scope.validThreshold(rule);
                    }
                    $scope.validThreshold = function(rule){
                        if(rule.threshold == ""){
                            rule.isErr = false;
                            return;
                        }
                        if(rule.type == 5){
                            if(!timeRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }else{
                            if(!numberRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }
                    }
                    $scope.validAllThreshold = function(){
                        for(var i=0;i<$scope.alarm.config.ruleGroup.length;i++){
                            for(var j=0;j<$scope.alarm.config.ruleGroup[i].rules.length;j++){
                                var rule = $scope.alarm.config.ruleGroup[i].rules[j];
                                if(rule.isErr || rule.isTypeErr || rule.type==0 || rule.threshold == ""){
                                    return false;
                                }
                            }
                        }
                        return true;
                    }
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        if(!$scope.validAllThreshold()){
                            return;
                        }
                        if($scope.alarm.events == null || $scope.alarm.events.length == 0){
                            return;
                        }
                        if($scope.alarm.type == 1){
                            $scope.alarm.ruleIds = null;
                        }else if($scope.alarm.type == 2){
                            if($scope.alarm.atomRules == null || $scope.alarm.atomRules.length == 0){
                                return;
                            }
                            var atomRuleIds = [];
                            for(var atomRule of $scope.alarm.atomRules){
                                atomRuleIds.push(atomRule.id);
                            }
                            $scope.alarm.ruleIds = atomRuleIds.join(",");
                        }
                        var eventCodes = [];
                        for(var event of $scope.alarm.events){
                            eventCodes.push(event.code);
                        }
                        $scope.alarm.eventCodes = eventCodes.join(",");
                        $scope.alarm.configId = $scope.alarm.config.id;
                        $scope.loading = Rest.all('alarm/update').post($scope.alarm).then(function () {
                            Messenger().post({type: 'success', message: '数据更新成功!'});
                            $uibModalInstance.close();
                            $scope.search();
                        });
                    };
                }
            });
        };
    });
})();
