<div class="box box-primary" cg-busy="loading">
    <div class="box-search">
        <form>
            <div class="form-group col-sm-2">
                <input ng-model="query.name" type="text" class="form-control input-md" placeholder="标签名称">
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.type" class="form-control input-md">
                    <option value="" selected="selected">--标签类型--</option>
                    <option value="1">事件告警</option>
                    <option value="2">规则告警</option>
                </select>
            </div>
            <div class="form-group col-sm-2" >
                <ui-select class="input-md" ng-model="query.event" theme="bootstrap" >
                    <ui-select-match placeholder="选择事件" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="eventObj in eventsList| filter: $select.search" refresh-delay="300">
                        <div uib-tooltip="{{eventObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="eventObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.ruleType" class="form-control input-md">
                    <option value="" selected="selected">--规则类型--</option>
                    <option value="0" >全类型</option>
                    <option value="1" >反作弊</option>
                    <option value="2" >反垃圾</option>
                </select>
            </div>
            <div class="form-group col-sm-2" >
                <ui-select class="input-md" ng-model="query.rule" theme="bootstrap" >
                    <ui-select-match placeholder="选择规则" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="ruleObj in atomRuleList| filter: $select.search" refresh-delay="300">
                        <div uib-tooltip="{{ruleObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="ruleObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.riskResult" class="form-control input-md">
                    <option value="" selected="selected">--风控结果--</option>
                    <option value="All" >全部</option>
                    <option value="REJECT" >拒绝</option>
                    <option value="REVIEW" >审核</option>
                    <option value="PASS" >通过</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.state" class="form-control input-md" ng-init="state = '1'">
                    <option value="" >--状态--</option>
                    <option value="1" >启用</option>
                    <option value="2" >停用</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.config.countType" class="form-control input-md">
                    <option value="" selected="selected">--时间窗口--</option>
                    <option value="1">分钟</option>
                    <option value="2">小时</option>
                    <option value="3">天</option>
                    <option value="4">周</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <button ng-click="search()" class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-search"></i> 搜索
                </button>
                <button ng-click="add()" class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-plus"></i>新增
                </button>
            </div>
        </form>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>标签名称</th>
                <th>标签类型</th>
                <th style="width: 200px;">风控事件</th>
                <th>规则类型</th>
                <th style="width: 220px;">风控规则</th>
                <th>风控结果</th>
                <th>状态</th>
                <th>统计时间窗口</th>
                <th>最后修改人</th>
                <th>修改时间</th>
                <th style="width:120px;">操作列</th>
            </tr>
            <tr ng-repeat="a in alarms">
                <td>{{a.name}}</td>
                <td>{{a.typeName}}</td>
                <td >
                    <div class="box box-widget widget-user-2" ng-if="a.events != null && a.events.length > 0" style="margin-bottom: 0">
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" >
                                <li ng-repeat="event in a.events" style="height: 48px" >
                                    <a href="javascript:void(0)" style="padding:15px 15px;">
                                        <span>{{event.name}}</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="box box-widget widget-user-2" ng-if="a.events == null || a.events.length == 0" style="margin-bottom: 0">
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" >
                                <li style="height: 48px" >
                                    <a href="javascript:void(0)" style="padding:15px 15px;">
                                        <span>全部事件</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td>{{a.type==1 ? "" : a.ruleTypeName}}</td>
                <td>
                    <div class="box box-widget widget-user-2" ng-if="a.type == 2 && a.atomRules != null && a.atomRules.length > 0" style="margin-bottom: 0" >
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked" >
                                <li ng-repeat="rule in a.atomRules" style="height: 48px" >
                                    <a href="javascript:void(0)" style="padding:15px 15px;">
                                        <span>{{rule.name}}</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="box box-widget widget-user-2" ng-if="a.type == 2 && (a.atomRules == null || a.atomRules == 0)" style="margin-bottom: 0" >
                        <div class="box-footer no-padding" style="padding:0">
                            <ul class="nav nav-stacked"  >
                                <li style="height: 48px" >
                                    <a href="javascript:void(0)" style="padding:15px 15px;">
                                        <span>全部规则</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
                <td>{{a.riskResult == "ALL" ? "全部" : (a.riskResult == "PASS" ? "通过" : (a.riskResult == "REVIEW" ? "审核" : "拒绝"))}}</td>
                <td>{{a.state == 1 ? "启用" : "停用"}}</td>
                <td>{{a.config.countTypeName}}</td>
                <td>{{a.modifier}}</td>
                <td><span>{{a.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</span></td>
                <td>
                    <button uib-tooltip="编辑" class="btn btn-flat btn-md btn-info" ng-click="edit(a)">
                        <i class="fa fa-pencil fa-fw"></i>
                    </button>
                    <button uib-tooltip="停用" ng-if = "a.state == 1" class="btn btn-flat btn-md btn-warning" ng-click="upStatus(2,a)">
                        <i class="fa fa-pause fa-fw"></i>
                    </button>
                    <button uib-tooltip="启用" ng-if = "a.state == 2" class="btn btn-flat btn-md btn-warning" ng-click="upStatus(1,a)">
                        <i class="fa fa-forward fa-fw"></i>
                    </button>
                    <button uib-tooltip="删除" aria-hidden="true" class="btn btn-flat btn-md btn-danger" ng-click="upStatus(3,a)">
                        <i class="fa fa-remove fa-fw"/>
                    </button>
                    <button uib-tooltip="生成模板" class="btn btn-flat btn-md btn-info" ng-click="generateTemplate(a)">
                        <i class="fa fa-copy fa-fw"></i>
                    </button>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
