<form class="form-horizontal w5c-form" name="editForm" novalidate w5c-form-validate="">
    <div cg-busy="loading">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click="close();"><span
                    aria-hidden="true">&times;</span></button>
            <h3 class="modal-title">编辑告警模板</h3>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label class="col-sm-3 control-label">模板名称*:</label>
                <div class="col-sm-8">
                    <input name="name" ng-model="config.name" type="text" class="form-control input-md"
                           autocomplete="off" ng-required="true"
                           placeholder="模板名称" ng-maxlength="128">
                    <span class="required" ng-show="editForm.name.$dirty && editForm.name.$invalid">
                        <span ng-show="editForm.name.$error.maxlength">最长为128位</span>
                    </span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">统计时间窗口*:</label>
                <div class="col-sm-8">
                    <select ng-model="config.countType" class="form-control input-md">
                        <option value="{{c.code}}" ng-repeat="c in countTypes" ng-value="{{c.code}}">{{c.name}}</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">告警钉钉群:</label>
                <div class="col-sm-8">
                    <input name="ddGroup" ng-model="config.ddGroup" type="text" class="form-control input-md"
                           autocomplete="off" placeholder="告警钉钉群" ng-maxlength="128">
                    <span class="required" ng-show="editForm.ddGroup.$dirty && editForm.ddGroup.$invalid">
                        <span ng-show="editForm.ddGroup.$error.maxlength">最长为128位</span>
                    </span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">告警关联人*:</label>
                <div class="col-sm-8">
                    <input name="relatedPersons" ng-model="config.relatedPersons" type="text" class="form-control input-md"
                           autocomplete="off" required placeholder="告警关联人，多个用户通过英文;分隔" ng-required="true" ng-maxlength="128">
                    <span class="required" ng-show="editForm.relatedPersons.$dirty && editForm.relatedPersons.$invalid">
                        <span ng-show="editForm.relatedPersons.$error.maxlength">最长为128位</span>
                    </span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注:</label>
                <div class="col-sm-8">
                    <textarea name="memo" ng-model="config.memo" type="text" class="form-control input-md"
                              placeholder="备注" autocomplete="off" rows="5" ng-maxlength="512"/>
                    <span class="required" ng-show="editForm.memo.$dirty && editForm.memo.$invalid">
                        <span ng-show="editForm.memo.$error.maxlength">最长为512位</span>
                    </span>
                </div>
            </div>
            <div class="box box-default" style="border-radius: 0px;border-top:2px solid #d2d6de">
                <div class="box-header with-border">
                    <h3 class="box-title" style="font-size: 14px;font-weight: 700">告警规则</h3>
                </div>
                <div class="box-body">
                    <div ng-repeat="group in config.ruleGroup">
                        <div class="container-fluid" style="background-color: #f7f7f9;">
                            <div style="border-bottom:solid 1px #d2d6de;" class="row">
                                <label class="control-label" style="text-align: left;padding:7px 0px 7px 10px;">规则组{{$index+1}}</label>
                                <div class="pull-right" style="padding:2px 10px 2px 0px;">
                                    <button type="button" class="btn btn-box-tool" ng-click="addRuleGroup()"><i class="fa fa-plus"></i></button>
                                    <button type="button" class="btn btn-box-tool" ng-click="removeRuleGroup($index)"><i class="fa fa-minus"></i></button>
                                </div>
                            </div>
                            <div class="form-group" style="margin-top:10px;margin-bottom:10px;" ng-repeat="rule in group.rules">
                                <label class="col-sm-2 control-label">规则类型:</label>
                                <div class="col-sm-4" ng-class="{ 'has-error' : editForm.$submitted && (rule.isTypeErr || rule.type == 0)}" style="width:36%;">
                                    <select ng-model="$parent.config.ruleGroup[$parent.$index].rules[$index].type" class="form-control input-md" ng-change="validType(rule,$parent.$index,$index)">
                                        <option value="{{r.code}}" ng-repeat="r in ruleTypes" ng-value="{{r.code}}">{{r.name}}</option>
                                    </select>
                                    <span class="required" ng-show="rule.isTypeErr">不能选择重复类型</span>
                                    <span class="required" ng-show="editForm.$submitted && rule.type == 0">该选项不能为空</span>
                                </div>
                                <label class="col-sm-2 control-label" style="width: 10%;">阈值:</label>
                                <div class="col-sm-3"  ng-class="{ 'has-error' : editForm.$submitted && (rule.isErr || rule.threshold == '')}">
                                    <input type="text" class="form-control input-md"
                                           autocomplete="off" placeholder="阈值" ng-model="$parent.config.ruleGroup[$parent.$index].rules[$index].threshold" ng-value="rule.threshold" ng-keyup="validThreshold(rule)"/>
                                    <span class="required" ng-show="rule.isErr">格式不正确</span>
                                    <span class="required" ng-show="editForm.$submitted && rule.threshold == ''">该选项不能为空</span>
                                </div>
                                <div style="float: left!important">
                                    <button type="button" class="btn btn-box-tool" ng-click="addRule($parent.$index)"><i class="fa fa-plus"></i></button>
                                    <button type="button" class="btn btn-box-tool" ng-click="removeRule($parent.$index,$index)"><i class="fa fa-minus"></i></button>
                                </div>
                            </div>
                        </div>
                        <hr ng-if="$index < (config.ruleGroup.length-1)" style="margin-top:10px;margin-bottom:10px;"/>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <div class="col-sm-offset-3 col-sm-9">
                <button type="reset" class="btn btn-default btn-md" ng-click="close();">取消</button>
                <button type="submit" class="btn btn-success" w5c-form-submit="confirm();">
                    <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> 确认
                </button>
            </div>
        </div>
    </div>
</form>
