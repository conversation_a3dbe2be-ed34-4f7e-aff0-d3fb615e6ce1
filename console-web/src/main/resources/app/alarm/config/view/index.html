<div class="box box-primary" cg-busy="loading">
    <div class="box-header with-border">
        <h3 class="box-title"><i class="fa fa-copy"/> 告警模板</h3>
    </div>
    <div class="box-search">
        <form>
            <div class="form-group col-sm-3">
                <input ng-model="name" type="text" class="form-control input-md" placeholder="模板名称">
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="countType" class="form-control input-md">
                    <option value="" selected="selected">--统计时间--</option>
                    <option value="1">分钟</option>
                    <option value="2">小时</option>
                    <option value="3">天</option>
                    <option value="4">周</option>
                </select>
            </div>
            <button ng-click="search()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-search"></i> 搜索
            </button>
            <button ng-click="add()" class="btn btn-flat btn-md btn-info">
                <i class="fa fa-plus"></i>新增
            </button>
        </form>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>模板名称</th>
                <th>统计时间窗口</th>
                <th>最后修改人</th>
                <th>修改时间</th>
                <th>操作列</th>
            </tr>
            <tr ng-repeat="c in configs">
                <td>{{c.name}}</td>
                <td>{{c.countType == 1 ? '分钟' : (c.countType == 2 ? '小时':(c.countType == 3 ? '天':'周'))}}</td>
                <td>{{c.modifier}}</td>
                <td>{{c.updateTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>
                    <button uib-tooltip="编辑" class="btn btn-flat btn-md btn-info" ng-click="edit(c)">
                        <i class="fa fa-pencil fa-fw"></i>
                    </button>
                    <button uib-tooltip="删除" aria-hidden="true" class="btn btn-flat btn-md btn-danger" ng-click="delete(c)">
                        <i class="fa fa-remove fa-fw"/>
                    </button>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
