(function () {
    var app = angular.module(app_name);

    app.controller('alarmConfigCtrl', function ($scope, $state, $stateParams, $uibModal, Rest, modal, $aside, AuthService, $sce) {
        $scope.page = 1;

        $scope.search = function(){
            $scope.loading = Rest.all('alarm/config/search?p=' + $scope.page).post({"name":$scope.name,"countType":$scope.countType,"state":1,"type":1}).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.configs = response.list;
                $scope.total = response.total;
            });
        }
        $scope.search();
        $scope.pageChanged = function () {
            $scope.search();
        };
        $scope.delete = function(config){
            modal.confirm('确定删除 [ ' + config.name + ' ] ?', function () {
                $scope.loading = Rest.all('alarm/config/delete/'+config.id).post({}).then(function (response) {
                    $scope.search();
                });
            });
        }
        $scope.add = function(){
            $scope.addWithCallBack(function (){
                $scope.search();
            });
        }
        $scope.addWithCallBack = function (callback) {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'alarm/config/view/add.html',
                controller: function (rest, $scope, modal, $uibModalInstance) {
                    $scope.config = {"countType":1};
                    $scope.config.ruleGroup = [{"rules":[{"type":0,"threshold":""}]}];
                    var timeRegExp = new RegExp("^([0-1][0-9]|2[0-3]):([0-5][0-9])-(([0-1][0-9]|2[0-3]):([0-5][0-9])|24:00)$");
                    var numberRegExp = new RegExp("^([1-9][0-9]*|0)$");
                    $scope.addRuleGroup = function(){
                        $scope.config.ruleGroup.push({"rules":[{"type":0,"threshold":""}]});
                    }
                    $scope.removeRuleGroup = function(index){
                        if($scope.config.ruleGroup.length<=1){
                            Messenger().post({type: 'error', message: '规则组必须有1个!'});
                            return;
                        }
                        $scope.config.ruleGroup.splice(index, 1);
                    }
                    $scope.addRule = function(parentIndex){
                        $scope.config.ruleGroup[parentIndex].rules.push({"type":0,"threshold":""});
                    }
                    $scope.removeRule = function(parentIndex,index){
                        if($scope.config.ruleGroup[parentIndex].rules.length<=1){
                            Messenger().post({type: 'error', message: '规则必须有1个!'});
                            return;
                        }
                        $scope.config.ruleGroup[parentIndex].rules.splice(index, 1);
                    }
                    $scope.validType = function(rule,parentIndex,index){
                        var isTypeErr = false;
                        for(var j=0;j<$scope.config.ruleGroup[parentIndex].rules.length;j++){
                            var itemRule = $scope.config.ruleGroup[parentIndex].rules[j];
                            if(j != index){
                                if(itemRule.type != 0 && itemRule.type == rule.type){
                                    isTypeErr = true;
                                    break;
                                }
                            }
                        }
                        rule.isTypeErr = isTypeErr;
                        if(!isTypeErr){
                            if(rule.type == 5 && rule.threshold == ""){
                                rule.threshold = "00:00-24:00";
                                rule.isErr = false;
                                return;
                            }
                        }
                        $scope.validThreshold(rule);
                    }
                    $scope.validThreshold = function(rule){
                        if(rule.threshold == ""){
                            rule.isErr = false;
                            return;
                        }
                        if(rule.type == 5){
                            if(!timeRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }else{
                            if(!numberRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }
                    }
                    $scope.validAllThreshold = function(){
                        for(var i=0;i<$scope.config.ruleGroup.length;i++){
                            for(var j=0;j<$scope.config.ruleGroup[i].rules.length;j++){
                                var rule = $scope.config.ruleGroup[i].rules[j];
                                if(rule.isErr || rule.isTypeErr || rule.type==0 || rule.threshold == ""){
                                    return false;
                                }
                            }
                        }
                        return true;
                    }
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        if(!$scope.validAllThreshold()){
                            return;
                        }
                        $scope.loading = Rest.all('alarm/config/add').post($scope.config).then(function () {
                            Messenger().post({type: 'success', message: '数据保存成功!'});
                            $uibModalInstance.close();
                            callback();
                        });
                    };
                }
            });
        };
        $scope.edit = function(config){
            $scope.editWithCallBack(config,function () {
                $scope.search();
            })
        }
        $scope.editWithCallBack = function (config,callback) {
            $uibModal.open({
                backdrop: 'static',
                templateUrl: 'alarm/config/view/edit.html',
                controller: function (rest, $scope, modal, $uibModalInstance) {
                    $scope.countTypes = [{"code":"2","name":"小时"},{"code":"3","name":"天"},{"code":"4","name":"周"}];
                    $scope.ruleTypes = [{"code":"0","name":"--请选择类型--"},{"code":"5","name":"告警时间范围"},{"code":"1","name":"最近N分钟总和>="},{"code":"2","name":"最近N分钟总和<="},{"code":"3","name":"最近N分钟总量上升%>="},{"code":"4","name":"最近N分钟总量下降%>="}];
                    var timeRegExp = new RegExp("^([0-1][0-9]|2[0-3]):([0-5][0-9])-(([0-1][0-9]|2[0-3]):([0-5][0-9])|24:00)$");
                    var numberRegExp = new RegExp("^([1-9][0-9]*|0)$");
                    $scope.config = {};
                    $scope.loading = Rest.all('alarm/config/get/'+config.id).post({}).then(function (response) {
                        $scope.config = response;
                    });
                    $scope.addRuleGroup = function(){
                        $scope.config.ruleGroup.push({"rules":[{"type":0,"threshold":""}]});
                    }
                    $scope.removeRuleGroup = function(index){
                        if($scope.config.ruleGroup.length<=1){
                            Messenger().post({type: 'error', message: '规则组必须有1个!'});
                            return;
                        }
                        $scope.config.ruleGroup.splice(index, 1);
                    }
                    $scope.addRule = function(parentIndex){
                        $scope.config.ruleGroup[parentIndex].rules.push({"type":0,"threshold":""});
                    }
                    $scope.removeRule = function(parentIndex,index){
                        if($scope.config.ruleGroup[parentIndex].rules.length<=1){
                            Messenger().post({type: 'error', message: '规则必须有1个!'});
                            return;
                        }
                        $scope.config.ruleGroup[parentIndex].rules.splice(index, 1);
                    }
                    $scope.validType = function(rule,parentIndex,index){
                        var isTypeErr = false;
                        for(var j=0;j<$scope.config.ruleGroup[parentIndex].rules.length;j++){
                            var itemRule = $scope.config.ruleGroup[parentIndex].rules[j];
                            if(j != index){
                                if(itemRule.type != 0 && itemRule.type == rule.type){
                                    isTypeErr = true;
                                    break;
                                }
                            }
                        }
                        rule.isTypeErr = isTypeErr;
                        if(!isTypeErr){
                            if(rule.type == 5 && rule.threshold == ""){
                                rule.threshold = "00:00-24:00";
                                rule.isErr = false;
                                return;
                            }
                        }
                        $scope.validThreshold(rule);
                    }
                    $scope.validThreshold = function(rule){
                        if(rule.threshold == ""){
                            rule.isErr = false;
                            return;
                        }
                        if(rule.type == 5){
                            if(!timeRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }else{
                            if(!numberRegExp.test(rule.threshold)){
                                rule.isErr = true;
                            }else{
                                rule.isErr = false;
                            }
                        }
                    }
                    $scope.validAllThreshold = function(){
                        for(var i=0;i<$scope.config.ruleGroup.length;i++){
                            for(var j=0;j<$scope.config.ruleGroup[i].rules.length;j++){
                                var rule = $scope.config.ruleGroup[i].rules[j];
                                if(rule.isErr || rule.isTypeErr || rule.type==0  || rule.threshold == ""){
                                    return false;
                                }
                            }
                        }
                        return true;
                    }
                    $scope.close = function () {
                        $uibModalInstance.close();
                    };
                    $scope.confirm = function () {
                        if(!$scope.validAllThreshold()){
                            return;
                        }
                        $scope.loading = Rest.all('alarm/config/update/'+config.id).post($scope.config).then(function () {
                            Messenger().post({type: 'success', message: '数据保存成功!'});
                            $uibModalInstance.close();
                            callback();
                        });
                    };
                }
            });
        };
    });
})();
