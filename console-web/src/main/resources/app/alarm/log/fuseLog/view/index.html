<div class="box box-primary" cg-busy="loading">
    <div class="box-search">
        <form>
            <div class="form-group col-sm-2">
                <input type="text" name="startTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="query.startTime" ng-click="popup1.opened=true"
                       is-open="popup1.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group col-sm-2">
                <input type="text" name="endTime" class="form-control" uib-datepicker-popup="yyyy-MM-dd HH:mm"
                       ng-model="query.endTime" ng-click="popup2.opened=true"
                       is-open="popup2.opened" datepicker-options="dateOptions"
                       close-text="Close" showError="时间格式错误"/>
            </div>
            <div class="form-group col-sm-2">
                <select ng-model="query.ruleType" class="form-control input-md">
                    <option value="" selected="selected">--规则类型--</option>
                    <option value="0" >全类型</option>
                    <option value="1" >反作弊</option>
                    <option value="2" >反垃圾</option>
                </select>
            </div>
            <div class="form-group col-sm-2">
                <ui-select class="input-md" ng-model="query.rule" theme="bootstrap">
                    <ui-select-match placeholder="选择规则" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="ruleObj in ruleList| filter: $select.search" refresh-delay="300" style="width: 300px;">
                        <div uib-tooltip="{{ruleObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="ruleObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="form-group col-sm-2">
                <ui-select class="input-md" ng-model="query.event" theme="bootstrap">
                    <ui-select-match placeholder="选择事件" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="eventObj in eventsList| filter: $select.search" refresh-delay="300" style="width: 300px;">
                        <div uib-tooltip="{{eventObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:350px;"  ng-bind-html="eventObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="form-group col-sm-2">
                <ui-select class="input-md" ng-model="query.alarm" theme="bootstrap">
                    <ui-select-match placeholder="选择标签" allow-clear="true">{{$select.selected.name}}</ui-select-match>
                    <ui-select-choices repeat="alarmObj in alarmList| filter: $select.search" refresh-delay="300" style="width: 400px;">
                        <div uib-tooltip="{{alarmObj.name}}" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;width:400px;"  ng-bind-html="alarmObj.name"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
            <div class="col-sm-12">
                <div class="form-group col-sm-2">
                    <select ng-model="query.handleState" class="form-control input-md">
                        <option value="" selected="selected">--处理状态--</option>
                        <option value="1" >未处理</option>
                        <option value="2" >处理中</option>
                        <option value="3" >已处理</option>
                    </select>
                </div>
                <div class="form-group col-sm-2">
                    <select ng-model="query.fuseState" class="form-control input-md">
                        <option value="" selected="selected">--熔断状态--</option>
                        <option value="1" >待熔断</option>
                        <option value="2" >已熔断</option>
                        <option value="3" >不熔断</option>
                    </select>
                </div>
                <button ng-click="search()" class="btn btn-flat btn-md btn-info">
                    <i class="fa fa-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>
    <div class="box-body">
        <table class="table table-bordered">
            <tr>
                <th>告警时间</th>
                <th>标签名称</th>
                <th>标签类型</th>
                <th>规则类型</th>
                <th>风控规则</th>
                <th>风控事件</th>
                <th>风控结果</th>
                <th>统计时间窗口</th>
                <th>处理状态</th>
                <th>熔断状态</th>
                <th style="width: 300px;">命中规则组内容</th>
                <th>操作</th>
            </tr>
            <tr ng-repeat="log in logs">
                <td>{{log.createTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{log.alarmName}}</td>
                <td>{{log.alarmType}}</td>
                <td>{{log.ruleTypeName}}</td>
                <td>{{log.ruleName}}</td>
                <td>{{log.eventName}}</td>
                <td>{{log.riskResult == "ALL" ? "全部" : (log.riskResult == "PASS" ? "通过" : (log.riskResult == "REVIEW" ? "审核" : "拒绝"))}}</td>
                <td>{{log.countType == 1 ? "分钟":(log.countType == 2 ? "小时" : (log.countType == 3 ? "天" : "周"))}}</td>
                <td>{{log.handleStateName}}</td>
                <td>{{log.fuseStateName}}</td>
                <td>{{log.content}}</td>
                <td>
                    <button ng-if="log.handleState == 1" uib-tooltip="开始处理" class="btn btn-flat btn-md btn-danger" ng-click="startProcessing(log)">
                        开始处理
                    </button>
                    <button ng-if="log.handleState == 2" uib-tooltip="完成处理" class="btn btn-flat btn-md btn-danger" ng-click="completeProcessing(log)">
                        完成处理
                    </button>
                    <button ng-if="log.handleState == 3" uib-tooltip="处理结果" class="btn btn-flat btn-md btn-info" ng-click="handleInfo(log)">
                        处理结果
                    </button>
                </td>
            </tr>
        </table>
    </div>
    <div class="box-footer">
        <ul uib-pagination class="pagination-sm no-margin pull-right" boundary-link-numbers="true"
            first-text="首页"
            last-text="尾页"
            previous-text="前一页"
            next-text="后一页"
            total-items="total"
            ng-model="page"
            ng-change="pageChanged()"
            items-per-page="size"
            max-size="10"
            rotate="false">
        </ul>
    </div>
</div>
