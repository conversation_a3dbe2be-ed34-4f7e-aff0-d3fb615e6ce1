(function () {
    var app = angular.module(app_name);

    app.controller('alarmLogCtrl', function ($scope, $state, $stateParams, $uibModal, Rest, modal, $aside, AuthService, $sce) {
        $scope.page = 1;
        $scope.eventsList = [];
        $scope.ruleList = [];
        $scope.alarmList = [];
        $scope.query = {};
        var endTime = new Date();
        endTime.setHours(23);
        endTime.setMinutes(59);
        endTime.setSeconds(59);
        endTime.setMilliseconds(999);
        var startTime = new Date(endTime.getTime() - 1000 * 60 * 60 * 24 * 7 + 1);
        $scope.query.startTime = startTime;
        $scope.query.endTime = endTime;
        $scope.loading = Rest.all('event/getEvents').post().then(function (result) {
            var data = result.data;
            $scope.eventsList.push({'code': 'ALL', 'name': '全部事件'});
            for (var key in data) {
                $scope.eventsList.push({'code': key, 'name': data[key]});
            }
        });
        $scope.loading = Rest.all('/rule/atom/all').post({status:'ENABLE'}).then(function(response){
            if(response){
               $scope.ruleList.push({'id': '0', 'name': '全部规则'});
              for(var key in response){
                $scope.ruleList.push({'id':response[key].id,'name':response[key].name});
              }
            }
        });
        $scope.loading = Rest.all('alarm/getAlarms').post({"actionType":1}).then(function (result) {
            $scope.alarmList = result;
        });
        $scope.search = function(){
            var timeSpan = ($scope.query.endTime.getTime() - $scope.query.startTime.getTime()) / 1000 / 60 / 60 / 24; // 时间跨度-天
            if (timeSpan > 30) {
                modal.alert("时间跨度不允许超过30天");
            }

            $scope.query.eventCode = $scope.query.event?$scope.query.event.code:null;
            $scope.query.ruleId = $scope.query.rule?$scope.query.rule.id:null;
            $scope.query.alarmId = $scope.query.alarm?$scope.query.alarm.id:null;
            $scope.query.actionType = 1;

            $scope.loading = Rest.all('alarm/log/search?p=' + $scope.page).post($scope.query).then(function (response) {
                Messenger().post({type: 'success', message: '数据加载完成!'});
                $scope.logs = response.list;
                $scope.total = response.total;
            });
        }
        $scope.search();
        $scope.pageChanged = function () {
            $scope.search();
        };
    });
})();
