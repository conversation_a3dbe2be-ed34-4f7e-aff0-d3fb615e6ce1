(function () {
    var app = angular.module(app_name);

    app.config(function ($stateProvider, $urlRouterProvider) {
        $urlRouterProvider.when('/alarmLogTab', '/alarmLogTab');
        $stateProvider.state('alarmLogTab', {
            parent: 'home',
            url: '/alarmLogTab',
            templateUrl: 'alarm/log/index.html'
        });
        $stateProvider.state('alarmLogTab.alarmLog', {
            url: '/alarmLog',
            templateUrl: 'alarm/log/alarmLog/view/index.html',
            controller: 'alarmLogCtrl'
        });
        $stateProvider.state('alarmLogTab.fuseLog', {
            url: '/fuseLog?id',
            templateUrl: 'alarm/log/fuseLog/view/index.html',
            controller: 'fuseLogCtrl'
        });
    });

})();
