package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.model.HotProhibitedWord;
import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.vo.HotProhibitedWordVO;
import com.yupaopao.risk.console.service.HotProhibitedWordService;
import com.yupaopao.risk.console.service.SceneService;
import com.yupaopao.risk.console.service.TagService;
import com.yupaopao.risk.console.service.WordService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prohibitedWord")
@AuthRequired(permissionName = "content:hotProhibitedWord:all")
@Slf4j
public class HotProhibitedWordController extends AbstractModelController<HotProhibitedWord,HotProhibitedWord, HotProhibitedWordService>{

    @Autowired
    private WordService wordService;

    @Autowired
    private TagService tagService;

    @Autowired
    private SceneService sceneService;

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控违禁词查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping("complexSearch")
    public JsonResult complexSearch(@RequestBody HotProhibitedWordVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size){
        return JsonResult.success(this.getService().complexSearch(record, page, size));
    }

    @OpRecordReport(opType = OpTypeEnum.UPDATE, opDesc = "风控热点违禁词批量更新",isReportResponse = false)
    @RequestMapping("batchUpdate")
    public JsonResult batchUpdate(@RequestBody List<HotProhibitedWord> words){
        return JsonResult.success(this.getService().updateList(words));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控标签查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping("selectTag")
    public JsonResult selectTag(@RequestBody Tag tag, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size){
        return JsonResult.success( this.tagService.showInSel(tag,page,size));
    }

    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping("/selectScene")
    public JsonResult show(Scene scene) {
        scene.setToSystem(ToSystemType.CREDIT.getCode());

        return JsonResult.success(this.sceneService.search(scene));
    }

    @Override
    @OpRecordReport(opType = OpTypeEnum.UPDATE, opDesc = "风控热点违禁词更新",isReportResponse = false)
    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody HotProhibitedWord record, @CurrentUser User user){
        record.setUpdateTime(new Date());
        return JsonResult.success(this.getService().update(record));
    }

    @AuthRequired(permissionName = "content:hotProhibitedWord:export")
    @RequestMapping("export")
    public JsonResult export(@CurrentUser User user, @RequestBody HotProhibitedWordVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        try {
            //最多导出一万行
            page = 1;
            size = 10000;
            PageInfo pageInfo = this.getService().complexSearch(record, page, size);
            Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                    ExcelFormatter.builder("seq").addName("top排名").build(),
                    ExcelFormatter.builder("times").addName("总数").build(),
                    ExcelFormatter.builder("content").addName("违禁内容").build(),
                    ExcelFormatter.builder("eventCode").addName("事件").build()
            );
            List<Map<String,Object>> list = Lists.newArrayList();
            if((pageInfo!=null)&&CollectionUtils.isNotEmpty(pageInfo.getList())){
                for(Object hotProhibitedWord:pageInfo.getList()){
                    JSONObject jsonObject = (JSONObject)JSONObject.toJSON(hotProhibitedWord);
                    Map<String,Object> map = JSONObject.toJavaObject(jsonObject,Map.class);
                    list.add(map);
                }
            }

            String fileName = ExcelUtils.exportExcelByMap(list,"违禁词热点排行榜",formatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            log.error("导出违禁词热点排行榜Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }

    /**
     * 填充检测渠道下拉框
     */
    @RequestMapping("/getDetectChannelList")
    public JsonResult getDetectChannelList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(DetectChannel.class).forEach(channel -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(channel.getCode()));
            temp.put("msg", String.valueOf(channel.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    /**
     * 填充词条类型下拉框
     */
    @RequestMapping("/getWordTypeList")
    public JsonResult getWordTypeList(){
        List<Map<String,String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(WordType.class).forEach(type ->{
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(type.getCode()));
            temp.put("msg", String.valueOf(type.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

}
