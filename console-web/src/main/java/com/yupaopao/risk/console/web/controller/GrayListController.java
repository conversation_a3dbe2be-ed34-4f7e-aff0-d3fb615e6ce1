package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.Dimensions;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.BlackListService;
import com.yupaopao.risk.console.service.GrayGroupService;
import com.yupaopao.risk.console.service.GrayListService;
import com.yupaopao.risk.console.service.impl.ConfigService;
import com.yupaopao.risk.console.utils.CheckIdCard;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/gray/list")
@AuthRequired(permissionName = "core:graylist:all")
@Slf4j
public class GrayListController extends AbstractModelController<GrayList, GrayList, GrayListService> {

    @Autowired
    protected ConfigService configService;
    @Autowired
    protected GrayListService grayListService;
    @Autowired
    protected GrayGroupService grayGroupService;
    @Autowired
    private ApolloOpenApiService apolloOpenApiService;
    @Autowired
    private BlackListService blackListService;


    @Override
    protected void addValid(GrayList record, User user) {
        Assert.notNull(record.getType(), "请指定名单类型");
        Assert.notNull(record.getDimension(), "请指定名单维度");
        Assert.notNull(record.getValue(), "请指定名单数据");
        Assert.notNull(record.getGroupId(), "请指定名单组");
        if (record.getStartTime() == null) {
            record.setStartTime(new Date());
        }
        if (record.getExpireTime() == null) {
            record.setExpireTime(new Date(record.getStartTime().getTime() + configService.getDefaultExpiredTime() * 1000));
        } else {
            Assert.isTrue(record.getStartTime().before(record.getExpireTime()), "过期时间必须大于生效时间");
            Assert.isTrue(record.getExpireTime().after(new Date()), "过期时间必须大于当前时间");
        }
        record.setAuthor(user.getName());
    }

    @Override
    protected void updateValid(GrayList record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getExpireTime(), "请指定名单过期时间");
        Assert.isTrue(record.getExpireTime().after(new Date()), "过期时间必须大于当前时间");
        record.setAuthor(null);
        record.setStartTime(null);
        record.setType(null);
        record.setValue(null);
        record.setDimension(null);
        record.setGroupId(null);
        record.setModifier(user.getName());
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody GrayList record, @CurrentUser User user) {
        record.setModifier(user.getName());
        addValid(record, user);
        return JsonResult.success(super.getService().save(record));
    }

    @RequestMapping("/getDefaultExpireTime")
    public JsonResult getDefaultExpireTime() {
        return JsonResult.success(configService.getDefaultExpiredTime());
    }

    @RequestMapping("/deleteExpired")
    public JsonResult deleteExpired() {
        return JsonResult.success(getService().deleteExpired());
    }

    @RequestMapping("/export")
    @AuthRequired(permissionName = "core:graylist:export")
    public JsonResult export(@RequestBody GrayList record) {
        //本导出最多只能导出100000条
        PageInfo<GrayList> pageInfo = grayListService.search(record, 1, 100000);
        List<GrayGroup> grayGroupList = grayGroupService.selectAll();
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("type").addName("类型").build(),
                ExcelFormatter.builder("groupId").addName("名单").addConverter(grayGroupList.stream().collect(Collectors.toMap(GrayGroup::getId, GrayGroup::getName))).build(),
                ExcelFormatter.builder("dimension").addName("维度").build(),
                ExcelFormatter.builder("value").addName("数据").addWidth(80).build(),
                ExcelFormatter.builder("startTime").addName("生效时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("expireTime").addName("过期时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("comment").addName("备注").build(),
                ExcelFormatter.builder("author").addName("创建人").build(),
                ExcelFormatter.builder("updatedAt").addName("最后修改时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build()
        );
        try {
            String fileName = ExcelUtils.exportExcelByBean(GrayList.class, pageInfo.getList(), "名单管理", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出名单管理数据异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    public JsonResult batchDelete(@Valid @RequestBody GrayList record) {
        return JsonResult.success(getService().batchDelete(record));
    }

    @Override
    @RequestMapping("/search")
    public JsonResult search(@RequestBody GrayList record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        if(Dimensions.ENCRYPTID.name().equals(record.getDimension()) ||
                (StringUtils.isBlank(record.getDimension()) && CheckIdCard.check(record.getValue()))){
            record.setValue(blackListService.getEncId(record.getValue()));
        }
        PageInfo<GrayList> search = grayListService.search(record, page, size);
        return JsonResult.success(search);
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild graylist to redis cache , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_SHOOT)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.JOB_GRAYLIST_TO_REDIS_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger graylist to redis rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

    /**
     * 检测
     */
    @RequestMapping("check")
    public JsonResult check(@RequestParam(value = "idNo") String idNo) {
        Assert.hasLength(idNo, "请输入身份证号");
        Map<String, Object> map = new HashMap<>();
        String[] idNos = idNo.split(",");
        for (String id : idNos) {
            if (!CheckIdCard.check(id)) {
                map.put(id, "非正确的身份证号");
            } else {
                map.put(id, blackListService.getEncId(id));
            }
        }
        return JsonResult.success(map);
    }

}

