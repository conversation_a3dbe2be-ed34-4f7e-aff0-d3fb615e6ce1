package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.yupaopao.risk.common.enums.RecallStatus;
import com.yupaopao.risk.common.model.RecallWord;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.RecallWordService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-5-7
 */
@RestController
@RequestMapping("/recall/word")
@AuthRequired(permissionName = "core:recallword:all")
public class RecallWordController extends AbstractModelController<RecallWord, RecallWord, RecallWordService> {

    @Autowired
    private RecallWordService recallWordService;

    @RequestMapping("/search")
    @Override
    public JsonResult search(@RequestBody RecallWord record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(recallWordService.search(record, page, size, "id desc"));
    }
    /**
     * 查询可操作的回溯内容列表
     */
    @RequestMapping("/getActive")
    public JsonResult getActive() {
        return JsonResult.success(recallWordService.getActive());
    }

    /**
     * 查询待执行的回溯内容列表
     */
    @RequestMapping("/getWait")
    public JsonResult getWait() {
        return JsonResult.success(recallWordService.getWait());
    }

    /**
     * 查询回溯状态列表，会添加【0:全部】
     */
    @RequestMapping("/getStatusList")
    public JsonResult getStatusList(@Valid @RequestBody Integer type) {
        List<Map<String,String>> list = Lists.newArrayList();

        Map<String,String> all = new MapMaker().makeMap();
        all.put("code","0");
        all.put("msg","全部");
        list.add(all);

        // 回溯内容的状态列表
        if(type==4){
            RecallStatus.getWordStatusList().forEach(status -> {
                Map<String,String> temp = new MapMaker().makeMap();
                temp.put("code",String.valueOf(status.getCode()));
                temp.put("msg",String.valueOf(status.getMsg()));
                list.add(temp);
            });
            // 回溯任务的状态列表
        }else if(type==1){
            RecallStatus.getParentJobStatusList().forEach(status -> {
                Map<String,String> temp = new MapMaker().makeMap();
                temp.put("code",String.valueOf(status.getCode()));
                temp.put("msg",String.valueOf(status.getMsg()));
                list.add(temp);
            });
        }else if(type==2){
            RecallStatus.getChildJobStatusList().forEach(status -> {
                Map<String,String> temp = new MapMaker().makeMap();
                temp.put("code",String.valueOf(status.getCode()));
                temp.put("msg",String.valueOf(status.getMsg()));
                list.add(temp);
            });
        }else if(type==3){
            EnumUtils.getEnumList(RecallStatus.class).forEach(status -> {
                Map<String,String> temp = new MapMaker().makeMap();
                temp.put("code",String.valueOf(status.getCode()));
                temp.put("msg",String.valueOf(status.getMsg()));
                list.add(temp);
            });
        }

        return JsonResult.success(list);
    }

    @RequestMapping("/reset")
    public JsonResult result() {
        return JsonResult.success(recallWordService.reset());
    }

    @Override
    protected void addValid(RecallWord record, User user) {
        Assert.isTrue(StringUtils.trim(record.getContent()).length() > 0, "该回溯内容包含空字符");
        record.setStatus(RecallStatus.WAIT.getCode());
        record.setAuthor(user.getName());
    }
}
