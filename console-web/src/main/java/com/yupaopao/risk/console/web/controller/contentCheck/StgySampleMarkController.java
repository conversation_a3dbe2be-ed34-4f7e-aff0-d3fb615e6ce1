package com.yupaopao.risk.console.web.controller.contentCheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.StrategyEvaluateType;
import com.yupaopao.risk.common.model.StrategySampleMark;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.StrategyEvaluateService;
import com.yupaopao.risk.console.service.StrategySampleMarkService;
import com.yupaopao.risk.console.vo.StrategyEvaluateVO;
import com.yupaopao.risk.console.vo.StrategySampleMarkVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2020/6/23 17:03
 */
@Slf4j
@RestController
@RequestMapping("/contentCheck/stgySampleMark")
@AuthRequired(permissionName = "contentcheck:stgySampleMark:all")
public class StgySampleMarkController extends AbstractModelController<StrategySampleMark, StrategySampleMarkVO, StrategySampleMarkService> {

    @Autowired
    private StrategyEvaluateService strategyEvaluateService;

    @RequestMapping(value = "/getStrategyTask/{id}", method = RequestMethod.POST)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(strategyEvaluateService.getStrategyEvaluate(id));
    }

    @RequestMapping("/search")
    @AuthRequired(permissionName = "contentcheck:stgySampleMark:search")
    public JsonResult search(@RequestBody StrategySampleMarkVO stgySampleMarkVO, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        return JsonResult.success(this.getService().searchVO(stgySampleMarkVO,page,size));
    }

    @RequestMapping(value = "/batchUpdateResult", method = RequestMethod.POST)
    @AuthRequired(permissionName = "contentcheck:stgySampleMark:update")
    public JsonResult batchUpdateResult(@RequestBody List<StrategySampleMark> stgySampleMarks, @CurrentUser User user) {
        if(CollectionUtils.isEmpty(stgySampleMarks)){
            throw new RiskException("请选择要标注的样本");
        }
        if(null == stgySampleMarks.get(0).getTaskId()){
            throw new RiskException("任务id不存在");
        }
        return JsonResult.success(strategyEvaluateService.batchUpdateResult(stgySampleMarks,user));
    }

    @RequestMapping("/export")
    @AuthRequired(permissionName = "contentcheck:stgySampleMark:export")
    public JsonResult export(@RequestBody StrategySampleMarkVO stgySampleMarkVO) {
        //本导出最多只能导出10000条
        StrategyEvaluateVO strategyEvaluateVO = strategyEvaluateService.getStrategyEvaluate(stgySampleMarkVO.getTaskId());
        PageInfo<StrategySampleMarkVO> pageInfo = this.getService().searchVO(stgySampleMarkVO,1,10000);
        List<ExcelFormatter> formatterList = Lists.newArrayList();

        formatterList.add(ExcelFormatter.builder("strUid").addName("UID").build());
        formatterList.add(ExcelFormatter.builder("deviceId").addName("设备ID").build());
        if(StrategyEvaluateType.REAL.getCode().equals(strategyEvaluateVO.getType())){
            formatterList.add(ExcelFormatter.builder("riskLevel").addName("风控结果").build());
        }
        List<String> baseFieldKeys = Lists.newArrayList("uid","deviceId","riskLevel");
        List<StrategySampleMarkVO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            StrategySampleMarkVO firstSampleMarkVO = list.get(0);
            JSONObject infoJson = firstSampleMarkVO.getInfoJson();
            for(String key : infoJson.keySet()){
                if(!baseFieldKeys.contains(key)){
                    formatterList.add(ExcelFormatter.builder(key).addName(key).build());
                }
            }
        }
        formatterList.add(ExcelFormatter.builder("markStatus").addName("标注状态").build());
        formatterList.add(ExcelFormatter.builder("resultName").addName("标注结果").build());
        formatterList.add(ExcelFormatter.builder("operator").addName("标注人").build());

        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(formatterList.toArray(new ExcelFormatter[formatterList.size()]));
        List<Map<String,Object>> resultList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(list)){
            for(StrategySampleMarkVO vo : list){
                JSONObject infoJson = vo.getInfoJson();
                Map<String,Object> map = JSON.parseObject(JSON.toJSONString(vo));
                map.putAll(infoJson);
                map.put("markStatus",vo.getResult() == 0 ? "待标注" : "已标注");
                map.put("resultName",vo.getResult() == 0 ? "" : (vo.getResult() == 1 ? "正常" : (vo.getResult() == 2 ? "可疑" : "高危")));
                resultList.add(map);
            }
        }
        try {
            String fileName = ExcelUtils.exportExcelByMap(resultList, "样本标注记录", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出样本标注记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

}
