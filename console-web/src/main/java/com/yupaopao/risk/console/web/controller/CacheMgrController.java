package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.console.bean.CacheOperation;
import com.yupaopao.risk.console.service.CacheService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AuthRequired(permissionName = "system:cachemgr:all")
@RequestMapping("/cache")
public class CacheMgrController {

    @Autowired
    private CacheService cacheService;

    @RequestMapping("/execute")
    public JsonResult execute(@RequestBody CacheOperation cacheOperation) {
        return JsonResult.success(cacheService.execute(cacheOperation));
    }

    @RequestMapping("/hints")
    public JsonResult hints() {
        return JsonResult.success(cacheService.hints());
    }

}
