package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Sets;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.mapper.GrayListMapper;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.utils.RiskModelIdUtils;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.service.GrayGroupService;
import com.yupaopao.risk.console.service.impl.ConfigService;
import com.yupaopao.risk.console.vo.GroupVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/gray/group")
@AuthRequired(permissionName = "core:graygroup:all")
public class GrayGroupController extends AbstractModelController<GrayGroup, GrayGroup, GrayGroupService> {

    @Autowired
    private ConfigService configService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GrayListMapper grayListMapper;

    @Autowired
    private GrayGroupService grayGroupService;

    @Autowired
    private EventService eventService;

    @RequestMapping("/search")
    public JsonResult search(@RequestBody GrayGroup record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo pageInfo = this.grayGroupService.search(record, page, size);

        if(CollectionUtils.isEmpty(pageInfo.getList())){
            List<GroupVO> groupVOS = new ArrayList<>();
            pageInfo.setList(groupVOS);

            return JsonResult.success(pageInfo);
        }

        List<Event> allEvents = eventService.selectAll();
        Map<Long,Set<Long>> eventIdToGrayGroupIdsMap = convertToEventIdToGrayGroupIdsMap(allEvents);
        Map<Long,Set<Long>> grayGroupIdToEventIdsMap = convertToGrayGroupIdToEventIdsMap(eventIdToGrayGroupIdsMap);

        List<GroupVO> groupVOS = getGrayGroupVOs(pageInfo.getList(),allEvents,grayGroupIdToEventIdsMap);
        pageInfo.setList(groupVOS);
        return JsonResult.success(pageInfo);
    }

    private List<GroupVO> getGrayGroupVOs(List<GrayGroup> grayGroups, List<Event> allEvents, Map<Long,Set<Long>> grayGroupIdToEventIdsMap){
        List<GroupVO> result = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(grayGroups)&&MapUtils.isNotEmpty(grayGroupIdToEventIdsMap)){
            Map<Long,Event> idToEvent = allEvents.stream().collect(Collectors.toMap(Event::getId,event->event));

            for(GrayGroup grayGroup:grayGroups){
                GroupVO groupVO = new GroupVO();

                try {
                    BeanUtils.copyProperties(groupVO,grayGroup);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                }

                Set<Long> eventIds = grayGroupIdToEventIdsMap.get(grayGroup.getId());

                Set<Event>  events = new HashSet<>();
                if(CollectionUtils.isNotEmpty(eventIds)){
                    for(Long eventId:eventIds){
                        Event event = idToEvent.get(eventId);
                        events.add(event);
                    }
                }

                groupVO.setEvents(events);

                result.add(groupVO);
            }
        }

        return result;
    }

    private Map<Long,Set<Long>> convertToGrayGroupIdToEventIdsMap(Map<Long,Set<Long>> eventIdToGrayGroupIdsMap){

        Map<Long,Set<Long>> result = new HashMap<>();

        if(MapUtils.isNotEmpty(eventIdToGrayGroupIdsMap)){
            for(Map.Entry<Long,Set<Long>> entry:eventIdToGrayGroupIdsMap.entrySet()){
                Long eventId = entry.getKey();
                Set<Long> grayGroupIds = entry.getValue();

                if(CollectionUtils.isNotEmpty(grayGroupIds)){
                    for(Long grayGroupId:grayGroupIds){
                        if(result.containsKey(grayGroupId)){
                            result.get(grayGroupId).add(eventId);
                        }else{
                            Set<Long> eventIds = new HashSet<>();
                            eventIds.add(eventId);

                            result.put(grayGroupId,eventIds);
                        }
                    }
                }
            }
        }

        return result;
    }

    private Map<Long,Set<Long>> convertToEventIdToGrayGroupIdsMap(List<Event> allEvents){
        Map<Long,Set<Long>> result = new HashMap<>();

        if(CollectionUtils.isNotEmpty(allEvents)){
            for(Event event:allEvents){
                Long eventId = event.getId();
                String grayGroups = event.getGrayGroups();

                Set<Long> grayGroupIds = new HashSet<>();
                if(StringUtil.isNotEmpty(grayGroups)){
                    List<String> grayGroupIdStrs = Arrays.asList(grayGroups.split(","));
                    if(CollectionUtils.isNotEmpty(grayGroupIdStrs)){
                        for(String grayGroupIdStr:grayGroupIdStrs){
                            Long grayGroupId = Long.parseLong(grayGroupIdStr);
                            grayGroupIds.add(grayGroupId);
                        }
                    }
                }

                result.put(eventId,grayGroupIds);
            }
        }

        return result;
    }
    @Override
    protected void addValid(GrayGroup record, User user) {
        Assert.notNull(record.getName(), "请指定名单分组名称");
    }

    @Override
    protected void updateValid(GrayGroup record, User user) {
        addValid(record, user);
        Assert.notNull(record.getId(), "请指定记录ID");
    }

    @RequestMapping("/clearOldCache")
    public JsonResult build(String preKey) {
        List<GrayGroup> list = getService().selectAll();
        if(null != list){
            for(GrayGroup g:list){
                redisService.del(String.format(preKey.concat("#%d"), g.getId()));
            }
        }
        int page = 1;
        Set<String> eventCodeSet = Sets.newHashSet();
        do {
            Example example = new Example(GrayList.class, false);
            example.setOrderByClause("id");
            Example.Criteria criteria = example.createCriteria();
            criteria.andNotEqualTo("eventList","");
            RowBounds rowBounds = new RowBounds((page-1)*1000,1000);
            List<GrayList> grayLists = grayListMapper.selectByExampleAndRowBounds(example,rowBounds);
            if(null == grayLists){
                break;
            }
            for(GrayList g:grayLists){
                if (StringUtils.isNotEmpty(g.getEventList())){
                    String[] split = g.getEventList().split(",");
                    for (String eventCode : split) {
                        eventCodeSet.add(eventCode);
                    }
                }
            }
            if(grayLists.size()<1000){
                break;
            }
            page++;
        } while (true);
        for(String eventCode:eventCodeSet){
            redisService.del(String.format(preKey.concat("#%s"), eventCode));
        }
        return JsonResult.success();
    }

    @RequestMapping("/searchByEvent")
    public JsonResult search(@RequestBody Event event) {
        List<Long> grayGroupIds = RiskModelIdUtils.split(event.getGrayGroups());
        if(CollectionUtils.isNotEmpty(grayGroupIds)){
            return JsonResult.success(grayGroupService.selectByIds(grayGroupIds));
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

}
