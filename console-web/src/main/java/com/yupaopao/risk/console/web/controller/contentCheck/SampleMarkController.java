package com.yupaopao.risk.console.web.controller.contentCheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.SampleMark;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.CheckTaskService;
import com.yupaopao.risk.console.service.SampleMarkService;
import com.yupaopao.risk.console.vo.CheckTaskVO;
import com.yupaopao.risk.console.vo.SampleMarkVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2020/6/23 17:03
 */
@Slf4j
@RestController
@RequestMapping("/contentCheck/sampleMark")
@AuthRequired(permissionName = "contentcheck:samplemark:all")
public class SampleMarkController extends AbstractModelController<SampleMark, SampleMarkVO, SampleMarkService> {

    @Autowired
    private CheckTaskService checkTaskService;

    @RequestMapping(value = "/getCheckTask/{id}", method = RequestMethod.POST)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(checkTaskService.getCheckTask(id));
    }

    @RequestMapping("/search")
    @AuthRequired(permissionName = "contentcheck:samplemark:search")
    public JsonResult search(@RequestBody SampleMarkVO sampleMarkVO, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        return JsonResult.success(this.getService().searchVO(sampleMarkVO,page,size));
    }

    @RequestMapping(value = "/batchUpdateResult", method = RequestMethod.POST)
    @AuthRequired(permissionName = "contentcheck:samplemark:update")
    public JsonResult batchUpdateResult(@RequestBody List<SampleMark> sampleMarks,@CurrentUser User user) {
        if(CollectionUtils.isEmpty(sampleMarks)){
            throw new RiskException("请选择要标注的样本");
        }
        if(null == sampleMarks.get(0).getTaskId()){
            throw new RiskException("任务id不存在");
        }
        return JsonResult.success(checkTaskService.batchUpdateResult(sampleMarks,user));
    }

    @RequestMapping("/export")
    @AuthRequired(permissionName = "contentcheck:samplemark:export")
    public JsonResult export(@RequestBody SampleMarkVO sampleMarkVO) {
        //本导出最多只能导出10000条
        CheckTaskVO checkTaskVO = checkTaskService.getCheckTask(sampleMarkVO.getTaskId());
        PageInfo<SampleMarkVO> pageInfo = this.getService().searchVO(sampleMarkVO,1,10000);
        List<ExcelFormatter> formatterList = Lists.newArrayList();
        List<String> baseFieldKeys = Lists.newArrayList();
        for(Map<String,String> baseField : checkTaskVO.getBaseFields()){
            formatterList.add(ExcelFormatter.builder(MapUtils.getString(baseField,"code","")).addName(MapUtils.getString(baseField,"msg","")).build());
            baseFieldKeys.add(MapUtils.getString(baseField,"code",""));
        }
        List<SampleMarkVO> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            SampleMarkVO firstSampleMarkVO = list.get(0);
            JSONObject infoJson = firstSampleMarkVO.getInfoJson();
            for(String key : infoJson.keySet()){
                if(!baseFieldKeys.contains(key)){
                    formatterList.add(ExcelFormatter.builder(key).addName(key).build());
                }
            }
        }
        formatterList.add(ExcelFormatter.builder("sampleTypeName").addName("样本类型").build());
        formatterList.add(ExcelFormatter.builder("eventName").addName("关联场景").build());
        formatterList.add(ExcelFormatter.builder("channelName").addName("检测渠道").build());
        formatterList.add(ExcelFormatter.builder("markStatus").addName("标注状态").build());
        formatterList.add(ExcelFormatter.builder("resultName").addName("标注结果").build());
        formatterList.add(ExcelFormatter.builder("executor").addName("标注人").build());

        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(formatterList.toArray(new ExcelFormatter[formatterList.size()]));
        List<Map<String,Object>> resultList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(list)){
            for(SampleMarkVO vo : list){
                JSONObject infoJson = vo.getInfoJson();
                Map<String,Object> map = JSON.parseObject(JSON.toJSONString(vo));
                map.putAll(infoJson);
                map.put("sampleTypeName",checkTaskVO.getSampleTypeName());
                map.put("eventName",checkTaskVO.getEvent().getName());
                map.put("channelName",checkTaskVO.getChannelName());
                map.put("markStatus",vo.getResult() == 0 ? "待标注" : "已标注");
                map.put("resultName",vo.getResult() == 0 ? "" : (vo.getResult() == 1 ? "误伤" : "风险"));
                resultList.add(map);
            }
        }
        try {
            String fileName = ExcelUtils.exportExcelByMap(resultList, "样本标注记录", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出样本标注记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

}
