package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.mining.word.WordInfo;
import com.yupaopao.risk.console.web.support.JsonResult;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fencing/hot")
public class FencingHotController {

    private final static Logger LOGGER = LoggerFactory.getLogger(FencingHotController.class);

    @RequestMapping("/search")
    public JsonResult search(@RequestBody ContentAndQuantity contentAndQuantity){

        try{
            if(StringUtils.isNotBlank(contentAndQuantity.getContent())&&(contentAndQuantity.getQuantity()!=null)&&(contentAndQuantity.getQuantity()>0)){
                InputStream inputStream = new ByteArrayInputStream(contentAndQuantity.getContent().getBytes());
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                List<WordInfo> wordInfoList =HanLP.extractWords(reader,20000,true,9,0.001f,0.0f,6f);
                List<HotWord> hotWords = Lists.newArrayList();

                List<HotWord> bigHotWords = Lists.newArrayList();

                int i = 1;
                if(CollectionUtils.isNotEmpty(wordInfoList)){
                    for(WordInfo wordInfo:wordInfoList){
                        HotWord hotWord = new HotWord(i++,wordInfo.text,wordInfo.frequency);

                        Integer relation = contain(hotWords,wordInfo);
                        if(relation==2){
                            bigHotWords.add(hotWord);
                            hotWords.add(hotWord);
                        }else if(relation==3){
                            hotWords.add(hotWord);
                        }
                    }
                }

                List<HotWord> filterHotWords = removeDuplicate(hotWords,bigHotWords,contentAndQuantity.getQuantity());
                if(CollectionUtils.isNotEmpty(filterHotWords)){
                    int j = 1;
                    for(HotWord hotWord:filterHotWords){
                        hotWord.setSeq(j++);
                    }
                }
                return JsonResult.success(filterHotWords);
            }

            return JsonResult.success(null);
        }catch (Exception e){
            LOGGER.error("提取热点词失败",e);
            return JsonResult.error();
        }
    }

    private List<HotWord> removeDuplicate(List<HotWord> hotWords, List<HotWord> bigHotWords,Integer quantity){

         List<HotWord> result = Lists.newArrayList();

         if(CollectionUtils.isNotEmpty(hotWords)&&CollectionUtils.isNotEmpty(bigHotWords)){
             for(HotWord hotWord:hotWords){
                 boolean isContain = false;
                 for(HotWord bigHitWord:bigHotWords){
                     if(bigHitWord.text.contains(hotWord.text)&&!bigHitWord.text.equals(hotWord.text)){
                         bigHitWord.setFrequency(hotWord.frequency+bigHitWord.getFrequency());
                         isContain = true;
                         break;
                     }
                 }
                 if(!isContain){
                     result.add(hotWord);
                 }
             }
         }

         if(CollectionUtils.isNotEmpty(result)&&CollectionUtils.isNotEmpty(bigHotWords)){
             Map<String,HotWord> map = bigHotWords.stream().collect(Collectors.toMap(HotWord::getText,hotWord -> hotWord));

             for(HotWord hotWord : result){
                 if(map.containsKey(hotWord.getText())){
                    hotWord.setFrequency(map.get(hotWord.getText()).frequency);
                 }
             }
         }

         if(quantity<=result.size()){
             return result.subList(0,quantity-1);
         }else{
             return result;
         }

    }

    private Integer contain(List<HotWord> hotWords, WordInfo wordInfo){
        if(CollectionUtils.isNotEmpty(hotWords)){
            for(HotWord hotWord:hotWords){
                // 待插入的词 已经被包含在已有热点词库中
                if(hotWord.text.contains(wordInfo.text)){
                    hotWord.setFrequency(hotWord.getFrequency()+wordInfo.frequency);
                    return 1;
                }else if(wordInfo.text.contains(hotWord.text)){
                    return 2;
                }
            }
        }

        return 3;
    }

    @Getter
    @Setter
    static class ContentAndQuantity implements Serializable {

        private String content;
        private Integer quantity;

    }

    @Getter
    @Setter
    @AllArgsConstructor
    static class HotWord implements Serializable{

        private Integer seq;
        private String text;
        private Integer frequency;

    }

}
