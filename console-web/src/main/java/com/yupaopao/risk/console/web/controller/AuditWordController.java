package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Maps;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.platform.audit.mx.api.channel.ChannelApiService;
import com.yupaopao.platform.audit.mx.api.model.channel.ChannelFullDTO;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.WordType;
import com.yupaopao.risk.common.model.AuditTextStatistics;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.vo.AuditTextStatisticsVO;
import com.yupaopao.risk.console.bean.ExportedAuditTextStatistic;
import com.yupaopao.risk.console.service.AuditTextStatisticsService;
import com.yupaopao.risk.console.service.SceneService;
import com.yupaopao.risk.console.service.TagService;
import com.yupaopao.risk.console.service.WordService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auditTextStatistics")
@AuthRequired(permissionName = "content:auditTextStatistics:all")
@Slf4j
public class AuditWordController extends AbstractModelController<AuditTextStatistics, AuditTextStatisticsVO,
        AuditTextStatisticsService> {

    @Autowired
    private WordService wordService;

    @Autowired
    private TagService tagService;

    @Autowired
    private SceneService sceneService;

    @DubboReference(check = false)
    private ChannelApiService channelApiService;

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "审核违禁词查询", isReportResponse = false)
    @AuthRequired(permissionName = "content:auditTextStatistics:all")
    @RequestMapping("complexSearch")
    public JsonResult complexSearch(@RequestBody AuditTextStatisticsVO record, @RequestParam(value = "p",
            defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        return JsonResult.success(this.getService().complexSearch(record, page, size));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控标签查询", isReportResponse = false)
    @AuthRequired(permissionName = "content:auditTextStatistics:all")
    @RequestMapping("selectTag")
    public JsonResult selectTag(@RequestBody Tag tag, @RequestParam(value = "p", defaultValue = "1") Integer page,
                                @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(this.tagService.showInSel(tag, page, size));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "审核通道查询", isReportResponse = false)
    @AuthRequired(permissionName = "content:auditTextStatistics:all")
    @RequestMapping("/getAuditChannelList")
    public JsonResult getAuditChannelList() {
        List<Map<String, String>> list = Lists.newArrayList();

        List<ChannelFullDTO> channels = channelApiService.listAllEnable();
        if (CollectionUtils.isNotEmpty(channels)) {
            for (ChannelFullDTO channel : channels) {
                Map<String, String> channelMap = Maps.newHashMap();

                channelMap.put("code", channel.getCode());
                channelMap.put("name", channel.getName());

                list.add(channelMap);
            }
        }

        return JsonResult.success(list);
    }

    /**
     * 填充词条类型下拉框
     */
    @RequestMapping("/getWordTypeList")
    public JsonResult getWordTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(WordType.class).forEach(type -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(type.getCode()));
            temp.put("msg", String.valueOf(type.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "审核文本违禁词命中统计导出", isReportResponse = false)
    @AuthRequired(permissionName = "content:auditTextStatistics:all")
    @RequestMapping("export")
    public JsonResult export(@CurrentUser User user, @RequestBody AuditTextStatisticsVO record) {
        try {
            //最多导出一万行
            Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                    ExcelFormatter.builder("content").addName("违禁词").build(),
                    ExcelFormatter.builder("firstTagNames").addName("一级标签").build(),
                    ExcelFormatter.builder("tagNames").addName("二级标签").build(),
                    ExcelFormatter.builder("sceneNames").addName("场景").build(),
                    ExcelFormatter.builder("channelName").addName("通道").build(),
                    ExcelFormatter.builder("wordLocationTypeName").addName("词条类型").build(),
                    ExcelFormatter.builder("hitCount").addName("命中次数").build(),
                    ExcelFormatter.builder("rejectCount").addName("拒绝次数").build(),
                    ExcelFormatter.builder("passCount").addName("通过次数").build(),
                    ExcelFormatter.builder("effectiveRate").addName("有效率").build(),
                    ExcelFormatter.builder("rejectRate").addName("拒绝率").build()
            );

            String fileName = ExcelUtils.exportExcelByBean(ExportedAuditTextStatistic.class,
                    this.getService().export(user, record), "审核文本命中违禁词统计",formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出审核文本违禁词统计Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

}
