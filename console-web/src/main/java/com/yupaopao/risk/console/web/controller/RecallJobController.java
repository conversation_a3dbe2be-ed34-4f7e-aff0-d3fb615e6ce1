package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.RecallStatus;
import com.yupaopao.risk.common.model.Log;
import com.yupaopao.risk.common.model.RecallJob;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.bean.ExportedRecallWord;
import com.yupaopao.risk.console.bean.ExportedWord;
import com.yupaopao.risk.console.enums.RecallType;
import com.yupaopao.risk.console.service.RecallJobService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-5-7
 */
@Slf4j
@RestController
@RequestMapping("/recall/job")
@AuthRequired(permissionName = "core:recalljob:all")
public class RecallJobController extends AbstractModelController<RecallJob, RecallJob, RecallJobService> {

    @Autowired
    private RecallJobService recallJobService;

    @Value("${recall.scenes:[{\"type\":1,\"name\":\"场景1、昵称\"},{\"type\":2,\"name\":\"场景2、签名\"},{\"type\":3,\"name\":\"场景3、比心动态\"}," +
            "{\"type\":4,\"name\":\"场景4、比心动态评论\"},{\"type\":5,\"name\":\"场景5、小星球动态评论\"},{\"type\":6,\"name\":\"场景6、资质介绍修改\"}," +
            "{\"type\":7,\"name\":\"场景7、大神对用户的评论\"},{\"type\":8,\"name\":\"场景8、用户对大神的评论\"},{\"type\":10,\"name\":\"场景10、鱼耳动态评论\"}," +
            "{\"type\":11,\"name\":\"场景11、鱼耳动态\"},{\"type\":12,\"name\":\"场景12、小星球动态\"},{\"type\":13,\"name\":\"场景13、鱼耳聊天室房间名称\"}," +
            "{\"type\":14,\"name\":\"场景14、鱼耳聊天室房间公告标题文本\"},{\"type\":15,\"name\":\"场景15、鱼耳聊天室房间公告内容文本\"}," +
            "{\"type\":16,\"name\":\"场景16、鱼耳聊天室欢迎语内容文本\"},{\"type\":21,\"name\":\"场景21、大神资质-服务标题\"}]}")
    private String scenes;

    @RequestMapping("/search")
    @Override
    public JsonResult search(@RequestBody RecallJob record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<RecallJob> pageInfo = recallJobService.search(record, page, size, "id desc");
        if(pageInfo!=null){
            List<RecallJob> list = pageInfo.getList();
            if(CollectionUtils.isNotEmpty(list)){
                for(RecallJob recallJob:list){
                    Date startTime = recallJob.getStartTime();
                    Date endTime = recallJob.getEndTime();
                    if(Objects.equals(startTime,endTime)){
                        recallJob.setStartTime(null);
                        recallJob.setEndTime(null);
                    }

                    recallJob.setExecuteLimit(recallJobService.executeLimit(recallJob));
                }
            }
        }

        return JsonResult.success(pageInfo);
    }

    /**
     * 查询子任务
     */
    @RequestMapping("/searchTask")
    public JsonResult searchTask(@RequestBody RecallJob record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<RecallJob> pageInfo = null;
        if(record!=null){
            if(record.getType()==1){
                pageInfo  = recallJobService.searchTask(record, page, size, "id desc");
            }else if(record.getType()==2){
                pageInfo = recallJobService.searchTask(record, page, size, "quantity desc");
            }else{
                pageInfo = recallJobService.searchTask(record, page, size, "id desc");
            }
        }

        if(pageInfo!=null){
            List<RecallJob> list = pageInfo.getList();
            if(CollectionUtils.isNotEmpty(list)){
                for(RecallJob recallJob:list){
                    Date startTime = recallJob.getStartTime();
                    Date endTime = recallJob.getEndTime();
                    if(Objects.equals(startTime,endTime)){
                        recallJob.setStartTime(null);
                        recallJob.setEndTime(null);
                    }

                    recallJob.setExecuteLimit(recallJobService.executeLimit(recallJob));
                }
            }
        }

        return JsonResult.success(pageInfo);
    }

    /**
     * 新增回溯任务，并更新对应回溯内容的状态、任务编号
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public JsonResult save(@RequestParam(value = "scenes") String scenes, @CurrentUser User user) {
        RecallJob job = new RecallJob();
        job.setAuthor(user.getName());
        job.setMessage(scenes);
        job.setQuantity(0);
        recallJobService.saveAll(job);
        return JsonResult.success();
    }

//    /**
//     * 新增回溯任务，并更新对应回溯内容的状态、任务编号
//     */
//    @RequestMapping(value = "/batchSave", method = RequestMethod.POST)
//    public JsonResult batchSave(@RequestParam(value = "scenes") String scenes,@RequestParam(value = "onlyOne") int onlyOne, @CurrentUser User user) {
//        recallJobService.batchSaveAll(scenes,onlyOne==1?true:false,user.getName());
//        return JsonResult.success();
//    }

    /**
     * 新增回溯任务，并更新对应回溯内容的状态、任务编号
     */
    @RequestMapping(value = "/batchSave", method = RequestMethod.POST)
    public JsonResult batchSave(@RequestBody NewMasterJobRequest request, @CurrentUser User user) {
        RecallType recallType = RecallType.getByCode(request.getRecallType());
        Assert.isTrue(!Objects.equals(recallType.getCode(),RecallType.UNKNOWN.getCode()),"请选择回溯类型!");

        recallJobService.batchSaveAll(recallType,request.getScenes(), request.getOnlyOne() == 1,request.getStartTime(),request.getEndTime(),user.getName());
        return JsonResult.success();
    }

    /**
     * 取消进行中的回溯任务，并更新对应回溯内容的状态
     */
    @RequestMapping(value = "/runJob/{id}", method = RequestMethod.POST)
    public JsonResult runJob(@PathVariable long id) {
        recallJobService.runJob(id);
        return JsonResult.success();
    }

    /**
     * 取消进行中的回溯任务，并更新对应回溯内容的状态
     */
    @RequestMapping(value = "/cancel/{id}", method = RequestMethod.POST)
    public JsonResult cancel(@PathVariable long id) {
        recallJobService.cancelById(id);
        return JsonResult.success();
    }

    /**
     * 取消进行中的主回溯任务，并更新对应回溯内容的状态
     */
    @RequestMapping(value = "/cancelParentJob/{id}", method = RequestMethod.POST)
    public JsonResult cancelParentJob(@PathVariable long id) {
        recallJobService.cancelParentJob(id);
        return JsonResult.success();
    }

    /**
     * 取消进行中的主回溯任务，并更新对应回溯内容的状态
     */
    @RequestMapping(value = "/cancelChildJob/{id}", method = RequestMethod.POST)
    public JsonResult cancelChildJob(@PathVariable long id) {
        recallJobService.cancelChildJob(id);
        return JsonResult.success();
    }

    @RequestMapping(value = "/statisticParentJob/{id}", method = RequestMethod.POST)
    public JsonResult statisticParentJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.statisticParentJob(id, user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/statisticChildJob/{id}", method = RequestMethod.POST)
    public JsonResult statisticChildJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.statisticChildJob(id, user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/executeParentJob/{id}", method = RequestMethod.POST)
    public JsonResult executeParentJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.executeParentJob(id,user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/executeChildJob/{id}", method = RequestMethod.POST)
    public JsonResult executeChildJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.executeChildJob(id,user);
        return JsonResult.success();
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "回溯内容导出",isReportResponse = false)
    @RequestMapping(value = "/download/{jobNo}",method = RequestMethod.POST)
    public JsonResult download(@PathVariable String jobNo,@CurrentUser User user){
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("jobNo").addName("任务编号").build(),
                ExcelFormatter.builder("recallWord").addName("回溯内容").build()
        );

        try{
            String fileName =  ExcelUtils.exportExcelByBean(ExportedRecallWord.class, this.getService().export(user,jobNo),"回溯内容",formatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            log.error("导出回溯内容Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }

    @RequestMapping(value = "/redo/{id}", method = RequestMethod.POST)
    public JsonResult redo(@PathVariable long id,@CurrentUser User user) {
        recallJobService.reRecall(id,user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/redoParentJob/{id}", method = RequestMethod.POST)
    public JsonResult redoParentJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.redoParentJob(id,user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/redoChildJob/{id}", method = RequestMethod.POST)
    public JsonResult redoChildJob(@PathVariable long id,@CurrentUser User user) {
        recallJobService.redoChildJob(id,user);
        return JsonResult.success();
    }

    @RequestMapping(value = "/openRecall", method = RequestMethod.POST)
    public JsonResult openRecall(@RequestBody String toOpen,@CurrentUser User user){
        try{
            recallJobService.openRecall(toOpen,user);
        }catch (Exception e){
            throw e;
        }

        return JsonResult.success();
    }

    @RequestMapping(value = "/getRecallOpenStatus", method = RequestMethod.POST)
    public JsonResult getRecallOpenStatus(){
        return JsonResult.success(recallJobService.getRecallOpenStatus());
    }

    @Override
    protected void addValid(RecallJob record, User user) {
        record.setStatus(RecallStatus.WAIT.getCode());
        record.setAuthor(user.getName());
    }

    @RequestMapping("/scenes")
    public JsonResult scenes() {
        return JsonResult.success(JSONArray.parse(scenes));
    }

    @RequestMapping("/getRecallTypeList")
    public JsonResult getRecallTypeList(){

        List<Map<String,String>> list = Lists.newArrayList();
        for(RecallType recallType:RecallType.values()){
            if(!Objects.equals(recallType.getCode(),RecallType.UNKNOWN.getCode())){
                Map<String,String> map = Maps.newHashMap();
                map.put("code",String.valueOf(recallType.getCode()));
                map.put("msg",recallType.getMsg());

                list.add(map);
            }
        }

        return JsonResult.success(list);
    }

}

@Data
class NewMasterJobRequest{
    private String scenes;
    private int onlyOne;
    private Date startTime;
    private Date endTime;
    private Integer recallType;
}
