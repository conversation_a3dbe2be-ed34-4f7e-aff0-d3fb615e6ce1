package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.enums.CheckTaskState;
import com.yupaopao.risk.common.enums.StrategyEvaluateType;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.StrategyEvaluate;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.bean.ValidResult;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.service.StrategyEvaluateService;
import com.yupaopao.risk.console.vo.StrategyEvaluateVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contentCheck/strategyEvaluate")
@AuthRequired(permissionName = "strategy:evaluate:all")
@Slf4j
public class StrategyEvaluateController extends AbstractModelController<StrategyEvaluate, StrategyEvaluateVO, StrategyEvaluateService>{

    @Autowired
    private EventService eventService;


    protected void addValid(StrategyEvaluate record) {
        commonValid(record);
    }

    protected void commonValid(StrategyEvaluate record) {
        Assert.isTrue(StringUtils.trim(record.getName()).length() > 0, "任务名称不能为空");
        Assert.isTrue(StringUtils.isNotBlank(record.getType()),"任务类型不能为空");
        if(record.getSampleCount()!=null){
            Assert.isTrue(record.getSampleCount()>0&&record.getSampleCount()<=1000,"随机抽取样本数不能大于1000条");
        }
    }

    @RequestMapping("/complexSearch")
    public JsonResult complexSearch(@CurrentUser User user, @RequestBody StrategyEvaluateVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        return JsonResult.success(this.getService().search(user,record,page,size));
    }

    /**
     * 校验策略评估任务的查询语句
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/validSearchSql", method = RequestMethod.POST)
    public JsonResult validSearchSql(@Valid @RequestBody StrategyEvaluate record, @CurrentUser User user) {
        addValid(record);

        Map<String,Object> result = Maps.newHashMap();

        ValidResult validResult = this.getService().validSearchSql(record.getSearchSql());
        if(!validResult.isSuccess()){
            result.put("code",validResult.getCode());
            result.put("msg",validResult.getMsg());
            result.put("success",false);
        }else{
            result.put("success",true);
        }

        return JsonResult.success(result);
    }

    /**
     * 创建抽检任务
     * @param record
     * @param user
     * @return
     */
    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody StrategyEvaluateVO record, @CurrentUser User user) {

        addValid(record);

        Map<String,Object> result = Maps.newHashMap();
        result.put("success",this.getService().add(record,user));

        return JsonResult.success(result);
    }

    /**
     * 开始标注抽检任务
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/toMark", method = RequestMethod.POST)
    public JsonResult toMark(@Valid @RequestBody StrategyEvaluate record, @CurrentUser User user) {
        return JsonResult.success(this.getService().toMark(record,user));
    }

    /**
     * 评估策略评估任务结果
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/evaluate", method = RequestMethod.POST)
    public JsonResult evaluate(@Valid @RequestBody StrategyEvaluate record, @CurrentUser User user){
        return JsonResult.success(this.getService().evaluate(record,user));
    }

    /**
     * 取消策略评估任务
     * @param strategyTaskReq
     * @param user
     * @return
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public JsonResult cancel(@Valid  @RequestBody StrategyTaskReq strategyTaskReq, @CurrentUser User user) {
        if(strategyTaskReq==null||strategyTaskReq.getTaskId()==0){
            return JsonResult.error("8001","没有传入有效的任务id",null);
        }

        return JsonResult.success(this.getService().cancel(strategyTaskReq.getTaskId(),user));
    }

    /**
     * 获取所有的关注场景(风控事件)列表
     * @return
     */
    @RequestMapping("/getEvents")
    public JsonResult getEvents(){
        List<Event> events = eventService.selectAll();

        List<Map<String,String>> maps = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(events)){
            Map<String,String> map0 = Maps.newHashMap();
            map0.put("code","ALL");
            map0.put("msg","全部事件");

            maps.add(map0);

            for(Event event:events){
                Map<String,String> map = Maps.newHashMap();
                map.put("code",event.getCode()+"");
                map.put("msg",event.getName());

                maps.add(map);
            }
        }

        return JsonResult.success(maps);
    }

    /**
     * 获取所有的规则列表
     * @return
     */
    @RequestMapping("/getAllRules")
    public JsonResult getAllRules(){
        return JsonResult.success(this.getService().loadAllRules());
    }

    /**
     * 获取所有的实时规则列表
     * @return
     */
    @RequestMapping("/getRealRules")
    public JsonResult getRealRules(){
       return JsonResult.success(this.getService().loadRealRules());
    }


    /**
     * 获取所有的异步规则，除了巡检规则
     * @return
     */
    @RequestMapping("/getAsyRules")
    public JsonResult getAsyRules(){
        return JsonResult.success(this.getService().loadAsyRules());
    }



    /**
     * 获取所有的cep规则
     * @return
     */
    @RequestMapping("/getCepRules")
    public JsonResult getCepRules(){
        return JsonResult.success(this.getService().loadCepRules());
    }

    /**
     * 获取所有的离线任务
     * @return
     */
    @RequestMapping("/getOfflineRules")
    public JsonResult getOfflineRules(){

        //todo 11111
        return JsonResult.success(this.getService().loadOfflineRules());
    }

    /**
     * 查询策略评估规则类型列表
     */
    @RequestMapping("/getRuleTypeList")
    public JsonResult getRuleTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(StrategyEvaluateType.class).forEach(item -> {
            if(!item.getCode().equals("UNKNOWN")){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(item.getCode()));
                temp.put("msg", String.valueOf(item.getMsg()));
                list.add(temp);
            }
        });

        return JsonResult.success(list);
    }

    /**
     * 查询抽检任务状态列表
     */
    @RequestMapping("/getTaskStateList")
    public JsonResult getTaskStateList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(CheckTaskState.class).forEach(item -> {
            if(item.getCode()!=0){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(item.getCode()));
                temp.put("msg", String.valueOf(item.getMsg()));
                list.add(temp);
            }
        });
        return JsonResult.success(list);
    }


}

@Data
class StrategyTaskReq implements Serializable {

    private Long taskId;
}
