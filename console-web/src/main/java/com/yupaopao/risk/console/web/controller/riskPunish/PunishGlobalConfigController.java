package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.utils.TreeUtils;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.bean.ConfigTreeModel;
import com.yupaopao.risk.punish.bean.TagInfo;
import com.yupaopao.risk.punish.manage.PunishCmdService;
import com.yupaopao.risk.punish.manage.PunishConfigService;
import com.yupaopao.risk.punish.request.manage.PkgBO;
import com.yupaopao.risk.punish.request.manage.PunishAbilityBO;
import com.yupaopao.risk.punish.request.manage.PunishViolationBO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 惩罚违规类型
 *
 * wangxinqi
 * 2021/4/26 15:04
 */
@Slf4j
@RequestMapping("riskPunish/globalConfig")
@RestController
@AuthRequired(permissionName = "riskPunish:globalConfig:all")
public class PunishGlobalConfigController {
    @DubboReference(timeout = 20000)
    private PunishCmdService punishCmdService;
    @DubboReference(timeout = 20000)
    private PunishConfigService punishConfigService;

    // 获取全量违规类型、惩罚包配置
    @RequestMapping("getAllConfig")
    public JsonResult getAllConfig() {
        List<ConfigTreeModel> list = punishConfigService.getConfigTree(false);
        return JsonResult.success(TreeUtils.toTree(list));
    }

    // 违规类型添加
    @RequestMapping("violationAdd")
    public JsonResult violationAdd(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        punishViolation.setCreator(user.getName());
        punishViolation.setModifier(user.getName());
        return assembleResp(punishConfigService.addViolation(punishViolation));
    }

    // 违规类型编辑
    @RequestMapping("violationEdit")
    public JsonResult violationEdit(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        punishViolation.setModifier(user.getName());
        return assembleResp(punishConfigService.editViolation(punishViolation));
    }

    // 违规类型删除
    @RequestMapping("violationDel/{id}")
    public JsonResult violationDel(@PathVariable("id") Long id, @CurrentUser User user) {
        Response<Void> resp = punishConfigService.delViolation(id, user.getName());
        return assembleResp(resp);
    }

    // 获取违规类型信息
    @RequestMapping("getVioById/{id}")
    public JsonResult getVioById(@PathVariable("id") Long id) {
        return JsonResult.success(punishConfigService.getViolationById(id));
    }

    // 惩罚包添加
    @RequestMapping("pkgAdd")
    public JsonResult pkgAdd(@RequestBody PkgWithAbilities pkgWithAbilities, @CurrentUser User user) {
        PkgBO pkg = pkgWithAbilities.getPkg();
        pkg.setCreator(user.getName());
        pkg.setModifier(user.getName());

        List<PunishAbilityBO> abilities = pkgWithAbilities.getAbilities();
        abilities.forEach(p -> {
            p.setCreator(user.getName());
            p.setModifier(user.getName());
        });

        Response<Long> resp = punishConfigService.addPkgWithAbilities(pkg, abilities);
        return assembleResp(resp);
    }

    // 惩罚包编辑
    @RequestMapping("pkgEdit")
    public JsonResult pkgEdit(@RequestBody PkgWithAbilities pkgWithAbilities, @CurrentUser User user) {
        PkgBO pkg = pkgWithAbilities.getPkg();
        pkg.setModifier(user.getName());

        List<PunishAbilityBO> abilities = pkgWithAbilities.getAbilities();
        abilities.forEach(p -> p.setModifier(user.getName()));

        Response<Void> resp = punishConfigService.editPkgWithAbilities(pkg, abilities);
        return assembleResp(resp);
    }

    // 惩罚包删除
    @RequestMapping("pkgDel/{id}")
    public JsonResult pkgDel(@PathVariable("id") Long id, @CurrentUser User user) {
        Response<Void> resp = punishCmdService.delPkg(id, user.getName());
        return assembleResp(resp);
    }

    // 获取惩罚包信息
    @RequestMapping("getPkgById/{id}")
    public JsonResult getPkgById(@PathVariable("id") Long id) {
        Response<PkgBO> resp = punishCmdService.getPkgById(id);
        return assembleResp(resp);
    }

    // 重置惩罚包顺序
    @RequestMapping("resetPkgPriorities")
    public JsonResult resetPkgPriorities(@RequestBody List<Long> pidList, @CurrentUser User user) {
        Response<Void> resp = punishCmdService.resetPkgPriority(pidList);
        return assembleResp(resp);
    }

    // 获取指定包下的惩罚项列表
    @RequestMapping("getItemsByPkgId/{pid}")
    public JsonResult getItemsByPkgId(@PathVariable("pid") Long pid) {
        Response<List<PunishAbilityBO>> resp = punishCmdService.getAbilityByPid(pid);
        return assembleResp(resp);
    }

    // 获取惩罚项列表
    @RequestMapping("getItems")
    public JsonResult getItems() {
        Response<List<TagInfo>> resp = punishCmdService.getAllAbilities(null);
        return assembleResp(resp);
    }

    private JsonResult assembleResp(Response<?> response) {
        if (response == null) {
            return JsonResult.error(new RiskException("网络异常，请重试"));
        }

        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        } else {
            return JsonResult.error(new RiskException(response.getMsg()));
        }
    }

    @Data
    private static class PkgWithAbilities {
        private PkgBO pkg;
        private List<PunishAbilityBO> abilities;
    }
}
