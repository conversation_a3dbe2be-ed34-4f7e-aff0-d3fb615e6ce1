package com.yupaopao.risk.console.web.controller.analysis;

import com.google.common.collect.Maps;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.service.RiskHitLogService;
import com.yupaopao.risk.console.service.UserImDetailService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/detail/im")
@AuthRequired(permissionName = "system:detailim:all")
public class IMessageController {

    @Autowired
    private UserImDetailService userImDetailService;
    @Autowired
    private ElasticSearchService elasticSearchService;

    @RequestMapping("users")
    public JsonResult users(@RequestBody LogSearchVO.ImUserLogSearchVO vo) {
        return JsonResult.success(userImDetailService.getUsers(vo));
    }

    @RequestMapping("countUsers")
    public JsonResult countUsers(@RequestBody LogSearchVO.ImUserLogSearchVO vo) {
        long value = userImDetailService.countUsers(vo);
        Map<String,Object> result = Maps.newHashMap();
        result.put("total",value);
        return JsonResult.success(result);
    }

    @RequestMapping("content")
    public JsonResult content(@RequestBody LogSearchVO.ImContentLogSearchVO vo) {
        return JsonResult.success(userImDetailService.getContent(vo));
    }

    @RequestMapping("complainReason")
    public JsonResult complainReason(){
        return JsonResult.success(userImDetailService.getComplainReasonList());
    }

    @RequestMapping("apps")
    public JsonResult apps(){
        return JsonResult.success(userImDetailService.getApps());
    }

    @RequestMapping("getRecentApp")
    public JsonResult getRecentApp(@RequestBody LogSearchVO.ImContentLogSearchVO vo){
        vo.setDesc(true);

        return JsonResult.success(userImDetailService.getRecentApp(vo));
    }

    @RequestMapping("executeComplain")
    public JsonResult executeComplain(@RequestBody Map<String,Object> param){
        Map<String,Object> result = userImDetailService.executeComplain(param);
        if("true".equalsIgnoreCase(result.get("isSuccess").toString())){
            return JsonResult.success(true);
        }else{
            return JsonResult.error("8001",result.get("errorMsg").toString(),null);
        }
    }

    @RequestMapping("image")
    public JsonResult image(@RequestBody LogSearchVO.HitLogImageSearchVO vo) {
        return JsonResult.success(userImDetailService.getImage(vo));
    }

    @RequestMapping("extend")
    public JsonResult extend(@RequestBody LogSearchVO.HitLogExtendSearchVO vo) {
        return JsonResult.success(userImDetailService.getExtend(vo));
    }

    @RequestMapping("aggregation")
    public JsonResult extendAgg(@RequestBody LogSearchVO.HitLogAggregationVO vo) {
        Assert.isTrue(vo.getAggregations()!=null&&vo.getAggregations().size()>0,"聚合参数非法");
        return JsonResult.success(userImDetailService.getAggregation(vo));
    }
}

