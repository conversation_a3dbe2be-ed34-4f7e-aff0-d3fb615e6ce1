package com.yupaopao.risk.console.web.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 惩罚标签额外参数配置
 */
@Slf4j
@Component
public class CategoryConfig {

    @ApolloConfig
    private Config config;
    private static List<Map<String, Object>> categoryList = new ArrayList<>();

    @PostConstruct
    public void init() {
        String configKey = "punish.package.categories";
        String jsonArrayCategories = config.getProperty(configKey, "[]");
        freshAllCategories(jsonArrayCategories);
        config.addChangeListener((event) -> {
            if (event.isChanged(configKey)) {
                freshAllCategories(config.getProperty(configKey, "[]"));
            }
        });
    }


    private void freshAllCategories(String categories) {
        JSONArray kvs = JSONObject.parseArray(categories);
        if (CollectionUtils.isEmpty(kvs)) {
            categoryList = new ArrayList<>();
            return;
        }

        List<Map<String, Object>> tempList = new ArrayList<>();
        kvs.forEach(p -> tempList.add((JSONObject) p));
        categoryList = tempList;
    }

    public List<Map<String, Object>> getCategoryList() {
        if (CollectionUtils.isEmpty(categoryList)) {
            return new ArrayList<>();
        }
        return categoryList;
    }
}
