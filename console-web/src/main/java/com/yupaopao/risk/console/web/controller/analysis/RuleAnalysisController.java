package com.yupaopao.risk.console.web.controller.analysis;

import com.yupaopao.risk.common.model.GroupRule;
import com.yupaopao.risk.common.model.RuleRelation;
import com.yupaopao.risk.common.vo.AtomRuleVO;
import com.yupaopao.risk.console.bean.AttributeRuleReq;
import com.yupaopao.risk.console.bean.FactorRuleReq;
import com.yupaopao.risk.console.bean.RiskLogReq;
import com.yupaopao.risk.console.bean.RuleHitQuery;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则详情
 * author: wangxinqi
 * date: 2021/6/29 11:52
 */
@Slf4j
@RestController
@RequestMapping("/detail/rule")
@AuthRequired(permissionName = "system:ruleDetail:all")
public class RuleAnalysisController {

    @Autowired
    private EventService eventService;
    @Autowired
    private GroupRuleService groupRuleService;
    @Autowired
    private AtomRuleService atomRuleService;
    @Autowired
    private AttributeService attributeService;
    @Autowired
    private FactorService factorService;
    @Autowired
    private EventDetailService eventDetailService;
    @Autowired
    private LogService logService;
    @Autowired
    private RiskHitLogService riskHitLogService;
    @Autowired
    private RuleDetailService ruleDetailService;
    @Autowired
    private RuleRelationService ruleRelationService;

    @RequestMapping("/getRules")
    public JsonResult getRules() {
        return JsonResult.success(atomRuleService.simpleAll());
    }

    @RequestMapping("/getEventCount/{ruleId}")
    public JsonResult getEventCount(@PathVariable(value = "ruleId") Long ruleId) {
        return JsonResult.success(ruleDetailService.getEventCount(ruleId));
    }

    @RequestMapping("/getRiskCount")
    public JsonResult getRiskCount(@RequestBody RuleHitQuery ruleHitQuery) {
        return JsonResult.success(ruleDetailService.getRiskCount(ruleHitQuery));
    }

    @RequestMapping("/getRiskUserCount")
    public JsonResult getRiskUserCount(@RequestBody RuleHitQuery ruleHitQuery) {
        return JsonResult.success(ruleDetailService.getRiskUserCount(ruleHitQuery));
    }

    @RequestMapping("/getRiskMobileCount")
    public JsonResult getRiskMobileCount(@RequestBody RuleHitQuery ruleHitQuery) {
        return JsonResult.success(ruleDetailService.getRiskMobileCount(ruleHitQuery));
    }

    @RequestMapping("/detail")
    public JsonResult detail(@RequestBody AtomRuleVO record) {
        return JsonResult.success(ruleDetailService.getRuleDetail(record.getId()));
    }

    @RequestMapping("/searchAttr")
    public JsonResult searchAttr(@RequestBody AttributeRuleReq req) {
        if(req.getRuleId() == null || StringUtils.isBlank(req.getType())){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        return JsonResult.success(attributeService.listByRuleId(req));
    }

    @RequestMapping("/searchFactor")
    public JsonResult searchFactor(@RequestBody FactorRuleReq req) {
        if(req.getRuleId() == null && (null == req.getAttributes() || req.getAttributes().size() == 0)){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        return JsonResult.success(factorService.listByRuleId(req));
    }

    @RequestMapping("/searchRuleGroup")
    public JsonResult searchRuleGroup(@RequestBody AtomRuleVO record) {
        if(record.getId() == null){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        RuleRelation ruleRelation = new RuleRelation();
        ruleRelation.setRuleId(record.getId());
        List<RuleRelation> ruleRelations = ruleRelationService.search(ruleRelation);
        if(CollectionUtils.isNotEmpty(ruleRelations)){
            List<Long> ids = ruleRelations.stream().map(item -> item.getGroupId()).collect(Collectors.toList());
            List<GroupRule> list = groupRuleService.selectByIds(ids);
            return JsonResult.success(groupRuleService.relationFetch(list));
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    @RequestMapping("/searchEvent")
    public JsonResult searchEvent(@RequestBody AtomRuleVO record) {
        if(record.getId() == null) {
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        return JsonResult.success(ruleDetailService.getEvents(record.getId()));
    }

    @RequestMapping("/searchRiskLog")
    public JsonResult searchRiskLog(@RequestBody RiskLogReq req, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(logService.search(req,page,size));
    }
}
