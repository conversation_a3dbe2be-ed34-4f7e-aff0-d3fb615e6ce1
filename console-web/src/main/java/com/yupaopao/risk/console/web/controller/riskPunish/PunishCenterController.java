package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.risk.common.config.AppIdConfig;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.PunishCenterService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/riskPunish/punishCenter")
@AuthRequired(permissionName = "riskPunish:punishCenter:all")
public class PunishCenterController {
    @Autowired
    private PunishCenterService punishCenterService;

    @RequestMapping("getPunishPackages")
    public JsonResult getPunishPackages() {
        return JsonResult.success(punishCenterService.getPunishPackages());
    }

    @RequestMapping("batchPunish")
    public JsonResult batchPunish(@RequestBody PunishCenterService.PunishCenterRequest punishCenterRequest, @CurrentUser User user) {
        return JsonResult.success(punishCenterService.batchPunish(user, punishCenterRequest));
    }

    @RequestMapping("showNo2Uid")
    public JsonResult showNo2Uid(@RequestBody PunishCenterService.ShowNo2UidRequest request, @CurrentUser User user) {
        return JsonResult.success(punishCenterService.showNo2Uid(request));
    }

    @RequestMapping("getApps")
    public JsonResult getApps() {
        List<AppIdConfig.App> apps = new ArrayList(16);
        AppIdConfig.getAppIdMap().forEach((k, v) -> apps.add(v));
        apps.sort((r1, r2) -> r1.getAppId() > r2.getAppId() ? 1 : -1);
        return JsonResult.success(apps);
    }
}
