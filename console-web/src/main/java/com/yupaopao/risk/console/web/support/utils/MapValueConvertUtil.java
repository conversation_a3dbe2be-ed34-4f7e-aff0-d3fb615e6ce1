package com.yupaopao.risk.console.web.support.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2019/12/7 11:47
 */
@Slf4j
public class MapValueConvertUtil {

    public static void convertValueToString(Map<String,Object> map,String ... fields){
        if(fields == null || fields.length==0){
            return;
        }
        for(String field : fields){
            String[] keys = field.split("\\.");
            int i = 0;
            Map<String,Object> temp = map;
            for(String key : keys){
                i++;
                Object value = temp.get(key);
                if(null == value){
                    break;
                }
                if(i == keys.length){
                    temp.put(key,value.toString());
                }else{
                    if(value instanceof Map){
                        temp = (Map<String, Object>) value;
                    }else{
                        Map<String,Object> m = obj2Map(value);
                        temp.put(key,m);
                        temp = m;
                    }
                }
            }
        }
    }

    public static void convertValueToString(List<Map<String,Object>> list, String ... fields){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(Map<String,Object> m : list){
            convertValueToString(m,fields);
        }
    }

    public static void encryptPersonalInfo(List<Map<String,Object>> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        for(Map<String,Object> m : list){
            if(m.containsKey("data")){
                Map data = (Map)m.get("data");
                if(MapUtils.isNotEmpty(data)){
                    data.remove("userName");
                    data.remove("idCard");
                    data.remove("imgProfile");
                    data.remove("imgIdCard");
                    data.remove("imgBackIdCard");

                    if(data.containsKey("userMobile")){
                        Object userMobile = data.get("userMobile");
                        if(userMobile!=null){
                            Map userMobileMap = JSONObject.parseObject(JSONObject.toJSONString(userMobile),Map.class);
                            userMobileMap.remove("mobile");
                            data.put("userMobile",userMobileMap);
                        }

                    }

                    m.put("data",data);
                }
            }

            if(m.containsKey("content")){
                Map content = (Map)m.get("content");
                if(MapUtils.isNotEmpty(content)){
                    content.remove("userName");
                    content.remove("idCard");
                    content.remove("imgProfile");
                    content.remove("imgIdCard");
                    content.remove("imgBackIdCard");
                    content.remove("imgIdBack");
                    content.remove("mobile");
                    content.remove("name");
                    content.remove("certNo");

                    m.put("content",content);
                }
            }

            if(m.containsKey("returnMap")){
               Map returnMap = JSON.parseObject(m.get("returnMap").toString(),Map.class);
               if(MapUtils.isNotEmpty(returnMap)){
                   returnMap.remove("Mobile");
                   returnMap.remove("userName");
                   returnMap.remove("idCard");
                   returnMap.remove("imgProfile");
                   returnMap.remove("imgIdCard");
                   returnMap.remove("imgBackIdCard");

                   m.put("returnMap",JSONObject.toJSONString(returnMap));
               }
            }

            if(m.containsKey("audits")){
                List<Map<String,Object>> audits = (List<Map<String, Object>>) m.get("audits");
                if(CollectionUtils.isNotEmpty(audits)){
                    for(Map<String,Object> audit:audits){
                        if(audit.containsKey("content")){
                            Map<String,Object> content = (Map<String,Object>)audit.get("content");
                            if(MapUtils.isNotEmpty(content)){
                                content.remove("userName");
                                content.remove("idCard");
                                content.remove("imgProfile");
                                content.remove("imgIdCard");
                                content.remove("imgBackIdCard");

                                audit.put("content",content);
                            }
                        }
                    }
                }
            }

        }

    }

    public static void encryptMobile(List<Map<String,Object>> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        for(Map<String,Object> m : list){
            if(m.containsKey("data")){
                Map data = (Map)m.get("data");
                if(MapUtils.isNotEmpty(data)){
                    data.remove("Mobile");
                    m.put("data",data);
                }
            }
        }
    }

    public static void obtainReason(List<Map<String,Object>> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }

        for(Map<String,Object> m:list){
           if(m.containsKey("result")){
               Map<String,Object> result = (Map<String, Object>) m.get("result");
               m.put("reason",result.get("ruleName")!=null?String.valueOf(result.get("ruleName")):(result.get("reason")!=null?String.valueOf(result.get("reason")):""));
           }
        }
    }

    public static Map<String,Object> obj2Map(Object obj){
        Map<String,Object> map =new HashMap<String, Object>();
        Field[] fields = obj.getClass().getDeclaredFields();
        for(int i = 0;i < fields.length;i++){
            String varName = fields[i].getName();
            try{
                boolean accessFlag = fields[i].isAccessible();
                fields[i].setAccessible(true);
                Object o = fields[i].get(obj);
                if(o != null){
                    map.put(varName, o);
                }
                fields[i].setAccessible(accessFlag);
            }catch(IllegalArgumentException ex){
                log.info("obj to map error");
            }catch(IllegalAccessException ex){
                log.info("obj to map error");
            }
        }
        return map;
    }
}
