package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.enums.RuleStatus;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.bean.AtomRuleDTO;
import com.yupaopao.risk.console.bean.ConflictRulesCheckReq;
import com.yupaopao.risk.console.service.AllPunishService;
import com.yupaopao.risk.console.service.AtomRuleService;
import com.yupaopao.risk.console.service.RiskLabelsService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/rule/atom")
@AuthRequired(permissionName = "core:atomrule:all")
public class AtomRuleController extends AbstractModelController<AtomRule, AtomRuleDTO, AtomRuleService> {

    @Autowired
    private AllPunishService allPunishService;
    @Autowired
    private RiskLabelsService riskLabelsService;

    @Override
    protected void addValid(AtomRuleDTO record, User user) {
        record.setModifier(user.getName());
        record.setAuthor(user.getName());
        if (CollectionUtils.isNotEmpty(record.getPunishPackages())) {
            List<Long> punishPackageIds = record.getPunishPackages().stream().map(punishPkgVO -> punishPkgVO.getCode()).collect(Collectors.toList());
            record.setPunishPackageIds(StringUtils.join(punishPackageIds, ","));
        } else {
            record.setPunishPackageIds("");
            record.setPunishPackageAttr("");
        }
    }

    @Override
    protected void updateValid(AtomRuleDTO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        record.setModifier(user.getName());
        record.setAuthor(null);
        if (CollectionUtils.isNotEmpty(record.getPunishPackages())) {
            List<Long> punishPackageIds = record.getPunishPackages().stream().map(punishPkgVO -> punishPkgVO.getCode()).collect(Collectors.toList());
            record.setPunishPackageIds(StringUtils.join(punishPackageIds, ","));
        } else {
            record.setPunishPackageIds("");
            record.setPunishPackageAttr("");
        }
    }

    @RequestMapping(value = "/changeStatus/{id}", method = RequestMethod.POST)
    public JsonResult updateStatus(@PathVariable("id") long id, @Param("status") String status, @CurrentUser User user) {
        return JsonResult.success(getService().changeStatus(id, RuleStatus.nameOf(status),user));
    }

    @RequestMapping("/search")
    public JsonResult search(@RequestBody AtomRuleDTO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo result = getService().search(record, page, size);
        List<AtomRuleDTO> dtoList = getService().relationFetch(result.getList());
        result.setList(dtoList);
        return JsonResult.success(result);
    }

    @RequestMapping("/getConstFormatter")
    public JsonResult getRiskConst(@Param("dependent") String dependent, @Param("id") Long id) {
        Map<String, Object> map = getService().getConstFormatter(dependent, id);
        if (null == map) {
            return JsonResult.success();
        }
        return JsonResult.success(JSON.toJSONString(map));
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody AtomRuleDTO record, @CurrentUser User user) {
        addValid(record, user);
        //为了去掉多余的空格
        if (StringUtils.isNotBlank(record.getRiskConst())) {
            JSONObject json = JSON.parseObject(record.getRiskConst());
            record.setRiskConst(JSON.toJSONString(json));
        }
        if (StringUtils.isNotBlank(record.getPunishPackageAttr())) {
            JSONArray json = JSONArray.parseArray(record.getPunishPackageAttr());
            record.setPunishPackageAttr(JSON.toJSONString(json));
        }
        return JsonResult.success(getService().insertSelective(record));
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody AtomRuleDTO record, @CurrentUser User user) {
        updateValid(record, user);
        //为了去掉多余的空格
        if (StringUtils.isNotBlank(record.getRiskConst())) {
            JSONObject json = JSON.parseObject(record.getRiskConst());
            record.setRiskConst(JSON.toJSONString(json));
        }
        if (StringUtils.isNotBlank(record.getPunishPackageAttr())) {
            JSONArray json = JSONArray.parseArray(record.getPunishPackageAttr());
            record.setPunishPackageAttr(JSON.toJSONString(json));
        }
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping(value = "/conflictRulesCheck", method = RequestMethod.POST)
    public JsonResult conflictRulesCheck(@RequestBody ConflictRulesCheckReq req) {
        Assert.notNull(req.getType(), "请指定检查类型");
        if (req.getType() == ConflictRulesCheckReq.CheckType.ATOM_RULE.getType()) {
            Assert.notNull(req.getAtomRuleVO(), "规则参数必填");
        }
        if (req.getType() == ConflictRulesCheckReq.CheckType.GROUP_RULE.getType()) {
            Assert.notNull(req.getGroupRuleVO(), "规则组参数必填");
        }
        if (req.getType() == ConflictRulesCheckReq.CheckType.EVENT.getType()) {
            Assert.notNull(req.getEventVO(), "事件参数必填");
        }
        return JsonResult.success(getService().conflictRulesCheck(req));
    }

    @RequestMapping(value = "/packagePunishFields", method = RequestMethod.POST)
    public JsonResult conflictRulesCheck() {
        final List<String> list = new ArrayList<>();
        ReflectionUtils.doWithFields(BatchPunishRequest.class, (Field field) -> {
            list.add(field.getName());
        });
        list.remove("serialVersionUID");
        list.remove("bizId");
        list.remove("channel");
        list.remove("packageId");
        list.remove("operator");
        list.add("requestKeys");
        Map<String, String> fieldsResult = Maps.newHashMapWithExpectedSize(1);
        fieldsResult.put("fields", StringUtils.join(list, ","));
        return JsonResult.success(fieldsResult);
    }

    @RequestMapping("/getPackagesAndObjectTypes")
    public JsonResult getPackagesAndObjectTypes() {
        return JsonResult.success(allPunishService.listPackages());
    }

    @RequestMapping("/getRiskSubLabels/{type}")
    public JsonResult getRiskSubLabels(@PathVariable Integer type) {
        return JsonResult.success(riskLabelsService.getRiskSubLabels(type));
    }
}
