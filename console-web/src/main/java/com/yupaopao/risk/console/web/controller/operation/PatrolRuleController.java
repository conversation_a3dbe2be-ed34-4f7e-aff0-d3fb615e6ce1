package com.yupaopao.risk.console.web.controller.operation;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.PatrolRuleStateEnum;
import com.yupaopao.risk.common.enums.SupportRuleType;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.PatrolRuleService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.PatrolRuleVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *  巡检规则
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("operation/patrolRule")
@AuthRequired(permissionName = "operation:patrol:rule")
public class PatrolRuleController extends AbstractModelController<PatrolRule, PatrolRuleVO, PatrolRuleService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;

    @Override
    protected void addValid(PatrolRuleVO record, User user) {
        record.setType(SupportRuleType.PATROL_RULE.getCode());
        record.setModifier(user.getName());
        record.setAuthor(user.getName());
    }

    @Override
    protected void updateValid(PatrolRuleVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        record.setModifier(user.getName());
        record.setAuthor(null);
    }

    protected void deleteValid(PatrolRule record, User user){
        Assert.notNull(record.getId(),"请指定记录ID");
        record.setState(PatrolRuleStateEnum.DELETE.getCode());
        record.setModifier(user.getName());
        record.setAuthor(null);
    }

    /**
     * 获取 惩罚包 下拉框中内容
     * @return
     */
    @RequestMapping("/getPunishPackages")
    public JsonResult getPunishPackages(){
        return JsonResult.success(getService().getPunishPackages(SupportRuleType.PATROL_RULE));
    }

    @Override
    @RequestMapping("/search")
    public JsonResult search(@RequestBody PatrolRuleVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        try{
            record.setType(SupportRuleType.PATROL_RULE.getCode());
            PageInfo response = getService().search(record, page, size);

//            if(response!=null){
//
//                List<PatrolRule> patrolRules = response.getList();
//                if(CollectionUtils.isNotEmpty(patrolRules)){
//                    List<PatrolRuleVO> patrolRuleVOS = new ArrayList<>();
//
//                    for(PatrolRule patrolRule:patrolRules){
//                        PatrolRuleVO patrolRuleVO = new PatrolRuleVO();
//
//                        BeanUtils.copyProperties(patrolRuleVO,patrolRule);
//                        if(StringUtils.isNotBlank(patrolRule.getParameters())){
//                            JSONObject jsonObject = JSON.parseObject(patrolRule.getParameters());
//                            if((jsonObject.get("bizId")!=null)&&StringUtils.isNotBlank(jsonObject.get("bizId").toString())){
//                                patrolRuleVO.setBizId(jsonObject.get("bizId").toString());
//                            }
//
//                            if((jsonObject.get("briefDesc")!=null)&&StringUtils.isNotBlank(jsonObject.get("briefDesc").toString())){
//                                patrolRuleVO.setBriefDesc(jsonObject.get("briefDesc").toString());
//                            }
//
//                            if((jsonObject.get("fromUid")!=null)&&StringUtils.isNotBlank(jsonObject.get("fromUid").toString())){
//                                patrolRuleVO.setFromUid(jsonObject.get("fromUid").toString());
//                            }
//
//                            if((jsonObject.get("targetId")!=null)&&StringUtils.isNotBlank(jsonObject.get("targetId").toString())){
//                                patrolRuleVO.setTargetId(jsonObject.get("targetId").toString());
//                            }
//                        }
//
//                        patrolRuleVOS.add(patrolRuleVO);
//                    }
//
//                    response.setList(patrolRuleVOS);
//
//                    return JsonResult.success(response);
//                }
//            }

            return JsonResult.success(response);
        }catch(Exception exp){
            log.error("查询巡检规则报异常,ErrorMsg:{}",exp.getMessage());
            return JsonResult.error("8002","查询巡检规则报异常",exp.getMessage());
        }

    }

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody PatrolRuleVO record, @CurrentUser User user) {
        addValid(record, user);

//        Map<String,Object> parameterMap = new HashMap<>(4);
//
//        if(StringUtils.isNotBlank(record.getBizId())){
//            parameterMap.put("bizId",record.getBizId());
//        }
//
//        if(StringUtils.isNotBlank(record.getBriefDesc())){
//            parameterMap.put("briefDesc",record.getBriefDesc());
//        }
//
//        if(StringUtils.isNotBlank(record.getFromUid())){
//            parameterMap.put("fromUid",record.getFromUid());
//        }
//
//        if(StringUtils.isNotBlank(record.getTargetId())){
//            parameterMap.put("targetId",record.getTargetId());
//        }
//
//        record.setParameters(JSONObject.toJSONString(parameterMap));

        return JsonResult.success(getService().insertSelective(record));
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody PatrolRuleVO record, @CurrentUser User user) {
        updateValid(record, user);
//        Map<String,Object> parameterMap = new HashMap<>(4);
//
//        if(StringUtils.isNotBlank(record.getBizId())){
//            parameterMap.put("bizId",record.getBizId());
//        }
//
//        if(StringUtils.isNotBlank(record.getBriefDesc())){
//            parameterMap.put("briefDesc",record.getBriefDesc());
//        }
//
//        if(StringUtils.isNotBlank(record.getFromUid())){
//            parameterMap.put("fromUid",record.getFromUid());
//        }
//
//        if(StringUtils.isNotBlank(record.getTargetId())){
//            parameterMap.put("targetId",record.getTargetId());
//        }
//
//        record.setParameters(JSONObject.toJSONString(parameterMap));
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping(value = "/virtualDelete", method = RequestMethod.POST)
    public JsonResult delete(@Valid @RequestBody PatrolRule record, @CurrentUser User user) {
        deleteValid(record,user);
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping(value = "/getPatrolRuleStates", method = RequestMethod.POST)
    public JsonResult getPatrolRuleStates() {
        return JsonResult.success(getService().getPatrolRuleStates());
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild rule engine , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_MAGIC)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.PATROL_RULE_RELOAD_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger patrol rule rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

}
