package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Maps;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.console.api.RiskAppealService;
import com.yupaopao.risk.console.bean.RiskGrayDimensionEnum;
import com.yupaopao.risk.console.bean.RiskGrayTypeEnum;
import com.yupaopao.risk.console.bean.RiskResult;
import com.yupaopao.risk.console.service.GrayListService;
import com.yupaopao.risk.shoot.api.RiskListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2020/4/28 14:13
 */
@Slf4j
@Component
@Service
public class RiskAppealServiceImpl implements RiskAppealService {

    private static final String RISK_PASS = "PASS";
    private static final String RISK_FACTOR_OPERATE_TOPIC = "risk_factor_operate";
    private static final String RISK_FACTOR_OPERATE_CONFIG_PREFIX = "risk.factor.operate.";

    @Value("${appeal.login.groupIds:}")
    private String groupIds;
    @Value("${appeal.login.white.groupId:}")
    private String whiteGroupId;
    @Value("${appeal.login.factorIds:}")
    private String factorIds;
    @Value("${appeal.login.whiteRiskGrayDays:3}")
    private Integer whiteRiskGrayDays;
    @Value("${appeal.login.black.groupId:}")
    private String blackGroupId;
    @Value("${appeal.login.blackRiskGrayDays:60}")
    private Integer blackRiskGrayDays;


    @Reference(timeout = 2000)
    private RiskListService riskListService;
    @KafkaAutowired("middleware.kafka.risk")
    private KafkaProducer kafkaProducer;
    @ApolloConfig
    private Config config;
    @Autowired
    private GrayListService grayListService;

    @Override
    public RiskResult loginAppeal(Map<String,Object> params) {
        log.info("风控登录申诉处理开始,params:{}",params);
        if(MapUtils.isEmpty(params)){
            log.info("风控登录申诉处理参数为空");
            return RiskResult.error("风控登录申诉处理参数为空");
        }
        Long uid = MapUtils.getLong(params,"uid");
        if(null == uid){
            log.info("风控登录申诉处理uid为空");
            return RiskResult.error("风控登录申诉处理uid为空");
        }
        if(StringUtils.isNotBlank(groupIds)){
            String[] groupIdArray = groupIds.split(",");
            try{
                for(String groupId : groupIdArray){
                    GrayList grayList = new GrayList();
                    grayList.setType(RiskGrayTypeEnum.BLACK.getCode());
                    grayList.setValue(uid.toString());
                    grayList.setGroupId(Long.parseLong(groupId));
                    grayList.setDimension(RiskGrayDimensionEnum.USERID.getCode());
                    List<GrayList> result = this.grayListService.select(grayList);
                    if(CollectionUtils.isEmpty(result)){
                        continue;
                    }
                    for(GrayList record : result){
                        this.riskListService.delete(record);
                    }
                }
            }catch (Exception e){
                log.error("调用删除黑名单出错,uid:{}",uid,e);
                return RiskResult.error("风控登录申诉处理删除黑名单出错");
            }
        }
        if(StringUtils.isNotBlank(whiteGroupId)){
            try {
                GrayList grayList = new GrayList();
                grayList.setType(RiskGrayTypeEnum.WHITE.getCode());
                grayList.setDimension(RiskGrayDimensionEnum.USERID.getCode());
                grayList.setExpireTime(DateUtils.addDays(new Date(), whiteRiskGrayDays));
                grayList.setValue(uid+"");
                grayList.setGroupId(Long.parseLong(whiteGroupId));
                grayList.setAuthor("system");
                grayList.setComment("登录申诉成功添加白名单");
                if(!riskListService.saveOrUpdate(grayList)){
                    return RiskResult.error("风控登录申诉处理添加白名单出错");
                }else{
                    log.info("保存名单成功！{}",grayList);
                }
            }catch (Exception e){
                log.error("调用添加白名单出错,uid:{}",uid,e);
                return RiskResult.error("风控登录申诉处理添加白名单出错");
            }
        }
        if(StringUtils.isNotBlank(blackGroupId)){
            try {
                GrayList grayList = new GrayList();
                grayList.setType(RiskGrayTypeEnum.BLACK.getCode());
                grayList.setDimension(RiskGrayDimensionEnum.USERID.getCode());
                grayList.setExpireTime(DateUtils.addDays(new Date(), blackRiskGrayDays));
                grayList.setValue(uid+"");
                grayList.setGroupId(Long.parseLong(blackGroupId));
                grayList.setAuthor("system");
                grayList.setComment("账号标记处理，未实际处罚");
                if(!riskListService.saveOrUpdate(grayList)){
                    return RiskResult.error("风控登录申诉处理添加黑名单出错");
                }else{
                    log.info("保存名单成功！{}",grayList);
                }
            }catch (Exception e){
                log.error("调用添加黑名单出错,uid:{}",uid,e);
                return RiskResult.error("风控登录申诉处理添加黑名单出错");
            }
        }
        Map<String,Object> operateParams = buildFactorParam(params);
        if(MapUtils.isNotEmpty(operateParams)){
            String factorParams = JSON.toJSONString(operateParams);
            ListenableFuture<SendResult<String, String>> dataFuture = kafkaProducer.send(RISK_FACTOR_OPERATE_TOPIC,uid.toString(), factorParams);
            dataFuture.addCallback(stringStringSendResult -> log.trace("登录申诉清理累积因子消息发送成功"), throwable -> log.error("登录申诉清理累积因子消息发送失败:{}", factorParams));
        }
        log.info("风控登录申诉处理结束,params:{}",params);
        return RiskResult.success();
    }
    private Map<String,Object> buildFactorParam(Map<String,Object> params){
        if(StringUtils.isBlank(factorIds)){
            return null;
        }
        Map<String,Object> operateParams = Maps.newHashMap();
        Map<String,Object> dataPrams = Maps.newHashMap();
        String[] factorIdArray = factorIds.split(",");
        List<Map<String,Object>> factorList = Lists.newArrayList();
        for(String factorId : factorIdArray){
            String mapping = config.getProperty(RISK_FACTOR_OPERATE_CONFIG_PREFIX+factorId,"");
            if(StringUtils.isNotBlank(mapping)){
                Map<String,Object> factorMap = Maps.newHashMap();
                factorMap.put("factorId",Long.parseLong(factorId));
                factorList.add(factorMap);
                List<FactorMapping> list = JSONArray.parseArray(mapping,FactorMapping.class);
                list.forEach(factorMapping -> {
                    dataPrams.put(factorMapping.getKey(),MapUtils.getString(params,factorMapping.getParamKey()));
                });
            }
        }
        operateParams.put("action","RESET");
        operateParams.put("factor",factorList);
        operateParams.put("params",dataPrams);
        return operateParams;
    }

    public static class FactorMapping implements Serializable {

        private static final long serialVersionUID = 8880984755198869028L;

        private String key;
        private String paramKey;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getParamKey() {
            return paramKey;
        }

        public void setParamKey(String paramKey) {
            this.paramKey = paramKey;
        }
    }
}
