package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.FeedbackBO;
import com.yupaopao.risk.console.bean.FeedbackResult;
import com.yupaopao.risk.console.bean.ShumeiFeedBackType;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.service.ThirdService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 三方反馈接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping(value = "/third/feedback/")
@AuthRequired(permissionName = "third:feedback:all")
public class FeedbackController {
    private static final String LABEL_PREFIX = "feedback.labels.";

    private Map<String, Map<String, List<LabelObject>>> channelFunctionLabelMap = new HashMap<>(16);

    @ApolloConfig
    private Config config;

    @Autowired
    private ThirdService thirdService;

    @Autowired
    private AttributeService attributeService;

    @PostMapping("/getAttrSourceType")
    public JsonResult getAttrSourceType(@RequestBody List<String> attrNames) {
        List<Attribute> attributes = this.attributeService.queryRemote(attrNames);
        if (!CollectionUtils.isEmpty(attributes)) {
            Map<String, String> attrMap = attributes.stream().collect(Collectors.toMap(Attribute::getName, p -> {
                ShumeiFeedBackType shumeiFeedBackType = this.deduceFeedbackType(p.getFunction());
                return shumeiFeedBackType == null ? "" : shumeiFeedBackType.name();
            }));
            return JsonResult.success(attrMap);
        }
        return JsonResult.success(new HashMap<>(2));
    }

    /**
     * 获取漏杀标签
     *
     * @param channel    三方名称
     * @param sourceType 反馈类型
     * @return kv数组
     */
    @RequestMapping("/getMissRejectLabels")
    public JsonResult getMissRejectLabels(@RequestParam(value = "channel") String channel, @RequestParam(value = "sourceType") String sourceType) {
        Map<String, List<LabelObject>> stringListMap = channelFunctionLabelMap.getOrDefault(channel.toLowerCase(), new HashMap<>());
        List<LabelObject> labelObjects = stringListMap.get(sourceType.toLowerCase());
        if (CollectionUtils.isEmpty(labelObjects)) {
            return JsonResult.error(new RiskException("该属性暂不支持三方反馈喔"));
        } else {
            return JsonResult.success(labelObjects);
        }
    }

    private ShumeiFeedBackType deduceFeedbackType(String function) {
        if (function.contains("image")) {
            // 图片反馈
            return ShumeiFeedBackType.IMAGE;
        } else if (function.contains("text")) {

            // 文本反馈
            return ShumeiFeedBackType.TEXT;
        } else if (function.contains("audio")) {
            // 音频检测// 只有易盾可以？
            return ShumeiFeedBackType.AUDIO;
        } else if (function.contains("video")) {
            // 视频检测
            return ShumeiFeedBackType.IMAGE;
        } else {
            // 暂不支持的检测类型
            return null;
        }
    }

    @PostMapping("/feedback")
    public JsonResult feedback(@RequestBody FeedbackRequest request, @CurrentUser User user) {
        log.info("三方反馈请求入参: {}", request);
        // 参数校验，不支持反馈直接反馈错误
        String channel = request.getChannel();
        log.info("检测到[{}]的反馈请求", channel);
        FeedbackBO fbb = new FeedbackBO();
        fbb.setChannel(request.getChannel());
        String sourceType = request.getSourceType();
        ShumeiFeedBackType shumeiFeedBackType = ShumeiFeedBackType.get(sourceType);
        if (ShumeiFeedBackType.AUDIO.equals(shumeiFeedBackType)) {
            // 音频检测// 只有易盾可以？
            return JsonResult.error(new RiskException("暂不支持音频检测类型"));
        }

        if (shumeiFeedBackType != null) {
            fbb.setSourceType(shumeiFeedBackType.name());
        } else {
            // 暂不支持的检测类型
            return JsonResult.error(new RiskException("暂不支持的检测类型"));
        }
        fbb.setContent(request.getContent());
        fbb.setCheckDetail(request.getCheckDetail());
        fbb.setAttributeName(request.getAttributeName());
        fbb.setTargetLabel(request.getTargetLabel());
        fbb.setTargetLevel(request.getTargetLevel());
        fbb.setThirdRequestIds(request.getThirdRequestIds());
        fbb.setThirdTimeStamp(request.getThirdTimeStamp());
        fbb.setTraceId(request.getTraceId());
        fbb.setData(request.getExtData());
        FeedbackResult feedback = thirdService.feedback(fbb, user);
        return feedback.isSuccess() ? JsonResult.success() : JsonResult.error(new RiskException(feedback.getMessage()));
    }

    @PostConstruct
    private void init() {
        parseLabels(null);
        config.addChangeListener(this::parseLabels);
    }

    private void parseLabels(ConfigChangeEvent event) {
        Set<String> propertyNames;
        if (event != null) {
            propertyNames = event.changedKeys();
        } else {
            propertyNames = config.getPropertyNames();
        }

        // 配置格式如下
        //        feedback.labels.shumei = {
        //            "image": [
        //                {"name": "涉政", "value": 200},
        //                {"name": "色情", "value": 300}
        //            ]
        //        }
        propertyNames.stream().filter(p -> p.startsWith(LABEL_PREFIX)).forEach(p -> {
            String channel = p.substring(LABEL_PREFIX.length());
            String property = config.getProperty(p, "{}");
            Map<String, List<LabelObject>> stringListMap = JSONObject.parseObject(property, new TypeReference<Map<String, List<LabelObject>>>() {});
            channelFunctionLabelMap.put(channel, stringListMap);
        });
    }

    @Data
    private static class LabelObject {
        private String name;
        private String value;
    }

    @Data
    private static class FeedbackRequest {
        private String attributeName;
        private String channel;
        private String sourceType;
        private String content;
        private Map<String, Object> checkDetail;
        /**
         * 欲纠正成的目标级别
         */
        private String targetLevel;
        /**
         * 欲纠正成的目标标签
         */
        private String targetLabel;

        private List<String> thirdRequestIds;
        private String thirdTimeStamp;
        private String traceId;
        private Map<String, Object> extData;
    }
}
