package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.bean.TagInfo;
import com.yupaopao.risk.punish.manage.PunishCmdService;
import com.yupaopao.risk.punish.request.manage.PkgBO;
import com.yupaopao.risk.punish.request.manage.PunishAbilityBO;
import com.yupaopao.risk.punish.request.manage.PunishViolationBO;
import com.yupaopao.risk.punish.request.manage.PunishViolationReasonBO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * Description
 *
 * <AUTHOR>
 * @since Version
 * <p>
 * 2021/1/14 15:42
 */
@Slf4j
@RequestMapping("/punish")
@RestController
//@AuthRequired(permissionName = "core:punish:all")
public class PunishGatewayController {

    @DubboReference(timeout = 50000)
    private PunishCmdService cmdService;
    @Autowired
    private RedisService redisService;

    /**
     * 获取全量违规类型
     */
    @RequestMapping("/violation/list")
    public JsonResult getAllViolations() {
        Response<List<PunishViolationBO>> resp = this.cmdService.getAllViolations();
        return assembleResp(resp);
    }

    @RequestMapping("/violation/add")
    public JsonResult addViolation(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        return JsonResult.success();
    }

    @RequestMapping("/violation/generate")
    public JsonResult generateViolation(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        Response<List<PunishViolationBO>> resp = this.cmdService.generateViolationByParentIdAndGrade(punishViolation.getParentId(), punishViolation.getGrandId(), punishViolation.getGrade(), user.getName());
        return assembleResp(resp);
    }

    @RequestMapping("/violation/gen")
    public JsonResult genViolation(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        Response<PunishViolationBO> resp = this.cmdService.genViolationByParentIdAndGrade(punishViolation.getParentId(), punishViolation.getGrandId(), punishViolation.getGrade(), user.getName());
        return assembleResp(resp);
    }

    @RequestMapping("/violation/del")
    public JsonResult delViolation(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        punishViolation.setModifier(user.getName());
        Response<Void> resp = this.cmdService.delViolation(punishViolation);
        return assembleResp(resp);
    }

    @RequestMapping("/violation/edit")
    public JsonResult editViolation(@RequestBody PunishViolationBO punishViolation, @CurrentUser User user) {
        punishViolation.setModifier(user.getName());
        Response<Void> resp = this.cmdService.editViolation(punishViolation);
        return assembleResp(resp);
    }

    /**
     * 获取指定标签下的惩罚包
     */
    @RequestMapping("/pkg/refresh_by_vid")
    public JsonResult getPkgsByVid(@RequestParam(value = "vid") Long vid) {
        Response<Map<Integer, List<PkgBO>>> resp = this.cmdService.getPkgsWithLevelAndPriority(vid);
        return assembleResp(resp);
    }

    /**
     * 添加惩罚包
     */
    @RequestMapping("/pkg/add")
    public JsonResult addPkg(@RequestBody PunishPkg pkg, @CurrentUser User user) {
        // TODO 添加后，优先级最低
        // 触发钩子hook, 调整顺序
        return JsonResult.success();
    }

    /**
     * 编辑惩罚包
     */
    @RequestMapping("/pkg/edit_with_abilities")
    public JsonResult editPkgWithAbilities(@RequestBody PkgWithAbilities pkgWithAbilities, @CurrentUser User user) {
        PkgBO pkg = pkgWithAbilities.getPkg();
        pkg.setModifier(user.getName());

        List<PunishAbilityBO> abilities = pkgWithAbilities.getAbilities();
        abilities.forEach(p -> p.setModifier(user.getName()));

        Response<Void> resp = this.cmdService.editPkgWithAbilities(pkg, abilities);
        return assembleResp(resp);
    }

    @RequestMapping("/pkg/generate")
    public JsonResult genPkg(@RequestParam("vid") Long vid, @RequestParam("grade") Integer grade, @CurrentUser User user) {
        Response<Void> resp = this.cmdService.addPkgByVidAndGrade(vid, grade, user.getName());
        // TODO 添加后，优先级最低
        // 触发钩子hook, 调整顺序
        return assembleResp(resp);
    }

    /**
     * 删除惩罚包
     */
    @RequestMapping("/pkg/del")
    public JsonResult delPkg(@RequestBody PunishPkg pkg, @CurrentUser User user) {
        if (pkg == null) {
            return JsonResult.error(new RiskException("未获取到惩罚包ID, 请重试"));
        }

        Response<Void> resp = this.cmdService.delPkg(pkg.getId(), user.getName());
        return assembleResp(resp);
    }

    /**
     * 重置惩罚包顺序
     */
    @RequestMapping("/pkg/resetPriorities")
    public JsonResult resetPkgPriorities(@RequestBody List<Long> pidList, @CurrentUser User user) {
        Response<Void> resp = this.cmdService.resetPkgPriority(pidList);
        return assembleResp(resp);
    }

    /**
     * 获取惩罚能力
     */
    @RequestMapping("/ability/list_by_pid")
    public JsonResult getAbilityByPid(@RequestParam("pid") Long pid) {
        Response<List<PunishAbilityBO>> resp = this.cmdService.getAbilityByPid(pid);
        return assembleResp(resp);
    }

    /**
     * 获取惩罚能力
     */
    @RequestMapping("/ability/metadata")
    public JsonResult getAbilityMetadata() {
        Response<List<TagInfo>> resp = this.cmdService.getAllAbilities(null);
        return assembleResp(resp);
    }

    /**
     * 获取订阅的违规类型IDs
     */
    @RequestMapping("/subscribe_violation/list_vid")
    public JsonResult getViolationSubscribed(@RequestBody PunishSubscribeViolation psv) {
        Response<List<Long>> resp = this.cmdService.listSubscribedVids(psv.getChannel());
        return assembleResp(resp);
    }

    @RequestMapping("/subscribe_violation/add")
    public JsonResult addVioSubscribe(@RequestBody PunishSubscribeViolation psv, @CurrentUser User user) {
        Response<Void> resp = this.cmdService.addVioSubscribe(psv.getChannel(), psv.getVid(), user.getName());
        return assembleResp(resp);
    }

    @RequestMapping("/subscribe_violation/del")
    public JsonResult delVioSubscribe(@RequestBody PunishSubscribeViolation psv, @CurrentUser User user) {
        Response<Long> resp = this.cmdService.delVioSubscribe(psv.getChannel(), psv.getVid(), user.getName());
        Long id = resp.getResult();
        if (id != null && id > 0) {
            String key = "t_punish_subscribe_violation" + id;
            redisService.set(key, user.getName(), 3600L);
        }
        return assembleResp(resp);
    }

    @RequestMapping("/subscribe_pkg/add")
    public JsonResult addPkgSubscribe(@RequestParam("channel") String channel,
                                      @RequestParam("vid") Long vid,
                                      @RequestParam("pid") Long pid,
                                      @RequestParam("grade") Integer grade,
                                      @CurrentUser User user) {
        Response<Void> resp = this.cmdService.addPkgSubscribe(channel, pid, vid, grade, user.getName());
        return assembleResp(resp);
    }

    @RequestMapping("/subscribe_pkg/del")
    public JsonResult delPkgSubscribe(@RequestParam("id") Long id,
                                      @RequestParam("channel") String channel,
                                      @RequestParam("vid") Long vid,
                                      @RequestParam("pid") Long pid,
                                      @RequestParam("grade") Integer grade,
                                      @CurrentUser User user) {
        Response<Void> resp = this.cmdService.delPkgSubscribe(channel, pid, vid, grade, user.getName());
        if (id != null && id > 0) {
            String key = "t_punish_subscribe_pkg" + id;
            redisService.set(key, user.getName(), 3600L);
        }
        return assembleResp(resp);
    }

    /**
     * 获取订阅的惩罚包IDs
     */
    @RequestMapping("/subscribe_pkg/reset_priorities")
    public JsonResult resetPkgPrioritiesSubscribed(@RequestParam("channel") String channel, @RequestBody List<Long> pidList, @CurrentUser User user) {
        Response<Void> resp = this.cmdService.resetPkgPrioritySubscribed(channel, pidList);
        return assembleResp(resp);
    }

    @RequestMapping("/subscribe_pkg/list_vid")
    public JsonResult getPkgSubscribed(@RequestBody PunishSubscribePkg psp) {
        Response<List<Long>> resp = this.cmdService.listSubscribedPids(psp.getChannel(), psp.getVid());
        return assembleResp(resp);
    }

    /**
     * 获取违规类型定制化对内外原因
     */
    @RequestMapping("/violation_reason/list")
    public JsonResult getReasons(@RequestBody PunishViolationReason pvr) {
        Response<List<PunishViolationReasonBO>> resp = this.cmdService.getSubscribeReason(pvr.getChannel(), pvr.getVid());
        return assembleResp(resp);
    }

    @RequestMapping("/violation_reason/edit")
    public JsonResult getReasons(@RequestParam("channel") String channel, @RequestParam("vid") Long vid, @RequestBody List<PunishViolationReasonBO> pvr, @CurrentUser User user) {
        Response<Void> resp = this.cmdService.editSubscribeReason(channel, vid, pvr, user.getName());
        return assembleResp(resp);
    }

    @RequestMapping("/channel/list")
    public JsonResult getChannelList() {
        Response<List<Map<String, String>>> resp = this.cmdService.getAllChannels();
        return assembleResp(resp);
    }


    private JsonResult assembleResp(Response<?> response) {
        if (response == null) {
            return JsonResult.error(new RiskException("网络异常，请重试"));
        }

        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        } else {
            return JsonResult.error(new RiskException(response.getMsg()));
        }
    }

    @Data
    private static class PkgWithAbilities {
        private PkgBO pkg;
        private List<PunishAbilityBO> abilities;
    }
}
