package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.console.service.CheckTaskService;
import com.yupaopao.risk.console.vo.CheckTaskVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contentCheck/checkTaskDetailInfo")
@AuthRequired(permissionName = "check:task:detail")
@Slf4j
public class CheckTaskDetailInfoController extends AbstractModelController<CheckTask, CheckTaskVO, CheckTaskService>{

    /**
     * 查看抽检任务的详情
     * @param checkTaskDetailReq
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public JsonResult detail(@Valid @RequestBody CheckTaskDetailReq checkTaskDetailReq) {

        if(checkTaskDetailReq==null||checkTaskDetailReq.getTaskId()==0){
            return JsonResult.error("8001","没有传入有效的任务id",null);
        }

        return JsonResult.success(this.getService().detail(checkTaskDetailReq.getTaskId()));
    }
}


@Data
class CheckTaskDetailReq implements Serializable {

    private Long taskId;
}

