package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Advertisement;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.AdService;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/ad")
@AuthRequired(permissionName = "core:fencingad:all")
public class FencingAdController extends AbstractModelController<Advertisement, Advertisement, AdService> {

    @Override
    protected void addValid(Advertisement record, User user) {
    }

    @Override
    protected void updateValid(Advertisement record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
    }

}
