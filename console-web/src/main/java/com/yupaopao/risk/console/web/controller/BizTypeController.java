package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Sets;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.BizChannel;
import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.bean.BizTypeVO;
import com.yupaopao.risk.console.bean.ThirdChannelVO;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.BizTypeService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Set;

/**
 * author: lijianjun
 * date: 2021/3/25 14:39
 */
@RestController
@RequestMapping("/biz/type")
@AuthRequired(permissionName = "core:biztype:all")
public class BizTypeController extends AbstractModelController<BizType, BizTypeVO, BizTypeService>{

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;

    @RequestMapping("/search")
    @AuthRequired(permissionName = "core:biztype:search")
    public JsonResult search(@RequestBody BizTypeVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(getService().search(record,page,size));
    }

    @Override
    protected void addValid(BizTypeVO record, User user) {
        Assert.notNull(record.getCheckType(), "请指定检测类型");
        Assert.notNull(record.getName(), "请输入业务名称");
        Assert.notNull(record.getCode(), "请输入业务标识");
        Assert.notEmpty(record.getThirdChannelVOList(),"请预绑定通道");
        Set<String> channels = Sets.newHashSet();
        for(ThirdChannelVO thirdChannelVO : record.getThirdChannelVOList()){
            if(channels.contains(thirdChannelVO.getChannel())){
                throw new RiskException("一个业务类型不能绑定多个相同渠道的三方通道");
            }
            channels.add(thirdChannelVO.getChannel());
        }
        Assert.isNull(getService().getByName(record.getName()),"业务名称已存在");
        Assert.isNull(getService().getByCode(record.getCode()),"业务标识已存在");

        record.setModifier(user.getName());
        record.setAuthor(user.getName());
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:biztype:manage")
    public JsonResult add(@RequestBody BizTypeVO bizTypeVO, @CurrentUser User user) {
        addValid(bizTypeVO,user);
        return JsonResult.success(getService().insert(bizTypeVO));
    }

    @Override
    protected void updateValid(BizTypeVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getCheckType(), "请指定检测类型");
        Assert.notNull(record.getName(), "请输入业务名称");
        Assert.notNull(record.getCode(), "请输入业务标识");
        Assert.notEmpty(record.getThirdChannelVOList(),"请预绑定通道");
        Set<String> channels = Sets.newHashSet();
        for(ThirdChannelVO thirdChannelVO : record.getThirdChannelVOList()){
            if(channels.contains(thirdChannelVO.getChannel())){
                throw new RiskException("一个业务类型不能绑定多个相同渠道的三方通道");
            }
            channels.add(thirdChannelVO.getChannel());
        }
        BizType bizType = getService().getByName(record.getName());
        if(null != bizType && !bizType.getId().equals(record.getId())){
            throw new RiskException("业务名称已存在");
        }
        bizType = getService().getByCode(record.getCode());
        if(null != bizType && !bizType.getId().equals(record.getId())){
            throw new RiskException("业务标识已存在");
        }
        record.setModifier(user.getName());
        record.setAuthor(null);
        record.setCreateTime(null);
        record.setState(null);
        record.setUpdateTime(null);
        record.setCheckType(null);
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:biztype:manage")
    public JsonResult update(@PathVariable long id, @Valid @RequestBody BizTypeVO record, @CurrentUser User user) {
        updateValid(record, user);
        return JsonResult.success(getService().update(record));
    }

    @RequestMapping(value = "/switch", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:biztype:manage")
    public JsonResult switchThirdChannel(@RequestBody BizChannel record, @CurrentUser User user) {
        Assert.notNull(record, "参数错误");
        Assert.notNull(record.getBizTypeId(), "请指定业务类型ID");
        Assert.notNull(record.getThirdChannelId(), "请指定三方通道ID");
        Assert.notNull(record.getState(), "请指定要切换的状态");
        return JsonResult.success(getService().switchThirdChannel(record,user.getName()));
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:thirdchannel:manage")
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        return JsonResult.success(getService().delete(id,user.getName()));
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_SHOOT)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.BIZ_TYPE_RELOAD)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

    @RequestMapping(value = "/resetPriority", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:thirdchannel:manage")
    public JsonResult resetPriority(@RequestBody BizTypeVO record,@CurrentUser User user) {
        return JsonResult.success(getService().resetPriority(record,user.getName()));
    }

    @RequestMapping(value = "/relateRules", method = RequestMethod.POST)
    public JsonResult relateRules(@RequestBody BizTypeVO record) {
        return JsonResult.success(getService().getRelateRules(record));
    }

}
