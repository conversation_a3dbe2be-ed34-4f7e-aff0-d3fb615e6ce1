package com.yupaopao.risk.console.web.listener;

import com.yupaopao.risk.console.bean.OperationLog;
import com.yupaopao.risk.console.service.ElasticSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class OperationLogListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(OperationLogListener.class);

    public static String[] WHITE_SUFFIX_GET = {".js", ".js.map", ".css", ".html", ".png", ".woff2", "/user/getLoginUser"};

    @Autowired
    private ElasticSearchService elasticSearchServiceImpl;

    @EventListener
    @Async
    public void onOperationRecordEvent(OperationLogEvent event) {
        //静态资源或常用查询接口无需记录
        if (!isRecord((OperationLog) event.getSource())) {
            return;
        }
        try {
            elasticSearchServiceImpl.indexOperationLog((OperationLog) event.getSource());
        } catch (Exception e) {
            LOGGER.error("操作日志留存失败", e);
        }
    }

    private boolean isRecord(OperationLog vo) {
        if ("GET".equals(vo.getMethod())) {
            for (int i = 0; i < WHITE_SUFFIX_GET.length; i++) {
                if (vo.getUrl().endsWith(WHITE_SUFFIX_GET[i])) {
                    return false;
                }
            }
        }
        return true;
    }
}
