package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.access.bean.RiskAction;
import com.yupaopao.risk.console.service.RiskTestService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.engine.bean.TestRuleResult;
import com.yupaopao.risk.engine.bean.TestRuleVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-5-20
 */
@RestController
@RequestMapping("/risk/test")
@AuthRequired(permissionName = "system:risktest:all")

public class RiskTestController{

    @Autowired
    private RiskTestService riskTestService;

    private Expression expression;

    private final static ExpressionParser SPEL_EXPRESSION_PARSER = new SpelExpressionParser();


    @RequestMapping("/execute")
    public JsonResult execute(@RequestBody JSONContent content) {
        return JsonResult.success(riskTestService.execute(content.getContent()));
    }

    @RequestMapping("/getRequestParameter")
    public JsonResult getRequestParameter(@RequestBody LogSearchVO.HitLogSearchVO vo){
        return JsonResult.success(riskTestService.getRequestParameter(vo));
    }

    @RequestMapping("/batch/execute")
    public JsonResult batchExecute(@RequestBody JSONContent content) {
        return JsonResult.success(riskTestService.batchExecute(content.getContent()));
    }

    @RequestMapping("/rule/execute")
    public JsonResult ruleExecute(@RequestBody Map request){

        TestRuleVO testRuleVO = new TestRuleVO();
        Map ruleObj = (Map) request.get("ruleObj");
        TestRuleResult testRuleResult;
        try {
            Map maps = (Map) JSON.parse(request.get("content").toString());
            RiskAction riskAction = JSONObject.parseObject(request.get("content").toString(), RiskAction.class);
            riskAction.getData().putAll(maps);
            testRuleVO.setRiskAction(riskAction);
            testRuleVO.setRuleCondition(ruleObj.get("condition")+"");
            testRuleVO.setDependent(ruleObj.get("dependent").toString());
            testRuleVO.setRiskConst(ruleObj.get("riskConst").toString());
            testRuleResult = riskTestService.testDetect(testRuleVO);
        } catch (Exception e) {
            return JsonResult.success(e.getMessage());
        }
        return JsonResult.success(testRuleResult);
    }

    @RequestMapping("/attribute/execute")
    public JsonResult attributeExecute(@RequestBody Map request) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        Map factor = (Map) request.get("factor");
        String riskLevel = "";
        if (request.get("content").toString().contains("PASS")){
            riskLevel = "PASS";
        }else if (request.get("content").toString().contains("REJECT")){
            riskLevel = "REJECT";
        }else if (request.get("content").toString().contains("REVIEW")){
            riskLevel =  "REVIEW";
        }
        Map map = JSONObject.parseObject(request.get("content").toString(), Map.class);
        Map data;
        if (null != map.get("data")){
            data = (Map) map.get("data");
        }else {
            data = map;
        }
        data.put("RiskLevel", riskLevel);
        setRiskLevel(map, data);
        context.setVariables(data);
        Object result = "";
        try {
            expression = SPEL_EXPRESSION_PARSER.parseExpression((String) factor.get("condition"));
            result = expression.getValue(context);
        } catch (Exception e) {
            return JsonResult.success(e.getMessage());
        }

        return JsonResult.success(result+"");
    }

    private void setRiskLevel(Map map, Map data) {
        if (null != map.get("level")){
            data.put("RiskLevel", map.get("level"));
        }

    }

    static class SearchCondition implements Serializable {

        private String eventCode;

        private String traceId;

        public String getEventCode() {
            return eventCode;
        }

        public void setEventCode(String eventCode) {
            this.eventCode = eventCode;
        }

        public String getTraceId() {
            return traceId;
        }

        public void setTraceId(String traceId) {
            this.traceId = traceId;
        }
    }

}
 class JSONContent implements Serializable {

    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

}


