package com.yupaopao.risk.console.web.support.utils;

import com.yupaopao.platform.common.constant.APP;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.passport.api.AccessTokenService;
import com.yupaopao.risk.console.bean.PunishResponse;
import com.yupaopao.risk.console.service.PunishManageService;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

@Slf4j
public class DropUserCallable implements Callable<DropResult> {

    private PunishManageService punishManageService;

    private Long uid;
    private Long pkgId;
    private String operator;

    public DropUserCallable(long uid,Long pkgId,PunishManageService punishManageService,String operator){
        this.uid = uid;
        this.pkgId = pkgId;
        this.punishManageService = punishManageService;
        this.operator = operator;
    }

    @Override
    public DropResult call() {
        try{
            PunishResponse response = punishManageService.invalidAccessToken(uid,pkgId,operator);
            if (response == null || !response.isSuccess()) {
                return new DropResult(uid,pkgId,false);
            } else {
                return new DropResult(uid,pkgId,true);
            }
        }catch (Exception e){
            log.error("调用踢出登录用户惩罚包发生异常,uid:"+uid+",pkgId:"+pkgId+",operator:"+operator,e);
            return new DropResult(uid,pkgId,false);
        }
    }
}
