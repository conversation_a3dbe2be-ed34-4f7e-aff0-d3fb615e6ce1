package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.enums.PropertyChangeType;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.risk.common.enums.Dimensions;
import com.yupaopao.risk.common.enums.GrayListType;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.api.RiskGrayListService;
import com.yupaopao.risk.console.bean.GrayIdentityTypeMapping;
import com.yupaopao.risk.console.bean.RiskGrayCheckRequest;
import com.yupaopao.risk.console.bean.RiskGrayListRequest;
import com.yupaopao.risk.console.bean.RiskResult;
import com.yupaopao.risk.console.service.GrayGroupService;
import com.yupaopao.risk.console.service.GrayListService;
import com.yupaopao.risk.shoot.api.RiskListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Component
@Service
public class RiskGrayListServiceImpl implements RiskGrayListService {

    @Reference
    private RiskListService riskListService;

    @Autowired
    private GrayListService grayListService;

    @Autowired
    private GrayGroupService grayGroupService;

    @ApolloConfig("gray_identity_type_config")
    private Config grayIdentityTypeConfig;

    private Map<String, GrayIdentityTypeMapping> codeToGroupIdAndTypeMappings = new ConcurrentHashMap<>();

    @PostConstruct
    public void init(){
        grayIdentityTypeConfig.addChangeListener(event->{
            Set<String> changeKeys = event.changedKeys();
            if(!CollectionUtils.isEmpty(changeKeys)){
                for(String key:changeKeys){
                    if(event.getChange(key).getChangeType() == PropertyChangeType.DELETED){
                        codeToGroupIdAndTypeMappings.remove(key);
                    }else{
                        ParseGrayGroupIdentityMappings(key);
                    }
                }
            }
        });

        initGrayGroupIdentityMappings(grayIdentityTypeConfig);

    }

    private void initGrayGroupIdentityMappings(Config grayIdentityTypeConfig){
        Set<String> propertyNames = grayIdentityTypeConfig.getPropertyNames();
        if(CollectionUtils.isNotEmpty(propertyNames)){
            for(String propertyName : propertyNames){
                String groupIdAndTypeStr = this.grayIdentityTypeConfig.getProperty(propertyName,"");
                if(StringUtils.isNotBlank(groupIdAndTypeStr)){
                    GrayIdentityTypeMapping mapping = JSONObject.parseObject(groupIdAndTypeStr,GrayIdentityTypeMapping.class);
                    codeToGroupIdAndTypeMappings.put(propertyName,mapping);
                }
            }
        }
    }

    private void ParseGrayGroupIdentityMappings(String code){
        if(StringUtils.isBlank(code)){
            return;
        }

        String value = this.grayIdentityTypeConfig.getProperty(code, "");
        log.info("解析名单组标识映射配置：{}->{}",code,value);
        if(StringUtils.isNotBlank(value)){
            GrayIdentityTypeMapping mapping = JSONObject.parseObject(value,GrayIdentityTypeMapping.class);
            codeToGroupIdAndTypeMappings.put(code,mapping);
        }

    }

    private RiskResult checkValid(RiskGrayListRequest request){

        if(request==null){
           return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL);
        }

        if(StringUtils.isEmpty(request.getAuthor())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_AUTHOR);
        }

        if(StringUtils.isBlank(request.getDimension())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_DIMENSION);
        }

        if(StringUtils.isBlank(request.getCode())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_GROUP_IDENTITY);
        }

        if(StringUtils.isBlank(request.getType())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_TYPE);
        }

        if(!Objects.equals(request.getType().toUpperCase(),"WHITE")&&!Objects.equals(request.getType().toUpperCase(),"BLACK")){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_INVALID_TYPE);
        }

        if(StringUtils.isBlank(request.getValue())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_VALUE);
        }

        return RiskResult.success();
    }

    private RiskResult checkInParameterValid(RiskGrayCheckRequest request){
        if(request==null){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL);
        }

        if(StringUtils.isBlank(request.getDimension())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_DIMENSION);
        }

        if(StringUtils.isBlank(request.getCode())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_GROUP_IDENTITY);
        }

        if(StringUtils.isBlank(request.getType())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_TYPE);
        }

        if(!Objects.equals(request.getType().toUpperCase(),"WHITE")&&!Objects.equals(request.getType().toUpperCase(),"BLACK")){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_INVALID_TYPE);
        }

        if(StringUtils.isBlank(request.getValue())){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NULL_VALUE);
        }

        return RiskResult.success();
    }

    @Override
    public RiskResult add(RiskGrayListRequest request) {

        log.info("添加名单请求:"+JSONObject.toJSONString(request));

        RiskResult checkResult = checkValid(request);
        if(!checkResult.isSuccess()){
            return checkResult;
        }

        //获取名单组标识与(名单组id,名单类型type)映射关系
        GrayIdentityTypeMapping mapping = this.codeToGroupIdAndTypeMappings.get(request.getCode());
        if(mapping==null){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_INVALID_GROUP_IDENTITY);
        }

        //校验黑白名单权限
        String configType = mapping.getType();
        String requestType =  request.getType();
        if(StringUtils.isNotBlank(configType)&&!configType.equalsIgnoreCase(requestType)){
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_NO_PERMISSION);
        }

        try{
            GrayList grayList = new GrayList();
            BeanUtils.copyProperties(grayList,request);
            grayList.setGroupId(mapping.getGroupId());

            if(grayList.getExpireTime()==null){
               grayList.setExpireTime(mapping.getExpireTime());
            }

            if(!riskListService.saveOrUpdate(grayList)){
                return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_SAVE_ERROR);
            }else{
                log.info("保存名单成功！{}",JSONObject.toJSONString(grayList));
            }

        }catch (Exception exp){
            log.error("保存名单出现异常:",exp);
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_SAVE_EXCEPTION);
        }

        return RiskResult.success();
    }

    @Override
    public RiskResult delete(RiskGrayListRequest request) {

        log.info("删除名单请求:"+JSONObject.toJSONString(request));

        RiskResult checkResult = checkValid(request);
        if(!checkResult.isSuccess()){
            return checkResult;
        }

        //获取名单组标识与名单名映射关系
        GrayIdentityTypeMapping mapping = this.codeToGroupIdAndTypeMappings.get(request.getCode());
        if(mapping==null){
            log.info(request.getCode()+"业务场景标识的映射关系不存在");
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_DELETE__ERROR_MESSAGE1);
        }

        try{
            GrayList grayList = new GrayList();
            BeanUtils.copyProperties(grayList,request);
            grayList.setGroupId(mapping.getGroupId());

            List<GrayList> result = this.grayListService.select(grayList);
            if(CollectionUtils.isEmpty(result)){
                log.info("未在数据库中找到该名单,认为删除名单成功！");
                return RiskResult.success();
            }

            this.riskListService.delete(result.get(0));
            log.info("删除名单成功"+JSONObject.toJSONString(grayList));
        }catch(Exception exp){
            log.error("删除名单发生异常",exp);
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_DELETE_EXCEPTION);
        }

        return RiskResult.success();
    }

    @Override
    public RiskResult detect(RiskGrayCheckRequest request){

        log.info("检测名单请求:"+JSONObject.toJSONString(request));

        RiskResult checkResult = checkInParameterValid(request);
        if(!checkResult.isSuccess()){
            return checkResult;
        }

        //获取名单组标识与名单名映射关系
        GrayIdentityTypeMapping mapping = this.codeToGroupIdAndTypeMappings.get(request.getCode());
        if(mapping==null){
            log.info(request.getCode()+"业务场景标识的映射关系不存在");
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_DELETE__ERROR_MESSAGE1);
        }

        try{
            GrayListType type = GrayListType.nameOf(request.getType());
            Dimensions dimensions = Dimensions.nameOf(request.getDimension());

            Boolean isHit = this.riskListService.isHit(mapping.getGroupId(),type,dimensions,request.getValue());

            log.info("检测名单是否被命中: {}",isHit);

            return RiskResult.success(isHit);
        }catch(Exception exp){
            log.error("检测名单发生异常",exp);
            return RiskResult.error(ConsoleConstants.RISK_GRAY_LIST_REQUEST_DELETE_EXCEPTION);
        }

    }

}
