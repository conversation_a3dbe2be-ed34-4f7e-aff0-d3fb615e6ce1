package com.yupaopao.risk.console.web.support.utils;

import lombok.Getter;

import java.util.Map;

@Getter
public class ExcelFormatter {
    /**
     * map集合中key值
     */
    private String key;

    /**
     * 导出到Excel中的title
     */
    private String name;

    /**
     * 日期格式, 如: yyyy-MM-dd
     */
    private String dateFormat;

    /**
     * 导出时在excel中每个列的宽 单位为字符
     */
    private int width = 20;

    /**
     * 自定义类型 如image表示url转图片
     */
    private String type;

    /**
     * 替换集合
     */
    private Map<Object, Object> converter;

    private ExcelFormatter() {
        super();
    }

    public static Builder builder(String key) {
        return new Builder(key);
    }

    public static class Builder {

        private ExcelFormatter formatter;

        public Builder() {
            super();
            formatter = new ExcelFormatter();
        }

        public Builder(String key) {
            this();
            formatter.key = key;
        }

        public Builder addName(String name) {
            formatter.name = name;
            return this;
        }

        public Builder addDateFormat(String dateFormat) {
            formatter.dateFormat = dateFormat;
            return this;
        }

        public Builder addWidth(int width) {
            formatter.width = width;
            return this;
        }

        public Builder addConverter(Map<Object, Object> converter) {
            formatter.converter = converter;
            return this;
        }

        public Builder addType(String type) {
            formatter.type = type;
            return this;
        }

        public ExcelFormatter build() {
            return formatter;
        }
    }
}
