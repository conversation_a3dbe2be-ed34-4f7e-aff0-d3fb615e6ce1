package com.yupaopao.risk.console.web.config;

import com.yupaopao.platform.passport.sdk.config.PassportCommonConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * author: <PERSON><PERSON><PERSON><PERSON>
 * date: 2021/3/10 11:32
 */
@Component
@Slf4j
public class ConfigInitListener implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) { // 根容器为Spring容器
            PassportCommonConfig.init();
        }
    }
}
