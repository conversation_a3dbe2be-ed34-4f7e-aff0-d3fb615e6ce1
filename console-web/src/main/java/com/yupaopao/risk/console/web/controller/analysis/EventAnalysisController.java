package com.yupaopao.risk.console.web.controller.analysis;

import com.google.common.collect.Lists;
import com.yupaopao.risk.common.model.GroupRule;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.bean.AttributeReq;
import com.yupaopao.risk.console.bean.EventHitQuery;
import com.yupaopao.risk.console.bean.FactorReq;
import com.yupaopao.risk.console.bean.RiskLogReq;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lijianjun
 * date: 2020/7/11 17:34
 */
@Slf4j
@RestController
@RequestMapping("/detail/event")
@AuthRequired(permissionName = "system:eventDetail:all")
public class EventAnalysisController {

    @Autowired
    private EventService eventService;
    @Autowired
    private GroupRuleService groupRuleService;
    @Autowired
    private AtomRuleService atomRuleService;
    @Autowired
    private AttributeService attributeService;
    @Autowired
    private FactorService factorService;
    @Autowired
    private EventDetailService eventDetailService;
    @Autowired
    private LogService logService;
    @Autowired
    private RiskHitLogService riskHitLogService;

    @RequestMapping("/getEvents")
    public JsonResult getEvents() {
        return JsonResult.success(eventService.simpleAll());
    }

    @RequestMapping("/detail")
    public JsonResult detail(@RequestBody EventVO record) {
        return JsonResult.success(eventService.getDetailByCode(record.getCode()));
    }

    @RequestMapping("/searchRuleGroup")
    public JsonResult searchRuleGroup(@RequestBody EventVO record) {
        String ruleGroupId = record.getRuleGroupId();
        if(StringUtils.isBlank(ruleGroupId)){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        List<Long> ids = Lists.newArrayList(ruleGroupId.split(",")).stream().filter(id -> StringUtils.isNotBlank(id))
                .map(id -> Long.valueOf(id)).collect(Collectors.toList());
        List<GroupRule> list = groupRuleService.selectByIds(ids);
        return JsonResult.success(groupRuleService.relationFetch(list));
    }

    @RequestMapping("/searchRuleAtom")
    public JsonResult searchRuleAtom(@RequestBody EventVO record) {
        String ruleGroupId = record.getRuleGroupId();
        if(StringUtils.isBlank(ruleGroupId)){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        List<Long> ids = Lists.newArrayList(ruleGroupId.split(",")).stream().filter(id -> StringUtils.isNotBlank(id))
                .map(id -> Long.valueOf(id)).collect(Collectors.toList());
        return JsonResult.success(atomRuleService.listByGroupIdsWithGroup(ids));
    }

    @RequestMapping("/searchAttr")
    public JsonResult searchAttr(@RequestBody AttributeReq req) {
        if(StringUtils.isBlank(req.getRuleGroupId())){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        return JsonResult.success(attributeService.list(req));
    }

    @RequestMapping("/searchFactor")
    public JsonResult searchFactor(@RequestBody FactorReq req) {
        if(StringUtils.isBlank(req.getRuleGroupId()) && (null == req.getAttributes() || req.getAttributes().size() == 0)){
            return JsonResult.success(Collections.EMPTY_LIST);
        }
        return JsonResult.success(factorService.list(req));
    }

    @RequestMapping("/riskSummary")
    public JsonResult riskSummary(@RequestBody EventHitQuery eventHitQuery) {
        Assert.isTrue(StringUtils.isNotBlank(eventHitQuery.getEventCode()),"请选择事件");
        Assert.isTrue(null != eventHitQuery.getDays(),"请选择时间范围");
        Assert.isTrue(eventHitQuery.getDays()<=30,"时间范围过大");
        return JsonResult.success(eventDetailService.getRiskSummary(eventHitQuery));
    }

    @RequestMapping("/searchRiskLog")
    public JsonResult searchRiskLog(@RequestBody RiskLogReq req, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(logService.search(req,page,size));
    }

    @RequestMapping("/riskData")
    public JsonResult riskData(@RequestBody EventHitQuery eventHitQuery) {
        Assert.isTrue(StringUtils.isNotBlank(eventHitQuery.getEventCode()),"请选择事件");
        Assert.isTrue(null != eventHitQuery.getDays(),"请选择时间范围");
        Assert.isTrue(eventHitQuery.getDays()<=30,"时间范围过大");
        return JsonResult.success(riskHitLogService.getRiskData(eventHitQuery));
    }
}
