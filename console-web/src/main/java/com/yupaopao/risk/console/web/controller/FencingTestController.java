package com.yupaopao.risk.console.web.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.text.api.RiskTextService;
import com.yupaopao.risk.text.bean.HitWord;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/test")
@AuthRequired(permissionName = "core:fencingtest:all")
public class FencingTestController {

    private final static Logger LOGGER = LoggerFactory.getLogger(FencingTestController.class);
    @Reference(check = false)
    private RiskTextService riskTextService;

    @RequestMapping("/specialChars")
    public JsonResult specialChars() {
        String specialChars = "=,-,\\^,\\|,\\\\,/,\\!,\\%,\\*,\\&,\\(,\\),\\$,\\#,\\@,\\~,\\.,\\,,\\?,\\<,\\>,\\_,\\+";
        return JsonResult.success(specialChars);
    }

    @RequestMapping("/hitword")
    public JsonResult hitword(@RequestBody ContentAndScene contentAndScene) {
        try {
            HitWord[] fire = riskTextService.fireByScenes(contentAndScene.getContent(), Arrays.asList(contentAndScene.getScene()));
            return JsonResult.success(JSON.toJSONString(fire));
        } catch (Exception e) {
            LOGGER.error("违禁词测试失败", e);
            return JsonResult.error();
        }

    }

    @Getter
    @Setter
    static class ContentAndScene implements Serializable {

        private static final long serialVersionUID = 9136831773008567269L;

        private String content;
        private String scene;
    }
}
