package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.AlarmActionType;
import com.yupaopao.risk.common.enums.AlarmConfigType;
import com.yupaopao.risk.common.enums.AlarmType;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.service.AlarmService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.web.bind.annotation.*;

/**
 * author: liji<PERSON>jun
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/alarmFuse")
@AuthRequired(permissionName = "alarm:alarmFuse:all")
public class AlarmFuseController extends AbstractModelController<Alarm, AlarmVO, AlarmService> {

    @RequestMapping("/search")
    public JsonResult search(@RequestBody AlarmVO vo, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        vo.setType(AlarmType.RULE_TYPE.getCode());
        vo.setActionType(AlarmActionType.FUSE.getCode());
        return JsonResult.success(this.getService().searchAlarm(vo,page,size));
    }

    @Override
    protected void addValid(AlarmVO alarmVO, User user) {
        Alarm alarm = this.getService().getByName(alarmVO.getName());
        Assert.isNull(alarm, "标签名称重复！");
        alarmVO.setModifier(user.getName());
        alarmVO.setAuthor(user.getName());
        alarmVO.getConfig().setAuthor(user.getName());
        alarmVO.getConfig().setModifier(user.getName());
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        addValid(alarm,user);
        return JsonResult.success(this.getService().saveFuseAlarm(alarm));
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.POST)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(this.getService().getDetailById(id));
    }

    @Override
    protected void updateValid(AlarmVO alarmVO, User user) {
        Alarm alarm = this.getService().getByName(alarmVO.getName());
        if(null != alarm && !alarm.getId().equals(alarmVO.getId())){
            throw new RiskException("标签名称重复！");
        }
        alarmVO.setModifier(user.getName());
        alarmVO.getConfig().setModifier(user.getName());
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public JsonResult update(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        updateValid(alarm,user);
        return JsonResult.success(this.getService().updateFuseAlarm(alarm));
    }

    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public JsonResult updateStatus(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        alarm.setModifier(user.getName());
        return JsonResult.success(this.getService().updateStatus(alarm));
    }

}
