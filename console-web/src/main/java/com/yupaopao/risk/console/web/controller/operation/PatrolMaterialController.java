package com.yupaopao.risk.console.web.controller.operation;


import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.PatrolMaterials;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.service.PatrolMaterialsService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.utils.MapValueConvertUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 巡检相关
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("operation/patrolMaterial")
@AuthRequired(permissionName = "operation:patrol:record")
public class PatrolMaterialController extends AbstractModelController<PatrolMaterials, PatrolMaterials, PatrolMaterialsService> {
    @Autowired
    private ElasticSearchService elasticSearchService;

    @RequestMapping("/searchPunishRecord")
    public JsonResult searchPunishRecord(@RequestBody LogSearchVO.PunishLogSearchVO vo) {
        if (StringUtils.isNotBlank(vo.getTargetValue())) {
            if (Objects.equals(vo.getTargetType(), "uid") || Objects.equals(vo.getTargetType(), "liveRoomId")) {
                if (!NumberUtils.isNumber(vo.getTargetValue())) {
                    return JsonResult.success(new HashMap<>());
                }
            }
        }
        CommonUtil.setFieldValue(vo.getQuery(), vo.getTargetType(), vo.getTargetValue());
        PageInfo<Map<String, Object>> result = elasticSearchService.searchPunishLog(vo);
        MapValueConvertUtil.convertValueToString(result.getList(), "uid", "liveRoomId", "fromUid");
        return JsonResult.success(result);
    }
}
