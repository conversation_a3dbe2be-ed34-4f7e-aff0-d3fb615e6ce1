package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.console.service.AlarmService;
import com.yupaopao.risk.console.web.support.JsonResult;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * author: liji<PERSON><PERSON>
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/alarm")
public class AlarmWhiteController{

    @Autowired
    private AlarmService alarmService;

    @RequestMapping(value = "/pause/{id}", method = RequestMethod.GET)
    public JsonResult pause(@PathVariable long id) {
        Alarm alarm = new Alarm();
        alarm.setId(id);
        alarm.setStartTime(DateUtils.addHours(new Date(),1));
        alarmService.updateSelectiveById(alarm);
        return JsonResult.success();
    }

}
