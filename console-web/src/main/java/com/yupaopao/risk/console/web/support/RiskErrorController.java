package com.yupaopao.risk.console.web.support;

import com.yupaopao.risk.common.enums.ErrorMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.ServletWebRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 异常处理 see: http://stackoverflow.com/questions/25356781/spring-boot-remove-whitelabel-error-page
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
public class RiskErrorController implements ErrorController {

    private final static Logger logger = LoggerFactory.getLogger(RiskErrorController.class);

    private static final String PATH = "/error";

    @Autowired
    private ErrorAttributes errorAttributes;


    @RequestMapping(value = PATH)
    public JsonResult error(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> errorAttributes = this.getErrorAttributes(request);

        logger.warn("请求出错: {}", errorAttributes);

        response.setStatus(HttpServletResponse.SC_OK);
        return JsonResult.error(ErrorMessage.SYSTEM_ERROR, errorAttributes);
    }

    private Map<String, Object> getErrorAttributes(HttpServletRequest request) {
        return errorAttributes.getErrorAttributes(new ServletWebRequest(request), false);
    }

    @Override
    public String getErrorPath() {
        return PATH;
    }
}
