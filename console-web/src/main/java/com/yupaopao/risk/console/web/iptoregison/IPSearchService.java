package com.yupaopao.risk.console.web.iptoregison;

import com.yupaopao.ops.ip.geo.IpGeoAutoConfiguration;
import com.yupaopao.ops.ip.geo.api.IpInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public class IPSearchService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IPSearchService.class);

    public static IPDetail getIpInfo(String ip) {
        try {
            return (StringUtils.isNotBlank(ip) && Util.isIpAddress(ip)) ? getIpDetail(ip) : null;
        } catch (Exception e) {
            LOGGER.error("解析IP信息失败: ip="+ip, e);
        }
        return null;
    }

    private static IPDetail getIpDetail(String ip) throws Exception {
        IpInfoDTO search = IpGeoAutoConfiguration.search(ip);
        IPDetail ipDetail = new IPDetail();
        String city = search.getCity();
        ipDetail.setCity(city.length() > 2 && city.indexOf("市") == city.length() - 1 ? city.substring(0, city.length() - 1) : city);
        ipDetail.setCountry(search.getCountry());
        ipDetail.setIsp(search.getIsp());
        ipDetail.setRegion(search.getCounty());
        String province = search.getProvince();
        ipDetail.setProvince(province.length() > 2 && (province.indexOf("省") == province.length() - 1 || province.indexOf("市") == province.length() - 1) ?
            province.substring(0, province.length() - 1) : province);
        ipDetail.setLatitude(search.getLatitude());
        ipDetail.setLongitude(search.getLongitude());
        return ipDetail;
    }

}
