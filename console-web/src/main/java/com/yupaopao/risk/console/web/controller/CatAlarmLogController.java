package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.AlarmLog;
import com.yupaopao.risk.common.model.CatAlarmLog;
import com.yupaopao.risk.common.vo.AlarmLogVO;
import com.yupaopao.risk.console.bean.CatAlarmLogVO;
import com.yupaopao.risk.console.service.AlarmLogService;
import com.yupaopao.risk.console.service.CatAlarmLogService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * author: lijianjun
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/catAlarmLog")
@AuthRequired(permissionName = "system:catAlarmLog:all")
public class CatAlarmLogController extends AbstractModelController<CatAlarmLog, CatAlarmLogVO, CatAlarmLogService> {

    @RequestMapping("/search")
    public JsonResult search(@RequestBody CatAlarmLogVO catAlarmLogVO, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(this.getService().searchLog(catAlarmLogVO,page,size));
    }

}
