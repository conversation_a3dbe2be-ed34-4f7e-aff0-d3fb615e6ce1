package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.console.api.RiskGrayService;
import com.yupaopao.risk.console.bean.RiskGrayEntity;
import com.yupaopao.risk.console.bean.RiskGrayRequest;
import com.yupaopao.risk.console.bean.RiskIsGrayRequest;
import com.yupaopao.risk.console.bean.RiskResult;
import com.yupaopao.risk.console.service.GrayGroupService;
import com.yupaopao.risk.console.service.GrayListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@Component
public class RiskGrayServiceImpl implements RiskGrayService {
    @Autowired
    private GrayListService grayListService;
    @Autowired
    private GrayGroupService grayGroupService;

    @Override
    public RiskResult search(RiskGrayRequest request) {
        if(StringUtils.isBlank(request.getDimension())){
            return RiskResult.error("维度不能为空");
        }
        if(StringUtils.isBlank(request.getValue())){
            return RiskResult.error("维度值不能为空");
        }
        if(!RiskGrayRequest.validDimension(request.getDimension())){
            return RiskResult.error("维度非法");
        }
        if(!RiskGrayRequest.validType(request.getType())){
            return RiskResult.error("名单类型非法");
        }
        log.debug("准备处理风控名单请求:{}", request);
        try {
            List<RiskGrayEntity> entities = new ArrayList<>();
            GrayList grayList = new GrayList();
            grayList.setDimension(request.getDimension()); // 维度
            grayList.setValue(request.getValue()); // 维度值
            if(StringUtils.isNotBlank(request.getType())){ // 名单类型
                grayList.setType(request.getType());
            }
            List<GrayList> grayLists = grayListService.searchRiskGray(grayList,true);
            if(CollectionUtils.isNotEmpty(grayLists)){
                List<GrayGroup> groups = grayGroupService.selectAll();
                Map<Long,String> groupMap = new HashMap<>();
                if(CollectionUtils.isNotEmpty(groups)){
                    for(GrayGroup group : groups){
                        groupMap.put(group.getId(),group.getName());
                    }
                }
                for(GrayList gray : grayLists){
                    RiskGrayEntity entity = new RiskGrayEntity();
                    entity.setGroupId(gray.getGroupId());
                    entity.setDimension(gray.getDimension());
                    entity.setExpireTime(gray.getExpireTime());
                    entity.setGroup(groupMap.get(gray.getGroupId()));
                    entity.setType(gray.getType());
                    entity.setValue(gray.getValue());
                    entities.add(entity);
                }
            }
            return RiskResult.success(entities);
        } catch (Throwable e){
            log.error("搜索风控名单数据失败", e);
            return RiskResult.error("请求处理失败");
        }
    }

    @Override
    public Response<Boolean> isGray(RiskIsGrayRequest request) {
        if(StringUtils.isBlank(request.getType())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "名单类型不能为空");
        }
        if(!RiskIsGrayRequest.validType(request.getType())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "名单类型非法");
        }
        if(StringUtils.isBlank(request.getDimension())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "维度不能为空");
        }
        if(!RiskIsGrayRequest.validDimension(request.getDimension())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "维度非法");
        }
        if(StringUtils.isBlank(request.getValue())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "维度值不能为空");
        }
        if(CollectionUtils.isEmpty(request.getGroupIds())){
            return Response.fail(Code.ERROR_PARAM.getCode(), "名单组列表不能为空");
        }
        log.debug("获取命中黑白名单情况 request:{}", request);
        try {
            return Response.success(grayListService.isGray(request));
        } catch (Throwable e){
            log.error("获取命中黑白名单情况异常", e);
            return Response.fail("8020", "获取命中黑白名单情况失败");
        }
    }

}
