package com.yupaopao.risk.console.web;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.yupaopao.framework.cat.web.EnableCatWebMonitorConfiguration;
import com.yupaopao.framework.spring.boot.datasource.annotation.EnableDataSourceConfiguration;
import com.yupaopao.framework.spring.boot.kafka.annotation.EnableKafkaConfiguration;
import com.yupaopao.framework.spring.boot.redis.annotation.EnableRedisConfiguration;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动入口
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.yupaopao.risk"})
@EnableApolloConfig({"application", "middleware.actuator2x", "risk-console-cas","bizLogConfig","middleware.event-mapping-pub", "middleware.public-event-cache"})
@EnableScheduling
@EnableDataSourceConfiguration("middleware.db-v2.risk")
@EnableRedisConfiguration({"middleware.redis.risk","middleware.redis.risk-magic"})
@EnableKafkaConfiguration({"middleware.kafka","middleware.kafka.risk","middleware.kafka-bigdata"})
@EnableCatWebMonitorConfiguration
@ComponentScan(basePackages = {"com.yupaopao.risk","com.yupaopao.operation.common.sdk"})
@MapperScan(basePackages = {"com.yupaopao.risk.console.clickhouse.mapper"})
//@EnableAsync
public class ConsoleApplication {

    private final static Logger LOGGER = LoggerFactory.getLogger(ConsoleApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(ConsoleApplication.class, args);
        LOGGER.info("Risk-Console服务启动成功!");
    }

}
