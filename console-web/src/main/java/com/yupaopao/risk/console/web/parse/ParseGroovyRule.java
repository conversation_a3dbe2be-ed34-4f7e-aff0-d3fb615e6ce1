package com.yupaopao.risk.console.web.parse;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Sets;
import groovy.lang.GroovyClassLoader;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Pattern;

@Service
public class ParseGroovyRule {

    private final static Logger LOGGER = LoggerFactory.getLogger(com.yupaopao.risk.engine.parse.ParseGroovyRule.class);

    public static final String ContextMapName = "thismaps";
    private final static Pattern ruleSplitPattern = Pattern.compile("(&&|\\|\\|)");
    private final static Pattern allNumberPattern = Pattern.compile("[0-9]+");
    private final Set<String> notCharacterSet = new HashSet<>();

    @ApolloConfig
    private Config config;
    private Set<Character.UnicodeBlock> unicodeBlocks;

    @PostConstruct
    private void init(){

        this.parseChineseUnicodeBlockNames(config.getProperty("chinese.unicode.block.names",""));

        config.addChangeListener(configChangeEvent -> {
            if(configChangeEvent.isChanged("chinese.unicode.block.names")){
                this.parseChineseUnicodeBlockNames(configChangeEvent.getChange("chinese.unicode.block.names").getNewValue());
            }
        });

    }

    private void parseChineseUnicodeBlockNames(String chineseUnicodeBlockNames){
        if(StringUtils.isNotBlank(chineseUnicodeBlockNames)){
            String[] blockNames = chineseUnicodeBlockNames.split(",");
            Set<Character.UnicodeBlock> unicodeBlocks = new HashSet<>();
            for(String blockName:blockNames){
                unicodeBlocks.add(Character.UnicodeBlock.forName(blockName));
            }

            this.unicodeBlocks = unicodeBlocks;
        }
    }

    public ParseGroovyRule() {
        notCharacterSet.add("int");
        notCharacterSet.add("double");
        notCharacterSet.add("def");
        notCharacterSet.add("String");
        notCharacterSet.add("Integer");
        notCharacterSet.add("Double");
        notCharacterSet.add("Long");
        notCharacterSet.add("equals");
        notCharacterSet.add("Math");
        notCharacterSet.add("contains");
        notCharacterSet.add("containsKey");
        notCharacterSet.add("true");
        notCharacterSet.add("false");
        notCharacterSet.add("if");
        notCharacterSet.add("else");
        notCharacterSet.add("return");
        notCharacterSet.add("null");
        notCharacterSet.add(ContextMapName);
    }

    public String checkRuleCompile(String strRule) {
        String retValue = null;
        try {
            GroovyClassLoader groovyClassLoader = new GroovyClassLoader();
            groovyClassLoader.parseClass(strRule);
            groovyClassLoader.close();
        } catch (Exception e) {
            retValue = e.toString();
        }
        return retValue;
    }

    public Map<String,Object> parseAsychronousCondition(String condition, Set<String> characterSets,Boolean hasIllegalChinese) {
        Map<String,Object> result = new HashMap<>(2);

        try {
            if(StringUtils.isBlank(condition)){
                result.put("containChinese",false);
                result.put("attributeSet",null);
                return result;
            }
            condition = removeUnmatchedBrace(condition);
            String[] expressions = ruleSplitPattern.split(condition);

            Set<String> validExpressions = new HashSet<>();
            for (String expression : expressions) {
                validExpressions.add(expression.trim());
            }
            Set<String> dependencySet = new HashSet<>();
            for (String expression : validExpressions) {
                Set<String> characterSet = new HashSet<>();
                Set<String> retainSet = findCharacterSet(expression, characterSet);

                if(containIllegalChinese(retainSet,characterSet)){
                    result.put("containChinese",true);
                    result.put("attributeSet",null);
                    return result;
                }

                if (characterSet.size() <= 0) {
                    continue;
                }
                for (String character : characterSet) {
                    if (character.startsWith("\"")) {
                        character = character.substring(1, character.length() - 1).trim();
                    }
                    if (notCharacterSet.contains(character) || allNumberPattern.matcher(character).matches()) {
                        continue;
                    }
                    dependencySet.add(character);
                    characterSets.add(character);
                }
            }
            if (characterSets != null && !characterSets.isEmpty()) {
                LOGGER.info("从规则[{}]中找到Attribute依赖:{}", condition, characterSets);
            }

            result.put("containChinese",false);
            result.put("attributeSet",dependencySet);
            return result;
        } catch (Exception e) {
            LOGGER.error("解析表达式失败:" + condition, e);
        }

        result.put("containChinese",false);
        result.put("attributeSet",null);
        return result;
    }

    private boolean containIllegalChinese(Set<String> retainSet,Set<String> characterSet){

        if(CollectionUtils.isNotEmpty(retainSet)){
            for(String retainString:retainSet){
                char[] retainChars = retainString.toCharArray();
                for(char ch:retainChars){
                    if(this.unicodeBlocks.contains(Character.UnicodeBlock.of(ch))){
                        return true;
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(characterSet)){
            for(String characterStr:characterSet){
                char[] chars = characterStr.toCharArray();
                for(char ch:chars){
                    if(this.unicodeBlocks.contains(Character.UnicodeBlock.of(ch))){
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public Set<String> parseCondition(String condition, Set<String> characterSets) {
        try {
            String[] expressions = ruleSplitPattern.split(condition);

            Set<String> validExpressions = new HashSet<>();
            for (String expression : expressions) {
                String validExpression = removeUnmatchedBrace(expression);
                if (validExpression.length() > 1) {
                    validExpressions.add(validExpression);
                }
            }

            Set<String> dependencySet = new HashSet<>();
            for (String expression : validExpressions) {
                Set<String> characterSet = new HashSet<>();
                findCharacter(expression, characterSet);
                if (characterSet.size() <= 0) {
                    continue;
                }
                for (String character : characterSet) {
                    if (character.startsWith("\"")) {
                        character = character.substring(1, character.length() - 1).trim();
                    }
                    if (notCharacterSet.contains(character) || allNumberPattern.matcher(character).matches()) {
                        continue;
                    }
                    dependencySet.add(character);
                    characterSets.add(character);
                }
            }
            if (characterSets != null && !characterSets.isEmpty()) {
                LOGGER.info("从规则[{}]中找到Attribute依赖:{}", condition, characterSets);
            }
            return dependencySet;
        } catch (Exception e) {
            LOGGER.error("解析表达式失败:" + condition, e);
        }
        return null;
    }

    private String removeUnmatchedBrace(String expression) {
        List<Integer> leftBracesIndexes = new ArrayList<Integer>();
        List<Integer> unmatchedBracesIndexes = new ArrayList<Integer>();
        for (int i = 0; i < expression.length(); i++) {
            if (expression.charAt(i) == '(') {
                leftBracesIndexes.add(i);
            } else if (expression.charAt(i) == ')') {
                if (leftBracesIndexes.size() > 0) {
                    leftBracesIndexes.remove(leftBracesIndexes.size() - 1);
                } else {
                    unmatchedBracesIndexes.add(i);
                }
            }
        }

        unmatchedBracesIndexes.addAll(leftBracesIndexes);
        StringBuilder newExpression = new StringBuilder();
        for (int i = 0; i < expression.length(); i++) {
            if (!unmatchedBracesIndexes.contains(i)) {
                newExpression.append(expression.charAt(i));
            }
        }

        return newExpression.toString().trim();
    }

    private void findCharacter(String expression, Set<String> characterSet) {
        StringBuilder builder = new StringBuilder();
        String state = "start";
        String lastToken = "";

        for (int i = 0; i < expression.length(); i++) {
            char ch = expression.charAt(i);
            if (state.equals("start") || state.equals("logic")) {
                if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || ch == '_') {
                    state = "var";
                    builder.append(ch);
                }
                if (ch == '!') {
                    state = "logic";
                }
                if (ch == '"') {
                    state = "constant";
                    builder.append(ch);
                }
                if (ch >= '0' && ch <= '9') {
                    state = "number";
                    builder.append(ch);
                }
                if (ch == '(' || ch == ',' || ch == ' ') {

                }
            } else if (state.equals("var")) {
                if (ch == '.') {
                    characterSet.add(builder.toString().trim());
                    lastToken = builder.toString();
                    state = "method";
                    builder = new StringBuilder();
                    builder.append(ch);
                } else if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch == '_') || (ch >= '0' && ch <= '9')) {
                    state = "var";
                    builder.append(ch);
                } else {
                    characterSet.add(builder.toString().trim());
                    lastToken = builder.toString();
                    state = "start";
                    builder = new StringBuilder();
                }
            } else if (state.equals("method")) {
                if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch == '_') || (ch >= '0' && ch < '9')) {
                    state = "method";
                    builder.append(ch);
                } else if (ch == '(') {
                    state = "start";
                    builder = new StringBuilder();
                }
            } else if (state.equals("constant")) {
                if (ch == '"') {
                    if (builder.length() > 0 && builder.charAt(builder.length() - 1) == '\\') {
                        state = "constant";
                        builder.append(ch);
                    } else {
                        state = "start";
                        builder.append(ch);
                        if (lastToken.equals(ContextMapName)) {
                            characterSet.add(builder.toString().trim());
                        }
                        builder = new StringBuilder();
                    }
                } else {
                    state = "constant";
                    builder.append(ch);
                }
            } else if (state.equals("number")) {
                if (ch < '0' || ch > '9') {
                    i--;
                    state = "start";
                    builder = new StringBuilder();
                } else if (ch >= '0' && ch <= '9') {
                    state = "number";
                    builder.append(ch);
                }
            }
        }
    }

    private Set<String> findCharacterSet(String expression, Set<String> characterSet) {

        StringBuilder builder = new StringBuilder();
        String state = "start";
        String lastToken = "";

        Set<String> retainSet = Sets.newHashSet();

        for (int i = 0; i < expression.length(); i++) {
            char ch = expression.charAt(i);
            if (state.equals("start") || state.equals("logic")) {
                if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || ch == '_'|| (ch >= '0' && ch <= '9')|| ch == '\n' || this.unicodeBlocks.contains(Character.UnicodeBlock.of(ch))) {
                    state = "var";
                    builder.append(ch);
                }
                if (ch == '!') {
                    state = "logic";
                }
                if (ch == '"'|| ch == '\'') {
                    state = "constant";
                    builder.append(ch);
                }
                if (ch >= '0' && ch <= '9') {
                    state = "number";
                    builder.append(ch);
                }
                if (ch == '(' || ch == ',' || ch == ' ') {

                }
            } else if (state.equals("var")) {
                if (ch == '.') {
                    characterSet.add(builder.toString().trim());
                    lastToken = builder.toString();
                    state = "method";
                    builder = new StringBuilder();
                    builder.append(ch);
                } else if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch == '_') || (ch >= '0' && ch <= '9')|| ch == '\n' || this.unicodeBlocks.contains(Character.UnicodeBlock.of(ch))) {
                    state = "var";
                    builder.append(ch);
                } else {
                    characterSet.add(builder.toString().trim());
                    lastToken = builder.toString();
                    state = "start";
                    builder = new StringBuilder();
                }
            } else if (state.equals("method")) {
                if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || (ch == '_') || (ch >= '0' && ch < '9')|| ch == '.' ||ch == '\n' || this.unicodeBlocks.contains(Character.UnicodeBlock.of(ch))) {
                    state = "method";
                    builder.append(ch);
                } else if (ch == '('|| ch == ' ' || ch == '=') {
                    retainSet.add(builder.toString().trim());
                    state = "start";
                    builder = new StringBuilder();
                }
            } else if (state.equals("constant")) {
                if (ch == '"'|| ch == '\'') {
                    if (builder.length() > 0 && builder.charAt(builder.length() - 1) == '\\') {
                        state = "constant";
                        builder.append(ch);
                    } else {
                        state = "start";
                        builder.append(ch);
//                        if (lastToken.equals(ContextMapName)) {
//                            characterSet.add(builder.toString().trim());
//                        }
                        builder = new StringBuilder();
                    }
                } else {
                    state = "constant";
                    builder.append(ch);
                }
            } else if (state.equals("number")) {
                if (ch < '0' || ch > '9') {
                    i--;
                    state = "start";
                    builder = new StringBuilder();
                } else if (ch >= '0' && ch <= '9') {
                    state = "number";
                    builder.append(ch);
                }
            }
        }
        if("var".equals(state)){
            characterSet.add(builder.toString().trim());
        }
        if("method".equals(state)||"logic".equals(state)||"number".equals(state)){
            retainSet.add(builder.toString().trim());
        }

        return retainSet;
    }

}
