package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.AllPunishService;
import com.yupaopao.risk.console.service.PunishManageService;
import com.yupaopao.risk.console.utils.ThreadPoolUtils;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.DropResult;
import com.yupaopao.risk.console.web.support.utils.DropUserCallable;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/drop")
@AuthRequired(permissionName = "system:userDrop:all")
@Slf4j
public class DropUserController {

    @Autowired
    private PunishManageService punishManageService;
    @Autowired
    private AllPunishService allPunishService;
    @Autowired
    private ThreadPoolUtils threadPoolUtils;

    @Value("${invalidAccessTokenPunishIds:}")
    private String invalidAccessTokenPunishIds;

    @RequestMapping("/listPackages")
    public JsonResult listPackages() {
        if(StringUtils.isNotBlank(invalidAccessTokenPunishIds)){
            return JsonResult.success(allPunishService.listPackagesByIds(Lists.newArrayList(invalidAccessTokenPunishIds.split(",")).stream().map(id->Long.valueOf(id)).collect(Collectors.toList())));
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    private List<Long> validCheck(UserDropVO userDropVO) {
        if (userDropVO == null) {
            throw new RiskException("踢出用户入参不能为空！");
        }
        if(null == userDropVO.getPkgCode()){
            throw new RiskException("请选择惩罚包！");
        }
        if (StringUtils.isBlank(userDropVO.getUids())) {
            throw new RiskException("踢出用户uids不能为空");
        } else {
            List<Long> uidList = new ArrayList<>();
            for (String uidStr : userDropVO.getUids().split("\n")) {
                try{
                    if(StringUtils.isNumeric(uidStr)){
                        uidList.add(Long.parseLong(uidStr));
                    }
                }catch(Exception exp){
                    log.error("转换uid发生异常",exp.getMessage());
                }
            }
            if (uidList.size() > 10000) {
                throw new RiskException("踢出用户uids总数不能超过1万");
            }
            return uidList;
        }
    }

    @RequestMapping("/dropUsers")
    public JsonResult dropUsers(@RequestBody UserDropVO userDropVO, @CurrentUser User user) {
        log.info("需要踢出以下用户:" + JSONObject.toJSONString(userDropVO));
        try {
            List<Long> uids = validCheck(userDropVO);
            List<Long> unDoneUids = new ArrayList<>();
            AtomicInteger count = new AtomicInteger(0);
            List<Future<DropResult>> futureList = new ArrayList<>();
            for (Long uid : uids) {
                DropUserCallable callable = new DropUserCallable(uid,userDropVO.getPkgCode(),punishManageService,user.getName());
                Future<DropResult> future = threadPoolUtils.submit(callable);
                futureList.add(future);
            }
            for(Future<DropResult> future : futureList){
                DropResult dropResult = future.get();
                if(dropResult.getDone()){
                    count.incrementAndGet();
                }else{
                    unDoneUids.add(dropResult.getUid());
                }
            }
            Map<String, Object> map = new HashMap<>(1);
            map.put("count", count);
            map.put("unDoneUids", StringUtils.join(unDoneUids,"\n"));
            return JsonResult.success(map);
        } catch (RiskException exception){
            log.error("踢出用户产生异常"+JSONObject.toJSONString(exception));
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, exception.getMessage());
        }catch (Exception exception) {
            log.error("调用业务方接口踢出用户产生异常", exception);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, exception.getMessage());
        }
    }

    @Getter
    @Setter
    static class UserDropVO implements Serializable {
        private Long pkgCode;
        private String uids;
    }

}



