package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.Permission;
import com.yupaopao.risk.common.model.PunishBusiness;
import com.yupaopao.risk.common.model.PunishChannel;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.PunishBusinessVO;
import com.yupaopao.risk.console.service.PermissionService;
import com.yupaopao.risk.console.service.PunishBusinessService;
import com.yupaopao.risk.console.service.PunishChannelService;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 业务方管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/riskPunish/business")
@AuthRequired(permissionName = "riskPunish:channelManage:all")
@Slf4j
public class PunishBusinessController extends AbstractModelController<PunishBusiness, PunishBusiness, PunishBusinessService> {

    @Autowired
    private PermissionService permissionService;
    @Value("${punish.business.permission.parentId}")
    private long parentId;
    @Autowired
    private PunishChannelService punishChannelService;
    private final String PERMISSION_NAME = "riskPunish:business:";

    @Override
    protected void addValid(PunishBusiness record, User user) {
        //不可重名校验
        if (getService().isRepeat(record.getCode(), record.getName())) {
            throw new RiskException("编号或名称已被使用！");
        }
        record.setModifier(user.getName());
        //新增权限
        Permission permission = new Permission();
        try {
            permission.setName(PERMISSION_NAME + record.getCode());
            permission.setDescription(record.getName());
            permission.setParentId(parentId);
            permission.setType(2);
            permission.setOrderNum(getService().selectCount(null) + 1);
            permission.setAuthor("system");
            permissionService.insertSelective(permission);
        } catch (Exception e) {
            log.info("惩罚业务方自动新增权限异常 record:{}", record, e);
        }
    }

    @Override
    protected void updateValid(PunishBusiness record, User user) {
        //不可重名校验
        if (getService().isRepeat(null, record.getName())) {
            throw new RiskException("名称已被使用！");
        }
        record.setModifier(user.getName());
    }

    @Override
    protected void deleteValid(long id, User user) {
        PunishBusiness business = getService().get(id);
        if (business == null) {
            throw new RiskException("未查询到该业务方信息，请刷新后再试！");
        }
        //含绑定的渠道不可删除
        if (getService().hasChannel(id)) {
            throw new RiskException("该业务方已绑定渠道，不可删除！");
        }
        //删除权限
        Permission permission = new Permission();
        try {
            permission.setName(PERMISSION_NAME + business.getCode());
            permission.setDescription(business.getName());
            permission.setParentId(parentId);
            permission.setType(2);
            permission.setAuthor("system");
            permission.setDelFlag(false);
            List<Permission> permissions = permissionService.search(permission);
            if (permissions != null && permissions.size() == 1) {
                permissionService.deleteLogicalById(permissions.get(0).getId());
            }
        } catch (Exception e) {
            log.info("惩罚业务方自动删除权限异常 permission:{}", permission, e);
        }
    }

    @AuthRequired(permissionName = "*")
    @RequestMapping("getChannels")
    public JsonResult getChannels() {
        List list = new ArrayList();
        List<PunishBusiness> businesses = getService().search(new PunishBusiness());
        if (CollectionUtils.isNotEmpty(businesses)) {
            businesses.stream().forEach(record -> {
                List<PunishChannel> channels = new ArrayList<>();
                PunishBusinessVO business = new PunishBusinessVO();
                business.setCode(record.getCode());
                business.setName(record.getName());
                business.setPermissionName(PERMISSION_NAME + business.getCode());
                business.setChannels(channels);
                List<PunishChannel> punishChannels = punishChannelService.getByBid(record.getId());
                if (CollectionUtils.isNotEmpty(punishChannels)) {
                    punishChannels.stream().forEach(r -> {
                        PunishChannel channel = new PunishChannel();
                        channel.setCode(r.getCode());
                        channel.setName(r.getName());
                        channels.add(channel);
                    });
                }
                list.add(business);
            });
        }
        return JsonResult.success(list);
    }
}
