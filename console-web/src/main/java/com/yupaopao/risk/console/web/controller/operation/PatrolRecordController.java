package com.yupaopao.risk.console.web.controller.operation;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.PatrolRuleStateEnum;
import com.yupaopao.risk.common.enums.SupportRuleType;
import com.yupaopao.risk.common.model.PatrolRecord;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.PatrolRecordService;
import com.yupaopao.risk.console.service.PatrolRuleService;
import com.yupaopao.risk.console.utils.DateUtils;
import com.yupaopao.risk.console.vo.PatrolRecordVO;
import com.yupaopao.risk.console.vo.PatrolRuleVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 巡检相关
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("operation/patrolRecord")
@AuthRequired(permissionName = "operation:patrol:record")
public class PatrolRecordController extends AbstractModelController<PatrolRecord, PatrolRecordVO, PatrolRecordService> {

    @Autowired
    private PatrolRuleService patrolRuleService;

    @RequestMapping("/hit")
    public JsonResult hit(@RequestBody PatrolRecordVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<PatrolRecord> result = getService().hit(record, page, size);
        getService().fetchRelations(result.getList());
        return JsonResult.success(result);
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @RequestBody PatrolRecordVO record, @CurrentUser User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getDealState(), "请指定操作动作");

        record.setModifier(user.getName());
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @SuppressWarnings("unchecked")
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "巡检记录导出", isReportResponse = false)
    @RequestMapping("export")
    @AuthRequired(permissionName = "operation:patrol:recordExport")
    public JsonResult export(@RequestBody PatrolRecordVO vo) {
        //本导出最多只能导出10000条
        try {
            PageInfo<PatrolRecord> search = getService().hit(vo, 1, 10000);
            List<PatrolRecord> result = search.getList();
            if (!CollectionUtils.isEmpty(result)) {
                List<PatrolRule> patrolRules = patrolRuleService.selectAll();
                Map<Long, String> ruleNameMap = new HashMap<>(32);
                if (null != patrolRules) {
                    for (PatrolRule patrolRule : patrolRules) {
                        ruleNameMap.put(patrolRule.getId(), patrolRule.getName());
                    }
                }

                Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils
                        .buildExcelFormatter(ExcelFormatter.builder("traceId").addName("TraceID").addWidth(50).build(), ExcelFormatter.builder("id").addName("记录ID").build(),
                                ExcelFormatter.builder("patrolName").addName("巡检规则名称").addWidth(50).build(), ExcelFormatter.builder("eventCode").addName("事件Code").build(),
                                ExcelFormatter.builder("uid").addName("用户UID").build(), ExcelFormatter.builder("deviceId").addName("设备ID").addWidth(70).build(),
                                ExcelFormatter.builder("dealState").addName("当前状态").build(), ExcelFormatter.builder("modifier").addName("处理人").build(),
                                ExcelFormatter.builder("times").addName("连续命中次数").build(),
                                ExcelFormatter.builder("msgTime").addName("初次触发时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                                ExcelFormatter.builder("updateTime").addName("处理时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build());

                List<Map<String, Object>> list = new ArrayList<>();
                for (PatrolRecord patrolRecord : result) {
                    Map<String, Object> ssm = (Map<String, Object>) JSONObject.toJSON(patrolRecord);
                    BeanUtils.copyProperties(patrolRecord, ssm);
                    Integer dealState = patrolRecord.getDealState();
                    if (dealState.equals(2)) {
                        ssm.put("dealState", "不处理");
                    } else if (dealState.equals(1)) {
                        ssm.put("dealState", "已处理");
                    } else {
                        ssm.put("dealState", "未处理");
                    }
                    ssm.put("patrolName", ruleNameMap.getOrDefault(patrolRecord.getPatrolRuleId(), "/"));
                    list.add(ssm);
                }
                String timeStamp = DateUtils.formatDate(new Date(), DateUtils.YYYYMMDDHHMM);
                String fileName = ExcelUtils.exportExcelByMap(list, "巡检记录_" + timeStamp, formatterMap);
                return JsonResult.success(fileName);
            } else {
                return JsonResult.error(new RiskException("无记录相应记录"));
            }
        } catch (Throwable e) {
            log.error("导出惩罚记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

    @RequestMapping("/searchAllRules")
    public JsonResult searchAllRules() {
        PatrolRuleVO record = new PatrolRuleVO();
        record.setType(SupportRuleType.PATROL_RULE.getCode());
        record.setState(PatrolRuleStateEnum.ENABLE.getCode());
        PageInfo<PatrolRule> search = patrolRuleService.search(record, 1, 10_000);
        return JsonResult.success(search);
    }
}
