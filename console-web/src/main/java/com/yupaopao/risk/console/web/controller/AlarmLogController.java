package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.AlarmLog;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.AlarmLogVO;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.service.AlarmLogService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.web.bind.annotation.*;

/**
 * author: lijianjun
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/alarm/log")
@AuthRequired(permissionName = "alarm:log:all")
public class AlarmLogController extends AbstractModelController<AlarmLog, AlarmLogVO, AlarmLogService> {

    @RequestMapping("/search")
    public JsonResult search(@RequestBody AlarmLogVO alarmLogVO, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(this.getService().searchLog(alarmLogVO,page,size));
    }

    @RequestMapping(value = "/updateHandleState", method = RequestMethod.POST)
    public JsonResult updateHandleState(@RequestBody AlarmLogVO vo, @CurrentUser User user) {
        Assert.notNull(vo.getId(), "缺少必要参数！");
        Assert.notNull(vo.getHandleState(), "缺少必要参数！");
        vo.setModifier(user.getName());
        return JsonResult.success(this.getService().updateHandleState(vo));
    }

}
