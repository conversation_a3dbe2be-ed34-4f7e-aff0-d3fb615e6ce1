package com.yupaopao.risk.console.web.controller.content;

import com.github.pagehelper.PageInfo;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.console.service.ContentService;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/content")
@Slf4j
public class ContentController {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Autowired
    private ContentService contentService;

    @AuthRequired(permissionName = "content:search:all")
    @RequestMapping("search")
    public JsonResult search(@RequestBody LogSearchVO.ContentLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchContentLog(vo));
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控内容汇总查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:trend:all")
    @RequestMapping("summary")
    public JsonResult summary(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.summary(vo));
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控内容趋势查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:trend:all")
    @RequestMapping("trend")
    public JsonResult trend(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.trend(vo));
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控事件分析",isReportResponse = false)
    @AuthRequired(permissionName = "content:trend:all")
    @RequestMapping("event")
    public JsonResult event(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.event(vo));
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控拒绝内容查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:trend:all")
    @RequestMapping("reject")
    public JsonResult reject(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.reject(vo));
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控审核内容查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:trend:all")
    @RequestMapping("review")
    public JsonResult review(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.review(vo));
    }


    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控违禁词查询",isReportResponse = false)
    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping("hotProhibitedWord")
    public JsonResult hotProhibitedWord(@RequestBody LogSearchVO vo) {
        return JsonResult.success(contentService.hotProhibitedWords(vo));
    }

    @AuthRequired(permissionName = "content:hotProhibitedWord:all")
    @RequestMapping("export")
    public JsonResult export(@RequestBody LogSearchVO vo) {
        try {
            PageInfo pageInfo = contentService.hotProhibitedWords(vo);
            Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                    ExcelFormatter.builder("id").addName("top排名").build(),
                    ExcelFormatter.builder("value").addName("总数").build(),
                    ExcelFormatter.builder("key").addName("违禁内容").build()
            );
            String fileName = ExcelUtils.exportExcelByMap(pageInfo.getList(),"违禁词热点排行榜",formatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            log.error("导出违禁词热点排行榜Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }

}
