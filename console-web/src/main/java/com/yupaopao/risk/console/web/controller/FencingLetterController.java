package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Letter;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.LetterService;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/letter")
@AuthRequired(permissionName = "core:fencingletter:all")
public class FencingLetterController extends AbstractModelController<Letter, Letter, LetterService> {


    @Override
    protected void addValid(Letter record, User user) {
        record.setAuthor(user.getName());
        record.setModifier(user.getName());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
    }

    @Override
    protected void updateValid(Letter record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");

        record.setModifier(user.getName());
        record.setUpdateTime(new Date());
    }
}
