package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.utils.RiskModelIdUtils;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.web.config.bizlog.BizLogConfig;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/event")
@AuthRequired(permissionName = "join:event:all")
@Slf4j
public class EventController extends AbstractModelController<Event, EventVO, EventService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;
    @Autowired
    private BizLogConfig bizLogConfig;

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控事件查询查询",isReportResponse = false)
    @RequestMapping("/search")
    @AuthRequired(permissionName = "join:event:search")
    public JsonResult search(@RequestBody EventVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo result = getService().search(record, page, size);
        List<EventVO> list = getService().relationFetch(result.getList());
        result.setList(list);
        return JsonResult.success(result);
    }

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @AuthRequired(permissionName = "join:event:up")
    public JsonResult add(@Valid @RequestBody EventVO record, @CurrentUser User user) {
        addValid(record, user);
        boolean result = getService().insertSelective(record);
        if (result) {
            getService().bindParamValidators(record, user);
        }
        return JsonResult.success(result);
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    @AuthRequired(permissionName = "join:event:up")
    public JsonResult update(@PathVariable long id, @Valid @RequestBody EventVO record, @CurrentUser User user) {
        updateValid(record, user);
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控事件查询查询",isReportResponse = false)
    @RequestMapping("/getEvents")
    @AuthRequired(role = {Role.ADMIN, Role.MANAGER, Role.CREDIT, Role.NORMAL},permissionName = "*")
    public JsonResult getEvents() {
        return JsonResult.success(getService().simpleAll());
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控事件查询查询",isReportResponse = false)
    @RequestMapping("/getEventsByCodes")
    @AuthRequired(role = {Role.ADMIN, Role.MANAGER, Role.CREDIT, Role.NORMAL},permissionName = "*")
    public JsonResult getEventsByCodes() {
        Set<String> codes = new HashSet<>();
        if(null != bizLogConfig.getSearch()){
            codes.addAll(bizLogConfig.getSearch().keySet());
        }
        if(null != bizLogConfig.getShow()){
            codes.addAll(bizLogConfig.getShow().keySet());
        }
        return JsonResult.success(getService().getEventsByCodes(codes));
    }
    @OpRecordReport(opType = OpTypeEnum.ADD, opDesc = "风控新增事件",isReportResponse = false)
    @Override
    protected void addValid(EventVO record, User user) {
        Assert.notNull(record.getBusinessCode(), "请选择业务类型");
        Assert.notNull(record.getCode(), "请指定事件Code");
        Assert.notNull(record.getName(), "请指定事件名称");
        Assert.isTrue(CollectionUtils.isNotEmpty(record.getGroupRules()), "请选择规则");
        Assert.hasLength(record.getAccessBy(),"请填写接入方");
        Event event = this.getService().getByCode(record.getCode());
        Assert.isNull(event,"事件Code重复");
        event = this.getService().getByName(record.getName());
        Assert.isNull(event,"事件名称重复");
        record.setAuthor(user.getName());
        valid(record, user);
    }
    @OpRecordReport(opType = OpTypeEnum.ADD, opDesc = "风控修改事件",isReportResponse = false)
    @Override
    protected void updateValid(EventVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getName(), "请指定事件名称");
        Assert.isTrue(CollectionUtils.isNotEmpty(record.getGroupRules()), "请选择规则");
        Event event = this.getService().getByName(record.getName());
        Assert.isTrue((event==null || event.getId().equals(record.getId())),"事件名称重复");
        record.setBusinessCode(null); // 禁止修改
        record.setCode(null); // 禁止修改
        record.setAuthor(null); // 禁止修改
        valid(record, user);
        record.setTimeOut(record.getTimeOut() == null ? 0 : record.getTimeOut());
        getService().bindParamValidators(record, user);
    }

    protected void valid(EventVO record, User user) {
        record.setModifier(user.getName());
        if (CollectionUtils.isEmpty(record.getGroupRules())) {
            record.setRuleGroupId("");
        } else {
            Set<Long> ids = record.getGroupRules().stream().map(group -> group.getId()).collect(Collectors.toSet());
            record.setRuleGroupId(RiskModelIdUtils.join(ids));
        }
        if (CollectionUtils.isEmpty(record.getGrayGroupList())) {
            record.setGrayGroups("");
        } else {
            Set<Long> ids = record.getGrayGroupList().stream().map(group -> group.getId()).collect(Collectors.toSet());
            record.setGrayGroups(RiskModelIdUtils.join(ids));
        }

    }


    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild event cache , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_ACCESS)
                .namespace(ConstantsForApollo.EVENT_CACHE)
                .itemKey(ConstantsForApollo.EVENT_CACHE_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger event cache rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

    @OpRecordReport(opType = OpTypeEnum.UPDATE, opDesc = "风控修改事件是否保存规则结果",isReportResponse = false)
    @RequestMapping(value = "/updateSaveRuleResult", method = RequestMethod.POST)
    public JsonResult updateSaveRuleResult(@Valid @RequestBody EventVO record, @CurrentUser User user) {
        return JsonResult.success(getService().updateSaveRuleResult(record,user));
    }

    @OpRecordReport(opType = OpTypeEnum.UPDATE, opDesc = "风控修改事件默认结果",isReportResponse = false)
    @RequestMapping(value = "/updateDefaultLevel", method = RequestMethod.POST)
    @AuthRequired(permissionName = "join:event:updateDefaultLevel")
    public JsonResult updateDefaultLevel(@Valid @RequestBody EventVO record, @CurrentUser User user) {
        return JsonResult.success(getService().updateDefaultLevel(record,user));
    }

}
