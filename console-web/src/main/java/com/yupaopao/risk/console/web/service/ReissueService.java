package com.yupaopao.risk.console.web.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.github.pagehelper.PageInfo;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.model.HitResult;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.util.concurrent.ListenableFuture;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ReissueService {

    @Autowired
    private RedisService redisService;
    @Autowired
    private ElasticSearchService elasticSearchService;
    @KafkaAutowired("middleware.kafka.risk")
    private KafkaProducer kafkaProducer;

    @Value("${reissue.time.start:2020-04-24 11:30:00}")
    private String startTime;
    @Value("${reissue.time.end:2020-04-24 12:00:00}")
    private String endTime;
    @Value("${reissue.switch:false}")
    private boolean sendSwitch;
    @Value("${reissue.test.batchId:}")
    private String tBatchId;
    @Value("${reissue.test.userId:}")
    private String tUserId;


    private List<Map<String, Object>> getList(int page) {
        //查询所有的batchId
        PageInfo<Map<String, Object>> pageInfo1 = new PageInfo<>();
        pageInfo1.setPageSize(1000);
        pageInfo1.setPageNum(1);
        getBatchIds(pageInfo1, page);
        return pageInfo1.getList();
    }

    public void excute() {
        log.info("审核数据补发开始");
        RedisService.RedisLocker redisLocker = null;
        try {
            redisLocker = redisService.buildLock("LOCK_RISK_CONSOLE_REISSUE", 300, TimeUnit.SECONDS);
            if (!redisLocker.tryLock()) {
                log.info("审核数据补发 获取锁失败");
                return;
            }
            int page = 1;
            while (true && page < 50) {
                List<Map<String, Object>> pageList1 = getList(page);
                if (CollectionUtils.isEmpty(pageList1)) {
                    log.info("审核数据补发 pageList1为空 page:{}", page);
                    return;
                }
                page++;
                doExcute(pageList1);
            }
            log.info("审核数据补发成功");
        } catch (Exception e) {
            log.error("审核数据补发 执行异常", e);
        } finally {
            if (redisLocker != null) {
                redisLocker.release();
            }
        }
    }

    private void doExcute(List<Map<String, Object>> pageList1) {
        try {
            //遍历查询batchId下的审核数据
            for (Map<String, Object> page1 : pageList1) {
                PageInfo<Map<String, Object>> pageInfo2 = new PageInfo<>();
                pageInfo2.setPageSize(1000);
                pageInfo2.setPageNum(1);
                String batchId = ((Map) page1.get("data")).get("BatchId") + "";
                if (StringUtils.isEmpty(batchId)) {
                    log.info("审核数据补发 batchId为空 page1:{}", page1);
                    continue;
                }
                Long uid = Long.parseLong(page1.get("userId") + "");
                getData(pageInfo2, batchId, uid);
                List<Map<String, Object>> pageList2 = pageInfo2.getList();
                if (CollectionUtils.isEmpty(pageList2)) {
                    log.info("审核数据补发 pageList2为空 page1:{}", page1);
                    continue;
                }
                List<Map<String, Object>> data10 = new ArrayList<>();
                List<Map<String, Object>> data20 = new ArrayList<>();
                List<Map<String, Object>> data30 = new ArrayList<>();
                List<Map<String, Object>> data70 = new ArrayList<>();
                for (Map<String, Object> page2 : pageList2) {
                    JSONObject json = new JSONObject(page2);
                    Map<String, Object> map = new HashMap<>();
                    String appId = json.getJSONObject("data").getString("AppId");
                    map.put("batchId", batchId);
                    map.put("appId", appId);
                    map.put("traceId", json.getString("traceId"));
                    map.put("riskLevel", json.getString("level"));

//                    log.info("riskReason--json.getJSONObject(\"result\"):{}",json.getJSONObject("result"));

                    map.put("riskReason", json.getJSONObject("result").getString("reason"));
                    map.put("sendTime", json.getDate("createdAt").getTime());
                    map.put("bizId", json.getJSONObject("data").getString("bizId"));
                    String source = json.getJSONObject("data").getString("source");
                    map.put("source", StringUtils.isEmpty(source) ? "0" : source);
                    map.put("uid", null == uid ? 0L : uid);

                    Map<String, Object> audits = new HashMap<>();
                    audits.put("subId", "sub-1");
                    audits.put("eventType", "BIGGIE_CERT_AUDIT");

                    Map<String, Object> content = new HashMap<>();

                    JSONObject data = json.getJSONObject("data");
                    if (!StringUtils.isEmpty(data.get("avatar"))) {
                        content.put("image", data.get("avatar"));
                    }
                    if (!StringUtils.isEmpty(data.get("type"))) {
                        content.put("type", data.get("type"));
                    }
                    if (!StringUtils.isEmpty(data.get("audio"))) {
                        content.put("audio", data.get("audio"));
                    }
                    if (data.containsKey("audioCheck") && !StringUtils.isEmpty(data.getJSONObject("audioCheck").getString("gender"))) {
                        content.put("gender", data.getJSONObject("audioCheck").getString("gender"));
                    }
                    if (data.containsKey("audioCheck") && !StringUtils.isEmpty(data.getJSONObject("audioCheck").getString("audioText"))) {
                        content.put("convertText", data.getJSONObject("audioCheck").getString("audioText"));
                    }
                    if (!StringUtils.isEmpty(data.get("content"))) {
                        content.put("text", data.get("content"));
                    }
                    if (!StringUtils.isEmpty(data.get("certImage"))) {
                        content.put("certImage", data.get("certImage"));
                    }
                    if (!StringUtils.isEmpty(data.get("certContent"))) {
                        content.put("certText", data.get("certContent"));
                    }
                    if (!StringUtils.isEmpty(data.get("certId"))) {
                        content.put("certId", data.get("certId"));
                    }
                    if (!StringUtils.isEmpty(data.get("mode"))) {
                        content.put("mode", data.get("mode"));
                    }

                    audits.put("content", content);
                    map.put("audits", audits);

                    if ("10".equals(appId)) {
                        data10.add(map);
                        Cat.logMetricForCount("audit.forward.reissue.success");
                    } else if ("20".equals(appId)) {
                        data20.add(map);
                        Cat.logMetricForCount("audit.forward.reissue.success");
                    } else if ("30".equals(appId)) {
                        data30.add(map);
                        Cat.logMetricForCount("audit.forward.reissue.success");
                    } else if ("70".equals(appId)) {
                        data70.add(map);
                        Cat.logMetricForCount("audit.forward.reissue.success");
                    } else {
                        log.info("审核数据补发 appId不存在 page2:{}", page2);
                    }
                }
                sendMsg(data10);
                sendMsg(data20);
                sendMsg(data30);
                sendMsg(data70);
                Cat.logMetricForCount("audit.forward.reissue.times");

            }
        } catch (Exception e) {
            log.error("审核数据补发异常", e);
        }
    }

    private void sendMsg(List<Map<String, Object>> data) {
        try {
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            String appId = data.get(0).get("appId") + "";
            if (StringUtils.isEmpty(appId)) {
                log.info("审核数据补发 appId不存在 data:{}", data);
                return;
            }
            String json = JSONArray.toJSONString(data);
            String topic = getTargetTopic(Integer.parseInt(appId));
            log.info("审核数据补发 转发数据流到审核系统详情:{} / {}", topic, json);
            if (!sendSwitch) {
                log.info("审核数据补发 开关关闭");
                return;
            }
            ListenableFuture<SendResult<String, String>> future = kafkaProducer.send(topic, json); // 通过MQ转发数据到审核系统
            future.addCallback(r -> log.info("审核数据补发 转发数据流到审核系统成功"), e -> log.error("审核数据补发 转发数据流到审核系统失败", e));
        } catch (Exception e) {
            log.error("审核数据补发 异常", e);
        }
    }

    private String getTargetTopic(int appId) {
        return String.format("AUDIT_%d", appId);
    }

    public LogSearchVO.HitLogSearchVO getVo(int page) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = format.parse(startTime);
        Date end = format.parse(endTime);
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setPage(page);
        vo.setSize(1000);
        vo.setStartTime(start);
        vo.setEndTime(end);
        HitResult hitResult = new HitResult();
        hitResult.setEventCode("biggie-cert-audit");
        vo.setQuery(hitResult);
        return vo;
    }

    private void getBatchIds(PageInfo<Map<String, Object>> pageInfo, int page) {
        try {
            SearchRequest searchRequest = RiskLogESUtil.buildSearchRequestIm(getVo(page), RiskLogESUtil.HIT_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            query.must(QueryBuilders.termQuery("data.type", "other"));
            if (!StringUtils.isEmpty(tBatchId)) {
                query.must(QueryBuilders.termQuery("data.BatchId", tBatchId));
            }
            if (!StringUtils.isEmpty(tUserId)) {
                query.must(QueryBuilders.termQuery("data.UserId", tUserId));
            }
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补发 获取文档getBatchIds异常", e);
        }
    }

    private void getData(PageInfo<Map<String, Object>> pageInfo, String batchId, Long uid) {
        try {
            SearchRequest searchRequest = RiskLogESUtil.buildSearchRequestIm(getVo(1), RiskLogESUtil.HIT_LOG_INDEX, RiskLogESUtil.pattern_default);
            BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
            if (!StringUtils.isEmpty(tBatchId)) {
                batchId = tBatchId;
            }
            if (!StringUtils.isEmpty(tUserId)) {
                uid = Long.parseLong(tUserId);
            }
            query.must(QueryBuilders.termQuery("data.BatchId", batchId));
            query.must(QueryBuilders.termQuery("data.UserId", uid));
            handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_LOG_INDEX);
        } catch (Exception e) {
            log.error("审核数据补发 获取文档getData异常", e);
        }
    }

    public void handleResponse(SearchRequest request, PageInfo pageInfo, String index) {
        try {
            SearchResponse response = elasticSearchService.getClient().search(request);
            if (response != null && response.status() == RestStatus.OK) {
                RiskLogESUtil.handleResponse(response, pageInfo);
            } else {
                log.error("审核数据补发 搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("审核数据补发 搜索文档异常", e);
        }
    }

}
