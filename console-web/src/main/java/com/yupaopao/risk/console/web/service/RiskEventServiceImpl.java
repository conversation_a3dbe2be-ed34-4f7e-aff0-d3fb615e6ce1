package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.console.api.RiskEventService;
import com.yupaopao.risk.console.bean.RiskEvent;
import com.yupaopao.risk.console.service.EventService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * author: liji<PERSON>jun
 * date: 2020/4/27 20:23
 */
@Slf4j
@Component
@Service
public class RiskEventServiceImpl implements RiskEventService {

    @Autowired
    private EventService eventService;

    @Override
    public Map<String,RiskEvent> list(List<String> codes) {
        Map<String, Event> eventMap = eventService.map(codes);
        Map<String,RiskEvent> riskEventMap = Maps.newHashMapWithExpectedSize(eventMap.size());
        eventMap.forEach((k,v) -> {
            RiskEvent riskEvent = new RiskEvent();
            BeanUtils.copyProperties(v,riskEvent);
            riskEventMap.put(k, riskEvent);
        });
        return riskEventMap;
    }
}
