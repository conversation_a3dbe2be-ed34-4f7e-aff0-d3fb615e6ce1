package com.yupaopao.risk.console.web.config.bizlog;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.beans.PropertyDescriptor;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2019/11/21 11:43
 */
@Component
@Slf4j
public class RefreshBizLog implements ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @ApolloConfigChangeListener("bizLogConfig")
    private void someChangeHandler(ConfigChangeEvent changeEvent) {
        changeEvent.changedKeys().stream().map(changeEvent::getChange).forEach(change -> {
            String vKey = change.getPropertyName();
            String newValue = change.getNewValue();
            changeProperty(vKey,newValue,"event.bizlog.search","search");
            changeProperty(vKey,newValue,"event.bizlog.show","show");
        });
    }
    private void changeProperty(String vKey,String newValue,String prefix,String propertyName){
        if (vKey.startsWith(prefix)) {
            //取出对应的field 反射重新赋值
            BizLogConfig bizLogConfig = applicationContext.getBean(BizLogConfig.class);
            try {
                PropertyDescriptor propertyDescriptor = new PropertyDescriptor(propertyName, BizLogConfig.class);
                Map<String, String> map = (Map<String, String>) propertyDescriptor.getReadMethod().invoke(bizLogConfig);
                map.put(vKey.substring(prefix.length()+1,vKey.length()), newValue);
                propertyDescriptor.getWriteMethod().invoke(bizLogConfig, map);
            } catch (Exception e) {
                log.error("replace field {} error", vKey);
            }
        }
    }
}