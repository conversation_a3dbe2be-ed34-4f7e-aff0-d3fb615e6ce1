package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.RiskHitLogSearch;
import com.yupaopao.risk.console.api.RiskHitLogService;
import com.yupaopao.risk.console.bean.LogRequestSearchVO;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Service
@Component
public class HitLogServiceImpl implements RiskHitLogService {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Override
    public PageInfo<Map<String, Object>> search(LogRequestSearchVO.HitLogRequestSearchVO request) {

        try{
            LogSearchVO.HitLogRequestSearchVO vo = new LogSearchVO.HitLogRequestSearchVO();
            RiskHitLogSearch search = new RiskHitLogSearch();
            BeanUtils.copyProperties(search,request.getQuery());

            vo.setQuery(search);
            vo.setStartTime(request.getStartTime());
            vo.setEndTime(request.getEndTime());
            vo.setPage(request.getPage());
            vo.setSize(request.getSize());
            vo.setRmMobile(false);

            PageInfo<Map<String, Object>> result = elasticSearchService.searchHitLogByRequest(vo);
            return result;
        }catch(Exception exp){
            log.error("查询风控命中记录异常",exp.getMessage());
        }

        return null;
    }
}
