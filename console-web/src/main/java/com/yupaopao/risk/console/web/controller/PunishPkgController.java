package com.yupaopao.risk.console.web.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.audit.complain.api.entity.CodeDesc;
import com.yupaopao.platform.audit.complain.api.service.ComplainProviderService;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.web.config.CategoryConfig;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.api.ManageService;
import com.yupaopao.risk.punish.bean.TagInfo;
import com.yupaopao.risk.punish.request.manage.PackageRichBO;
import com.yupaopao.risk.punish.request.manage.PackageRichRequest;
import com.yupaopao.risk.punish.request.manage.PackageRichResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 惩罚包管理接口
 *
 * <AUTHOR> Hu
 * @since 1.12.20
 * <p>
 * 2020/8/3 15
 */
@Slf4j
@RestController
@RequestMapping("/punish/package")
//@AuthRequired(permissionName = "core:punish:all")
public class PunishPkgController {

    @Reference(check = false)
    private ManageService manageService;
    @Reference(check = false)
    private ComplainProviderService complainProviderService;
    @Autowired
    private CategoryConfig categoryConfig;
    @ApolloConfig
    private Config config;
    private static final String DEFAULT_REALTIME_PUNISH_SCENES = "通用惩罚,巡检规则,举报规则";

    @RequestMapping("/getCategories")
    public JsonResult getCategories() {
        return JsonResult.success(categoryConfig.getCategoryList());
    }

    @RequestMapping("/getRealtimeScenes")
    public JsonResult getRealtimeScenes() {
        return JsonResult.success(config.getArrayProperty("realtime.punish.scenes", ",", DEFAULT_REALTIME_PUNISH_SCENES.split(",")));
    }

    @RequestMapping("/search")
    public JsonResult search(@RequestBody PackageRichRequest record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {

        PackageRichBO req = record;
        if (req == null) {
            req = new PackageRichBO();
        }

        Response<PageInfo<PackageRichResponse>> search = manageService.search(req, page, size);
        PageInfo<PackageRichResponse> ret = search.getResult();
        return JsonResult.success(ret);
    }

    @RequestMapping("/add")
    public JsonResult add(@Valid @RequestBody PackageRichRequest record, @CurrentUser User user) {
        log.info("开始创建惩罚包: {}", JSONObject.toJSONString(record));
        record.setModifier(user.getName());
        Response<?> response = manageService.create(record);
        if (response.isSuccess()) {
            return JsonResult.success();
        } else {
            return JsonResult.error(new RiskException("9201", "调用远程服务异常, msg: " + response.getMsg()));
        }
    }

    @RequestMapping("/getTags")
    public JsonResult getTags(@RequestParam(value = "channel", defaultValue = "REALTIME_RISK") String channel) {
        log.info("[查询所有惩罚能力]");
        Response<List<TagInfo>> tagCodeAndNames = manageService.getTags(channel);
        if (tagCodeAndNames.isSuccess()) {
            return JsonResult.success(tagCodeAndNames.getResult());
        } else {
            log.error("请求Dubbo出错, response: {}", tagCodeAndNames);
            return JsonResult.error(new RiskException("9201", "调用远程服务异常"));
        }
    }

    @PostMapping(value = "/update/{id}")
    public JsonResult update(@PathVariable Long id, @Valid @RequestBody PackageRichRequest record, @CurrentUser User user) {
        log.info("开始修改惩罚包: id: {}, record: {}", id, JSONObject.toJSONString(record));
        record.setModifier(user.getName());
        Response<?> response = this.manageService.update(record);
        if (response.isSuccess()) {
            return JsonResult.success();
        } else {
            return JsonResult.error(new RiskException("9201", "调用远程服务异常"));
        }
    }

    @PostMapping("delete/{id}")
    public JsonResult delete(@PathVariable Long id) {
        log.info("删除惩罚包ID: {}", id);
        if (null != id) {
            Response<?> deleteRet = manageService.delete(id);
            if (deleteRet.isSuccess()) {
                return JsonResult.success();
            }
            return JsonResult.error(new RiskException("9201", "调用远程服务异常"));
        } else {
            return JsonResult.error(new RiskException("9200", "删除失败, ID非法"));
        }
    }

    @RequestMapping("getNecessary")
    public JsonResult getNecessary(@RequestParam String source) {
        Response<List<CodeDesc>> response = complainProviderService.getRequestParam(source);
        if (response != null && response.getResult() != null) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(new ArrayList());
    }

    @RequestMapping("getReasons")
    public JsonResult getReasons(@RequestParam String source) {
        Response<List<CodeDesc>> response = complainProviderService.getComplainReasons(source);
        if (response != null && response.getResult() != null) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(new ArrayList());
    }
}
