package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.PunishChannel;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.PunishChannelService;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渠道管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/riskPunish/channel")
@AuthRequired(permissionName = "riskPunish:channelManage:all")
@Slf4j
public class PunishChannelController extends AbstractModelController<PunishChannel, PunishChannel, PunishChannelService> {

    @Override
    protected void addValid(PunishChannel record, User user) {
        //不可重名校验
        if (getService().isRepeat(record.getCode(), record.getName())) {
            throw new RiskException("编号或名称已被使用！");
        }
        record.setModifier(user.getName());
    }

    @Override
    protected void updateValid(PunishChannel record, User user) {
        //不可重名校验
        if (getService().isRepeat(null, record.getName())) {
            throw new RiskException("名称已被使用！");
        }
        record.setModifier(user.getName());
    }

    @Override
    protected void deleteValid(long id, User user) {
        PunishChannel channel = getService().get(id);
        if (channel == null) {
            throw new RiskException("未查询到该渠道信息，请刷新后再试！");
        }
        //渠道已绑定惩罚包不可删除
        if (getService().isSubscribedPkg(channel.getCode())) {
            throw new RiskException("该渠道已订阅惩罚包，请先取消订阅！");
        }
        //渠道一周有流量不可删除
        if (getService().hasPunishRecord(channel.getCode())) {
            throw new RiskException("该渠道7天内含惩罚记录不可删除！");
        }
        super.deleteValid(id, user);
    }
}
