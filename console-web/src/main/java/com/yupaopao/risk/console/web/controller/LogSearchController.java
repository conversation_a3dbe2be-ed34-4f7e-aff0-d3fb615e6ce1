package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.SqlConstants;
import com.yupaopao.risk.console.service.AtomRuleService;
import com.yupaopao.risk.console.service.AuditService;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.service.GetOffLineRiskDataService;
import com.yupaopao.risk.console.utils.DateUtils;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import com.yupaopao.risk.console.web.support.utils.MapValueConvertUtil;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 风控日志搜索
 *
 * <AUTHOR>
 * @date 2019/2/27 2:36 PM
 */
@RestController
@RequestMapping("/log/search")
public class LogSearchController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LogSearchController.class);
    private static final String EVENT_CODE = "eventCode";

    @Autowired
    private ElasticSearchService elasticSearchService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private AtomRuleService atomRuleService;

    @Autowired
    private GetOffLineRiskDataService getOffLineRiskDataService;

    @ApolloConfig
    private Config config;

    @RequestMapping("getSensitiveEvent")
    public JsonResult getSensitiveEvent(@RequestBody Map<String,Object> log){
        Map<String,Object> result = new HashMap<>(2);

        if(MapUtils.isEmpty(log)||(log.get(EVENT_CODE)==null)){
            result.put("isSensitiveEvent",false);
            return JsonResult.success(result);
        }

        List<String> sensitiveEvents = Arrays.asList(config.getProperty("sensitive.events","").split(","));
        if(sensitiveEvents.contains(log.get(EVENT_CODE).toString())){
            result.put("isSensitiveEvent",true);
            return JsonResult.success(result);
        }

        result.put("isSensitiveEvent",false);
        return JsonResult.success(result);
    }

    @RequestMapping("hasAuth")
    @AuthRequired(permissionName = "search.sensitive.info")
    public JsonResult hasAuth(@CurrentUser User user){
        Map<String,Object> result = new HashMap<>(2);
        result.put("auth",true);

        return JsonResult.success(result);
    }

    @RequestMapping("hit")
    @AuthRequired(permissionName = "view:logsearch:hit")
    public JsonResult search(@RequestBody LogSearchVO.HitLogSearchVO vo) {
        PageInfo<Map<String, Object>> result = elasticSearchService.searchHitLog(vo);
        MapValueConvertUtil.convertValueToString(result.getList(), "data.userData.uid");
        MapValueConvertUtil.encryptMobile(result.getList());
        MapValueConvertUtil.encryptPersonalInfo(result.getList());

        MapValueConvertUtil.obtainReason(result.getList());
        return JsonResult.success(result);
    }

    @RequestMapping("reason")
    @AuthRequired(permissionName = "view:logsearch:reason")
    public JsonResult fetchReason(@RequestParam(value = "ruleId") Long ruleId, @CurrentUser User user) {
        LOGGER.info("内外话术查询: ruleId{}, user: {}", ruleId, user.getChineseName());
        return JsonResult.success(atomRuleService.fetchReasons(ruleId));
    }

    @RequestMapping("third")
    @AuthRequired(permissionName = "view:logsearch:third")
    public JsonResult search(@RequestBody LogSearchVO.ThirdLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchThirdLog(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控送审历史查询", isReportResponse = false)
    @RequestMapping("audit")
    @AuthRequired(permissionName = "view:logsearch:audit")
    public JsonResult search(@RequestBody LogSearchVO.AuditLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchAuditLog(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控送审结果查询", isReportResponse = false)
    @RequestMapping("auditnotify")
    @AuthRequired(permissionName = "view:logsearch:auditnotify")
    public JsonResult search(@RequestBody LogSearchVO.AuditNotifyLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchAuditNotifyLog(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控业务通知查询", isReportResponse = false)
    @RequestMapping("notify")
    @AuthRequired(permissionName = "view:logsearch:notify")
    public JsonResult search(@RequestBody LogSearchVO.NotifyLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchNotifyLog(vo));
    }

    @RequestMapping("trace")
    @AuthRequired(permissionName = "view:logsearch:trace")
    public JsonResult search(@RequestBody LogSearchVO.TraceSearchVO vo, @CurrentUser User user) {

        PageInfo<Map<String, Object>> result = elasticSearchService.searchTraceLog(vo, user);
        if(!"CONTENT".equalsIgnoreCase(vo.getTraceType())){
            MapValueConvertUtil.encryptPersonalInfo(result.getList());
            MapValueConvertUtil.convertValueToString(result.getList(), "uid");
        }

        if("CONTENT".equalsIgnoreCase(vo.getTraceType())){
            List<Map<String,Object>> list = result.getList();
            if(CollectionUtils.isNotEmpty(list)){
                Map<String, Set<Map<String,Object>>> asyRuleAttrMap = Maps.newHashMap();

                for(Map<String,Object> item:list){
                    String asyRuleId = (String)item.get("asyRuleId");
                    if(!asyRuleAttrMap.containsKey(asyRuleId)){
                        Set<Map<String,Object>> attrs = Sets.newHashSet();
                        attrs.add(item);

                        asyRuleAttrMap.put(asyRuleId,attrs);
                    }else{
                        Set<Map<String,Object>> attrs = asyRuleAttrMap.get(asyRuleId);
                        attrs.add(item);
                    }
                }

                List<Map<String,Object>> newList = Lists.newArrayList();
                for(Map.Entry<String,Set<Map<String,Object>>> entry:asyRuleAttrMap.entrySet()){
                    Map<String,Object> asyRuleAttrItem = Maps.newHashMap();

                    String asyRuleId = entry.getKey();
                    asyRuleAttrItem.put("asyRuleId",asyRuleId);

                    List<Map<String,Object>> absenceRemoteAttrs = Lists.newArrayList();

                    Set<Map<String,Object>> attrs = entry.getValue();
                    if(CollectionUtils.isNotEmpty(attrs)){
                        for(Map<String,Object> attr:attrs){
                            attr.remove("traceId");
                            attr.remove("asyRuleId");
                            attr.remove("eventCode");
                            attr.remove("attrStatus");
                            attr.remove("traceType");
                            attr.remove("full_text");
                            attr.remove("save2Ck");

                            absenceRemoteAttrs.add(attr);
                        }
                    }

                    asyRuleAttrItem.put("absenceRemoteAttrs",absenceRemoteAttrs);

                    asyRuleAttrItem.put("traceType","CONTENT");

                    newList.add(asyRuleAttrItem);
                }

                result.setList(newList);
            }
        }

        return JsonResult.success(result);
    }

    @RequestMapping("asyncAttr")
    @AuthRequired(permissionName = "view:logsearch:trace")
    public JsonResult searchCK(@RequestBody LogSearchVO.TraceSearchVO vo,@CurrentUser User user){

        List<Map<String,Object>> result = Lists.newArrayList();

        String startTime = DateUtils.formatDate(vo.getStartTime(),DateUtils.YYYY_MM_DD_HH_MM_SS);
        String endTime = DateUtils.formatDate(vo.getEndTime(),DateUtils.YYYY_MM_DD_HH_MM_SS);
        String sql = String.format(SqlConstants.QUERY_ASYNC_RULE_ATTR_RESULT,vo.getQuery().getTraceId(),startTime,endTime);

        List<Map<String,Object>> list = getOffLineRiskDataService.getDataBySql(sql);

        if(CollectionUtils.isNotEmpty(list)){
            for(Map<String,Object> item:list){
                if(item.containsKey("jsonData")){
                    Map<String,Object> jsonDataMap = JSON.parseObject(item.get("jsonData").toString(),Map.class);
                    if(jsonDataMap.containsKey("TraceId")){
                        jsonDataMap.remove("TraceId");
                    }

                    if(jsonDataMap.containsKey("Event")){
                        jsonDataMap.remove("Event");
                    }

                    if(jsonDataMap.containsKey("source")){
                        jsonDataMap.remove("source");
                    }

                    jsonDataMap.put("traceType","CONTENT");

                    result.add(jsonDataMap);
                }
            }

        }

        return JsonResult.success(result);

    }


    @RequestMapping("result")
    @AuthRequired(permissionName = "view:logsearch:hit")
    public JsonResult result(@RequestBody LogSearchVO.TraceSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchResult(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控人机对比查询", isReportResponse = false)
    @RequestMapping("diff")
    @AuthRequired(permissionName = "view:logsearch:diff")
    public JsonResult search(@RequestBody LogSearchVO.DiffLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchDiffLog(vo));
    }

    @RequestMapping("operation")
    @AuthRequired(permissionName = "view:logsearch:operation")
    public JsonResult search(@RequestBody LogSearchVO.OperationLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchOperationLog(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控历史记录导出", isReportResponse = false)
    @RequestMapping("export")
    @AuthRequired(permissionName = "view:logsearch:export")
    public JsonResult export(@RequestBody LogSearchVO.HitLogSearchVO vo) {
        //本导出最多只能导出10000条
        vo.setPage(1);
        vo.setSize(10000);
        PageInfo<Map<String, Object>> pageInfo = elasticSearchService.searchHitLog(vo);
        MapValueConvertUtil.encryptMobile(pageInfo.getList());
        MapValueConvertUtil.encryptPersonalInfo(pageInfo.getList());
        MapValueConvertUtil.obtainReason(pageInfo.getList());

        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("level").addName("风险级别").build(),
                ExcelFormatter.builder("costTime").addName("耗时(毫秒)").build(),
                ExcelFormatter.builder("eventCode").addName("事件Code").build(),
                ExcelFormatter.builder("userId").addName("用户ID").addWidth(50).build(),
                ExcelFormatter.builder("deviceId").addName("设备ID").addWidth(100).build(),
                ExcelFormatter.builder("clientIp").addName("Client IP").build(),
                ExcelFormatter.builder("reason").addName("原因").build(),
                ExcelFormatter.builder("createdAt").addName("创建时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("data").addName("请求参数").addWidth(100).build(),
                ExcelFormatter.builder("result").addName("响应结果").addWidth(100).build()
        );
        try {
            String fileName = ExcelUtils.exportExcelByMap(pageInfo.getList(), "历史记录", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            LOGGER.error("导出历史记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }
}
