package com.yupaopao.risk.console.web.support;


import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.framework.spring.boot.common.properties.TraceContext;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.PermissionService;
import com.yupaopao.risk.console.service.UserService;
import com.yupaopao.risk.console.utils.AuthUtil;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.exception.NoPermissionException;
import com.yupaopao.risk.console.web.support.exception.UnLoginException;
import com.yupaopao.risk.console.web.support.utils.CookieUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Component
public class AuthInterceptor extends HandlerInterceptorAdapter {

    private final static Logger LOGGER = LoggerFactory.getLogger(AuthInterceptor.class);
    private static final String LEGAL_REFER_VALUE_REGEX = "legal.refer.value.regex";

    @Autowired
    private RedisService redisService;
    @Autowired
    private UserService userServiceImpl;
    @Autowired
    private PermissionService permissionServiceImpl;

    @ApolloConfig
    private Config config;

    private static Pattern legalReferPattern = null;

    @PostConstruct
    public void init() {

        String legalReferValueRegex = config.getProperty(LEGAL_REFER_VALUE_REGEX,"");
        initReferPattern(legalReferValueRegex);

        config.addChangeListener(configChangeEvent -> {
            if(configChangeEvent.isChanged(LEGAL_REFER_VALUE_REGEX)){
                String newValue = configChangeEvent.getChange(LEGAL_REFER_VALUE_REGEX).getNewValue();
                initReferPattern(newValue);
            }
        });

    }

    private void initReferPattern(String legalReferValueRegex){
        if(StringUtils.isNotBlank(legalReferValueRegex)){
            try{
                legalReferPattern = Pattern.compile(legalReferValueRegex);
            }catch(Exception exp){
                LOGGER.error("配置的合法refer字段值的正则表达式不正确:{}",legalReferValueRegex);
            }
        }else{
            legalReferPattern = null;
        }
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (request.getRequestURI().equals("/error")) {
            response.sendRedirect("/index.html");
        } else {
            String referer=request.getHeader("Referer");
            if(StringUtils.isNotBlank(referer)&&(legalReferPattern!=null)){
                Matcher matcher = legalReferPattern.matcher(referer);

                if(!matcher.find()){
                    LOGGER.error("AuthInterceptor拦截不合法的请求, refer:{}",referer);
                    throw new NoPermissionException();
                }
            }

            User user = this.getCurrentUser(request, response);
            if (null !=user && StringUtils.isNotEmpty(user.getName())){
                TraceContext.getContext().put("loginName",user.getName());
            }else {
                TraceContext.getContext().put("loginName","风控运营平台未登陆用户");
            }

            if (handler instanceof HandlerMethod) {
                HandlerMethod method = (HandlerMethod) handler;
                AuthRequired auth = method.getMethodAnnotation(AuthRequired.class);
                if (auth == null) {
                    auth = AnnotationUtils.findAnnotation(method.getBeanType(), AuthRequired.class);
                }
                if (auth != null) {
                    if (auth.login() && user == null) {
                        throw new UnLoginException();
                    }
                    if (auth.login() && !AuthUtil.hasPermission(auth.permissionName(), user.getPermissionList(), user.getRoles())) {
                        throw new NoPermissionException();
                    }
                }
            }
        }
        return super.preHandle(request, response, handler);
    }
    /**
     * 获取登陆用户，如果redis中基于cookie查不到，则构建并设置进redis，并更新cookie
     *
     * @param request
     * @return
     */
    private User getCurrentUser(HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        String token = CookieUtils.getCookieValue(request, Constants.COOKIE_USER_TOKEN);
        User user = StringUtils.isBlank(token) ? null : (User) redisService.hget(ConsoleConstants.USER_CACHE_KEY, token);
        if (user == null) {
            if(request.getUserPrincipal() == null){
                return null;
            }
            String userName = request.getUserPrincipal().getName();
            user = userServiceImpl.selectOrAdd(userName);
            token = StringUtils.isBlank(token) ? request.getSession().getId() : token;
            CookieUtils.setCookie(response, Constants.COOKIE_USER_TOKEN, token);
            user.setPermissionList(permissionServiceImpl.getPermissionTreeList(user.getId()));
            redisService.hset(ConsoleConstants.USER_CACHE_KEY, token, user);
        }
        request.setAttribute(Constants.CURRENT_USER_KEY, user);
        return user;
    }
}
