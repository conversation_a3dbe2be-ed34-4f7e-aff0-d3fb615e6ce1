package com.yupaopao.risk.console.web.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

/**
 * Created by Avalon on 2021/6/4 11:27 上午
 **/
@Configuration
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 24 * 60 * 60, redisNamespace = "risk")
public class ClusterSessionConfig {
}
