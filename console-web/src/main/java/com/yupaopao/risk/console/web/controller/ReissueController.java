package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.ReissueLog;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.TopicConstants;
import com.yupaopao.risk.console.bean.ReissueLogVO;
import com.yupaopao.risk.console.service.AuditService;
import com.yupaopao.risk.console.service.ReissueLogService;
import com.yupaopao.risk.console.vo.ReissueVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/reissue")
@AuthRequired(permissionName = "system:reissue:all")
@Slf4j
public class ReissueController extends AbstractModelController<ReissueLog, ReissueLog, ReissueLogService> {

    @Autowired
    private AuditService auditService;

    /**
     * 送审数据补偿
     */
    @RequestMapping("audit")
    @AuthRequired(permissionName = "system:reissue:handle")
    public JsonResult audit(@RequestBody ReissueVO reissueVO, @CurrentUser User user) {
        reissueVO.setTopic(TopicConstants.RISK_AUDIT_REISSUE);
        return JsonResult.success(auditService.reissue(reissueVO, user));
    }

    /**
     * 数据补偿-批量
     */
    @RequestMapping("batch")
    public JsonResult batch(@RequestBody ReissueLogVO record, @CurrentUser User user) {
        addValid(record, user);
        super.add(record, user);
        return JsonResult.success(auditService.batchReissue(record, user));
    }

    /**
     * 数据补偿预览-批量
     */
    @RequestMapping("previewBatch")
    public JsonResult previewBatch(@RequestBody ReissueLogVO record, @CurrentUser User user) {
        addValid(record, user);
        return JsonResult.success(auditService.previewBatchReissue(record, user));
    }

    @Override
    protected void addValid(ReissueLog record, User user) {
        Assert.isTrue(record.getReissueType() != null, "业务类型必需！");
        Assert.isTrue(record.getReissueType() >= 0 && record.getReissueType() <= 2, "业务类型不合法！");
        Assert.isTrue(StringUtils.isNotEmpty(record.getEventCode()), "事件必需！");
        Assert.isTrue(record.getStartTime() != null && record.getStartTime().getTime() < System.currentTimeMillis(), "开始时间不合法！");
        Assert.isTrue(record.getEndTime() != null && record.getEndTime().getTime() < System.currentTimeMillis(), "结束时间不合法！");
        Assert.isTrue(record.getStartTime().getTime() < record.getEndTime().getTime(), "开始时间要小于结束时间！");
        record.setModifier(user.getName());
        record.setTaskType(1);
    }

    /**
     * 机审通知数据补偿
     */
    @RequestMapping("business/machine")
    @AuthRequired(permissionName = "system:reissue:handle")
    public JsonResult businessMachine(@RequestBody ReissueVO reissueVO, @CurrentUser User user) {
        reissueVO.setTopic(TopicConstants.RISK_BUSINESS_REISSUE);
        reissueVO.setReissueType(1);
        return JsonResult.success(auditService.reissue(reissueVO, user));
    }

    /**
     * 人审通知数据补偿
     */
    @RequestMapping("business/person")
    @AuthRequired(permissionName = "system:reissue:handle")
    public JsonResult businessPerson(@RequestBody ReissueVO reissueVO, @CurrentUser User user) {
        reissueVO.setTopic(TopicConstants.RISK_BUSINESS_REISSUE);
        reissueVO.setReissueType(2);
        return JsonResult.success(auditService.reissueAuditNotify(reissueVO, user));
    }

    /**
     * 人审事件 event's codes
     */
    @AuthRequired(permissionName = "*")
    @RequestMapping("getAuditEvents")
    public JsonResult getAuditEvents() {
        return JsonResult.success(auditService.getAuditEvents());
    }
}
