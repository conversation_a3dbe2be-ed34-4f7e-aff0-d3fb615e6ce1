package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.service.BaseService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 抽象的模型存取控制器
 *
 * <AUTHOR>
 * @date 2018/8/31 下午2:22
 */
@AuthRequired(permissionName = "system:abstract:all")
public abstract class AbstractModelController<M, V extends M, S extends BaseService<M>> {

    @Autowired
    private S service;

    @RequestMapping("/all")
    public JsonResult all(@RequestBody M record) {
        return JsonResult.success(service.search(record));
    }

    @RequestMapping("/search")
    public JsonResult search(@RequestBody V record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(service.search(record, page, size));
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody V record, @CurrentUser User user) {
        addValid(record, user);
        return JsonResult.success(service.insertSelective(record));
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody V record, @CurrentUser User user) {
        updateValid(record, user);
        return JsonResult.success(service.updateSelectiveById(record));
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        deleteValid(id, user);
        return JsonResult.success(service.deleteById(id));
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(service.get(id));
    }

    protected S getService() {
        return service;
    }

    /**
     * 添加数据action前置校验
     *
     * @param record
     * @param user
     */
    protected void addValid(V record, User user) {
    }

    /**
     * 更新数据action前置校验
     *
     * @param record
     * @param user
     */
    protected void updateValid(V record, User user) {
    }

    /**
     * 删除数据action前置校验
     *
     * @param id
     * @param user
     */
    protected void deleteValid(long id, User user) {
    }

}
