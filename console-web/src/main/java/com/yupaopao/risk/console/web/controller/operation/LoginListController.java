package com.yupaopao.risk.console.web.controller.operation;

import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.passport.response.AccountDto;
import com.yupaopao.risk.common.config.AccountTypeConfig;
import com.yupaopao.risk.common.config.AppIdConfig;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.config.OperationsEventConfig;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.service.UserDetailService;
import com.yupaopao.risk.console.suppport.OperationLoginListRedisSupport;
import com.yupaopao.risk.console.vo.LoginGrayListVO;
import com.yupaopao.risk.console.vo.OperationsEventVO;
import com.yupaopao.risk.console.web.controller.GrayListController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/12 5:06 PM
 */
@RestController
@RequestMapping("operation/login")
@AuthRequired(permissionName = "operation:loginlist:all")
@Slf4j
public class LoginListController extends GrayListController {

    private static final String INVALID = "无效";
    @Autowired
    OperationLoginListRedisSupport operationLoginListRedisSupport;

    @Autowired
    EventService eventService;

    @Autowired
    OperationsEventConfig operationsEventConfig;

    @Autowired
    UserDetailService userDetailService;

    @RequestMapping("convert")
    @AuthRequired(login = false)
    public JsonResult user(@RequestBody String telephone) {

        List<AccountDto> accountDtos = userDetailService.getAccountsByMobileNo(telephone);
        List<Map<String,Object>> list = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(accountDtos)){
            accountDtos.forEach(account -> {
                Map<String,Object> map = new HashMap<>();
                map.put("uid",account.getUid());
                AppIdConfig.App app = AppIdConfig.getByAppId(account.getAppId());
                AccountTypeConfig.AccountType accountType = (null == app ? null : AccountTypeConfig.getByType(app.getAccountType()));
                map.put("accountName", null == accountType ? "" : accountType.getDesc());
                list.add(map);
            });
        }
        return JsonResult.success(list);
    }

    @RequestMapping(value = "/addUser", method = RequestMethod.POST)
    public JsonResult addValidUser(@RequestBody LoginGrayListVO record, @CurrentUser User user) {
        record.setGroupId(configService.getLoginListGroupId());
        super.addValid(record, user);
        if ("MOBILENO".equals(record.getDimension())) {
            operationLoginListRedisSupport.doCheckSave(record);
        }
        if (CollectionUtils.isNotEmpty(record.getEventOptList())&&record.getEventOptList().size()>10){
            return JsonResult.error("599", "最多可选10个事件", record);
        }
        resetRecord(record);
        return super.add(record,user);
    }

    private void resetRecord(@RequestBody LoginGrayListVO record) {
        if (CollectionUtils.isEmpty(record.getEventOptList())){
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Object opt : record.getEventOptList()) {
            Map map = (Map) opt;
            stringBuilder.append(map.get("eventCode")).append(",");
        }
        stringBuilder.delete(stringBuilder.length()-1, stringBuilder.length());
        record.setEventList(stringBuilder.toString());
    }

    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    protected JsonResult updateValid(@RequestBody LoginGrayListVO record, @CurrentUser User user) {
        record.setGroupId(configService.getLoginListGroupId());
        resetRecord(record);
        if (CollectionUtils.isNotEmpty(record.getEventOptList())&&record.getEventOptList().size()>10){
            return JsonResult.error("599", "最多可选10个事件", record);
        }
        GrayList gray = new GrayList();
        BeanUtils.copyProperties(record,gray);
        getService().saveOrUpdate(gray);
        return JsonResult.success();
    }

    @Override
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        GrayList grayList = new GrayList();
        grayList.setId(id);
        List<GrayList> select = grayListService.select(grayList);
        if (CollectionUtils.isEmpty(select)) {
            return JsonResult.success(false);
        }
        super.delete(id, user);
        //删除无效号码
        operationLoginListRedisSupport.delete(select.get(0));
        return JsonResult.success(true);
    }


    @RequestMapping("/name/export")
    @AuthRequired(permissionName = "operation:loginlist:export")
    public JsonResult loginNameExport(@RequestBody LoginGrayListVO record) {
        record.setGroupId(configService.getLoginListGroupId());
        //本导出最多只能导出100000条
        List<Object> objects;
        if (StringUtils.isNotEmpty(record.getHasExist()) && INVALID.equals(record.getHasExist())) {
            objects = operationLoginListRedisSupport.getList(1, 10000);
        } else {
            PageInfo search = grayListService.search(record, 1, 10000, "updated_at desc");
            objects = operationLoginListRedisSupport.checkBatch(search.getList());
        }
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("type").addName("类型").build(),
                ExcelFormatter.builder("dimension").addName("维度").build(),
                ExcelFormatter.builder("value").addName("数据").addWidth(80).build(),
                ExcelFormatter.builder("startTime").addName("生效时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("expireTime").addName("过期时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("hasExist").addName("状态").addWidth(30).build(),
                ExcelFormatter.builder("comment").addName("备注").build(),
                ExcelFormatter.builder("author").addName("创建人").build(),
                ExcelFormatter.builder("updatedAt").addName("最后修改时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build()
        );
        try {
            String fileName = ExcelUtils.exportExcelByBean(LoginGrayListVO.class, objects, "名单管理", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出名单管理数据异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

    @RequestMapping("/loginSearch")
    public JsonResult loginSearch(@RequestBody LoginGrayListVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<LoginGrayListVO> search = new PageInfo();
        record.setGroupId(configService.getLoginListGroupId());
        if (StringUtils.isNotEmpty(record.getHasExist()) && INVALID.equals(record.getHasExist())){
            search.setList(operationLoginListRedisSupport.getList(page, size));
            search.setTotal(operationLoginListRedisSupport.getSize());
        }else {
            PageInfo grays = grayListService.search(record, page, size, "updated_at desc");
            search = grays;
            search.setList(operationLoginListRedisSupport.checkBatch(grays.getList()));
        }
        for (LoginGrayListVO loginGrayListVO : search.getList()) {

            loginGrayListVO.setEventOptList(getEventList(loginGrayListVO.getEventList()));
        }
        return JsonResult.success(search);
    }


    private List<OperationsEventVO> getEventList(String event){
        List<OperationsEventVO> eventList = new ArrayList();
        if (StringUtils.isNotEmpty(event)){
            String[] split = event.split(",");
            for (String eventCode : split) {
                setEventList(eventList, eventCode);
            }
        }
        return eventList;
    }

    private void setEventList(List<OperationsEventVO> eventList, String eventCode) {
        for (OperationsEventVO operationsEventVO : operationsEventConfig.getList()) {
            if (eventCode.equals(operationsEventVO.getEventCode())){
                eventList.add(operationsEventVO);
                return;
            }
        }
    }


    @RequestMapping("getEventConf")
    public JsonResult getEventConf(){
        return JsonResult.success(operationsEventConfig.getList());
    }


}
