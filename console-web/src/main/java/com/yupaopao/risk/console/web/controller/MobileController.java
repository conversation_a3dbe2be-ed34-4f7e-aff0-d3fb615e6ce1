package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Mobile;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.MobileSerivce;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/mobile")
@AuthRequired(permissionName = "view:mobile:all")
public class MobileController extends AbstractModelController<Mobile, Mobile, MobileSerivce> {

    @Override
    protected void addValid(Mobile record, User user) {
    }

    @Override
    protected void updateValid(Mobile record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
    }

}
