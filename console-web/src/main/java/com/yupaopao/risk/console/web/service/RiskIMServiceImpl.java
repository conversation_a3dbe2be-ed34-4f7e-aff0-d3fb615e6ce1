package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.console.api.RiskIMService;
import com.yupaopao.risk.console.bean.*;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@Component
public class RiskIMServiceImpl implements RiskIMService {
    @Autowired
    private ElasticSearchService elasticSearchService;

    @Override
    public RiskResult search(RiskIMRequest request) {
        RiskResult validResult = RiskIMRequest.valid(request);
        if (!validResult.isSuccess()) {
            return validResult;
        }
        log.debug("准备处理IM消息请求:{}", request);

        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(request.getSize());
        pageInfo.setPageNum(request.getPage());

        LogSearchVO vo = new LogSearchVO();
        vo.setStartTime(request.getStartTime());
        vo.setEndTime(request.getEndTime());
        vo.setPage(request.getPage());
        vo.setSize(request.getSize());

        SearchRequest searchRequest = RiskLogESUtil.buildSearchRequestIm(vo, RiskLogESUtil.HIT_IM_LOG_INDEX, RiskLogESUtil.pattern_default);
        // 按创建时间升序
        if (request.isAsc()) {
            searchRequest.source().sorts().clear();
            searchRequest.source().sort("createdAt", SortOrder.ASC);
        }
        // 处理请求参数
        BoolQueryBuilder query = (BoolQueryBuilder) searchRequest.source().query();
        if (RiskIMEventCodeEnum.IM_MESSAGE.getCode().equals(request.getEventCode())) {
            BoolQueryBuilder shouldQuery = new BoolQueryBuilder();
            shouldQuery.should(handleParam(request, searchRequest));
            shouldQuery.should(handleEmojiParam(request, searchRequest));
            query.must(shouldQuery);
        } else {
            query.must(handleParam(request, searchRequest));
        }
        log.debug("搜索参数:\n{}", searchRequest.source().query());
        // 请求及响应解析
        handleResponse(searchRequest, pageInfo, RiskLogESUtil.HIT_IM_LOG_INDEX);
        //合并数据 因不同索引分页无法统一 暂只支持举报材料获取 后续再做调整
        return handleResult(pageInfo);
    }

    protected RiskResultPage handleResult(PageInfo<Map<String, Object>> pageInfo) {
        RiskResultPage result = new RiskResultPage();
        result.setSuccess(true);
        result.setPage(pageInfo.getPageNum());
        result.setSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        List<Map<String, Object>> pageList = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(pageList)) {
            List<RiskIMEntity> entities = new ArrayList<>();
            for (int i = 0; i < pageList.size(); i++) {
                Map<String, Object> hitMap = pageList.get(i);
                RiskIMEntity entity = new RiskIMEntity();
                entity.setTraceId(String.valueOf(hitMap.get("traceId")));
                entity.setContent(String.valueOf(hitMap.get("full_text")));
                entity.setReason(String.valueOf(hitMap.get("reason")));
                entity.setLevel(String.valueOf(hitMap.get("level")));
                entity.setCreatedAt(String.valueOf(hitMap.get("createdAt")));
                entity.setUserId(String.valueOf(hitMap.get("userId")));
                JSONObject dataJson = JSON.parseObject(JSON.toJSONString(hitMap.get("data")));
                if (dataJson != null) {
                    entity.setImage(dataJson.getString("images"));
                    entity.setVideo(dataJson.getString("video"));
                    entity.setTargetUserId(dataJson.getString("targetUserId"));
                    if (StringUtils.isEmpty(entity.getTargetUserId())) {
                        entity.setTargetUserId(dataJson.getString("toUid"));
                    }
                    entity.setRoomId(dataJson.getString("chatRoomId"));
                    entity.setAppId(dataJson.getString("AppId"));
                    entity.setTemplateType(dataJson.getString("templateType"));
                    entity.setTemplateDesc(dataJson.getString("templateDesc"));
                    handleWord(dataJson, entity);
                    handleAudio(dataJson, entity);
                }
                entities.add(entity);
                result.setData(entities);
            }
        }
        return result;
    }

    // 处理音频 - 音转文
    protected void handleAudio(JSONObject dataJson, RiskIMEntity entity) {
        try {
            String audio = dataJson.getString("audio");
            entity.setAudio(audio);
            if (StringUtils.isNotBlank(audio)) {
                JSONObject json = null;
                if (dataJson.containsKey("audioCheck")) {
                    json = dataJson.getJSONObject("audioCheck");
                } else if (dataJson.containsKey("audioDetect")) {
                    json = dataJson.getJSONObject("audioDetect");
                }
                if (json != null) {
                    entity.setContent(json.getString("audioText"));
                }
            }
        } catch (Exception e) {
            log.warn("处理IM消息音频出错:{}", dataJson);
        }
    }

    /**
     * 处理违禁词，取wordList下标为0的
     *
     * @param dataJson
     * @param entity
     */
    protected void handleWord(JSONObject dataJson, RiskIMEntity entity) {
        try {
            for (String key : dataJson.keySet()) {
                if (key.equalsIgnoreCase("textCheck") || key.equalsIgnoreCase("textDetect")) {
                    JSONObject checkJson = dataJson.getJSONObject(key);
                    if (checkJson != null) {
                        String wordList = checkJson.getString("wordList");
                        if (StringUtils.isNotBlank(wordList) && !wordList.equals("null") && wordList.startsWith("[") && wordList.endsWith("]")) {
                            JSONArray wordArr = JSON.parseArray(wordList);
                            if (wordArr.size() > 0) {
                                entity.setRiskLabels(checkJson.getString("riskLabels"));
                                entity.setRiskSubLabels(checkJson.getString("riskSubLabels"));
                                entity.setWords(wordArr.getString(0));
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("处理IM消息违禁词出错:{}", dataJson);
        }
    }

    protected BoolQueryBuilder handleParam(RiskIMRequest request, SearchRequest searchRequest) {
        String eventCode = request.getEventCode();
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("eventCode", eventCode));
        if (RiskIMRequest.singleChat(eventCode)) {
            if (request.isBoth()) { // 双方需求
                BoolQueryBuilder userIdQuery = new BoolQueryBuilder();
                userIdQuery.should(handleParamBool(request.getUserId(), request.getTargetUserId()));
                userIdQuery.should(handleParamBool(request.getTargetUserId(), request.getUserId()));
                query.must(userIdQuery);
            } else {
                if (StringUtils.isNotBlank(request.getUserId())) {
                    query.must(QueryBuilders.termQuery("userId", request.getUserId()));
                }
                if (StringUtils.isNotBlank(request.getTargetUserId())) {
                    query.must(QueryBuilders.termQuery("data.targetUserId", request.getTargetUserId()));
                }
            }
        } else if (RiskIMRequest.chatRoom(eventCode)) {
            if (StringUtils.isNotBlank(request.getUserId())) {
                query.must(QueryBuilders.termQuery("userId", request.getUserId()));
            }
            if (StringUtils.isNotBlank(request.getRoomId())) {
                query.must(QueryBuilders.termQuery("data.chatRoomId", request.getRoomId()));
            }
        }
        return query;
    }

    private BoolQueryBuilder handleEmojiParam(RiskIMRequest request, SearchRequest searchRequest) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        if (request.isBoth()) { // 双方需求
            BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
            queryBuilder.should(handleEmojiParamBool(request.getUserId(), request.getTargetUserId()));
            queryBuilder.should(handleEmojiParamBool(request.getTargetUserId(), request.getUserId()));
            query.must(queryBuilder);
        } else {
            query.must(QueryBuilders.termQuery("userId", request.getUserId()));
            if (StringUtils.isNotEmpty(request.getTargetUserId())) {
                query.must(QueryBuilders.termQuery("data.toUid", request.getTargetUserId()));
            }
            query.must(QueryBuilders.termQuery("eventCode", RiskIMEventCodeEnum.USER_EMOJI.getCode()));
        }
        return query;
    }


    protected BoolQueryBuilder handleParamBool(String userId, String targetUserId) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("userId", userId));
        query.must(QueryBuilders.termQuery("data.targetUserId", targetUserId));
        return query;
    }

    private BoolQueryBuilder handleEmojiParamBool(String userId, String targetUserId) {
        BoolQueryBuilder query = new BoolQueryBuilder();
        query.must(QueryBuilders.termQuery("userId", userId));
        query.must(QueryBuilders.termQuery("data.toUid", targetUserId));
        query.must(QueryBuilders.termQuery("eventCode", "user-emoji"));
        return query;
    }

    public void handleResponse(SearchRequest request, PageInfo pageInfo, String index) {
        try {
            SearchResponse response = elasticSearchService.getClient().search(request);
            if (response != null && response.status() == RestStatus.OK) {
                RiskLogESUtil.handleResponse(response, pageInfo);
            } else {
                log.error("搜索失败:{} / {}", index, request.source().query());
            }
        } catch (Exception e) {
            log.error("搜索文档异常", e);
        }
    }
}
