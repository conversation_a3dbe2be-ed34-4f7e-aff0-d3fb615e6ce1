package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.StateEnum;
import com.yupaopao.risk.common.model.BizType;
import com.yupaopao.risk.common.model.ThirdChannel;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.bean.ThirdChannelVO;
import com.yupaopao.risk.console.service.BizChannelService;
import com.yupaopao.risk.console.service.ThirdChannelService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * author: lijianjun
 * date: 2021/3/25 14:39
 */
@RestController
@RequestMapping("/third/channel")
@AuthRequired(permissionName = "core:thirdchannel:all")
public class ThirdChannelController extends AbstractModelController<ThirdChannel, ThirdChannelVO, ThirdChannelService>{

    @Autowired
    private BizChannelService bizChannelService;

    @RequestMapping("/search")
    @AuthRequired(permissionName = "core:thirdchannel:search")
    public JsonResult search(@RequestBody ThirdChannelVO thirdChannelVO, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        thirdChannelVO.setState(StateEnum.ENABLE.getType());
        PageInfo result = getService().search(thirdChannelVO, page, size);
        List<ThirdChannelVO> list = getService().relationFetch(result.getList());
        result.setList(list);
        return JsonResult.success(result);
    }

    @Override
    protected void addValid(ThirdChannelVO record, User user) {
        Assert.notNull(record.getCheckType(), "请指定检测类型");
        Assert.notNull(record.getName(), "请输入通道名称");
        Assert.notNull(record.getChannel(), "请指定channel");
        Assert.notNull(record.getConfig(), "请输入通道参数");
        Assert.isNull(getService().getByName(record.getName()),"通道名称已存在");
        record.setModifier(user.getName());
        record.setAuthor(user.getName());
        //为了去掉多余的空格
        if (StringUtils.isNotBlank(record.getConfig())) {
            JSONObject json = JSON.parseObject(record.getConfig());
            record.setConfig(JSON.toJSONString(json));
        }
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:thirdchannel:manage")
    public JsonResult add(@RequestBody ThirdChannelVO thirdChannelVO, @CurrentUser User user) {
        addValid(thirdChannelVO,user);
        return JsonResult.success(getService().insertSelective(thirdChannelVO));
    }

    @Override
    protected void updateValid(ThirdChannelVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getCheckType(), "请指定检测类型");
        Assert.notNull(record.getName(), "请输入通道名称");
        Assert.notNull(record.getChannel(), "请指定channel");
        Assert.notNull(record.getConfig(), "请输入通道参数");
        ThirdChannel tc = getService().getByName(record.getName());
        if(null != tc && !tc.getId().equals(record.getId())){
            throw new RiskException("通道名称已存在");
        }
        record.setModifier(user.getName());
        record.setAuthor(null);
        record.setCreateTime(null);
        record.setState(null);
        record.setUpdateTime(null);
        record.setCheckType(null);
        //为了去掉多余的空格
        if (StringUtils.isNotBlank(record.getConfig())) {
            JSONObject json = JSON.parseObject(record.getConfig());
            record.setConfig(JSON.toJSONString(json));
        }
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:thirdchannel:manage")
    public JsonResult update(@PathVariable long id, @Valid @RequestBody ThirdChannelVO record, @CurrentUser User user) {
        updateValid(record, user);
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @AuthRequired(permissionName = "core:thirdchannel:manage")
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        if(bizChannelService.countByThirdChannelId(id)>0){
            throw new RiskException("已绑定业务类型，请先解除绑定再删除！");
        }
        ThirdChannel thirdChannel = new ThirdChannel();
        thirdChannel.setState(StateEnum.DELETE.getType());
        thirdChannel.setId(id);
        thirdChannel.setModifier(user.getName());
        return JsonResult.success(getService().updateSelectiveById(thirdChannel));
    }

    @RequestMapping("/getAll")
    public JsonResult getAll(@RequestBody ThirdChannelVO thirdChannelVO) {
        return JsonResult.success(getService().search(thirdChannelVO));
    }

}
