package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Sets;
import com.qiniu.util.Json;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.*;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.WordVO;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.bean.ExportedWord;
import com.yupaopao.risk.console.bean.TemplateWord;
import com.yupaopao.risk.console.enums.ImportExcelErrorType;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/word")
@AuthRequired(permissionName = "core:fencingword:all")
@Slf4j
public class FencingWordController extends AbstractModelController<Word, WordVO, WordService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;

    @Autowired
    private LetterService letterService;

    @Autowired
    private TagService tagService;

    @Autowired
    private SceneService sceneService;

    @Autowired
    private WordTagService wordTagService;

    @Autowired
    private WordSceneService wordSceneService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    @Value("${excel.row.max.count:3000}")
    private Integer maxRowCount;

    private static final Map<String, ExcelFormatter> uploadFormatterMap;

    static{
         uploadFormatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("content").addName("词语").build(),
                ExcelFormatter.builder("weight").addName("权重").build(),

                ExcelFormatter.builder("toSystem").addName("所属系统(风控系统/审核系统)").build(),
                ExcelFormatter.builder("wordLocationTypeName").addName("词条类型(违禁词条/白词条)").build(),
                ExcelFormatter.builder("firstTagNames").addName("一级标签").build(),
                ExcelFormatter.builder("tagNames").addName("二级标签").addWidth(50).build(),
                ExcelFormatter.builder("sceneNames").addName("场景").addWidth(50).build(),
                ExcelFormatter.builder("matchTypeName").addName("匹配模式(模糊匹配/精确匹配/全词匹配/超强模糊匹配)").build(),

                ExcelFormatter.builder("source").addName("数据来源(网安/自建)").build(),
                ExcelFormatter.builder("comment").addName("备注(必填)").build()
//                ExcelFormatter.builder("author").addName("创建人").build()
        );
    }

    protected void commonValid(WordVO record, User user) {
        Assert.isTrue(StringUtils.trim(record.getContent()).length() > 0, "该违禁词包含空字符");
        Assert.notNull(record.getScenes(), "请绑定至少一个场景");
        Assert.notNull(record.getTags(), "请绑定至少一个标签");
        record.setModifier(user.getName());
    }

    @RequestMapping("/searchByToSystem")
    public JsonResult searchByToSystem(@CurrentUser User user,@RequestBody WordVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        PageInfo<Word> pageInfo = this.getService().commonSearch(user,record,page,size);


        PageInfo newPageInfo = pageInfo;

        List<WordVO> wordVOS = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(pageInfo.getList())){
            for(Word word:pageInfo.getList()){
                WordVO wordVO = new WordVO(word);
                wordVO.setToSystemName(ToSystemType.codeOf(word.getToSystem()).getMsg());

                wordVOS.add(wordVO);
            }
        }


        newPageInfo.setList(wordVOS);

        return JsonResult.success(newPageInfo);
    }

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody WordVO record, @CurrentUser User user) {
        addValid(record, user);
        return JsonResult.success(this.getService().insert(user,record));
    }

    @RequestMapping(value = "/checkRepeatWords",method = RequestMethod.POST)
    public JsonResult checkRepeatWords(@Valid @RequestBody WordVO record,@CurrentUser User user){
        addValid(record, user);
        return JsonResult.success(this.getService().checkRepeatWords(user,record));
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody WordVO record, @CurrentUser User user) {
        if(StringUtils.isNotBlank(record.getContent())&&record.getContent().contains("\n")){
            record.setContent(record.getContent().substring(0,record.getContent().length()-1));
        }

        updateValid(record, user);
        Assert.isTrue(!this.getService().existName(record), "该违禁词条名称已存在！");
        Assert.isTrue(this.getService().update(id,record,user),"更新失败");

        return JsonResult.success(true);
    }

    @Override
    @RequestMapping(value = "/deleteByToSystem/{id}", method = RequestMethod.POST)
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
         return JsonResult.success(this.getService().delete(id,user));
    }

    @Override
    protected void addValid(WordVO record, User user) {
        commonValid(record,user);
        record.setAuthor(user.getName());
    }

    @Override
    protected void updateValid(WordVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        commonValid(record,user);
        record.setAuthor(null);
    }

    protected void batchUpdateValid(WordVO record, User user){
        commonValid(record,user);
        record.setAuthor(null);
    }

    @OpRecordReport(opType = OpTypeEnum.UPDATE, opDesc = "批量修改",isReportResponse = false)
    @RequestMapping("batchUpdate")
    public JsonResult batchUpdate(@CurrentUser User user,@RequestBody WordVO record) {
        batchUpdateValid(record,user);
        String errorMsg = this.getService().batchUpdate(user,record);

        Map<String,Object> map = new HashMap<>();

        if(StringUtils.isNotBlank(errorMsg)){
            map.put("success",false);
            map.put("detail",errorMsg);

            return JsonResult.success(map);
        }else{
            map.put("success",true);
        }

        return JsonResult.success(map);
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "违禁词导出",isReportResponse = false)
    @RequestMapping("export")
    @AuthRequired(permissionName = "view:fencing:export")
    public JsonResult export(@CurrentUser User user,@RequestBody WordVO record) {
//        ID	词语	权重	一级标签	二级标签	场景	匹配模式	违禁词库/白名单	备注	创建人	更新人	修改时间

        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("content").addName("词语").build(),
                ExcelFormatter.builder("weight").addName("权重").build(),

                ExcelFormatter.builder("firstTagNames").addName("一级标签").build(),
                ExcelFormatter.builder("tagNames").addName("二级标签").addWidth(50).build(),
                ExcelFormatter.builder("sceneNames").addName("场景").addWidth(50).build(),
                ExcelFormatter.builder("matchTypeName").addName("匹配模式").build(),
                ExcelFormatter.builder("wordLocationTypeName").addName("违禁词库/白词条").build(),

                ExcelFormatter.builder("comment").addName("备注").build(),
                ExcelFormatter.builder("author").addName("创建人").build(),
                ExcelFormatter.builder("modifier").addName("更新人").build(),
                ExcelFormatter.builder("updatedAt").addName("修改时间").build()
        );

        try {
            String fileName = ExcelUtils.exportExcelByBean(ExportedWord.class, this.getService().export(user,record),"违禁词",formatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            log.error("导出历史记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "下载批量导入模板",isReportResponse = false)
    @RequestMapping("downloadTemplate")
    public JsonResult downloadTemplate(@CurrentUser User user) {
        try {
            String fileName = ExcelUtils.exportTemplatByBean(TemplateWord.class, this.getService().downloadTemplate(user),"违禁词",uploadFormatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            log.error("导出历史记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }

    @SuppressWarnings("resource")
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "校验批量上传违禁词excel的合法性",isReportResponse = false)
    @RequestMapping(value = "checkLegality",method = RequestMethod.POST)
    public JsonResult checkLegality(@RequestBody ExcelBean excelBean,@CurrentUser User user){
        try{
            // 校验 批量上传违禁词文件 结构的合法性
            if(StringUtils.isBlank(excelBean.getExcelUrl())){
                return JsonResult.error(ImportExcelErrorType.FILE_URL_NULL.getType()+"",ImportExcelErrorType.FILE_URL_NULL.getName(),null);
            }


            ImportExcelErrorType errorType =  ExcelUtils.checkExcelByBean(TemplateWord.class,excelBean.getExcelUrl(),uploadFormatterMap,maxRowCount);
            if(errorType!=null){
                return JsonResult.error(errorType.getType()+"",errorType.getName(),null);
            }

            Map<String,Object> parseResult = ExcelUtils.parseExcelByBean(TemplateWord.class,user,excelBean.getExcelUrl(),uploadFormatterMap);
            if(MapUtils.isEmpty(parseResult)){
               return JsonResult.error(ImportExcelErrorType.READ_CONTENT_ERROR.getType()+"",ImportExcelErrorType.READ_CONTENT_ERROR.getName(),null);
            }

            //"内容"列文本值非法，比如以"|"开头
            List<Integer> errorContentRows = (List<Integer>)parseResult.get("errorContentRows");
            if(CollectionUtils.isNotEmpty(errorContentRows)){
                return JsonResult.error(ImportExcelErrorType.PARSE_EXCEL_CONTENT_ERROR.getType()+"",ImportExcelErrorType.PARSE_EXCEL_CONTENT_ERROR.getName(),null);
            }

            //"权重"列文本值非法
            List<Integer>  errorTextRows = (List<Integer>)parseResult.get("errorRows");
            if(CollectionUtils.isNotEmpty(errorTextRows)){
                return JsonResult.error(ImportExcelErrorType.PARSE_EXCEL_WEIGHT_ERROR.getType()+"",ImportExcelErrorType.PARSE_EXCEL_WEIGHT_ERROR.getName(),errorTextRows);
            }

            //"标签"列和"场景"列文本值非法
            List<TemplateWord> rightText = (List<TemplateWord>) parseResult.get("rightRows");
            Set<String> firstTagNames = rightText.stream().map(TemplateWord::getFirstTagNames).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(this.tagService.searchNotFirstTags(firstTagNames))){
                return JsonResult.error(ImportExcelErrorType.FIRST_TAG_NAME_ERROR.getType()+"",ImportExcelErrorType.FIRST_TAG_NAME_ERROR.getName(),firstTagNames);
            }

            Set<String> secondTagNames = rightText.stream().map(TemplateWord::getTagNames).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(this.tagService.searchNotSecondTags(secondTagNames))){
                return JsonResult.error(ImportExcelErrorType.SECOND_TAG_NAME_ERROR.getType()+"",ImportExcelErrorType.SECOND_TAG_NAME_ERROR.getName(),secondTagNames);
            }

            Set<String> sceneNames = rightText.stream().map(TemplateWord::getSceneNames).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(this.sceneService.searchNotScenes(sceneNames))){
                return JsonResult.error(ImportExcelErrorType.SCENE_NAME_ERROR.getType()+"",ImportExcelErrorType.SCENE_NAME_ERROR.getName(),sceneNames);
            }

            // 录入"风控违禁词"与指定的场景是否匹配
            Map<String,List<TemplateWord>> toSystemWordMap = rightText.stream().collect(Collectors.groupingBy(TemplateWord::getToSystem));
            List<TemplateWord> creditWords = toSystemWordMap.get(ToSystemType.CREDIT.getMsg());
            if(CollectionUtils.isNotEmpty(creditWords)){
                Set<String> scenes = creditWords.stream().map(TemplateWord::getSceneNames).collect(Collectors.toSet());
                Set<String> notCreditScenes = this.sceneService.searchNotIn(scenes,ToSystemType.CREDIT.getCode());
                if(CollectionUtils.isNotEmpty(notCreditScenes)){
                    return JsonResult.error(ImportExcelErrorType.RISK_FENCING_WORD_SCENE_NOT_MATCH.getType()+"",ImportExcelErrorType.RISK_FENCING_WORD_SCENE_NOT_MATCH.getName(),notCreditScenes);
                }
            }

            // 录入"审核违禁词"与指定的场景是否匹配
            List<TemplateWord> auditWords = toSystemWordMap.get(ToSystemType.AUDIT.getMsg());
            if(CollectionUtils.isNotEmpty(auditWords)){
                Set<String> scenes = auditWords.stream().map(TemplateWord::getSceneNames).collect(Collectors.toSet());
                Set<String> notAuditScenes = this.sceneService.searchNotIn(scenes,ToSystemType.AUDIT.getCode());
                if(CollectionUtils.isNotEmpty(notAuditScenes)){
                    return JsonResult.error(ImportExcelErrorType.AUDIT_FENCING_WORD_SCENE_NOT_MATCH.getType()+"",ImportExcelErrorType.AUDIT_FENCING_WORD_SCENE_NOT_MATCH.getName(),notAuditScenes);
                }
            }

            //所属系统(风控系统/审核系统)
            Set<String> rightToSystems = Sets.newHashSet();
            rightToSystems.add(ToSystemType.CREDIT.getMsg());
            rightToSystems.add(ToSystemType.AUDIT.getMsg());

            Set<String> toSystemNames = rightText.stream().map(TemplateWord::getToSystem).collect(Collectors.toSet());
            String[] errorToSystems = new String[]{};
            if(CollectionUtils.isNotEmpty(toSystemNames)&& ArrayUtils.isNotEmpty(Sets.difference(toSystemNames,rightToSystems).toArray(errorToSystems))){
                return JsonResult.error(ImportExcelErrorType.TO_SYSTEM_NAME_ERROR.getType()+"",ImportExcelErrorType.TO_SYSTEM_NAME_ERROR.getName(),Sets.difference(toSystemNames,rightToSystems).toArray(errorToSystems));
            }

            //词条类型（违禁词条/白词条）
            Set<String> rightLocationTypeName = Sets.newHashSet();
            rightLocationTypeName.add(WordLocationType.FencingWordList.getMsg());
            rightLocationTypeName.add(WordLocationType.WhiteNameList.getMsg());

            Set<String> wordLocationTypeNames = rightText.stream().map(TemplateWord::getWordLocationTypeName).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(wordLocationTypeNames)&& ArrayUtils.isNotEmpty(Sets.difference(wordLocationTypeNames,rightLocationTypeName).toArray(errorToSystems))){
                return JsonResult.error(ImportExcelErrorType.WORD_LOCATION_TYPE_NAME_ERROR.getType()+"",ImportExcelErrorType.WORD_LOCATION_TYPE_NAME_ERROR.getName(),Sets.difference(wordLocationTypeNames,rightLocationTypeName).toArray(errorToSystems));
            }

            //匹配方式（模糊匹配/精确匹配/全词匹配/超强模糊匹配）
            Set<String> rightMatchTypeName = Sets.newHashSet();
            rightMatchTypeName.add(MatchType.CommonMatch.getMsg());
            rightMatchTypeName.add(MatchType.ExactMatch.getMsg());
            rightMatchTypeName.add(MatchType.WholeMatch.getMsg());
            rightMatchTypeName.add(MatchType.SuperFuzzyMatch.getMsg());

            Set<String> matchTypeNames = rightText.stream().map(TemplateWord::getMatchTypeName).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(matchTypeNames)&& ArrayUtils.isNotEmpty(Sets.difference(matchTypeNames,rightMatchTypeName).toArray(errorToSystems))){
                return JsonResult.error(ImportExcelErrorType.MATCH_TYPE_NAME_ERROR.getType()+"",ImportExcelErrorType.MATCH_TYPE_NAME_ERROR.getName(),Sets.difference(matchTypeNames,rightMatchTypeName).toArray(errorToSystems));
            }

            //数据来源（网安/自建）
            Set<String> rightSourceName = Sets.newHashSet();
            rightSourceName.add(FencingWordSource.SELF_CREATE.getMsg());
            rightSourceName.add(FencingWordSource.NETWORK_SAFE.getMsg());

            Set<String> sourceNames = rightText.stream().map(TemplateWord::getSource).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(sourceNames)&& ArrayUtils.isNotEmpty(Sets.difference(sourceNames,rightSourceName).toArray(errorToSystems))){
                return JsonResult.error(ImportExcelErrorType.SOURCE_ERROR.getType()+"",ImportExcelErrorType.SOURCE_ERROR.getName(),Sets.difference(sourceNames,rightSourceName).toArray(errorToSystems));
            }

        }catch(Exception exp){
            log.error("检测批量上传违禁词的excel文档的合法性",exp);
            return JsonResult.error(ImportExcelErrorType.CHECK_EXCEL_LEGALITY_ERROR.getType()+"",ImportExcelErrorType.CHECK_EXCEL_LEGALITY_ERROR.getName(),null);
        }

        return JsonResult.success("检测通过！");
    }

    private Set<String> needToAddContent(Map<String,List<TemplateWord>> toSystemWordMap, String toSystemName){
        List<TemplateWord> toSystemWords = toSystemWordMap.get(toSystemName);

        Set<String> addWords = Sets.newHashSet();
        if(CollectionUtils.isNotEmpty(toSystemWords)){
            Set<String> wordNames = toSystemWords.stream().map(TemplateWord::getContent).collect(Collectors.toSet());

            addWords = this.getService().searchNotIn(wordNames,ToSystemType.codeOf(toSystemName).getCode());
        }

        return addWords;
    }

    private List<TemplateWord> needToAddRow(Map<String,List<TemplateWord>> toSystemWordMap,String toSystemName){
        List<TemplateWord> needToAddWords = Lists.newArrayList();

        //找出需要插入的审核词和风控词
        List<TemplateWord> words = toSystemWordMap.get(toSystemName);
        if(CollectionUtils.isNotEmpty(words)){
            Set<String> addWords = needToAddContent(toSystemWordMap,toSystemName);
            if(CollectionUtils.isNotEmpty(addWords)){
                Map<String,List<TemplateWord>> wordMap = words.stream().collect(Collectors.groupingBy(TemplateWord::getContent));

                addWords.forEach(addWord->{
                    if(wordMap.containsKey(addWord)){
                        needToAddWords.add(wordMap.get(addWord).get(0));
                    }
                });
            }
        }

        return needToAddWords;
    }

    private List<TemplateWord> filter(Map<String,List<TemplateWord>> toSystemWordMap,String toSystemName,List<TemplateWord> addWords){
        List<TemplateWord> result = Lists.newArrayList();
        if(MapUtils.isNotEmpty(toSystemWordMap)){
            List<TemplateWord> toSystemWords = toSystemWordMap.get(toSystemName);

            if(CollectionUtils.isNotEmpty(toSystemWords)){
                if(CollectionUtils.isNotEmpty(addWords)){
                    List<String> toSystemWordNames = toSystemWords.stream().map(TemplateWord::getContent).collect(Collectors.toList());
                    Map<String,TemplateWord> wordMap = toSystemWords.stream().collect(Collectors.toMap(TemplateWord::getContent,templateWord->templateWord));
                    List<String> addWordNames = addWords.stream().map(TemplateWord::getContent).collect(Collectors.toList());

                    for(String toSystemWordName:toSystemWordNames){
                        // 新增词列表中不包含该词条，说明该词条需要更新
                        if(!addWordNames.contains(toSystemWordName)){
                            result.add(wordMap.get(toSystemWordName));
                        }
                    }
                }else{
                    return toSystemWords;
                }
            }

        }

        return result;
    }

    @SuppressWarnings("resource")
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "上传批量违禁词文件",isReportResponse = false)
    @RequestMapping(value = "uploadFile",method = RequestMethod.POST)
    public JsonResult uploadFile(@RequestBody ExcelBean excelBean,@CurrentUser User user){
        Map<String,Object> parseResult = ExcelUtils.parseExcelByBean(TemplateWord.class,user,excelBean.getExcelUrl(),uploadFormatterMap);
        List<TemplateWord> rightText = (List<TemplateWord>) parseResult.get("rightRows");

        Map<String,List<TemplateWord>> toSystemWordMap = rightText.stream().collect(Collectors.groupingBy(TemplateWord::getToSystem));

        //找出需要插入的审核词和风控词
        List<TemplateWord> creditTemplateWords = needToAddRow(toSystemWordMap,ToSystemType.CREDIT.getMsg());
        List<TemplateWord> auditTemplateWords = needToAddRow(toSystemWordMap,ToSystemType.AUDIT.getMsg());

        //找出需要去更新的审核词和风控词
        List<TemplateWord> updateCreditTemplateWords = filter(toSystemWordMap,ToSystemType.CREDIT.getMsg(),creditTemplateWords);
        List<TemplateWord> updateAuditTemplateWords = filter(toSystemWordMap,ToSystemType.AUDIT.getMsg(),auditTemplateWords);

        //转换成待插入的Word对象列表
        List<Word> creditWords = constructWordBean(creditTemplateWords);
        List<Word> auditWords = constructWordBean(auditTemplateWords);

        //转换成待更新的word对象列表
        List<Word> updateCreditWords = constructWordBean(updateCreditTemplateWords);
        List<Word> updateAuditWords = constructWordBean(updateAuditTemplateWords);

        //构造插入(违禁词，场景）、（违禁词，标签）关系
        List<WordScene> wordScenes = Lists.newArrayList();
        List<WordTag> wordTags = Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(creditWords)){
            //批量插入风控词
            int count = this.getService().batchInsertInto(creditWords);
            if(count<1){
                count = this.getService().batchInsertInto(creditWords);
                if(count<1){
                    count = this.getService().batchInsertInto(creditWords);
                    if(count<1){
                        return JsonResult.error(ImportExcelErrorType.WORD_INSERT_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_INSERT_IN_ERROR.getName(),null);
                    }
                }
            }
            constructWordSceneBean(creditWords,creditTemplateWords,wordTags,wordScenes);
        }

        if(CollectionUtils.isNotEmpty(updateCreditWords)){
            //批量更新风控词
            List<Word> failWords = Lists.newArrayList();
            int count = this.getService().batchUpdate(updateCreditWords,failWords);
            if(failWords.size()>0){
                List<Word> failWords1 = Lists.newArrayList();
                count = this.getService().batchUpdate(updateCreditWords,failWords1);
                if(failWords1.size()>0){
                    List<Word> failWords2 = Lists.newArrayList();
                    count = this.getService().batchUpdate(updateCreditWords,failWords2);
                    if(failWords2.size()>0){
                        return JsonResult.error(ImportExcelErrorType.WORD_UPDATE_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_UPDATE_IN_ERROR.getName(),null);
                    }
                }
            }
            constructWordSceneBean(updateCreditWords,updateCreditTemplateWords,wordTags,wordScenes);
        }

        if(CollectionUtils.isNotEmpty(auditWords)){
            //批量插入审核词
            int count = this.getService().batchInsertInto(auditWords);
            if(count<1){
                count = this.getService().batchInsertInto(auditWords);
                if(count<1){
                    count = this.getService().batchInsertInto(auditWords);
                    if(count<1){
                        return JsonResult.error(ImportExcelErrorType.WORD_INSERT_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_INSERT_IN_ERROR.getName(),null);
                    }
                }
            }
            constructWordSceneBean(auditWords,auditTemplateWords,wordTags,wordScenes);
        }

        if(CollectionUtils.isNotEmpty(updateAuditWords)){
            //批量更新审核词
            List<Word> failWords = Lists.newArrayList();
            int count = this.getService().batchUpdate(updateAuditWords,failWords);
            if(failWords.size()>0){
                List<Word> failWords1 = Lists.newArrayList();
                count = this.getService().batchUpdate(updateAuditWords,failWords1);
                if(failWords1.size()>0){
                    List<Word> failWords2 = Lists.newArrayList();
                    count = this.getService().batchUpdate(updateAuditWords,failWords2);
                    if(failWords2.size()>0){
                        return JsonResult.error(ImportExcelErrorType.WORD_UPDATE_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_UPDATE_IN_ERROR.getName(),null);
                    }
                }
            }
            constructWordSceneBean(updateAuditWords,updateAuditTemplateWords,wordTags,wordScenes);
        }

        // 移除数据库中已存在的（违禁词，标签）关系
        Set<Long> wordIds =  wordTags.stream().map(WordTag::getWordId).collect(Collectors.toSet());
        if(CollectionUtils.isNotEmpty(wordIds)){
            for(Long wordId:wordIds){
                if(!this.wordTagService.deleteByWordId(wordId)){
                    if(!this.wordTagService.deleteByWordId(wordId)){
                        this.wordTagService.deleteByWordId(wordId);
                    }
                }
            }
        }

        //批量插入（违禁词，标签）关系
        int count =  this.wordTagService.batchInsertList(wordTags);
        if(count<1){
            count =  this.wordTagService.batchInsertList(wordTags);
            if(count<1){
                count =  this.wordTagService.batchInsertList(wordTags);
                if(count<1){
                    return JsonResult.error(ImportExcelErrorType.WORD_INSERT_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_INSERT_IN_ERROR.getName(),null);
                }
            }
        }

        // 检测数据库中是否包含有部分（违禁词，场景）关系
        List<WordScene> hasWordScenes = this.wordSceneService.search(wordScenes);
        //移除已存在的（违禁词，场景）关系
        if(CollectionUtils.isNotEmpty(hasWordScenes)){
            wordScenes = removeExistWordScenes(wordScenes,hasWordScenes);
        }

        //批量插入(违禁词，场景）
        count = this.wordSceneService.batchInsertList(wordScenes);
        if(count<1){
            count = this.wordSceneService.batchInsertList(wordScenes);
            if(count<1){
                count = this.wordSceneService.batchInsertList(wordScenes);
                if(count<1){
                    return JsonResult.error(ImportExcelErrorType.WORD_INSERT_IN_ERROR.getType()+"",ImportExcelErrorType.WORD_INSERT_IN_ERROR.getName(),null);
                }
            }
        }

        return JsonResult.success("批量上传违禁词excel文件成功！");
    }

    private List<WordTag> removeExistWordTags(List<WordTag> wordTags,List<WordTag> hasWordTags){
        List<WordTag> result = Lists.newArrayList();
        for(WordTag wordTag:wordTags){
            if(!hasWordTags.contains(wordTag)){
                result.add(wordTag);
            }
        }

        return result;
    }

    private List<WordScene> removeExistWordScenes(List<WordScene> wordScenes,List<WordScene> hasWordScenes){
        List<WordScene> result = Lists.newArrayList();
        for(WordScene wordScene:wordScenes){
            boolean isContain = false;
            for(WordScene hasWordScene:hasWordScenes){
                if(Objects.equals(wordScene.getWordId(),hasWordScene.getWordId())&&Objects.equals(wordScene.getSceneId(),hasWordScene.getSceneId())){
                    isContain = true;
                    break;
                }
            }

            if(!isContain){
                result.add(wordScene);
            }
        }

        return result;
    }

    private void constructWordSceneBean(List<Word> savedWords,List<TemplateWord> templateWords,List<WordTag> wordTags,List<WordScene> wordScenes){

        if(CollectionUtils.isNotEmpty(savedWords)){
            Map<String,List<TemplateWord>> templateWordMap = templateWords.stream().collect(Collectors.groupingBy(TemplateWord::getContent));

            savedWords.forEach(word -> {
                Long wordId = word.getId();

                List<TemplateWord> list = templateWordMap.get(word.getContent());
                if(CollectionUtils.isEmpty(list)){
                    return;
                }

                TemplateWord templateWord =list.get(0);
                Map<String,List<Tag>> tagMap = this.tagService.selectAll().stream().collect(Collectors.groupingBy(Tag::getName));
                Long tagId = tagMap.get(templateWord.getTagNames()).get(0).getId();

                Map<String,List<Scene>> sceneMap = this.sceneService.selectAll().stream().collect(Collectors.groupingBy(Scene::getName));
                Long sceneId = sceneMap.get(templateWord.getSceneNames()).get(0).getId();

                WordTag wordTag = new WordTag();
                wordTag.setWordId(wordId);
                wordTag.setTagId(tagId);
                wordTags.add(wordTag);

                WordScene wordScene = new WordScene();
                wordScene.setWordId(wordId);
                wordScene.setSceneId(sceneId);
                wordScenes.add(wordScene);
            });
        }
    }

    private List<Word> constructWordBean(List<TemplateWord> templateWords){
        List<Word> words = Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(templateWords)){
            templateWords.forEach(creditTemplateWord->{
                Word word = new Word();
                word.setContent(creditTemplateWord.getContent());
                word.setWeight(Float.parseFloat(creditTemplateWord.getWeight().toString()));
                word.setAllowSkip(false);
                word.setAllowWholeMatch(false);
                word.setMatchType(MatchType.codeOf(creditTemplateWord.getMatchTypeName()).getCode());
                word.setWordLocationType(WordLocationType.codeOf(creditTemplateWord.getWordLocationTypeName()).getCode());
                word.setSource(FencingWordSource.codeOf(creditTemplateWord.getSource()).getCode());
                word.setToSystem(ToSystemType.codeOf(creditTemplateWord.getToSystem()).getCode());
                word.setComment(creditTemplateWord.getComment());
                word.setAuthor(creditTemplateWord.getAuthor());
                word.setModifier(creditTemplateWord.getAuthor());

                words.add(word);
            });
        }
        return words;
    }

    /**
     * 查询匹配类型列表
     */
    @RequestMapping("/getMatchTypeList")
    public JsonResult getMatchTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(MatchType.class).forEach(status -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(status.getCode()));
            temp.put("msg", String.valueOf(status.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    /**
     * 查询词条存放的位置 1 违禁词库  2 白词条
     */
    @RequestMapping("/getWordLocationTypeList")
    public JsonResult getWordLocationTypeList(){
        List<Map<String,String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(WordLocationType.class).forEach(type ->{
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(type.getCode()));
            temp.put("msg", String.valueOf(type.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    /**
     * 查询词条所属的系统 1 风控系统  2 审核系统
     */
    @RequestMapping("/getToSystemList")
    public JsonResult getToSystemList(){
        List<Map<String,String>> list = Lists.newArrayList();

        ToSystemType.getDifferentSystems().forEach(type ->{
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(type.getCode()));
            temp.put("msg", String.valueOf(type.getMsg()));
            list.add(temp);
        });

        return JsonResult.success(list);
    }

    /**
     * 查询词条所属的权重
     * @return
     */
    @RequestMapping("/getWeights")
    public JsonResult getWeights(){
        return JsonResult.success(this.getService().getWeights());
    }

    /**
     *  展示词条有效性枚举类型 字段说明
     */
    @RequestMapping("/getWordEffectTypeList")
    public JsonResult getWordEffectTypeList(){
        List<Map<String,String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(WordEffectType.class).forEach(type ->{
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("name","wordLocationType");
            temp.put("value",String.valueOf(type.getCode()));
            temp.put("description",String.valueOf(type.getMsg()));
            list.add(temp);
        });

        return JsonResult.success(list);
    }

    /**
     * 查询词条有效性类型 1 有效违禁词条  2 白词条  3 无效违禁词条
     */
    @RequestMapping("/getWordTypeList")
    public JsonResult getWordTypeList(){
        List<Map<String,String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(WordEffectType.class).forEach(type ->{
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code",String.valueOf(type.getCode()));
            temp.put("msg",String.valueOf(type.getMsg()));
            list.add(temp);
        });

        return JsonResult.success(list);
    }

    /**
     * 查询违禁词条的字列表
     */
    @RequestMapping(value = "/getLetterList", method = RequestMethod.POST)
    public JsonResult getLetterList(@Valid @RequestBody Word word){
        List<Letter> list = Lists.newArrayList();

        String content = word.getContent();
        char[] chars = content.toCharArray();
        if (chars.length>0){
            for(int i=0;i<chars.length;i++){
                Letter wordLetter = new Letter();
                wordLetter.setContent(String.valueOf(chars[i]));
                Letter letter = letterService.get(wordLetter);

                if(letter!=null){
                    list.add(letter);
                }
            }
        }

        return JsonResult.success(list);
    }

    /**
     * 查询违禁词来源列表
     */
    @RequestMapping("/getSourceList")
    public JsonResult getSourceList() {
        List<Map<String, String>> list = Lists.newArrayList();

//        Map<String, String> all = new HashMap<>();
//        all.put("code","0");
//        all.put("msg","全部");
//        list.add(all);

        EnumUtils.getEnumList(FencingWordSource.class).forEach(source -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(source.getCode()));
            temp.put("msg", String.valueOf(source.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    @RequestMapping("/getUserToSystem")
    public JsonResult getUserToSystem(@CurrentUser User user){
        return JsonResult.success(user.getToSystem());
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to build dictionary , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_TEXT)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.DICT_LOAD_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger dictionary reBuild error", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

}

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
class ExcelBean implements Serializable{
    private String excelUrl;

}