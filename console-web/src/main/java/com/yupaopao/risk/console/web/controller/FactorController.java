package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.AggFunction;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.FactorBusiness;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.Factor;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.service.FactorService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.FactorVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/factor")
@AuthRequired(permissionName = "core:factor:all")
@Slf4j
public class FactorController extends AbstractModelController<Factor, FactorVO, FactorService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;
    @Autowired
    private AttributeService attributeService;

    @Override
    protected void addValid(FactorVO record, User user) {
        Assert.notNull(record.getGroupKey(), "请指定GroupKey");
        Assert.notNull(AggFunction.nameOf(record.getFunction()), "请指定Function");
        Assert.isTrue(record.getTimeSpan() > 0, "请指定有效的周期");
        record.setAuthor(user.getName());
        record.setModifier(user.getName());
    }

    @Override
    protected void updateValid(FactorVO record, User user) {
        Assert.notNull(record.getId(), "请指定ID");
        addValid(record, user);
        record.setAuthor(null);
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild factor computer , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_MAGIC)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.FACTOR_COMPUTER_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger factor computer rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

    @RequestMapping("/info")
    public JsonResult search() {
        Map<String,Object> map = new HashMap<>(2);
        Map<Integer,String> businessMap = new HashMap<>(8);
        for(FactorBusiness item : FactorBusiness.values()){
            businessMap.put(item.getCode(),item.getMsg());
        }
        map.put("businessMap", businessMap);
        return JsonResult.success(map);
    }

    @RequestMapping("/search")
    public JsonResult search(@RequestBody FactorVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        if(StringUtils.isNotBlank(record.getAttributeName())){
            List<Attribute> attributeList = attributeService.queryLocal(Lists.newArrayList(record.getAttributeName()));
            if(CollectionUtils.isEmpty(attributeList)){
                return JsonResult.success(new PageInfo());
            }
            Attribute attribute = attributeList.get(0);
            String factorId = attribute.getDependent().substring(1,attribute.getDependent().indexOf("::::")-1);
            record.setAttributeName(null);
            record.setId(Long.parseLong(factorId));
        }
        PageInfo<Factor> result = getService().search(record, page, size);
        getService().relationFetch(result.getList());
        return JsonResult.success(result);
    }


    @RequestMapping(value ="/clean/{id}",method = RequestMethod.POST)
    @AuthRequired(permissionName = "factor:clean")
    public JsonResult clean(@PathVariable long id, @Valid @RequestBody Factor record,@CurrentUser User user){
        record.setResetTime(System.currentTimeMillis());
        return JsonResult.success(this.getService().updateSelectiveById(record));
    }

}
