package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Business;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.BusinessService;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/business")
@AuthRequired(permissionName = "join:business:all")
public class BusinessController extends AbstractModelController<Business, Business, BusinessService> {

    @Override
    protected void addValid(Business record, User user) {
        record.setAuthor(user.getName());
        record.setModifier(user.getName());
    }

    @Override
    protected void updateValid(Business record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        record.setAuthor(null);
        record.setModifier(user.getName());
    }

}
