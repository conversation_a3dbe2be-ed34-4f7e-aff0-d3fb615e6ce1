package com.yupaopao.risk.console.web.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.common.mapper.LogMapper;
import com.yupaopao.risk.common.model.DataClean;
import com.yupaopao.risk.common.model.Log;
import com.yupaopao.risk.console.bean.DataCleanVO;
import com.yupaopao.risk.console.service.DataCleanService;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/detail/cleanLog")
@AuthRequired(permissionName = "analysis:detail:clean")
public class DataCleanController extends AbstractModelController<DataClean,DataClean,DataCleanService> {

    @Resource
    private LogMapper logMapper;

    @RequestMapping("advancedSearch")
    public JsonResult advancedSearch(@RequestBody DataClean dataClean, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        if(dataClean==null){
            return JsonResult.error("8001","入参不能为空",null);
        }

        PageInfo<DataClean> pageInfo = this.getService().advancedSearch(dataClean,page,size);
        PageInfo newPageInfo = new PageInfo<>();
        try{
            BeanUtils.copyProperties(newPageInfo,pageInfo);

            if(pageInfo!=null&& CollectionUtils.isNotEmpty(pageInfo.getList())){
                List<DataCleanVO> list = new ArrayList<>();

                for(DataClean item:pageInfo.getList()){
                    DataCleanVO vo = new DataCleanVO();
                    vo.setId(item.getId().toString());
                    vo.setUid(item.getUid().toString());
                    vo.setShumei(item.getShumei());
                    vo.setPortrait(item.getPortrait());
                    vo.setBlackGray(item.getBlackGray());
                    vo.setFactor(item.getFactor());
                    vo.setResult(item.getResult());
                    vo.setBlackGrayList(item.getBlackGrayList());
                    vo.setFactorIds(item.getFactorIds());
                    vo.setNote(item.getNote());
                    vo.setErrorDetail(item.getErrorDetail());
                    vo.setOperator(item.getOperator());
                    vo.setCreateTime(item.getCreateTime());
                    vo.setUpdateTime(item.getUpdateTime());

                    list.add(vo);
                }

                newPageInfo.setList(list);
            }

            return JsonResult.success(newPageInfo);
        }catch(Exception exp){
            log.error("转化DataClean bean失败,异常:{}", JSONObject.toJSONString(exp));
            return JsonResult.error("8001","转化DataClean bean失败",newPageInfo);
        }

    }

    @Data
    private static class DeviceCleanReq {
        private String operator;
        private String deviceId;
        private Date createTime;
        private Date updateTime;
    }

    @RequestMapping("deviceSearch")
    public JsonResult deviceSearch(@RequestBody DeviceCleanReq param, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        // table name: 'temp_device_clean'
        String tbName = "temp_device_clean";
        Date createTime = param.getCreateTime();
        Date updateTime = param.getUpdateTime();
        String modifier = param.getOperator();
        String deviceId = param.getDeviceId();

        Example example = new Example(Log.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andGreaterThanOrEqualTo("createTime", createTime);
        criteria.andLessThan("updateTime", updateTime);

        criteria.andEqualTo("configType", tbName);
        if (!StringUtils.isEmpty(modifier)) {
            criteria.andLike("modifier", "%" + modifier + "%");
        }
        if (!StringUtils.isEmpty(deviceId)) {
            criteria.andEqualTo("bizIdConfigType", tbName + "_" + Md5Util.md5(deviceId));
        }

        PageHelper.startPage(page,size);
        List<Log> raws = logMapper.selectByExample(example);
        if (!StringUtils.isEmpty(deviceId)) {
            raws = raws.stream().filter(r -> {
                JSONObject after = JSONObject.parseObject(r.getAfter());
                return deviceId.equals(after.getString("deviceId"));
            }).collect(Collectors.toList());
        }

        List<Map<String, Object>> list = raws.stream().map(raw -> {
            Map<String, Object> map = new HashMap<>();
            map.put("createTime", raw.getCreateTime());
            map.put("deviceId", JSONObject.parseObject(raw.getAfter()).getString("deviceId"));
            map.put("operator", raw.getModifier());
            map.put("objType", "deviceId");
            return map;
        }).collect(Collectors.toList());

        return JsonResult.success(new PageInfo<>(list));
    }
}
