package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.AlarmActionType;
import com.yupaopao.risk.common.enums.AlarmConfigType;
import com.yupaopao.risk.common.enums.AlarmRuleType;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.AlarmVO;
import com.yupaopao.risk.console.service.AlarmService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * author: liji<PERSON>jun
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/alarm")
@AuthRequired(permissionName = "alarm:alarmMain:all")
public class AlarmController extends AbstractModelController<Alarm, AlarmVO, AlarmService> {

    @Override
    protected void addValid(AlarmVO alarmVO, User user) {
        Alarm alarm = this.getService().getByName(alarmVO.getName());
        Assert.isNull(alarm, "标签名称重复！");
        alarmVO.setModifier(user.getName());
        alarmVO.setAuthor(user.getName());
        if(alarmVO.getConfigId() == 0){
            alarmVO.getConfig().setAuthor(user.getName());
            alarmVO.getConfig().setModifier(user.getName());
        }
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        addValid(alarm,user);
        return JsonResult.success(this.getService().saveAlarm(alarm));
    }

    @RequestMapping("/search")
    public JsonResult search(@RequestBody AlarmVO alarm, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        alarm.setActionType(AlarmActionType.ALARM.getCode());
        return JsonResult.success(this.getService().searchAlarm(alarm,page,size));
    }
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public JsonResult updateStatus(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        alarm.setModifier(user.getName());
        return JsonResult.success(this.getService().updateStatus(alarm));
    }
    @RequestMapping(value = "/generateTemplate", method = RequestMethod.POST)
    public JsonResult generateTemplate(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        alarm.setModifier(user.getName());
        return JsonResult.success(this.getService().generateTemplate(alarm));
    }

    @RequestMapping(value = "/get/{id}", method = RequestMethod.POST)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(this.getService().getDetailById(id));
    }

    @Override
    protected void updateValid(AlarmVO alarmVO, User user) {
        Alarm alarm = this.getService().getByName(alarmVO.getName());
        if(null != alarm && !alarm.getId().equals(alarmVO.getId())){
            throw new RiskException("标签名称重复！");
        }
        alarmVO.setModifier(user.getName());
        if(alarmVO.getConfigId() == 0){
            alarmVO.getConfig().setAuthor(user.getName());
            alarmVO.getConfig().setModifier(user.getName());
        }else if(alarmVO.getConfig().getType() == AlarmConfigType.CUSTOME.getCode()){
            alarmVO.getConfig().setModifier(user.getName());
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public JsonResult update(@RequestBody AlarmVO alarm, @CurrentUser User user) {
        updateValid(alarm,user);
        return JsonResult.success(this.getService().updateAlarm(alarm));
    }

    @RequestMapping("/getAlarms")
    public JsonResult getEvents(@RequestBody AlarmVO vo) {
        return JsonResult.success(this.getService().simpleAll(vo));
    }

    @AuthRequired(permissionName = "*")
    @RequestMapping("/getAlarmRuleTypes")
    public JsonResult getAlarmRuleTypes(){
        List<Map<String,String>> ruleTypes = Lists.newArrayList();
        for(AlarmRuleType ruleType : AlarmRuleType.values()){
            Map<String,String> ruleTypeMap = Maps.newHashMap();
            ruleTypeMap.put("code",ruleType.getCode()+"");
            ruleTypeMap.put("name",ruleType.getName());
            ruleTypes.add(ruleTypeMap);
        }
        return JsonResult.success(ruleTypes);
    }

}
