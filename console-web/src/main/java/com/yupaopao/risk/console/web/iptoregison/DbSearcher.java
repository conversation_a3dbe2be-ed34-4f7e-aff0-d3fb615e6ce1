package com.yupaopao.risk.console.web.iptoregison;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

/**
 * ip db searcher class (Not thread safe)
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
@Slf4j
public class DbSearcher {
    /**
     * db file access handler
     */

    private InputStream raf;

    /**
     * super blocks info
     */
    private long firstIndexPtr = 0;
    private long lastIndexPtr = 0;
    private int totalIndexBlocks = 0;

    /**
     * for memory mode
     * the original db binary string
     */
    private byte[] dbBinStr = null;

    /**
     * construct class
     *
     * @param dbFile
     * @throws FileNotFoundException
     */
    public DbSearcher(String dbFile) {
        log.info("初始化IP信息库,目录:{}", this.getClass().getClassLoader().getResource(dbFile).getFile());
        raf = this.getClass().getClassLoader().getResourceAsStream(dbFile);
    }

    /**
     * get the region with a int ip address with memory binary search algorithm
     *
     * @param ip
     * @throws IOException
     */
    public IPDetail memorySearch(long ip) throws IOException {
        int blen = IndexBlock.getIndexBlockLength();
        if (dbBinStr == null) {
            dbBinStr = IOUtils.toByteArray(raf);
            //initialize the global vars
            firstIndexPtr = Util.getIntLong(dbBinStr, 0);
            lastIndexPtr = Util.getIntLong(dbBinStr, 4);
            totalIndexBlocks = (int) ((lastIndexPtr - firstIndexPtr) / blen) + 1;
        }

        //search the index blocks to define the data
        int l = 0, h = totalIndexBlocks;
        long sip, eip, dataptr = 0;
        while (l <= h) {
            int m = (l + h) >> 1;
            int p = (int) (firstIndexPtr + m * blen);

            sip = Util.getIntLong(dbBinStr, p);
            if (ip < sip) {
                h = m - 1;
            } else {
                eip = Util.getIntLong(dbBinStr, p + 4);
                if (ip > eip) {
                    l = m + 1;
                } else {
                    dataptr = Util.getIntLong(dbBinStr, p + 8);
                    break;
                }
            }
        }

        //not matched
        if (dataptr == 0) return null;

        //get the data
        int dataLen = (int) ((dataptr >> 24) & 0xFF);
        int dataPtr = (int) ((dataptr & 0x00FFFFFF));
        int cityId = (int) Util.getIntLong(dbBinStr, dataPtr);
        String region = new String(dbBinStr, dataPtr + 4, dataLen - 4, "UTF-8");

        return new IPDetail(cityId, region);
    }

    /**
     * get the region throught the ip address with memory binary search algorithm
     *
     * @param ip
     * @return IPDetail
     * @throws IOException
     */
    public IPDetail memorySearch(String ip) throws IOException {
        return memorySearch(Util.ip2long(ip));
    }

    /**
     * close the db
     *
     * @throws IOException
     */
    public void closeRaf() throws IOException {
        if (raf != null) {
            raf.close();
        }
        raf = null;
    }

    /**
     * close the db
     *
     * @throws IOException
     */
    public void clearDbBinStr() {
        dbBinStr = null;
    }
}
