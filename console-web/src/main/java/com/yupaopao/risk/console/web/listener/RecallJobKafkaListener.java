package com.yupaopao.risk.console.web.listener;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.enums.RecallCommand;
import com.yupaopao.risk.common.enums.RecallStatus;
import com.yupaopao.risk.common.model.RecallJob;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.RecallJobService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-5-9
 */
@Component
public class RecallJobKafkaListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(RecallJobKafkaListener.class);

    @Value("${recall.app-key:sensitiveWords}")
    private String appKey;

    private static final int successCode = 8000;

    @Autowired
    private RecallJobService recallJobService;

    @Autowired
    private RedisService redisService;

//    @KafkaListener(namespace = "middleware.kafka-bigdata" ,topics = "ypp_bigdata_sensitive_word_res")
   @KafkaListener(namespace = "middleware.kafka-bigdata" ,groupId = "recall.msg", topics = "ypp_bigdata_sensitive_word_res")
    public void listener(ConsumerRecord<String, String> record) {
        try {
            LOGGER.info("测试收到回溯任务消息，value={}",record.value());
            JSONObject jsonResult = JSON.parseObject(record.value());
            int code = jsonResult.getInteger("code");
            int messageType = jsonResult.getInteger("messageType");
            int commandType = jsonResult.getInteger("commandType");
            int recallType = jsonResult.getInteger("recallType");
            String jobNo = jsonResult.getString("appJobId");
            String message = jsonResult.getString("message") == null ? "" : jsonResult.getString("message");
            //暂时只需关注【1验证参数】【3任务计算的总结果】
            if(messageType != 1 && messageType != 3){
                return;
            }

            if(commandType !=1 && commandType!=2){
                return;
            }

            if(recallType !=1 && recallType!=2){
                return;
            }

            if(!jobNo.contains("_")){
                return;
            }

            String[] identitys = jobNo.split("_");
            if(identitys.length!=2){
                return;
            }

            jobNo = identitys[0];
            if(!StringUtils.isNumber(identitys[1])){
                return;
            }

            int times = Integer.parseInt(identitys[1]);
            RecallJob condition = new RecallJob();
            condition.setJobNo(jobNo);
            condition.setTimes(times);
            if(commandType==RecallCommand.STATISTIC.getCode()){
                condition.setStatus(RecallStatus.STATISTICING.getCode());
            }else if(commandType==RecallCommand.DETAIL.getCode()){
                condition.setStatus(RecallStatus.RUNNING.getCode());
            }

            List<RecallJob> jobs = recallJobService.search(condition,"times desc");
            if(CollectionUtils.isEmpty(jobs)){
                LOGGER.warn("没有找到回溯任务，jobNo={}",jobNo);
                return;
            }
            //场景和错误信息复用结果信息字段
            message = jobs.get(0).getMessage() + ";" +code+":"+message;

            //验证失败或执行失败
            if(code==successCode){
                if(messageType== 3){
                    // 所有执行成功
                    int count = jsonResult.getInteger("counts");

                    if(commandType==1){
                        message += (";统计出数量为count=" + count);

                        recallJobService.updateStatusByJobNo(jobNo, jobs.get(0).getTimes(),RecallStatus.STATISTICED.getCode(), message,count);
                        //汇总结果
                        recallJobService.updateParentJobStatus(jobs.get(0).getBatchId());
                        //将redis中删除
                        deleteFromRedis(jobs.get(0));
                    }else if(commandType==RecallCommand.DETAIL.getCode()){
                        message += (";统计及获取数量为count=" + count);

                        recallJobService.updateStatusByJobNo(jobNo, jobs.get(0).getTimes(),RecallStatus.FINISH.getCode(),message,count);
                        // 汇总结果
                        recallJobService.updateParentJobStatus(jobs.get(0).getBatchId());
                        // 将redis中删除
                        deleteFromRedis(jobs.get(0));
                    }

                    LOGGER.info("回溯任务的所有场景执行成功，jobNo={},count={}",jobNo,jsonResult.getInteger("counts"));
                }else{
                    //验证成功
                    LOGGER.info("回溯任务验证成功，jobNo={},count={}",jobNo,jsonResult.getInteger("counts")==null?0:jsonResult.getInteger("counts"));
                }

            }else{
                recallJobService.updateStatusByJobNo(jobNo,jobs.get(0).getTimes(), RecallStatus.ERROR.getCode(),message,0);
                // 汇总结果
                recallJobService.updateParentJobStatus(jobs.get(0).getBatchId());
                // 将redis中删除
                deleteFromRedis(jobs.get(0));

                LOGGER.warn("回溯任务验证或执行失败，jobNo={}",jobNo);
            }
        }catch (Throwable e){
            LOGGER.error("处理回溯任务消息失败，value=" + record.value(),e);
        }
    }

    private void deleteFromRedis(RecallJob job){

        Set<String> jobNos = (Set<String>)redisService.get(ConsoleConstants.KEY_TRY_DO_IN_REDIS);

        if(CollectionUtils.isNotEmpty(jobNos)){
            jobNos.removeIf(jobNo -> jobNo.equalsIgnoreCase(job.getJobNo()));
            redisService.set(ConsoleConstants.KEY_TRY_DO_IN_REDIS,jobNos);
        }
    }

}