package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.Log;
import com.yupaopao.risk.console.service.LogService;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/log")
@AuthRequired(permissionName = "system:log:all")
@Slf4j
public class LogController extends AbstractModelController<Log, Log, LogService> {

}
