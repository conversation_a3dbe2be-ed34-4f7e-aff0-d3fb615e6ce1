package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.enums.AttributeType;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.AttributeVO;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.service.impl.ConfigService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/attribute")
@AuthRequired(permissionName = "core:attribute:all")
public class AttributeController extends AbstractModelController<Attribute, AttributeVO, AttributeService> {

    @Autowired
    private ConfigService configService;

    @RequestMapping("/search")
    public JsonResult search(@RequestBody AttributeVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<Attribute> result = this.getService().search(record, page, size);
        getService().relationFetch(result.getList());
        return JsonResult.success(result);
    }

    @RequestMapping("/local/functions")
    public JsonResult getLocalFunctions() {
        return JsonResult.success(configService.getLocalFunctionList());
    }

    @RequestMapping("/remote/functions")
    public JsonResult getRemoteFunctions() {
        return JsonResult.success(configService.getRemoteFunctionList());
    }

    @Override
    protected void addValid(AttributeVO record, User user) {
        Assert.notNull(AttributeType.nameOf(record.getType()), "请指定属性类型");
        record.setAuthor(user.getName());
        record.setModifier(user.getName());
    }

    @Override
    protected void updateValid(AttributeVO record, User user) {
        Assert.notNull(AttributeType.nameOf(record.getType()), "请指定属性类型");
        record.setAuthor(null);
        record.setModifier(user.getName());
    }

    @RequestMapping("/remote/updateBizTypeFlag")
    public JsonResult updateBizTypeFlag(@Valid @RequestBody AttributeVO record, @CurrentUser User user) {
        return JsonResult.success(getService().updateBizTypeFlag(record,user));
    }

}
