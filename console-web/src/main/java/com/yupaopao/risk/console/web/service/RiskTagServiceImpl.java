package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.api.RiskTagService;
import com.yupaopao.risk.console.bean.RiskTag;
import com.yupaopao.risk.console.service.TagService;
import com.yupaopao.risk.console.utils.ObjectConversion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@Component
public class RiskTagServiceImpl implements RiskTagService {

    @Autowired
    private TagService tagService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    /**
     *  根据toSystem，从DB中获取一级标签
     * @return
     */
    private List<RiskTag> loadFirstTags(){

            List<Tag> tags = tagService.searchFirstTags();
            if(CollectionUtils.isNotEmpty(tags)){
                List<RiskTag> riskTags  = ObjectConversion.copy(tags,RiskTag.class);
                return riskTags;
            }

            return null;
        }

        @Override
        public List<RiskTag> searchFirstTags(Integer toSystem) {

            try{
            List<RiskTag> riskTagRedis = (List<RiskTag>)redisService.get(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX);
            if(CollectionUtils.isNotEmpty(riskTagRedis)){
                return riskTagRedis;
            }else{
                List<RiskTag> riskTagsDB = loadFirstTags();
                if(CollectionUtils.isNotEmpty(riskTagsDB)){
                    redisService.set(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX,riskTagsDB,ConsoleConstants.REDIS_EXPIRE_TIME);
                    return riskTagsDB;
                }
            }
        }catch (Exception exp){
            log.error("根据toSystem,获取一级标签失败",exp);
        }

        return null;
    }

    @Override
    public List<RiskTag> searchSecondTags(String firstTagName) {

        String key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX,Md5Util.md5(firstTagName));
        List<RiskTag> riskTagsRedis = (List<RiskTag>)redisService.get(key);
        if(CollectionUtils.isNotEmpty(riskTagsRedis)){
            return riskTagsRedis;
        }else{
            List<Tag> riskTagsDB = tagService.searchSecondTags(firstTagName);
            if(CollectionUtils.isNotEmpty(riskTagsDB)){
                List<RiskTag> riskTags = ObjectConversion.copy(riskTagsDB,RiskTag.class);

                redisService.set(key,riskTags,ConsoleConstants.REDIS_EXPIRE_TIME);
                return riskTags;
            }
        }

        return null;
    }

}
