package com.yupaopao.risk.console.web.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.config.CategoryConfig;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import com.yupaopao.risk.console.web.support.utils.MapValueConvertUtil;
import com.yupaopao.risk.punish.api.ManageService;
import com.yupaopao.risk.punish.bean.KeyDesc;
import com.yupaopao.risk.punish.enums.BizChannelEnum;
import com.yupaopao.risk.punish.enums.TagEnum;
import com.yupaopao.risk.punish.request.manage.PackageDTO;
import com.yupaopao.risk.punish.request.manage.PackageRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 惩罚日志搜索
 */
@RestController
@RequestMapping("/punish/log")
@AuthRequired(permissionName = "punish:log:all")
public class PunishLogController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PunishLogController.class);

    @Autowired
    private ElasticSearchService elasticSearchService;
    @Reference
    private ManageService manageService;
    @Autowired
    private CategoryConfig categoryConfig;

    @RequestMapping("hit")
    public JsonResult search(@RequestBody LogSearchVO.PunishLogSearchVO vo) {
        if (StringUtils.isNotBlank(vo.getTargetValue())) {
            if (vo.getTargetType() == "uid" || vo.getTargetType() == "liveRoomId") {
                if (!NumberUtils.isNumber(vo.getTargetValue())) {
                    return JsonResult.success(new HashMap<>());
                }
            }
        }
        CommonUtil.setFieldValue(vo.getQuery(), vo.getTargetType(), vo.getTargetValue());
        PageInfo<Map<String, Object>> result = elasticSearchService.searchPunishLog(vo);
        MapValueConvertUtil.convertValueToString(result.getList(), "uid", "liveRoomId", "fromUid");
        return JsonResult.success(result);
    }

    @RequestMapping("getPkgList")
    public JsonResult getPkgList() {
        PackageRequest request = new PackageRequest();
        List states = new ArrayList();
        states.add(1);
        states.add(2);
        request.setStates(states);
        Response<PageInfo<PackageDTO>> response = manageService.searchPackage(request);
        if (response != null && response.getResult() != null) {
            return JsonResult.success(response.getResult().getList());
        }
        return JsonResult.success(new ArrayList());
    }

    @RequestMapping("getTags")
    public JsonResult getTags() {
        return JsonResult.success(getPunishTags());
    }

    private List<KeyDesc> getPunishTags() {
        List<KeyDesc> list = null;
        try {
            list = manageService.getTags();
        } catch (Exception e) {
            LOGGER.error("惩罚记录 查询惩罚能力dubbo接口异常", e);
        }
        //dubbo异常则改用读取enum，可能会有最新数据未兼容
        return list == null ? TagEnum.getTagList1() : list;
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "惩罚记录导出", isReportResponse = false)
    @RequestMapping("export")
    @AuthRequired(permissionName = "punish:log:export")
    public JsonResult export(@RequestBody LogSearchVO.PunishLogSearchVO vo) {
        //本导出最多只能导出10000条
        vo.setPage(1);
        vo.setSize(10000);
        if (StringUtils.isNotBlank(vo.getTargetValue())) {
            if (vo.getTargetType() == "uid" || vo.getTargetType() == "liveRoomId") {
                if (!NumberUtils.isNumber(vo.getTargetValue())) {
                    return JsonResult.error(ErrorMessage.FORM_ERROR);
                }
            }
        }
        CommonUtil.setFieldValue(vo.getQuery(), vo.getTargetType(), vo.getTargetValue());
        PageInfo<Map<String, Object>> pageInfo = elasticSearchService.searchPunishLog(vo);
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("result").addName("结果").build(),
                ExcelFormatter.builder("punishPackageId").addName("惩罚包ID").build(),
                ExcelFormatter.builder("pkgName").addName("惩罚包").build(),
                ExcelFormatter.builder("punishCode").addName("惩罚项").build(),
                ExcelFormatter.builder("channel").addName("渠道").build(),
                ExcelFormatter.builder("uid").addName("用户UID").build(),
                ExcelFormatter.builder("deviceId").addName("设备ID").build(),
                ExcelFormatter.builder("mobile").addName("手机号").build(),
                ExcelFormatter.builder("chatRoomId").addName("聊天室ID").build(),
                ExcelFormatter.builder("liveRoomId").addName("直播间ID").build(),
                ExcelFormatter.builder("category").addName("类别").build(),
                ExcelFormatter.builder("pkgLevel").addName("惩罚级别").build(),
                ExcelFormatter.builder("pkgMemo").addName("惩罚包备注").build(),
                ExcelFormatter.builder("operator").addName("操作人").build(),
                ExcelFormatter.builder("innerReason").addName("对内原因").build(),
                ExcelFormatter.builder("outerReason").addName("对外原因").build(),
                ExcelFormatter.builder("appId").addName("入参平台业务线").build(),
                ExcelFormatter.builder("extConf").addName("附加配置信息").build(),
                ExcelFormatter.builder("createdAt").addName("创建时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build()
        );
        try {
            List<Map<String, Object>> reasons = categoryConfig.getCategoryList();
            List<KeyDesc> keyDescs = getPunishTags();
            for (Map<String, Object> map : pageInfo.getList()) {
                for (KeyDesc kd : keyDescs) {
                    if (kd.getKey().equals(map.get("punishCode"))) {
                        map.put("punishCode", kd.getDesc());
                    }
                }

                BizChannelEnum channelEnum = BizChannelEnum.getByName(map.get("channel") + "");
                map.put("channel", channelEnum == null ? "" : channelEnum.getMsg());

                String category = map.get("category") == null ? "" : (map.get("category") + "");
                if (StringUtils.isNotBlank(category)) {
                    for (Map<String, Object> reason : reasons) {
                        if (category.equals(reason.get("code"))) {
                            category = reason.get("desc") + "";
                            break;
                        }
                    }
                    map.put("category", category);
                }
            }
            String fileName = ExcelUtils.exportExcelByMap(pageInfo.getList(), "惩罚记录", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            LOGGER.error("导出惩罚记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "惩罚批次记录导出", isReportResponse = false)
    @RequestMapping("exportBatch")
    @AuthRequired(permissionName = "punish:log:exportbatch")
    public JsonResult exportBatch(@RequestBody LogSearchVO.PunishLogSearchVO vo) {
        //本导出最多只能导出10000条
        vo.setPage(1);
        vo.setSize(10000);
        if (StringUtils.isNotBlank(vo.getTargetValue())) {
            if (vo.getTargetType() == "uid" || vo.getTargetType() == "liveRoomId") {
                if (!NumberUtils.isNumber(vo.getTargetValue())) {
                    return JsonResult.error(ErrorMessage.FORM_ERROR);
                }
            }
        }
        CommonUtil.setFieldValue(vo.getQuery(), vo.getTargetType(), vo.getTargetValue());
        PageInfo<Map<String, Object>> pageInfo = elasticSearchService.searchPunishLog(vo);
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("uid").addName("用户UID").build(),
                ExcelFormatter.builder("deviceId").addName("设备ID").build(),
                ExcelFormatter.builder("mobile").addName("手机号").build(),
                ExcelFormatter.builder("chatRoomId").addName("聊天室ID").build(),
                ExcelFormatter.builder("liveRoomId").addName("直播间ID").build(),
                ExcelFormatter.builder("pkgName").addName("惩罚包").build(),
                ExcelFormatter.builder("pkgLevel").addName("惩罚级别").build(),
                ExcelFormatter.builder("channel").addName("渠道").build(),
                ExcelFormatter.builder("category").addName("类别").build(),
                ExcelFormatter.builder("innerReason").addName("对内原因").build(),
                ExcelFormatter.builder("outerReason").addName("对外原因").build(),
                ExcelFormatter.builder("operator").addName("操作人").build(),
                ExcelFormatter.builder("createdAt").addName("惩罚时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("memo").addName("备注").build(),
                ExcelFormatter.builder("images").addName("凭证链接").addWidth(50).addType("image").build()
        );
        try {
            List<Map<String, Object>> reasons = categoryConfig.getCategoryList();
            for (Map<String, Object> map : pageInfo.getList()) {
                BizChannelEnum channelEnum = BizChannelEnum.getByName(map.get("channel") + "");
                map.put("channel", channelEnum == null ? "" : channelEnum.getMsg());

                String category = map.get("category") == null ? "" : (map.get("category") + "");
                if (StringUtils.isNotBlank(category)) {
                    for (Map<String, Object> reason : reasons) {
                        if (category.equals(reason.get("code"))) {
                            category = reason.get("desc") + "";
                            break;
                        }
                    }
                    map.put("category", category);
                }
            }
            //合并惩罚记录
            Map<String, Map<String, Object>> batch = new HashMap<>(2048);
            if (!CollectionUtils.isEmpty(pageInfo.getList())) {
                for (Map<String, Object> map : pageInfo.getList()) {
                    String requestId = map.get("requestId") + "";
                    if (batch.containsKey(requestId)) {
                        //合并数据
                        if (map.get("uid") != null) {
                            batch.get(requestId).put("uid", map.get("uid"));
                        }
                        if (map.get("deviceId") != null) {
                            batch.get(requestId).put("deviceId", map.get("deviceId"));
                        }
                        if (map.get("mobile") != null) {
                            batch.get(requestId).put("mobile", map.get("mobile"));
                        }
                        if (map.get("chatRoomId") != null) {
                            batch.get(requestId).put("chatRoomId", map.get("chatRoomId"));
                        }
                        if (map.get("liveRoomId") != null) {
                            batch.get(requestId).put("liveRoomId", map.get("liveRoomId"));
                        }
                    } else {
                        batch.put(requestId, map);
                    }
                }
            }
            String fileName = ExcelUtils.exportExcelByMap(batch.values().stream().collect(Collectors.toList()), "批次惩罚记录", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            LOGGER.error("导出惩罚记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }
}
