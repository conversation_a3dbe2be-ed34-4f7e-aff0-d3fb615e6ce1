package com.yupaopao.risk.console.web.controller.content;

import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.service.DiffLogService;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/diff/log")
@Slf4j
public class DiffLogController extends AbstractModelController<Attribute, Attribute, AttributeService> {
    @Autowired
    private DiffLogService diffLogService;

    @RequestMapping("feedback")
    @AuthRequired(permissionName = "view:logsearch:diff")
    public JsonResult diffLogFeedback(@RequestParam("traceId") String traceId) {
        Assert.hasText(traceId,"关联标识缺失");
        return JsonResult.success(diffLogService.feedback(traceId));
    }

}
