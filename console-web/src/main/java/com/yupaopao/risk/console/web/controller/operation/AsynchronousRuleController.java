package com.yupaopao.risk.console.web.controller.operation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.PatrolRuleStateEnum;
import com.yupaopao.risk.common.enums.SupportRuleType;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.service.PatrolRuleService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.PatrolRuleCheckConstVO;
import com.yupaopao.risk.console.vo.PatrolRuleVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 异步规则
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("operation/asynchronousRule")
@AuthRequired(permissionName = "operation:asynchronous:rule")
public class AsynchronousRuleController extends AbstractModelController<PatrolRule, PatrolRuleVO, PatrolRuleService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;

    @Autowired
    private EventService eventService;

    @Override
    protected void addValid(PatrolRuleVO record, User user) {
        record.setModifier(user.getName());
        record.setAuthor(user.getName());
    }

    @Override
    protected void updateValid(PatrolRuleVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        record.setModifier(user.getName());
        record.setAuthor(null);
    }

    protected void deleteValid(PatrolRule record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        record.setState(PatrolRuleStateEnum.DELETE.getCode());
        record.setModifier(user.getName());
        record.setAuthor(null);
    }

    /**
     * 获取异步规则状态列表
     *
     * @return
     */
    @RequestMapping(value = "/getAsynchronousRuleStates", method = RequestMethod.POST)
    public JsonResult getAsynchronousRuleStates() {
        return JsonResult.success(getService().getPatrolRuleStates());
    }

    /**
     * 获取所有的规则类型
     *
     * @return 所有的规则类型
     */
    @RequestMapping(value = "/getAsynchronousRuleTypes", method = RequestMethod.POST)
    public JsonResult getAsynchronousRuleTypes() {
        return JsonResult.success(getService().getPatrolRuleTypes());
    }

    /**
     * 根据异步规则类型，获取所对应的惩罚包
     *
     * @param type
     * @return
     */
    @RequestMapping(value = "/getPunishPackages/{type}", method = RequestMethod.POST)
    public JsonResult getPunishPackages(@PathVariable Integer type) {
        return JsonResult.success(getService().getPunishPackages(SupportRuleType.getEnumByCode(type)));
    }

    @RequestMapping(value = "/getPunishPkgDetail/{pkgId}", method = RequestMethod.POST)
    public JsonResult getPunishPkgDetail(@PathVariable String pkgId) {
        return JsonResult.success(getService().getPunishPkgDetail(pkgId));
    }

    /**
     * 风控事件查询
     *
     * @return
     */
    @RequestMapping("/getEvents")
    public JsonResult getEvents() {
        return JsonResult.success(eventService.simpleAll());
    }

    /**
     * 根据关键字 搜索满足条件的规则列表
     *
     * @param record
     * @param page
     * @param size
     * @return
     */
    @Override
    @RequestMapping("/search")
    public JsonResult search(@RequestBody PatrolRuleVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {

        try {
            if (record.getType() != null) {
                record.setType(record.getType());
            }

            PageInfo response = getService().search(record, page, size);

            if (response != null) {
                List<PatrolRule> patrolRules = response.getList();
                if (CollectionUtils.isNotEmpty(patrolRules)) {
                    List<PatrolRuleVO> patrolRuleVOS = new ArrayList<>();

                    for (PatrolRule patrolRule : patrolRules) {
                        PatrolRuleVO patrolRuleVO = new PatrolRuleVO();

                        BeanUtils.copyProperties(patrolRuleVO, patrolRule);

                        List<Event> relateEvents = new ArrayList<>();
                        if (StringUtils.isNotBlank(patrolRule.getRelateEvent())) {
                            String[] eventCodes = patrolRule.getRelateEvent().split(",");

                            for (String eventCode : eventCodes) {
                                Event event = eventService.getByCode(eventCode);
                                relateEvents.add(event);
                            }
                        } else {
                            Event event = new Event();
                            event.setCode("ALL");
                            event.setName("全部");
                            relateEvents.add(event);
                        }

                        patrolRuleVO.setRelateEvents(relateEvents);
//                        if(StringUtils.isNotBlank(patrolRule.getParameters())){
//                            JSONObject jsonObject = JSON.parseObject(patrolRule.getParameters());
//                            if((jsonObject.get("bizId")!=null)&&StringUtils.isNotBlank(jsonObject.get("bizId").toString())){
//                                patrolRuleVO.setBizId(jsonObject.get("bizId").toString());
//                            }
//
//                            if((jsonObject.get("briefDesc")!=null)&&StringUtils.isNotBlank(jsonObject.get("briefDesc").toString())){
//                                patrolRuleVO.setBriefDesc(jsonObject.get("briefDesc").toString());
//                            }
//
//                            if((jsonObject.get("fromUid")!=null)&&StringUtils.isNotBlank(jsonObject.get("fromUid").toString())){
//                                patrolRuleVO.setFromUid(jsonObject.get("fromUid").toString());
//                            }
//
//                            if((jsonObject.get("targetId")!=null)&&StringUtils.isNotBlank(jsonObject.get("targetId").toString())){
//                                patrolRuleVO.setTargetId(jsonObject.get("targetId").toString());
//                            }
//                        }

                        patrolRuleVOS.add(patrolRuleVO);
                    }

                    response.setList(patrolRuleVOS);

                    return JsonResult.success(response);
                }
            }

            return JsonResult.success(response);
        } catch (Exception exp) {
            log.error("查询异步规则报异常,ErrorMsg:{}", exp.getMessage());
            return JsonResult.error("8002", "查询异步规则报异常", exp.getMessage());
        }
    }

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody PatrolRuleVO record, @CurrentUser User user) {
        addValid(record, user);

        if (CollectionUtils.isNotEmpty(record.getRelateEvents())) {
            StringBuilder eventCodes = new StringBuilder();
            for (Event event : record.getRelateEvents()) {
                if (event.getCode().equalsIgnoreCase("ALL")) {
                    eventCodes = new StringBuilder();
                    break;
                }

                eventCodes.append(event.getCode()).append(",");
            }
            record.setRelateEvent((eventCodes.length() == 0) ? "" : eventCodes.substring(0, eventCodes.length() - 1));
        }

//        if(StringUtils.isNotBlank(record.getParameters())){
//            record.setParameters(JSONObject.toJSONString(record.getParameters()));
//        }

//        if(Objects.equals(record.getType(),SupportRuleType.COMPLAIN_RULE.getCode())){
//            Map<String,Object> parameterMap = new HashMap<>(4);
//
//            if(StringUtils.isNotBlank(record.getBizId())){
//                parameterMap.put("bizId",record.getBizId());
//            }
//
//            if(StringUtils.isNotBlank(record.getBriefDesc())){
//                parameterMap.put("briefDesc",record.getBriefDesc());
//            }
//
//            if(StringUtils.isNotBlank(record.getFromUid())){
//                parameterMap.put("fromUid",record.getFromUid());
//            }
//
//            if(StringUtils.isNotBlank(record.getTargetId())){
//                parameterMap.put("targetId",record.getTargetId());
//            }
//
//            record.setParameters(JSONObject.toJSONString(parameterMap));
//        }

        return JsonResult.success(getService().insertSelective(record));
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody PatrolRuleVO record, @CurrentUser User user) {
        updateValid(record, user);

        if (CollectionUtils.isNotEmpty(record.getRelateEvents())) {
            StringBuilder eventCodes = new StringBuilder();
            for (Event event : record.getRelateEvents()) {
                if (event.getCode().equalsIgnoreCase("ALL")) {
                    eventCodes = new StringBuilder();
                    break;
                }

                eventCodes.append(event.getCode()).append(",");
            }
            record.setRelateEvent((eventCodes.length() == 0) ? "" : eventCodes.substring(0, eventCodes.length() - 1));
        }

//        if(Objects.equals(record.getType(),SupportRuleType.COMPLAIN_RULE.getCode())){
//            Map<String,Object> parameterMap = new HashMap<>(4);
//
//            if(StringUtils.isNotBlank(record.getBizId())){
//                parameterMap.put("bizId",record.getBizId());
//            }
//
//            if(StringUtils.isNotBlank(record.getBriefDesc())){
//                parameterMap.put("briefDesc",record.getBriefDesc());
//            }
//
//            if(StringUtils.isNotBlank(record.getFromUid())){
//                parameterMap.put("fromUid",record.getFromUid());
//            }
//
//            if(StringUtils.isNotBlank(record.getTargetId())){
//                parameterMap.put("targetId",record.getTargetId());
//            }
//
//            record.setParameters(JSONObject.toJSONString(parameterMap));
//        }

//        if(StringUtils.isNotBlank(record.getParameters())){
//            record.setParameters(JSONObject.toJSONString(record.getParameters()));
//        }

        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping(value = "/virtualDelete", method = RequestMethod.POST)
    public JsonResult delete(@Valid @RequestBody PatrolRule record, @CurrentUser User user) {
        deleteValid(record, user);
        record.setState(PatrolRuleStateEnum.DELETE.getCode());
        return JsonResult.success(getService().updateSelectiveById(record));
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild asynchronous rule engine , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_MAGIC)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.ASYNCHRONOUS_RULE_RELOAD_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger asynchronous rule rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

    @RequestMapping("/getConstFormatter")
    public JsonResult getRiskConst(@Param("dependent") String dependent, @Param("id") Long id, @RequestBody List<Event> relateEvents) {
        Map<String, Object> map = getService().getConstFormatter(relateEvents, dependent, id);
        if (null == map) {
            return JsonResult.success();
        }
        return JsonResult.success(JSON.toJSONString(map));
    }

    @RequestMapping("/edit/getConstFormatter")
    public JsonResult getRiskConstForEdit(@Param("dependent") String dependent, @Param("id") Long id, @RequestBody List<Event> relateEvents) {
        Map<String, Object> map = getService().getConstFormatterForEdit(relateEvents, dependent, id);
        if (null == map) {
            return JsonResult.success();
        }
        return JsonResult.success(JSON.toJSONString(map));
    }

    @RequestMapping("checkConstKeys")
    public JsonResult checkConstKeys(@RequestBody PatrolRuleCheckConstVO vo) {
        List<String> keys = getService().checkConstKeys(vo.getRiskConst(),vo.getRelateEvents());
        return JsonResult.success(keys);
    }

}
