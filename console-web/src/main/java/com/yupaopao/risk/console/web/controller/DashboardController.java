package com.yupaopao.risk.console.web.controller;

import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.console.service.DashboardService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2019/5/5 7:36 PM
 */
@Slf4j
@RestController
@RequestMapping("dashboard")
@AuthRequired(permissionName = "view:dashboard:all")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控大盘汇总",isReportResponse = false)
    @RequestMapping("summary")
    public JsonResult summary(@RequestBody String body) {
        return JsonResult.success(dashboardService.getSummary());
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控历史",isReportResponse = false)
    @RequestMapping("histogram")
    public JsonResult histogram(@RequestBody String body) {
        return JsonResult.success(dashboardService.getHistogram());
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控大盘拒绝原因汇总",isReportResponse = false)
    @RequestMapping("reject")
    public JsonResult reject(@RequestBody String body) {
        return JsonResult.success(dashboardService.getReject());
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控大盘审核原因汇总",isReportResponse = false)
    @RequestMapping("review")
    public JsonResult review(@RequestBody String body) {
        return JsonResult.success(dashboardService.getReview());
    }

}
