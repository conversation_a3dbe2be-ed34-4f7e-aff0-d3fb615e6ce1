package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.SQLService;
import com.yupaopao.risk.console.service.UserService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/sql")
@AuthRequired(permissionName = "system:sql:all")
public class SQLController extends AbstractModelController<User, User, UserService> {

    @Autowired
    private SQLService sqlService;

    @RequestMapping("/execute")
    public JsonResult execute(@RequestBody SQL sql) {
        return JsonResult.success(sqlService.execute(sql.getSql()));
    }

}

class SQL implements Serializable {

    private String sql;

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

}