package com.yupaopao.risk.console.web.controller.cs;

import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("cs")
@AuthRequired(permissionName = "cs:guide:all")
public class GuideController {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @RequestMapping("/guide")
    public JsonResult guide(@RequestBody LogSearchVO.HitLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchSingleHitLog(vo));
    }

}
