package com.yupaopao.risk.console.web.support.annotaion;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.yupaopao.risk.common.Constants;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Target({ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface CurrentUser {
    String value() default Constants.CURRENT_USER_KEY;
}
