package com.yupaopao.risk.console.web.controller;


import com.github.pagehelper.PageInfo;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.ParamEvent;
import com.yupaopao.risk.common.model.ParamValidator;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.utils.RiskModelIdUtils;
import com.yupaopao.risk.common.vo.EventVO;
import com.yupaopao.risk.common.vo.ParamValidatorVO;
import com.yupaopao.risk.console.service.ParamEventService;
import com.yupaopao.risk.console.service.ParamValidatorService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 风控入参检测规则相关
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/param/validator")
@AuthRequired(permissionName = "join:param:all")
public class ParamValidatorController extends AbstractModelController<ParamValidator, ParamValidatorVO, ParamValidatorService> {

    @Autowired
    private ParamEventService paramEventService;

    // TODO 是否需要配置去apollo配置当前controller的菜单及权限

    @Override
    @RequestMapping("/search")
    public JsonResult search(@RequestBody ParamValidatorVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<ParamValidator> result = getService().search(record, page, size);
        getService().fetchRelations(result.getList());
        return JsonResult.success(result);
    }

    @Override
    protected void addValid(ParamValidatorVO record, User user) {
        Assert.hasLength(record.getName(), "请指定规则名称");
        Assert.hasLength(record.getContent(), "请指定规则内容");
        ParamValidator paramValidator = this.getService().getByName(record.getName());
        Assert.isNull(paramValidator, "规则name重复");
        record.setUpdatedBy(user.getName());
    }

    @Override
    protected void updateValid(ParamValidatorVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getName(), "请指定规则名称");

        ParamValidator paramValidator = this.getService().getByName(record.getName());
        Assert.isTrue(paramValidator == null || record.getId().equals(paramValidator.getId()), "规则name重复");
    }

    @RequestMapping("/getValidators")
    public JsonResult getValidators() {
        return JsonResult.success(getService().simpleAll());
    }

    @RequestMapping("/searchByEvent")
    public JsonResult search(@RequestBody Event event) {
        // 填充绑定的入参校验规则
        List<ParamEvent> mappedParams = paramEventService.getByEventCode(event.getCode());
        if(CollectionUtils.isNotEmpty(mappedParams)){
            Set<Long> paramValidatorIds = mappedParams.stream().map(ParamEvent::getRuleId).collect(Collectors.toSet());
            return JsonResult.success(this.getService().getByIds(paramValidatorIds));
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }
}
