package com.yupaopao.risk.console.web.controller;


import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.Permission;
import com.yupaopao.risk.common.model.RolePermission;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.PermissionService;
import com.yupaopao.risk.console.service.RolePermissionService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;


@RestController
@RequestMapping("/permission")
@Slf4j
@AuthRequired(permissionName = "system:permission:all")
public class PermissionController extends AbstractModelController<Permission, Permission, PermissionService>{


    @Autowired
    private PermissionService service;
    @Autowired
    private RolePermissionService rolePermissionService;

    @RequestMapping("/search")
    public JsonResult search(@RequestBody Permission permission, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Map<String, Object> likeMap = new HashMap<>();
        Map<String, Object> equalMap = new HashMap<>();
        equalMap.put("delFlag", false);

        if(StringUtils.isNotBlank(permission.getDescription())){
            likeMap.put("description", permission.getDescription());
        }
        if(StringUtils.isNotBlank(permission.getName())){
            likeMap.put("name", permission.getName());
        }

        PageInfo<Permission> pageList = service.selectByConditions(page, size, equalMap, likeMap, "parent_id desc", Permission.class);
        List<Permission> list = pageList.getList();
        if(!CollectionUtils.isEmpty(list)){
            List<Permission> all = service.selectByCondition(equalMap, null, Permission.class);
            Map<Long, Permission> map = new HashMap<>(all.size() * 2);
            all.forEach(perm -> map.put(perm.getId(), perm));
            list.forEach(perm -> {
                if(null != perm.getParentId() && map.containsKey(perm.getParentId())){
                    perm.setParentDesc(map.get(perm.getParentId()).getDescription());
                }
            });
            map.clear();
        }

        return JsonResult.success(pageList);
    }


    @RequestMapping("/hint")
    public JsonResult hint(@RequestBody Permission permission, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Map<String, Object> likeMap = new HashMap<>();
        if(StringUtils.isNotBlank(permission.getDescription())){
            likeMap.put("description", permission.getDescription());
        }

        Map<String, Object> equalMap = new HashMap<>();
        if(permission.getDelFlag()!=null){
            equalMap.put("delFlag",permission.getDelFlag());
        }

        PageInfo<Permission> pageInfo = service.selectByConditions(1, size, equalMap, likeMap, "description desc", Permission.class);
        List<Permission> list = pageInfo.getList();

        return JsonResult.success(list);
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody Permission record, @CurrentUser User user) {
        if(null != record.getParentId() && id == record.getParentId()){
            return JsonResult.error(ErrorMessage.URM_PERMISSION_INVALID_PARENT);
        }
        updateValid(record, user);
        //先做 菜单变更
        try{
        boolean b = service.updateSelectiveById(record);
        // 是否 存在 父菜单 变更
        if (!record.getParentDesc().equals(record.getParentId()+"")){
            // 查询出 有 这个子菜单 权限的 角色ID
            RolePermission query = new RolePermission();
            query.setPermissionId(Long.valueOf(record.getId()));
            List<RolePermission> upRolePermission = rolePermissionService.select(query);

            List childrenMenuIdList = getOrderChildrenMenuID(record);//原来父菜单的子菜单

            for (RolePermission rolePermission : upRolePermission){

                //如果 这个角色 拥有 这个old父菜单下的其他子菜单权限，就不做变更 这个角色 新的一条 new 父菜单id，
                if (hasMenu(childrenMenuIdList, rolePermission.getRoleId())){
                    //新增前 需要再检查一下 这个roleId 是否有新的父菜单的权限

                    Map equestMap = new HashMap();
                    equestMap.put("roleId", rolePermission.getRoleId());
                    equestMap.put("permissionId", record.getParentId());
                    List newPermissionList = rolePermissionService.selectByCondition(equestMap, null, null, RolePermission.class);
                    if (CollectionUtils.isEmpty(newPermissionList) || newPermissionList.size()<=0){
                        RolePermission insertPermission = new RolePermission();
                        insertPermission.setPermissionId(record.getParentId());
                        insertPermission.setRoleId(rolePermission.getRoleId());
                        insertPermission.setCreatedAt(new Date());
                        rolePermissionService.insertSelective(insertPermission);
                    }

                }else {
                    // 否则用 这个 角色id + new 父菜单id 去更新 role_permission 表中的 记录为 变更后的 父菜单id

                    RolePermission upPermission = new RolePermission();
                    upPermission.setRoleId(rolePermission.getRoleId());
                    upPermission.setPermissionId(record.getParentId());
                    Map conditionMap = new HashMap();
                    conditionMap.put("roleId", rolePermission.getRoleId());
                    conditionMap.put("permissionId", Long.valueOf(record.getParentDesc()));
                    rolePermissionService.updateByCondition(upPermission, conditionMap, null, null, RolePermission.class);
                }
            }
        }
        }catch (Exception e){
            log.error("权限变更失败", e);
            return JsonResult.success("权限变更错误，请检查相应角色权限是否正常");
        }
        return JsonResult.success(true);
    }

    private boolean hasMenu(List childrenMenuIdList, long roleId) {
        Map roleQuery = new HashMap();
        Map equetsQuery = new HashMap();
        equetsQuery.put("roleId", roleId);
        roleQuery.put("permissionId", childrenMenuIdList);
        List permissionlist = rolePermissionService.selectByCondition(equetsQuery, null, roleQuery, RolePermission.class);
        if (CollectionUtils.isNotEmpty(permissionlist) && permissionlist.size()>0){
            return true;
        }
        return false;
    }

    /**
     * 获取父菜单下的其他子菜单
     * */
    private List getOrderChildrenMenuID(@RequestBody @Valid Permission record) {
        Map perMap = new HashMap();
        perMap.put("parentId", Long.valueOf(record.getParentDesc()));

        List<Permission> list = service.selectByCondition(perMap, null, null, Permission.class);
        List childrenMenuIdList = new ArrayList();
        for (Permission permission: list){
            Long childrenMenuId = permission.getId();
            if (childrenMenuId != record.getId()){
                childrenMenuIdList.add(childrenMenuId);
            }
        }
        return childrenMenuIdList;
    }

    @Override
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @Transactional
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        int r = service.deleteLogicalById(id);
        if(-1 == r){
            return JsonResult.error(ErrorMessage.URM_PERMISSION_HAS_CHILDREN);
        }else{
            return JsonResult.success(1 == r);
        }
    }


    @Override
    protected void addValid(Permission record, User user) {
        super.addValid(record, user);
        record.setAuthor(user.getName());
    }

    @AuthRequired(permissionName = "*")
    @RequestMapping(value = "/getCurrentPermissions", method = RequestMethod.POST)
    public JsonResult add(HttpServletRequest request, @CurrentUser User user) {
        return JsonResult.success(user == null ? null : user.getPermissionList());
    }
}
