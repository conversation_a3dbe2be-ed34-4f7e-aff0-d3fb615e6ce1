package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Maps;
import com.yupaopao.risk.common.config.AccountTypeConfig;
import com.yupaopao.risk.common.config.AppIdConfig;
import com.yupaopao.risk.console.service.ThirdChannelService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.utils.FileUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@AuthRequired(permissionName = "*")
public class UtilController {

    private final static Logger LOGGER = LoggerFactory.getLogger(UtilController.class);

    @Autowired
    private ThirdChannelService thirdChannelService;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.isValidFilename(fileName)) {
                response.setCharacterEncoding("utf-8");
                response.getWriter().print("文件名称非法，不允许下载");
                return;
            }
            String realFileName = System.currentTimeMillis() + "_" + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = FileUtils.getAbsoluteFile(fileName);

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            LOGGER.error("下载文件失败,fileName=" + fileName, e);
            try {
                response.setCharacterEncoding("utf-8");
                response.getWriter().print("下载文件失败");
            } catch (IOException e1) {
                LOGGER.error("下载文件时，返回错误信息异常", e);
            }
        }
    }

    @ResponseBody
    @RequestMapping("common/getAccountTypes")
    public JsonResult getAccountTypes() {
        List<Map<String,Object>> list = Lists.newArrayList();
        List<AccountTypeConfig.AccountType> accountTypes = AccountTypeConfig.getAccountTypes();
        for(AccountTypeConfig.AccountType accountType : accountTypes){
            Map<String,Object> accountTypeMap = Maps.newHashMap();
            accountTypeMap.put("type",accountType.getType());
            accountTypeMap.put("desc",accountType.getDesc());
            accountTypeMap.put("appList",AppIdConfig.getAppsByAccountType(accountType.getType()));
            list.add(accountTypeMap);
        }
        return JsonResult.success(list);
    }

    @ResponseBody
    @RequestMapping("common/getAppsByAccountType")
    public JsonResult getAccountTypes(@RequestParam("accountType") Integer accountType) {
        return JsonResult.success(AppIdConfig.getAppsByAccountType(accountType));
    }

    @ResponseBody
    @RequestMapping("common/getCheckTypes")
    public JsonResult getCheckTypes() {
        return JsonResult.success(thirdChannelService.getCheckTypes());
    }

    @ResponseBody
    @RequestMapping("common/getAllApps")
    public JsonResult getAllApps() {
        List<AppIdConfig.App> apps = new ArrayList(16);
        AppIdConfig.getAppIdMap().forEach((k, v) -> apps.add(v));
        apps.sort((r1, r2) -> r1.getAppId() > r2.getAppId() ? 1 : -1);
        return JsonResult.success(apps);
    }
}
