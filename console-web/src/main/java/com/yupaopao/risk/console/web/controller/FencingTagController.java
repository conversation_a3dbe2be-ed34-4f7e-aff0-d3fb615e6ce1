package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.common.enums.TagLevel;
import com.yupaopao.risk.common.enums.ToSystemType;
import com.yupaopao.risk.common.model.Tag;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.model.WordTag;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.TagService;
import com.yupaopao.risk.console.service.WordTagService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/tag")
@AuthRequired(permissionName = "core:fencingtag:all")
public class FencingTagController extends AbstractModelController<Tag, Tag, TagService> {

    @Autowired
    private TagService service;

    @Autowired
    private WordTagService wordTagService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    /**
     * "风控人员"只能查询属于风控系统的标签，"审核人员"只能查询属于审核系统的标签，"既属于风控人员又属于审核人员"可以查询属于风控系统和审核系统的标签
     *
     * 用于下拉框加载  不用分页
     *
     * @param user
     * @param tag
     * @return
     */
    @RequestMapping("/showInSel")
    public JsonResult showInSel(@CurrentUser User user,@RequestBody Tag tag, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        return JsonResult.success(this.getService().showInSel(tag,page,size));
    }


//    /**
//     * "风控人员"只能查询属于风控系统的标签，"审核人员"只能查询属于审核系统的标签，"既属于风控人员又属于审核人员"可以查询属于风控系统和审核系统的标签
//     *
//     * 用于下拉框加载  不用分页
//     *
//     * @param user
//     * @param tag
//     * @return
//     */
//    @RequestMapping("/showInSel")
//    public JsonResult showInSel(@CurrentUser User user,@RequestBody Tag tag, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
//        if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT.getCode())||Objects.equals(user.getToSystem(),ToSystemType.AUDIT.getCode())){
//            tag.setToSystem(user.getToSystem());
//        }else if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT_AUDIT.getCode())){
//            tag.setToSystem(null);
//        }
//
//        PageInfo<Tag> pageInfo =  this.getService().search(tag,page,size);
//        List<Tag> tags = pageInfo.getList();
//        if(!CollectionUtils.isEmpty(tags)){
//            PageInfo<JSONObject> jsonPage = new PageInfo<>();
//            List<JSONObject> tagJsons = new ArrayList<>();
//            Tag tt = new Tag();
//            tt.setLevel(TagLevel.FIRST_LEVEL.getCode());
//            if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT.getCode())||Objects.equals(user.getToSystem(),ToSystemType.AUDIT.getCode())){
//                tt.setToSystem(user.getToSystem());
//            }else if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT_AUDIT.getCode())){
//                tt.setToSystem(null);
//            }
//            List<Tag> firstTags = this.getService().select(tt);
//            Map<Long, Tag> tagMap = new HashMap<>(firstTags.size() * 2);
//            firstTags.forEach(t -> tagMap.put(t.getId(), t));
//            tags.forEach(t -> {
//                JSONObject tagJson = JSON.parseObject(JSON.toJSONString(t));
//                if(null != t.getPId() && tagMap.containsKey(t.getPId())){
//                    tagJson.put("parentName",tagMap.get(t.getPId()).getName());
//                }
//                tagJsons.add(tagJson);
//            });
//            tagMap.clear();
//            tags.clear();
//            jsonPage.setList(tagJsons);
//            jsonPage.setTotal(pageInfo.getTotal());
//            jsonPage.setPageNum(pageInfo.getPageNum());
//            jsonPage.setPageSize(pageInfo.getPageSize());
//            return JsonResult.success(jsonPage);
//        }
//
//        return JsonResult.success(pageInfo);
//    }

    /**
     * "风控人员"和"既属于风控人员又属于审核人员"可以查询所有的场景，"审核人员"只能查询属于审核系统的场景
     *
     * 用于列表加载  需要分页
     *
     * @param user
     * @param tag
     * @param page
     * @param size
     * @return
     */
    @RequestMapping("/searchByToSystem")
    public JsonResult searchByToSystem(@CurrentUser User user, @RequestBody Tag tag, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<Tag> pageInfo =  this.getService().search(tag,page,size);
        List<Tag> tags = pageInfo.getList();
        if(!CollectionUtils.isEmpty(tags)){
            PageInfo<JSONObject> jsonPage = new PageInfo<>();
            List<JSONObject> tagJsons = new ArrayList<>();
            Tag tt = new Tag();
            tt.setLevel(TagLevel.FIRST_LEVEL.getCode());
            List<Tag> firstTags = this.getService().select(tt);
            Map<Long, Tag> tagMap = new HashMap<>(firstTags.size() * 2);
            firstTags.forEach(t -> tagMap.put(t.getId(), t));
            tags.forEach(t -> {
                JSONObject tagJson = JSON.parseObject(JSON.toJSONString(t));
                if(null != t.getParentId() && tagMap.containsKey(t.getParentId())){
                    tagJson.put("parentName",tagMap.get(t.getParentId()).getName());
                }
                tagJsons.add(tagJson);
            });
            tagMap.clear();
            tags.clear();
            jsonPage.setList(tagJsons);
            jsonPage.setTotal(pageInfo.getTotal());
            jsonPage.setPageNum(pageInfo.getPageNum());
            jsonPage.setPageSize(pageInfo.getPageSize());
            return JsonResult.success(jsonPage);
        }

        return JsonResult.success(pageInfo);
    }

    /**
     * 查询隶属于某个系统类型列表
     */
    @RequestMapping("/getToSystemTypeList")
    public JsonResult getToSystemTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(ToSystemType.class).forEach(toSystemType -> {
            if(Objects.equals(ToSystemType.CREDIT.getCode(),toSystemType.getCode())||Objects.equals(ToSystemType.AUDIT.getCode(),toSystemType.getCode())){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(toSystemType.getCode()));
                temp.put("msg", String.valueOf(toSystemType.getMsg()));
                list.add(temp);
            }
        });
        return JsonResult.success(list);
    }

    @RequestMapping(value= "/searchToSystemTypeList/{toSystem}", method = RequestMethod.POST)
    public JsonResult searchToSystemTypeList(@PathVariable Integer toSystem){
        List<Map<String,String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(ToSystemType.class).forEach(toSystemType -> {
            if(Objects.equals(toSystemType.getCode(),toSystem)){
                Map<String,String> temp = new MapMaker().makeMap();
                temp.put("code",String.valueOf(toSystemType.getCode()));
                temp.put("msg",String.valueOf(toSystemType.getMsg()));
                list.add(temp);
            }
        } );

        return JsonResult.success(list);
    }

    @Override
    protected void addValid(Tag record, User user) {

    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Override
    public JsonResult add(@Valid @RequestBody Tag record, @CurrentUser User user) {
        record.setModifier(user.getName());
        addValid(record, user);
        if(service.insertSelective(record)){
            String key = "";
            if(Objects.equals(record.getLevel(),1)||(record.getParentId()==null)){
                deleteFromRedis(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX);  // 删除redis中一级标签的缓存

            }else if(Objects.equals(record.getLevel(),2)||(record.getParentId()!=null)){
                Tag firstTag = this.service.get(record.getParentId());
                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX,Md5Util.md5(firstTag.getName()));  // 删除redis中二级标签的缓存
                deleteFromRedis(key);
            }
        }

        return JsonResult.success(true);
    }

    @Override
    protected void updateValid(Tag record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    @Override
    public JsonResult update(@PathVariable long id, @Valid @RequestBody Tag record, @CurrentUser User user) {
        record.setModifier(user.getName());
        updateValid(record, user);

        Tag oldRecord = service.get(id);
        if(service.updateSelectiveById(record)){
            String key = "";
            if(Objects.equals(record.getLevel(),1)){
                deleteFromRedis(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX); // 删除redis中一级标签的缓存

                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX, Md5Util.md5(oldRecord.getName()));  // 删除redis中二级标签的缓存
                deleteFromRedis(key);
            }else if(Objects.equals(record.getLevel(),2)){
                Tag firstTag = this.service.get(oldRecord.getParentId());
                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX,Md5Util.md5(firstTag.getName()));  // 删除redis中二级标签的缓存
                deleteFromRedis(key);

                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_PREFIX,oldRecord.getId());
                deleteFromRedis(key);

            }
        }

        return JsonResult.success(true);
    }

    @RequestMapping(value = "/checkTagRelation/{id}",method = RequestMethod.POST)
    public JsonResult checkTagRelation(@PathVariable long id){

        Map<String,Object> result = new MapMaker().makeMap();

        Tag oldRecord = service.get(id);

        if(oldRecord!=null){
            if(Objects.equals(oldRecord.getLevel(),1)) { // 一级标签
                List<Tag> secondTags = this.service.searchSecondTags(oldRecord.getName());
                if(CollectionUtils.isNotEmpty(secondTags)){
                    result.put("hasRelation",1);  // 关联的有二级标签
                }else{
                    result.put("hasRelation",3);  // 没有关联二级标签
                }
            }else if(Objects.equals(oldRecord.getLevel(),2)){ // 二级标签
                List<WordTag> wordTags = this.wordTagService.selectByTagId(oldRecord.getId());
                if(CollectionUtils.isNotEmpty(wordTags)){
                    result.put("hasRelation",2); // 关联的有违禁词条
                }else{
                    result.put("hasRelation",3);  // 没有关联违禁词条
                }
            }
        }

        return JsonResult.success(result);
    }

    @Override
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {

        Tag oldRecord = service.get(id);

        if(service.deleteById(id)){
            String key = "";
            if(Objects.equals(oldRecord.getLevel(),1)){ // 删除的是一级标签
                deleteFromRedis(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX); // 删除redis中一级标签的缓存

                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX,Md5Util.md5(oldRecord.getName()));  // 删除redis中二级标签的缓存
                deleteFromRedis(key);
            }else if(Objects.equals(oldRecord.getLevel(),2)){ // 删除的是二级标签
                Tag firstTag = service.get(oldRecord.getParentId());
                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_LIST_PREFIX,Md5Util.md5(firstTag.getName()));  // 删除redis中二级标签的缓存
                deleteFromRedis(key);

                key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_PREFIX,oldRecord.getId());
                deleteFromRedis(key);
            }
        }

        return JsonResult.success(true);
    }



    private void deleteFromRedis(String key){
        if(redisService.del(key)<1){
            redisService.del(key);
        }
    }

}
