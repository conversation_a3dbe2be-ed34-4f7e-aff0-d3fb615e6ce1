package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.Constants;
import com.yupaopao.risk.common.enums.Role;
import com.yupaopao.risk.common.enums.ToSystemType;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.RoleService;
import com.yupaopao.risk.console.service.UserService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.CookieUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/user")
@AuthRequired(permissionName = "system:user:all")
public class UserController extends AbstractModelController<User, User, UserService> {

    private final static Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    @Value("${admin.password}")
    private String adminPassword;
    @Autowired
    private RedisService redisService;

    @Autowired
    private RoleService roleService;

    private long sessionTimeout = 24 * 60 * 60;

    @RequestMapping("/current")
    @AuthRequired(role = {Role.NORMAL, Role.MANAGER, Role.CREDIT, Role.ADMIN}, permissionName = "*")
    public JsonResult user(@CurrentUser User user) {
        return JsonResult.success(user);
    }

    @RequestMapping("/existName")
    public JsonResult existName(@Valid @RequestBody User user){
        return JsonResult.success(getService().existName(user));
    }

    @RequestMapping("/updateRole")
    public JsonResult updateRole(@Valid @RequestBody User user, HttpServletResponse response) {
        getService().updateRole(user);
        return JsonResult.success();
    }

    @RequestMapping("/getLoginUser")
    @AuthRequired(role = {Role.NORMAL, Role.MANAGER, Role.CREDIT, Role.ADMIN}, permissionName = "*")
    public JsonResult getLoginUser(HttpServletRequest request) throws Exception {
        String token = CookieUtils.getCookieValue(request, Constants.COOKIE_USER_TOKEN);
        return JsonResult.success(StringUtils.isBlank(token) ? null : redisService.hget(ConsoleConstants.USER_CACHE_KEY, token));
    }

    @RequestMapping("/logout")
    @AuthRequired(role = {Role.NORMAL, Role.MANAGER, Role.CREDIT, Role.ADMIN}, permissionName = "*")
    public JsonResult logout(HttpServletResponse response) throws Exception {
        //必须清除cookie，否则会造成切换用户登录时仍展示老用户的信息
        CookieUtils.deleteCookie(response, Constants.COOKIE_USER_TOKEN);
        //页面重定向到cas的注销路径
        response.setStatus(302);
        response.setHeader("location", "/logout");
        return JsonResult.success();
    }

    @RequestMapping("/getRoles")
    @AuthRequired(role = {Role.NORMAL, Role.MANAGER, Role.CREDIT, Role.ADMIN}, permissionName = "*")
    public JsonResult getRoles() {
        return JsonResult.success(roleService.selectAll());
    }


    /**
     * 查询隶属于某个系统类型列表
     */
    @RequestMapping("/getToSystemTypeList")
    public JsonResult getToSystemTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(ToSystemType.class).forEach(status -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(status.getCode()));
            temp.put("msg", String.valueOf(status.getMsg()));
            list.add(temp);
        });

        return JsonResult.success(list);
    }

    @Override
    protected void addValid(User record, User user) {

    }

    @Override
    protected void updateValid(User record, User user) {

    }
}
