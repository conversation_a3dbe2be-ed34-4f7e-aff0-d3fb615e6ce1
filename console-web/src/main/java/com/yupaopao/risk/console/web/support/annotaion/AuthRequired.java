package com.yupaopao.risk.console.web.support.annotaion;


import com.yupaopao.risk.common.enums.Role;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthRequired {
    boolean login() default true;

    @Deprecated
    Role[] role() default {Role.NORMAL};

    @Deprecated
    String[] extRole() default {};

    String permissionName() default "";
}
