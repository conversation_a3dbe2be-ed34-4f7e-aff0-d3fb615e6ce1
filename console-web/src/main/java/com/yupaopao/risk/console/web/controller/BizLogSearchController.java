package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.AuditService;
import com.yupaopao.risk.console.service.ElasticSearchService;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.web.config.bizlog.BizLogConfig;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import com.yupaopao.risk.console.web.support.utils.MapValueConvertUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 风控日志搜索
 *
 * <AUTHOR>
 * @date 2019/2/27 2:36 PM
 */
@RestController
@RequestMapping("/bizLog/search")
@AuthRequired(permissionName = "view:bizlogsearch:hit")
public class BizLogSearchController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BizLogSearchController.class);

    @Autowired
    private ElasticSearchService elasticSearchService;
    @Autowired
    private BizLogConfig bizLogConfig;
    @Autowired
    private AuditService auditService;

    @RequestMapping("hit")
    @AuthRequired(permissionName = "view:bizlogsearch:hit")
    public JsonResult search(@RequestBody LogSearchVO.HitBizLogSearchVO vo) {
        PageInfo<Map<String, Object>> pageInfo = elasticSearchService.searchBizHitLog(vo);
        List<Map<String,Object>> list = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(list)){
            for(Map<String,Object> map : list){
                boolean isNeedAudit = auditService.isNeedAudit(MapUtils.getString(map,"eventCode" ));
                map.put("isNeedAudit",isNeedAudit);
            }
            MapValueConvertUtil.convertValueToString(list,"data.userData.uid");
            MapValueConvertUtil.encryptMobile(list);
            MapValueConvertUtil.encryptPersonalInfo(list);
        }
        return JsonResult.success(pageInfo);
    }

    @RequestMapping("/pageConfig/{eventCode}")
    @AuthRequired(permissionName = "view:bizlogsearch:hit")
    public JsonResult pageConfig(@PathVariable("eventCode") String eventCode) {
        Map<String,Object> resultMap = Maps.newHashMap();
        String searchConfig = MapUtils.getString(bizLogConfig.getSearch(),eventCode);
        if(null != searchConfig){
            resultMap.put("search",JSON.parseArray(searchConfig));
        }
        String showConfig = MapUtils.getString(bizLogConfig.getShow(),eventCode);
        if(null != showConfig){
            resultMap.put("show",JSON.parseArray(showConfig));
        }
       return JsonResult.success(resultMap);
    }
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "业务历史记录导出",isReportResponse = false)
    @RequestMapping("export")
    @AuthRequired(permissionName = "view:bizlogsearch:export")
    public JsonResult export(@RequestBody LogSearchVO.HitBizLogSearchVO vo, @RequestParam(name = "csps",required = false) String csps) {
        //本导出最多只能导出10000条
        vo.setPage(1);
        vo.setSize(10000);
        PageInfo<Map<String, Object>> pageInfo = elasticSearchService.searchBizHitLog(vo);
        JSONArray jsonArray = null;
        if(null != vo.getQuery().get("eventCode")){
            String showConfig = MapUtils.getString(bizLogConfig.getShow(),MapUtils.getString(vo.getQuery(),"eventCode"));
            if(null != showConfig){
                jsonArray = JSON.parseArray(showConfig);
            }
        }
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("level").addName("风险级别").build(),
                ExcelFormatter.builder("createdAt").addName("创建时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("eventCode").addName("事件Code").build(),
                ExcelFormatter.builder("userId").addName("用户ID").addWidth(50).build(),
                ExcelFormatter.builder("data").addName("请求参数").addWidth(100).build(),
                ExcelFormatter.builder("result").addName("响应结果").addWidth(100).build()
        );
        if(null != jsonArray){
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                ExcelFormatter formatter = ExcelFormatter.builder(json.getString("key")).addName(json.getString("name")).build();
                formatterMap.put(formatter.getKey(),formatter);
            }
        }
        if(StringUtils.isNotBlank(csps)){
            String[] cspa = csps.split(",");
            for(String csp : cspa){
                if(StringUtils.isNotBlank(csp)){
                    String[] kt = csp.split(" ");
                    ExcelFormatter formatter = ExcelFormatter.builder(kt[0]).addName(kt[0]).build();
                    formatterMap.put(formatter.getKey(),formatter);
                }
            }
        }
        List<Map<String,Object>> list = pageInfo.getList();
        MapValueConvertUtil.encryptMobile(list);
        MapValueConvertUtil.encryptPersonalInfo(list);
        try {
            String fileName = ExcelUtils.exportExcelWithDepth(list,"历史记录",formatterMap);
            return JsonResult.success(fileName);
        }catch (Throwable e){
            LOGGER.error("导出历史记录Excel异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }
    }
    @RequestMapping("result")
    @AuthRequired(permissionName = "view:bizlogsearch:hit")
    public JsonResult result(@RequestBody LogSearchVO.TraceSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchResult(vo));
    }
    @RequestMapping("trace")
    @AuthRequired(permissionName = "view:bizlogsearch:trace")
    public JsonResult search(@RequestBody LogSearchVO.TraceSearchVO vo, @CurrentUser User user) {
        return JsonResult.success(elasticSearchService.searchTraceLog(vo, user));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "业务风控送审历史查询",isReportResponse = false)
    @RequestMapping("audit")
    @AuthRequired(permissionName = "view:bizlogsearch:audit")
    public JsonResult audit(@RequestBody LogSearchVO.AuditLogSearchVO vo) {
        return JsonResult.success(elasticSearchService.searchAuditLog(vo));
    }
}
