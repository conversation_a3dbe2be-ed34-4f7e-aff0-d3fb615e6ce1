package com.yupaopao.risk.console.web.controller.operation;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.yupaopao.risk.common.enums.ViolOperationStatus;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.model.ViolationOperation;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.ViolationService;
import com.yupaopao.risk.console.vo.ViolationOperationVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.*;


@RestController
@RequestMapping("operation/violation")
@AuthRequired(permissionName = "operation:violationlist:all")
@Slf4j
public class ViolationListController extends AbstractModelController<ViolationOperation, ViolationOperationVO, ViolationService>{

    protected void commonValid(ViolationOperationVO record, User user) {
        Assert.isTrue(StringUtils.trim(record.getContent()).length() > 0, "该违规文本包含空字符");
        Assert.notNull(record.getFromUid(),"请绑定发送用户id");
        Assert.notNull(record.getToUid(),"请绑定接收用户id");
        Assert.notNull(record.getMsgTime(), "请绑定消息发送时间");
        Assert.notNull(record.getEventCode(), "请绑定至少一个场景事件");
        Assert.notNull(record.getSecondTag(), "请绑定至少一个二级标签");

        record.setModifier(user.getName());
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody ViolationOperationVO record, @CurrentUser User user){
        updateValid(record, user);

        record.setUpdateTime(new Date());
        return JsonResult.success(this.getService().update(record));
    }

    @Override
    protected void updateValid(ViolationOperationVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        commonValid(record,user);
    }

    /**
     * 违规记录详细的处理类型 -- 用于处理违规记录
     */
    @RequestMapping("/getViolationStatusList")
    public JsonResult getViolationStatusList() {
        List<Map<String, String>> list = Lists.newArrayList();

        ViolOperationStatus.getOperationWays().forEach(status -> {
            Map<String, String> temp = new MapMaker().makeMap();
            temp.put("code", String.valueOf(status.getCode()));
            temp.put("msg", String.valueOf(status.getMsg()));
            list.add(temp);
        });
        return JsonResult.success(list);
    }

    /**
     * 违规记录处理类型 -- 用于搜索下拉框的填充
     */
    @RequestMapping("/getViolationTypeList")
    public JsonResult getViolationTypeList() {

        return JsonResult.success(ViolOperationStatus.getOperationTypes());
    }

    /**
     * 违规记录所涉及的所有场景事件列表
     * @return
     */
    @RequestMapping("/getViolationEventList")
    public JsonResult getViolationEventList(){
        return JsonResult.success(this.getService().getViolationEventList());
    }

    /**
     * 违规记录所涉及的所有二级标签
     * @return
     */
    @RequestMapping("/getViolationTagList")
    public JsonResult getViolationTagList(){
        return JsonResult.success(this.getService().getViolationTagList());
    }


    /**
     * 违规类型 如 现金单违规，抢单违规
     * @return
     */
    @RequestMapping("/getViolationList")
    public JsonResult getViolationList(){
        return JsonResult.success(this.getService().getViolationList());
    }

    /**
     * 多条件搜索
     */
    @RequestMapping("/searchByViolationType")
    public JsonResult searchByViolationType(@CurrentUser User user, @RequestBody ViolationOperationVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {

//        JsonResult result = JsonResult.success(this.getService().search(record, page, size));

        PageInfo<Map<String,String>> pageInfo1 = new PageInfo<>();
        PageInfo<ViolationOperation> pageInfo = this.getService().search(record, page, size);

        List<Map<String,String>> result1 = new ArrayList<>();
        List<ViolationOperation> violationOperations = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(violationOperations)){
            for(ViolationOperation violationOperation:violationOperations){
                Map<String,String> violation = (Map) JSONObject.parse(JSONObject.toJSONString(violationOperation));
                violation.put("fromUid",violationOperation.getFromUid()+"");
                violation.put("toUid",violationOperation.getToUid()+"");
                result1.add(violation);
            }
        }

        BeanUtils.copyProperties(pageInfo,pageInfo1);
        pageInfo1.setList(result1);

        return JsonResult.success(pageInfo1);
    }
}
