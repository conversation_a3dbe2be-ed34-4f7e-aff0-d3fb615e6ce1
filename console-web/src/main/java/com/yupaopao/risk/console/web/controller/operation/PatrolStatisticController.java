package com.yupaopao.risk.console.web.controller.operation;


import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.SupportRuleType;
import com.yupaopao.risk.common.model.PatrolRule;
import com.yupaopao.risk.common.model.PatrolStatistic;
import com.yupaopao.risk.console.service.PatrolRuleService;
import com.yupaopao.risk.console.service.PatrolStatisticService;
import com.yupaopao.risk.console.utils.DateUtils;
import com.yupaopao.risk.console.vo.PatrolRuleVO;
import com.yupaopao.risk.console.vo.PatrolStatisticVO;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * 巡检相关
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("operation/patrolStatistic")
@AuthRequired(permissionName = "operation:patrol:statistic")
public class PatrolStatisticController extends AbstractModelController<PatrolStatistic, PatrolStatisticVO, PatrolStatisticService> {

    @Autowired
    private PatrolRuleService patrolRuleService;

    @RequestMapping("/hit")
    public JsonResult hit(@RequestBody PatrolStatisticVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "30") Integer size) {
        PageInfo<PatrolStatistic> result = getService().hit(record, page, size);
        return JsonResult.success(result);
    }

    @RequestMapping("/rebuild")
    public JsonResult rebuild(@RequestBody PatrolStatisticVO record) {
        Map<String, Object> condition;
        if (!CollectionUtils.isEmpty(condition = record.getCondition())) {
            Date startTime = null;
            Date endTime = null;
            if (condition.get("startTime") != null) {
                startTime = DateUtils.formatDate((String) condition.get("startTime"), DateUtils.YYYY_MM_DD_HH_MM_SS);
            }
            if (condition.get("endTime") != null) {
                endTime = DateUtils.formatDate((String) condition.get("endTime"), DateUtils.YYYY_MM_DD_HH_MM_SS);
            }
            getService().rebuild(startTime, endTime, record.getRuleId());
            return JsonResult.success(null);
        } else {
            return JsonResult.error(new RiskException("未获取到日期范围"));
        }
    }

    @RequestMapping("/searchAllRules")
    public JsonResult searchAllRules() {
        PatrolRuleVO record = new PatrolRuleVO();
        record.setType(SupportRuleType.PATROL_RULE.getCode());
        PageInfo<PatrolRule> search = patrolRuleService.search(record, 1, 10_000);
        return JsonResult.success(search);
    }
}
