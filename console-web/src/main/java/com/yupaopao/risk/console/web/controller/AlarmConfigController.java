package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.AlarmConfigType;
import com.yupaopao.risk.common.enums.AlarmRuleType;
import com.yupaopao.risk.common.enums.CommonStatus;
import com.yupaopao.risk.common.model.Alarm;
import com.yupaopao.risk.common.model.AlarmConfig;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.AlarmConfigVO;
import com.yupaopao.risk.console.service.AlarmConfigService;
import com.yupaopao.risk.console.service.AlarmService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * author: lijianjun
 * date: 2020/2/18 14:09
 */
@RestController
@RequestMapping("/alarm/config")
@AuthRequired(permissionName = "alarm:config:all")
public class AlarmConfigController extends AbstractModelController<AlarmConfig, AlarmConfigVO, AlarmConfigService> {

    @Autowired
    private AlarmService alarmService;

    @Override
    protected void addValid(AlarmConfigVO config, User user) {
        AlarmConfig alarmConfig = this.getService().getByName(config.getName());
        Assert.isNull(alarmConfig, "模板名称重复！");
        config.setModifier(user.getName());
        config.setAuthor(user.getName());
        config.setType(AlarmConfigType.TEMPLATE.getCode());
    }

    @Override
    protected void updateValid(AlarmConfigVO config, User user) {
        AlarmConfig alarmConfig = this.getService().getByName(config.getName());
        if(null != alarmConfig && !alarmConfig.getId().equals(config.getId())){
            throw new RiskException("模板名称重复！");
        }
        config.setModifier(user.getName());
    }

    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@RequestBody AlarmConfigVO config, @CurrentUser User user) {
        addValid(config,user);
        return JsonResult.success(this.getService().saveAlarmConfig(config));
    }
    @Override
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        Assert.notNull(id, "请选择要删除的模板！");
        Alarm alarm = alarmService.getByConfigId(id);
        Assert.isNull(alarm,"该模板关联了告警标签，不能删除！");
        AlarmConfigVO config = new AlarmConfigVO();
        config.setId(id);
        config.setState(CommonStatus.DELETE.getCode());
        return JsonResult.success(this.getService().updateSelectiveById(config));
    }

    @Override
    @RequestMapping(value = "/get/{id}", method = RequestMethod.POST)
    public JsonResult get(@PathVariable long id) {
        return JsonResult.success(this.getService().getDetailById(id));
    }

    @Override
    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id,@RequestBody AlarmConfigVO config, @CurrentUser User user) {
        updateValid(config,user);
        return JsonResult.success(this.getService().updateAlarmConfig(config));
    }
    @RequestMapping("/getAlarmConfigs")
    public JsonResult getEvents() {
        return JsonResult.success(this.getService().simpleAll());
    }
}
