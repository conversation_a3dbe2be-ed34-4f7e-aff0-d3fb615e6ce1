package com.yupaopao.risk.console.web.controller;

import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.enums.KafkaType;
import com.yupaopao.risk.console.service.UserService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@Slf4j
@RestController
@RequestMapping("/mq")
@AuthRequired(permissionName = "system:mq:all")
public class MQController extends AbstractModelController<User, User, UserService> {

    @KafkaAutowired
    private KafkaProducer kafkaProducer;

    @KafkaAutowired("middleware.kafka.risk")
    private KafkaProducer riskKafkaProducer;

    @KafkaAutowired("middleware.kafka-bigdata")
    private KafkaProducer bigDataKafkaProducer;

    @RequestMapping("/execute")
    public JsonResult execute(@RequestBody MQ mq) {
        if (mq == null || StringUtils.isBlank(mq.getTopic()) || StringUtils.isBlank(mq.getMessage()) || null == mq.getKafkaType()) {
            return JsonResult.error();
        }
        KafkaProducer producer = getKafkaProducerByType(mq.getKafkaType());
        if(null == producer){
            return JsonResult.error();
        }
        String[] msg = mq.getMessage().split("\n");
        for (int i = 0; i < msg.length; i++) {
            if (StringUtils.isNotBlank(msg[i])) {
                log.debug("补偿MQ消息:{} / {}", mq.getTopic(), msg[i]);
                ListenableFuture<SendResult<String, String>> future = producer.send(mq.getTopic(), msg[i]);
                future.addCallback(r -> log.debug("发送补偿消息成功"), e -> log.error("发送补偿消息失败", e));
            }
        }
        return JsonResult.success(msg.length);
    }

    private KafkaProducer getKafkaProducerByType(Integer type){
        if(null == type){
            return null;
        }
        if(type == KafkaType.RISK.getType()){
            return riskKafkaProducer;
        }
        if(type == KafkaType.CORE.getType()){
            return kafkaProducer;
        }
        if(type == KafkaType.BIG_DATA.getType()){
            return bigDataKafkaProducer;
        }
        return null;
    }

}

class MQ implements Serializable {

    private String topic;
    private String message;
    private Integer kafkaType;

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getKafkaType() {
        return kafkaType;
    }

    public void setKafkaType(Integer kafkaType) {
        this.kafkaType = kafkaType;
    }
}