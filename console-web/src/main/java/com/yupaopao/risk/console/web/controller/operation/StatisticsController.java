package com.yupaopao.risk.console.web.controller.operation;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.model.ViolationStatistics;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.ViolationStatisticsService;
import com.yupaopao.risk.console.web.controller.AbstractModelController;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("operation/statistics")
@AuthRequired(permissionName = "operation:statisticslist:all")
@Slf4j
public class StatisticsController extends AbstractModelController<ViolationStatistics,ViolationStatistics, ViolationStatisticsService> {

    protected void commonValid(ViolationStatistics record, User user) {
        Assert.notNull(record.getMsgDate(),"请绑定业务日期");
        Assert.notNull(record.getViolationType(),"请绑定违规类型");
        Assert.notNull(record.getUid(), "请绑定用户id");
        Assert.notNull(record.getViolationCount(), "请绑定违规统计次数");
        Assert.notNull(record.getDealResult(), "请绑定处理方式");

        record.setModifier(user.getName());
    }

    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody ViolationStatistics record, @CurrentUser User user){
        updateValid(record, user);

        return JsonResult.success(this.getService().update(record));
    }

    @Override
    protected void updateValid(ViolationStatistics record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        commonValid(record,user);
    }

    /**
     * 违规类型 如 现金单违规，抢单违规
     * @return
     */
    @RequestMapping("/getViolationList")
    public JsonResult getViolationList(){
        return JsonResult.success(this.getService().getViolationList());
    }

    /**
     * 多条件搜索
     */
    @RequestMapping("/searchByViolationType")
    public JsonResult searchByViolationType(@CurrentUser User user, @RequestBody ViolationStatistics record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {

        PageInfo<Map<String,String>> pageInfo1 = new PageInfo<>();
        PageInfo<ViolationStatistics> pageInfo = this.getService().search(record, page, size);

        List<Map<String,String>> result1 = new ArrayList<>();
        List<ViolationStatistics> violationStatistics = pageInfo.getList();
        if(CollectionUtils.isNotEmpty(violationStatistics)){
            for(ViolationStatistics item:violationStatistics){
                Map<String,String> violation = (Map) JSONObject.parse(JSONObject.toJSONString(item));
                violation.put("uid",item.getUid()+"");
                result1.add(violation);
            }
        }

        BeanUtils.copyProperties(pageInfo,pageInfo1);
        pageInfo1.setList(result1);

        return JsonResult.success(pageInfo1);
    }
}
