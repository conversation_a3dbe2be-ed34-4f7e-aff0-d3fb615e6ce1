package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.StrategyEvaluate;
import com.yupaopao.risk.console.service.StrategyEvaluateService;
import com.yupaopao.risk.console.vo.StrategyEvaluateVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contentCheck/strategyTaskDetailInfo")
@AuthRequired(permissionName = "strategy:task:detail")
@Slf4j
public class StrategyTaskDetailInfoController extends AbstractModelController<StrategyEvaluate, StrategyEvaluateVO, StrategyEvaluateService>{

    /**
     * 查看抽检任务的详情
     * @param strategyTaskDetailReq
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public JsonResult detail(@Valid @RequestBody StrategyTaskDetailReq strategyTaskDetailReq) {

        if(strategyTaskDetailReq==null||strategyTaskDetailReq.getTaskId()==0){
            return JsonResult.error("8001","没有传入有效的任务id",null);
        }

        return JsonResult.success(this.getService().detail(strategyTaskDetailReq.getTaskId()));
    }

}


@Data
class StrategyTaskDetailReq implements Serializable {

    private Long taskId;
}


