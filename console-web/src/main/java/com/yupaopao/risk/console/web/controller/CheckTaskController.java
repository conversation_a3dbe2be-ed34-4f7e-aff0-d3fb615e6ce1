package com.yupaopao.risk.console.web.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.enums.CheckDetectChannel;
import com.yupaopao.risk.common.enums.CheckTaskState;
import com.yupaopao.risk.common.enums.SampleType;
import com.yupaopao.risk.common.model.CheckTask;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.bean.ValidResult;
import com.yupaopao.risk.console.service.CheckTaskService;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 抽检任务
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contentCheck/checkTask")
@AuthRequired(permissionName = "check:task:all")
@Slf4j
public class CheckTaskController extends AbstractModelController<CheckTask, CheckTask, CheckTaskService> {

    @Autowired
    private EventService eventService;

    protected void addValid(CheckTask record) {
        commonValid(record);
    }

    protected void commonValid(CheckTask record) {
        Assert.isTrue(StringUtils.trim(record.getName()).length() > 0, "任务名称不能为空");
        Assert.isTrue(StringUtils.trim(record.getDescription()).length()>0,"任务说明不能为空");
        Assert.notNull(record.getScene(), "请关联一个场景");
        Assert.notNull(record.getChannel(), "请关联一个检测渠道");
        Assert.notNull(record.getSampleType(),"请选择一个样本类型");
        Assert.isTrue(StringUtils.trim(record.getSearchSql()).length()>0,"任务查询语句不能为空");
    }

    @RequestMapping("/complexSearch")
    public JsonResult complexSearch(@CurrentUser User user, @RequestBody CheckTask record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "50") Integer size) {
        return JsonResult.success(this.getService().search(user,record,page,size));
    }

    /**
     * 校验抽检任务的查询语句
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/validSearchSql", method = RequestMethod.POST)
    public JsonResult validSearchSql(@Valid @RequestBody CheckTask record, @CurrentUser User user) {
        addValid(record);

        Map<String,Object> result = Maps.newHashMap();

        ValidResult validResult = this.getService().validSearchSql(record.getSearchSql(),record.getSampleType());
        if(!validResult.isSuccess()){
            result.put("code",validResult.getCode());
            result.put("msg",validResult.getMsg());
            result.put("success",false);
        }else{
            result.put("success",true);
        }

        return JsonResult.success(result);
    }

    /**
     * 创建抽检任务
     * @param record
     * @param user
     * @return
     */
    @Override
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody CheckTask record, @CurrentUser User user) {
        addValid(record);

        Map<String,Object> result = Maps.newHashMap();
        result.put("success",this.getService().add(record,user));

        return JsonResult.success(result);
    }

    /**
     * 开始标注抽检任务
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/toMark", method = RequestMethod.POST)
    public JsonResult toMark(@Valid @RequestBody CheckTask record, @CurrentUser User user) {
        return JsonResult.success(this.getService().toMark(record,user));
    }

    /**
     * 查看抽检任务的详情
     * @param checkTaskReq
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public JsonResult detail(@Valid @RequestBody CheckTaskReq checkTaskReq) {
        if(checkTaskReq==null||checkTaskReq.getTaskId()==0){
            return JsonResult.error("8001","没有传入有效的任务id",null);
        }

        return JsonResult.success(this.getService().detail(checkTaskReq.getTaskId()));
    }

    /**
     * 评估抽检任务结果
     * @param record
     * @param user
     * @return
     */
    @RequestMapping(value = "/evaluate", method = RequestMethod.POST)
    public JsonResult evaluate(@Valid @RequestBody CheckTask record, @CurrentUser User user){
        return JsonResult.success(this.getService().evaluate(record,user));
    }

    /**
     * 取消抽检任务的详情
     * @param checkTaskReq
     * @param user
     * @return
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public JsonResult cancel(@Valid  @RequestBody CheckTaskReq checkTaskReq, @CurrentUser User user) {
        if(checkTaskReq==null||checkTaskReq.getTaskId()==0){
            return JsonResult.error("8001","没有传入有效的任务id",null);
        }

        return JsonResult.success(this.getService().cancel(checkTaskReq.getTaskId(),user));
    }


    /**
     * 查询抽检任务状态列表
     */
    @RequestMapping("/getCheckTaskStateList")
    public JsonResult getCheckTaskStateList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(CheckTaskState.class).forEach(item -> {
            if(item.getCode()!=0){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(item.getCode()));
                temp.put("msg", String.valueOf(item.getMsg()));
                list.add(temp);
            }
        });
        return JsonResult.success(list);
    }


    /**
     * 查询样本类型列表
     */
    @RequestMapping("/getSampleTypeList")
    public JsonResult getSampleTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(SampleType.class).forEach(item -> {
            if(item.getCode()!=0){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(item.getName()));
                temp.put("msg", String.valueOf(item.getMsg()));
                list.add(temp);
            }
        });

        return JsonResult.success(list);
    }

    /**
     * 查询关联场景列表
     */
    @RequestMapping("/getSceneList")
    public JsonResult getSceneList() {
        List<Map<String, String>> list = Lists.newArrayList();

        List<Event> events = this.eventService.selectAll();
        if(CollectionUtils.isNotEmpty(events)){
            events.forEach(event -> {
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(event.getCode()));
                temp.put("msg", String.valueOf(event.getName()));
                list.add(temp);
            });
        }

        return JsonResult.success(list);
    }


    /**
     * 查询抽检任务检测渠道列表
     */
    @RequestMapping("/getCheckDetectChannelList")
    public JsonResult getCheckDetectChannelList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(CheckDetectChannel.class).forEach(item -> {
            if(item.getCode()!=0){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(item.getCode()));
                temp.put("msg", String.valueOf(item.getMsg()));
                list.add(temp);
            }
        });
        return JsonResult.success(list);
    }



}

@Data
class CheckTaskReq implements Serializable{

    private Long taskId;
}