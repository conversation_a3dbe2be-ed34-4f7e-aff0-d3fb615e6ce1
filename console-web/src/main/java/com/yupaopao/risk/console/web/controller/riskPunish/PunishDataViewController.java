package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.risk.console.config.RiskPunishConfig;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据看板
 *
 * wangxinqi
 * 2021/5/14 19:27
 */
@Slf4j
@RequestMapping("riskPunish/dataView")
@RestController
@AuthRequired(permissionName = "riskPunish:dataView:all")
public class PunishDataViewController {
    @Autowired
    private RiskPunishConfig riskPunishConfig;

    @RequestMapping("getDataViews")
    public JsonResult getDataViews() {
        return JsonResult.success(riskPunishConfig.getDataViews());
    }
}
