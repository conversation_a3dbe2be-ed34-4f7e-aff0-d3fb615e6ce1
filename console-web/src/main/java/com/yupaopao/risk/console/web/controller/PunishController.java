package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.service.AllPunishService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/punish")
@Slf4j
@AuthRequired(permissionName = "operation:punish:all")
public class PunishController{

    @Autowired
    private AllPunishService allPunishService;

    @RequestMapping("/getPackagesAndObjectTypes")
    public JsonResult getPackagesAndObjectTypes(@RequestBody AllPunishService.GetPunishPackagesRequest request){
        return JsonResult.success(allPunishService.getPackagesAndObjectTypes(request));
    }

    @RequestMapping("/doPunish")
    public JsonResult doPunish(@RequestBody Map<String,Object> parameters,@CurrentUser User user){
        return JsonResult.success(allPunishService.doPunish(user,parameters));
    }

    @RequestMapping("/executePunish")
    public JsonResult executePunish(@RequestBody Map<String,Object> parameters,@CurrentUser User user){
        return JsonResult.success(allPunishService.executePunish(user,parameters));
    }

    @RequestMapping("/getRecentLoginDevice")
    public JsonResult getRecentLoginDevice(@RequestBody Map<String,Object> parameters){
        return JsonResult.success(allPunishService.getRecentLoginDevice(parameters));
    }

}
