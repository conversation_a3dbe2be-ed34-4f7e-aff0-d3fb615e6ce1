package com.yupaopao.risk.console.web.controller.analysis;

import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.RiskHitLogService;
import com.yupaopao.risk.console.vo.RiskHitLogVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/user/risk/log")
@AuthRequired(permissionName = "system:user:all")
public class RiskHitLogController {

    @Autowired
    private RiskHitLogService riskHitLogService;


    @RequestMapping("/active")
    @AuthRequired(login = false)
    public JsonResult active(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);

//        RiskHitLogVO record1 = new RiskHitLogVO();
        List<Map<String, Object>> list = riskHitLogService.queryActiveByDate(record);
        return JsonResult.success(list);
    }

    @RequestMapping("/violationSceneTop")
    @AuthRequired(login = false)
    public JsonResult violationSceneTop(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        List<Map<String, Object>> list = riskHitLogService.queryViolationSceneTop(record);
        return JsonResult.success(list);
    }

    @RequestMapping("/violationTextTop")
    @AuthRequired(login = false)
    public JsonResult violationTextTop(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        List<Map<String, Object>> list = riskHitLogService.queryViolationTextTop(record);
        return JsonResult.success(list);
    }

    @RequestMapping("/activeSceneTop")
    @AuthRequired(login = false)
    public JsonResult activeSceneTop(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        List<Map<String, Object>> list = riskHitLogService.queryActiveSceneTop(record);
        return JsonResult.success(list);
    }


    @RequestMapping("/violationImageTop")
    @AuthRequired(login = false)
    public JsonResult violationImageTop(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        List<Map<String, Object>> list = riskHitLogService.queryViolationImageTop(record);
        return JsonResult.success(list);
    }


}
