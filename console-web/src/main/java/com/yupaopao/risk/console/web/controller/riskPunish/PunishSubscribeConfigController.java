package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.punish.manage.PunishCmdService;
import com.yupaopao.risk.punish.manage.PunishConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 惩罚违规类型
 *
 * wangxinqi
 * 2021/5/24 14:21
 */
@Slf4j
@RequestMapping("riskPunish/subscribeConfig")
@RestController
@AuthRequired(permissionName = "riskPunish:subscribeConfig:all")
public class PunishSubscribeConfigController {
    @DubboReference(timeout = 20000)
    private PunishCmdService punishCmdService;
    @DubboReference(timeout = 20000)
    private PunishConfigService punishConfigService;

    // 获取订阅的惩罚包ID列表f
    @RequestMapping("getSubscribePackageIds")
    public JsonResult getSubscribePackageIds(@RequestParam("channel") String channel) {
        return JsonResult.success(punishConfigService.getSubscribePkgIds(channel));
    }
}
