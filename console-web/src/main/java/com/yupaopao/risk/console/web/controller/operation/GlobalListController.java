package com.yupaopao.risk.console.web.controller.operation;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.config.OperationsEventConfig;
import com.yupaopao.risk.console.service.EventService;
import com.yupaopao.risk.console.suppport.OperationLoginListRedisSupport;
import com.yupaopao.risk.console.vo.LoginGrayListVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("operation/global")
@AuthRequired(permissionName = "operation:globallist:all")
@Slf4j
public class GlobalListController extends LoginListController {

    private static final String INVALID = "无效";
    /**
     * 全局名单事件
     */
    private static final String GLOBAL_EVENT = "GLOBAL";
    @Autowired
    OperationLoginListRedisSupport operationLoginListRedisSupport;

    @Autowired
    EventService eventService;

    @Autowired
    OperationsEventConfig operationsEventConfig;

    @Override
    @RequestMapping(value = "/addUser", method = RequestMethod.POST)
    public JsonResult addValidUser(@RequestBody LoginGrayListVO record, @CurrentUser User user) {
        record.setGroupId(configService.getGlobalListGroupId());
        record.setEventList(GLOBAL_EVENT);
        super.addValid(record, user);
        // 校验是否存在手机号对应用户
        if ("MOBILENO".equals(record.getDimension())) {
            operationLoginListRedisSupport.doCheckSave(record);
        }
        return super.add(record,user);
    }

    @Override
    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    protected JsonResult updateValid(@RequestBody LoginGrayListVO record, @CurrentUser User user) {
        record.setGroupId(configService.getGlobalListGroupId());
        record.setEventList(GLOBAL_EVENT);
        super.update(record.getId(), record, user);
        return JsonResult.success();
    }

    @Override
    @RequestMapping("/name/export")
    @AuthRequired(permissionName = "operation:globallist:export")
    public JsonResult loginNameExport(@RequestBody LoginGrayListVO record) {
        record.setGroupId(configService.getGlobalListGroupId());
        record.setEventList(GLOBAL_EVENT);
        //本导出最多只能导出100000条
        List<Object> objects;
        if (StringUtils.isNotEmpty(record.getHasExist()) && INVALID.equals(record.getHasExist())) {
            objects = operationLoginListRedisSupport.getList(1, 10000);
        } else {
            PageInfo search = grayListService.search(record, 1, 10000, "updated_at desc");
            objects = operationLoginListRedisSupport.checkBatch(search.getList());
        }
        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("type").addName("类型").build(),
                ExcelFormatter.builder("dimension").addName("维度").build(),
                ExcelFormatter.builder("value").addName("数据").addWidth(80).build(),
                ExcelFormatter.builder("startTime").addName("生效时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("expireTime").addName("过期时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build(),
                ExcelFormatter.builder("hasExist").addName("状态").addWidth(30).build(),
                ExcelFormatter.builder("comment").addName("备注").build(),
                ExcelFormatter.builder("author").addName("创建人").build(),
                ExcelFormatter.builder("updatedAt").addName("最后修改时间").addWidth(30).addDateFormat("yyyy-MM-dd HH:mm:ss.SSS").build()
        );
        try {
            String fileName = ExcelUtils.exportExcelByBean(LoginGrayListVO.class, objects, "名单管理", formatterMap);
            return JsonResult.success(fileName);
        } catch (Throwable e) {
            log.error("导出名单管理数据异常", e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR, e.getMessage());
        }
    }

    @RequestMapping("/globalSearch")
    public JsonResult globalSearch(@RequestBody LoginGrayListVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<LoginGrayListVO> search = new PageInfo();
        record.setGroupId(configService.getGlobalListGroupId());
        record.setEventList(GLOBAL_EVENT);
        if (StringUtils.isNotEmpty(record.getHasExist()) && INVALID.equals(record.getHasExist())){
            search.setList(operationLoginListRedisSupport.getList(page, size));
            search.setTotal(operationLoginListRedisSupport.getSize());
        }else {
            PageInfo grays = grayListService.search(record, page, size, "updated_at desc");
            search = grays;
            search.setList(operationLoginListRedisSupport.checkBatch(grays.getList()));
        }
        return JsonResult.success(search);
    }
}
