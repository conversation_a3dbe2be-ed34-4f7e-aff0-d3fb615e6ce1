package com.yupaopao.risk.console.web.support;

import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.enums.TableEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class AopController {

    @Autowired
    private RedisService redisService;

    @Pointcut("execution(* com.yupaopao.risk.console.web.controller.AbstractModelController.delete(..)))")
    public void deletePoint() {

    }

    @Before("deletePoint()")
    private void doBefore(JoinPoint joinPoint) {
        try {
            String controlName = joinPoint.getTarget().getClass().getSimpleName();
            String table = TableEnum.getTableByController(controlName);
            if (StringUtils.isEmpty(table)) {
                return;
            }
            Object[] params = joinPoint.getArgs();
            if (params == null || params.length != 2) {
                log.info("切面deletePoint处理 参数不符合预期 params:{}", params);
                return;
            }
            long id = (Long) params[0];
            User user = (User) params[1];
            String key = table + id;
            redisService.set(key, user.getName(), 3600L);
        } catch (Exception e) {
            log.error("切面deletePoint处理异常 joinPoint:{}", joinPoint);
        }
    }
}
