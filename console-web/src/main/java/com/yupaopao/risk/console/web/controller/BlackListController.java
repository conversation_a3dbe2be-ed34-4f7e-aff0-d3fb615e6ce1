package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.BlackList;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.BlackListVO;
import com.yupaopao.risk.console.config.BlackLimitAbilityConfig;
import com.yupaopao.risk.console.config.BlackSourceConfig;
import com.yupaopao.risk.console.service.BlackListService;
import com.yupaopao.risk.console.utils.CheckIdCard;
import com.yupaopao.risk.console.utils.DateUtils;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * G侧黑名单
 */
@RestController
@RequestMapping("operation/black")
@AuthRequired(permissionName = "operation:black:all")
@Slf4j
public class BlackListController extends AbstractModelController<BlackList, BlackListVO, BlackListService> {

    @Autowired
    private BlackSourceConfig blackSourceConfig;
    @Autowired
    private BlackLimitAbilityConfig blackLimitAbilityConfig;
    @Value("${g.black.tips:}")
    private String tips;

    @Override
    public JsonResult search(@RequestBody BlackListVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        //按时间倒序
        PageInfo pageInfo = getService().search(record, page, size, "create_time desc");
        List<BlackListVO> result = new ArrayList<>();
        List<BlackList> list = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (BlackList black : list) {
                BlackListVO vo = new BlackListVO(black);
                vo.setIdNo(CheckIdCard.tuoMin(vo.getIdNo()));
                vo.setLimitAbilities(new ArrayList<>());
                String[] limitAbilities = black.getLimitAbility().split(",");
                for (String ability : limitAbilities) {
                    if (blackLimitAbilityConfig.exist(ability)) {
                        vo.getLimitAbilities().add(ability);
                    }
                }
                result.add(vo);
            }
        }
        pageInfo.setList(result);
        return JsonResult.success(pageInfo);
    }

    @Override
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        BlackList record = getService().get(id);
        Assert.isTrue(record != null, "该记录已被删除，请刷新页面后重试！");

        //取消风控端惩罚
        BatchPunishRequest request = new BatchPunishRequest();
        request.setChannel("REALTIME_RISK_PUNISH");
        request.setEncryptId(getService().getEncId(record.getIdNo()));
        request.setOperator(user.getName());
        request.setInternalReason("G侧黑名单删除");
        getService().punish(2, Arrays.asList(record.getLimitAbility().split(",")), request, null);

        return JsonResult.success(getService().deleteById(id));
    }

    @Override
    protected void addValid(BlackListVO record, User user) {
        Assert.isTrue(CheckIdCard.check(record.getIdNo()), "请填写正确的身份证号");
        commonValid(record, user);
        record.setAuthor(user.getName());
    }

    @Override
    protected void updateValid(BlackListVO record, User user) {
        //因身份证后端脱敏，更新时需要置为空
        record.setIdNo(null);
        commonValid(record, user);
    }

    private void commonValid(BlackListVO record, User user) {
        Assert.hasLength(record.getSource(), "请选择名单来源");
        Assert.isTrue(CollectionUtils.isNotEmpty(record.getLimitAbilities()), "请选择限制能力");
        Assert.isTrue(record.getExpireTime().after(new Date()), "过期时间必须大于当前时间");
        Assert.isTrue(record.getStartTime().before(record.getExpireTime()), "过期时间必须大于生效时间");
        String suffix = DateUtils.formatDate(record.getStartTime(), "HH:mm:ss");
        String prefix = DateUtils.formatDate(record.getExpireTime(), DateUtils.YYYY_MM_DD);
        String date = prefix + " " + suffix;
        record.setExpireTime(DateUtils.formatDate(date, DateUtils.YYYY_MM_DD_HH_MM_SS));
        record.setModifier(user.getName());
        //优先执行惩罚
        getService().blackPunish(record);
        //再设值限制能力
        if (CollectionUtils.isNotEmpty(record.getLimitAbilities())) {
            String limitAbility = "";
            for (String ability : record.getLimitAbilities()) {
                limitAbility = limitAbility + "," + ability;
            }
            record.setLimitAbility(limitAbility.substring(1));
        }
    }

    /**
     * 名单来源
     */
    @AuthRequired(permissionName = "*")
    @RequestMapping("getSources")
    public JsonResult getSources() {
        return JsonResult.success(blackSourceConfig.getSources());
    }

    /**
     * 限制能力
     */
    @AuthRequired(permissionName = "*")
    @RequestMapping("getLimitAbilities")
    public JsonResult getLimitAbilities() {
        return JsonResult.success(blackLimitAbilityConfig.getLimitAbilities());
    }

    /**
     * 限制能力
     */
    @AuthRequired(permissionName = "*")
    @RequestMapping("refreshAbilities")
    public JsonResult refreshAbilities(@RequestParam(value = "desc") String desc) {
        return JsonResult.success(blackLimitAbilityConfig.getLimitAbilities().stream().filter(r -> r.getDesc().contains(desc)).collect(Collectors.toList()));
    }

    /**
     * 检测
     */
    @AuthRequired(permissionName = "operation:black:check")
    @RequestMapping("check")
    public JsonResult check(@RequestParam(value = "idNo") String idNo) {
        Assert.hasLength(idNo, "请输入身份证号");
        Map<String, Object> map = new HashMap<>();
        String[] idNos = idNo.split(",");
        for (String id : idNos) {
            map.put(id, getService().getUserData(id));
        }
        return JsonResult.success(map);
    }

    /**
     * 获取说明配置
     */
    @AuthRequired(permissionName = "*")
    @RequestMapping("tips")
    public JsonResult tips() {
        return JsonResult.success(tips);
    }
}
