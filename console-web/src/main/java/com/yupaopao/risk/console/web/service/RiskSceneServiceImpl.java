package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.common.model.Scene;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.api.RiskSceneService;
import com.yupaopao.risk.console.bean.RiskScene;
import com.yupaopao.risk.console.service.SceneService;
import com.yupaopao.risk.console.utils.ObjectConversion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Service
@Component
public class RiskSceneServiceImpl implements RiskSceneService {

    @Autowired
    private SceneService sceneService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    @Override
    public List<RiskScene> searchScenes(Integer toSystem) {

        String key = String.format(ConsoleConstants.REDIS_CACHE_SCENE_LIST_PREFIX, toSystem);
        List<RiskScene> riskScenesRedis = (List<RiskScene>) redisService.get(key);

        if (CollectionUtils.isNotEmpty(riskScenesRedis)) {
            return riskScenesRedis;
        } else {
            List<Scene> scenes = sceneService.findByToSystem(toSystem);
            if (CollectionUtils.isNotEmpty(scenes)) {
                List<RiskScene> riskScenesDB = ObjectConversion.copy(scenes, RiskScene.class);
                redisService.set(key, riskScenesDB, ConsoleConstants.REDIS_EXPIRE_TIME);

                return riskScenesDB;
            }
        }

        return null;
    }

}
