package com.yupaopao.risk.console.web.support.utils;

import java.util.LinkedHashMap;
import java.util.Map;

public class ExcelFormatterUtils {

    public static Map<String,ExcelFormatter> buildExcelFormatter(ExcelFormatter... arrFormatter){
        //按照插入顺序遍历
        Map<String, ExcelFormatter> formatterMap = new LinkedHashMap();
        for(ExcelFormatter formatter : arrFormatter){
            formatterMap.put(formatter.getKey(),formatter);
        }
        return  formatterMap;
    }
}
