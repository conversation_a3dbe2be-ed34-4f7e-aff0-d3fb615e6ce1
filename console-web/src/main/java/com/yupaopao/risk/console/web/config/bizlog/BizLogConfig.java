package com.yupaopao.risk.console.web.config.bizlog;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * author: l<PERSON><PERSON><PERSON>
 * date: 2019/11/21 11:00
 */
@Component
@ConfigurationProperties(prefix = "event.bizlog")
@Data
public class BizLogConfig {

    private Map<String,String> search;
    private Map<String,String> show;

}
