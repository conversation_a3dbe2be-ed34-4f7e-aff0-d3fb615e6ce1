package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yupaopao.risk.common.model.Attribute;
import com.yupaopao.risk.console.service.AttributeService;
import com.yupaopao.risk.console.web.parse.ParseGroovyRule;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/groovy")
@AuthRequired(permissionName = "core:parsegroovy:all")
public class ParseGroovyController {

    private final static Logger LOGGER = LoggerFactory.getLogger(ParseGroovyController.class);

    private final static com.yupaopao.risk.engine.parse.ParseGroovyRule parseGroovyRule = new com.yupaopao.risk.engine.parse.ParseGroovyRule();


    @Autowired
    private ParseGroovyRule parseGroovyRule1;

    @Autowired
    private AttributeService attributeService;

    /**
     * 检查是否编译通过
     *
     * @param queryMap
     * @return
     */
    @RequestMapping("/checkMapKey")
    public JsonResult checkMapKey(@RequestBody Map queryMap) {
        String a = (String) queryMap.get("returnJson");
        Map reMap = new HashMap();
        try {
            JSON.parseObject(a, new TypeReference<Map<String, String>>() {
            });
            reMap.put("success", true);
            reMap.put("msg", a);
            return JsonResult.success(reMap);
        } catch (Exception e) {
            reMap.put("success", false);
            reMap.put("msg", a);
            return JsonResult.success(reMap);
        }
    }

    @RequestMapping("/checkRuleCompile")
    public JsonResult check(@RequestBody Map queryMap) {
        String a = parseGroovyRule.checkRuleCompile((String) queryMap.get("ruleDetail"));
        Map map = new HashMap();
        if (StringUtils.isNotBlank(a)) {
            map.put("success", false);
            map.put("msg", a);
            return JsonResult.success(map);
        } else {
            map.put("success", true);
            return JsonResult.success(map);
        }
    }

    /**
     * 解析 groovy脚本的 因子和属性
     *
     * @param queryMap
     * @return
     */
    @RequestMapping("/parseCondition")
    public JsonResult parseCondition(@RequestBody Map queryMap) {
        String result = parseGroovyRule.checkRuleCompile((String) queryMap.get("ruleDetail"));//先校验合法性
        LOGGER.debug(result);
        Set set;
        Set attributeSet = new HashSet();
        Map map = new HashMap();
        if (StringUtils.isNotBlank(result)) {
            map.put("success", false);
            map.put("msg", result);
            return JsonResult.success(map);
        } else {
            set = parseGroovyRule.parseCondition((String) queryMap.get("ruleDetail"), attributeSet);
        }
        map.put("success", true);
        map.put("set", set);
        map.put("attributeSet", attributeSet);
        return JsonResult.success(map);
    }

    /**
     * 解析异步规则 groovy脚本的 因子和属性
     *
     * @param queryMap
     * @return
     */
    @RequestMapping("/parseAsynchronousCondition")
    public JsonResult parseAsynchronousCondition(@RequestBody Map queryMap) {
        String result = parseGroovyRule1.checkRuleCompile((String) queryMap.get("ruleDetail"));//先校验合法性
        LOGGER.debug(result);
        Set set;
        Set attributeSet = new HashSet();
        Map map = new HashMap();
        if (StringUtils.isNotBlank(result)) {
            map.put("success", false);
            map.put("msg", result);
            return JsonResult.success(map);
        } else {
            Map<String,Object> parseResult = parseGroovyRule1.parseAsychronousCondition((String) queryMap.get("ruleDetail"), attributeSet,false);
            if(Boolean.parseBoolean(parseResult.get("containChinese").toString())){
                map.put("success", false);
                map.put("msg", "规则内容中非常量字段含有中文字符");
                return JsonResult.success(map);
            }

            set = (Set<String>)parseResult.get("attributeSet");
            if(CollectionUtils.isNotEmpty(set)){
                List<Attribute> attributes = attributeService.queryRemoteAndLocal(set);
                if(CollectionUtils.isNotEmpty(attributes)){
                    set = attributes.stream().map(attribute -> attribute.getName()).collect(Collectors.toSet());
                    attributeSet =  attributes.stream().map(attribute -> attribute.getName()).collect(Collectors.toSet());
                }else{
                    set = Collections.EMPTY_SET;
                    attributeSet = Collections.EMPTY_SET;
                }
            }
        }

        map.put("success", true);
        map.put("set", set);
        map.put("attributeSet", attributeSet);
        return JsonResult.success(map);
    }

}
