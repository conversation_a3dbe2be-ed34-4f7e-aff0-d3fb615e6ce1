package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.risk.common.config.AppIdConfig;
import com.yupaopao.risk.common.model.PunishBusiness;
import com.yupaopao.risk.common.model.PunishChannel;
import com.yupaopao.risk.console.service.PunishBusinessService;
import com.yupaopao.risk.console.service.PunishChannelService;
import com.yupaopao.risk.console.service.UserDetailService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.punish.bean.TagInfo;
import com.yupaopao.risk.punish.enums.ObjTypeEnum;
import com.yupaopao.risk.punish.manage.PunishCmdService;
import com.yupaopao.risk.punish.request.manage.AbilityRecordRequest;
import com.yupaopao.risk.punish.request.manage.PkgBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 惩罚历史（对外的惩罚记录）
 */
@Slf4j
@RequestMapping("/riskPunish/abilityRecordOut")
@RestController
@AuthRequired(permissionName = "riskPunish:abilityRecordOut")
public class PunishRecordAbilityOutController {

    @DubboReference(timeout = 50000)
    private PunishCmdService cmdService;
    @Autowired
    private UserDetailService userDetailService;
    @Autowired
    private PunishChannelService punishChannelService;
    @Autowired
    private PunishBusinessService punishBusinessService;

    @RequestMapping("/getAllBusiness")
    public JsonResult getAllBusiness() {
        return JsonResult.success(punishBusinessService.selectAll());
    }

    /**
     * 获取处罚来源
     */
    @RequestMapping("/getAllChannels")
    public JsonResult getAllChannels() {
        List<PunishBusiness> businesses = punishBusinessService.selectAll();
        List<PunishChannel> channels = punishChannelService.selectAll();
        List<String> externalChannels = cmdService.getNotExternalChannels();
        List<Map<String, String>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channels)) {
            for (PunishChannel channel : channels) {
                //排除不对外开放查询的渠道
                if (externalChannels != null && externalChannels.contains(channel.getCode())) {
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                map.put("channel", channel.getCode());
                map.put("name", channel.getName());
                if (CollectionUtils.isNotEmpty(businesses)) {
                    for (PunishBusiness business : businesses) {
                        if (business.getId().equals(channel.getBid())) {
                            map.put("business", business.getCode());
                        }
                    }
                }
                list.add(map);
            }
        }
        return JsonResult.success(list);
    }

    /**
     * 获取处罚来源
     */
    @RequestMapping("/getAllObjTypes")
    public JsonResult getAllObjTypes() {
        Response<List<Map<String, String>>> response = cmdService.getAllObjTypes();
        if (response.isSuccess()) {
            response.getResult().stream().forEach(map -> {
                if (ObjTypeEnum.UID.getCode().equals(map.get("code"))) {
                    map.put("msg", "用户ID");
                }
            });
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 获取所有惩罚项
     */
    @RequestMapping("/getAllAbilities")
    public JsonResult getAllAbilities() {
        Response<List<TagInfo>> response = cmdService.getAllAbilities(null);
        if (response.isSuccess()) {
            List<TagInfo> result = response.getResult();
            //排除非外部可见的惩罚项
            List<String> notInternalCodes = cmdService.getNotExternalCodes();
            if (CollectionUtils.isNotEmpty(notInternalCodes)) {
                result = result.stream().filter(record -> !notInternalCodes.contains(record.getPunishCode())).collect(Collectors.toList());
            }
            return JsonResult.success(result);
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 获取所有惩罚包
     */
    @RequestMapping("/getAllPkg")
    public JsonResult getAllPkg() {
        Response<List<PkgBO>> response = cmdService.getAllPkg();
        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 查询惩罚项记录
     */
    @RequestMapping("/search")
    public JsonResult search(@RequestBody AbilityRecordRequest request) {
        //用户id showNo转uid
        if (ObjTypeEnum.UID.getCode().equals(request.getObjType())) {
            //惩罚类型选择了用户ID且为短号时 转换为uid查询惩罚记录表
            Integer appId = AppIdConfig.getDefaultAppId(request.getAccountType());
            Long uid = getUidByShowNo(request.getObjId(), appId);
            if (uid != null) {
                request.setObjId(String.valueOf(uid));
            }
        }
        return JsonResult.success(cmdService.search(request));
    }

    private Long getUidByShowNo(String value, Integer appId) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        String pattern = "[0-9]+";
        //表示是showNo
        if (value.length() < 11 && Pattern.matches(pattern, value)) {
            UserInfoDTO userInfoDTO = userDetailService.getUserInfoByYppNo(value, appId);
            if (null != userInfoDTO) {
                return userInfoDTO.getUid();
            }
        }
        return null;
    }

    /**
     * 查询实际通知内容
     */
    @RequestMapping("/getNoticeReal")
    public JsonResult getNoticeReal(@RequestParam(value = "requestId") String requestId) {
        return JsonResult.success(cmdService.getNoticeReal(requestId));
    }

}
