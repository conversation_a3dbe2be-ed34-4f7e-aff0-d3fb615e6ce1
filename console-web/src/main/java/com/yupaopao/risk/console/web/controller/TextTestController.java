package com.yupaopao.risk.console.web.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Lists;
import com.yupaopao.risk.access.api.RiskService;
import com.yupaopao.risk.access.bean.RiskAction;
import com.yupaopao.risk.access.bean.RiskLevel;
import com.yupaopao.risk.access.bean.RiskResult;
import com.yupaopao.risk.common.enums.WordEffectType;
import com.yupaopao.risk.console.service.SceneService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.text.api.RiskTextService;
import com.yupaopao.risk.text.bean.HitWord;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/text/test")
@AuthRequired(permissionName = "text:test:all")
@Slf4j
public class TextTestController {

    @Autowired
    private SceneService sceneService;

    @Reference(check = false, timeout = 60000)
    private RiskService riskService;

    @Reference(check = false)
    private RiskTextService riskTextService;

//    private static Map<String, String> RETURN_MAP_FIELD_NAME_MAPPING = new HashMap<>();
//
//    static {
//        RETURN_MAP_FIELD_NAME_MAPPING.put("tagList", "违禁词标签列表");
//        RETURN_MAP_FIELD_NAME_MAPPING.put("wordList", "违禁词列表");
//    }

    //language=JSON
    private String defaultTypes = "[{\n" +
            "  \"displayName\":\"UGC反垃圾检测\",\n" +
            "  \"event\":\"ugc-text\"\n" +
            "},{\n" +
            "  \"displayName\":\"IM消息检查\",\n" +
            "  \"event\":\"im-message\",\n" +
            "  \"contentField\":\"body\"\n" +
            "},{\n" +
            "  \"displayName\":\"私聊\",\n" +
            "  \"event\":\"private-chat\",\n" +
            "  \"contentField\":\"body\"\n" +
            "}]";

    private String returnMapFieldNames = "[{\"fieldName\":\"tagList\",\"ChineseName\":\"违禁词标签列表\"},{\"fieldName\":\"wordList\",\"ChineseName\":\"违禁词列表\"}]";
    private List<TextCheckType> typeList = Lists.newArrayList();//页面低频操作暂不考虑并发问题
    private Map<String, TextCheckType> map = new HashMap<>();
    private Map<String, String> returnMapFieldNameMap = new HashMap<>();

    @ApolloConfig
    private Config config;

    @PostConstruct
    private void init() {
        parseTypes();
        parseReturnMapFieldNameMap();

        config.addChangeListener(event -> {
            if (event.isChanged("text.check.type")) {
                log.info("text.check.type重新构建");
                parseTypes();
            }

            if (event.isChanged("text.check.return.map.field.map")) {
                log.info("text.check.return.map.field.map重新构建");
                parseReturnMapFieldNameMap();
            }
        });
    }

    private void parseTypes() {
        typeList = JSON.parseArray(config.getProperty("text.check.type", defaultTypes), TextCheckType.class);
        typeList.forEach(p -> {
            map.put(p.getEvent(), p);
        });
    }

    private void parseReturnMapFieldNameMap() {
        List<FieldNameMap> fieldNameMaps = JSON.parseArray(config.getProperty("text.check.return.map.field.map", returnMapFieldNames), FieldNameMap.class);
        fieldNameMaps.forEach(p -> {
            returnMapFieldNameMap.put(p.getFieldName(),p.getChineseName());
        });
    }

    /**
     * 查询前台页面中场景下拉框展示的内容
     */
    @RequestMapping("/getAuditTestSceneTypeList")
    public JsonResult getAuditTestSceneTypeList() {
        return JsonResult.success(typeList);
    }

    @RequestMapping("/hitword")
    public JsonResult hitword(@RequestBody TextDetectVO textDetectVO) {
        String event = textDetectVO.getEvent();
        String content = textDetectVO.getContent();

        // 参数校验
        if (StringUtils.isAnyEmpty(event, content)
                || !map.containsKey(event)) {
            return JsonResult.success(new HashMap<String, Object>() {{
                put("level", RiskLevel.PASS);
            }});
        }

        // 构建请求体
        RiskAction riskAction = RiskAction.create(event);
        String contentField;
        String contentFieldActual = StringUtils.isEmpty((contentField = map.get(event).getContentField())) ? "content" : contentField;
        riskAction.put(contentFieldActual, content);

        // 执行请求
        RiskResult ret;
        try {
            ret = riskService.detect(riskAction);
        } catch (Throwable e) {
            log.error("违禁词测试失败，访问风控业务异常", e);
            return JsonResult.error();
        }

        // 处理返回内容
        Map<String, Object> data = new HashMap<String, Object>(2) {{
            put("level", ret.getLevel());
//            put("returnMap", ret.getReturnMap());
            put("returnMap", JSONObject.toJSONString(formatResult(ret.getReturnMap())));
        }};

        return JsonResult.success(data);
    }

    private Map<String, String> formatResult(Map<String, String> returnMap) {
        Map<String, String> result = new HashMap<>();

        if (MapUtils.isNotEmpty(returnMap)) {
            for (Map.Entry<String, String> entry : returnMap.entrySet()) {
                if (this.returnMapFieldNameMap.containsKey(entry.getKey())) {
                    String chineseFieldName = returnMapFieldNameMap.get(entry.getKey());
                    result.put(chineseFieldName, entry.getValue());
                } else {
                    result.put(entry.getKey(), entry.getValue());
                }
            }
        }

        return result;
    }

    private List<HitWord> getValidFencingWordList(HitWord[] hitWords) {
        List<HitWord> words = Lists.newArrayList();

        if (ArrayUtils.isNotEmpty(hitWords)) {
            for (HitWord hitWord : hitWords) {
                if (Objects.equals(hitWord.getWordEffectType(), WordEffectType.WordList.getCode())) {
                    words.add(hitWord);
                }
            }
        }

        return words;
    }

    private List<HitWord> getWhiteWordList(HitWord[] hitWords) {
        List<HitWord> whiteWords = Lists.newArrayList();

        if (ArrayUtils.isNotEmpty(hitWords)) {
            for (HitWord hitWord : hitWords) {
                if (Objects.equals(hitWord.getWordEffectType(), WordEffectType.WhiteWordList.getCode())) {
                    whiteWords.add(hitWord);
                }
            }
        }

        return whiteWords;
    }

    private List<HitWord> getInvalidFencingWordList(HitWord[] hitWords) {
        List<HitWord> invalidFencingWords = Lists.newArrayList();

        if (ArrayUtils.isNotEmpty(hitWords)) {
            for (HitWord hitWord : hitWords) {
                if (Objects.equals(hitWord.getWordEffectType(), WordEffectType.inValidWordList.getCode())) {
                    invalidFencingWords.add(hitWord);
                }
            }
        }

        return invalidFencingWords;
    }

    @Getter
    @Setter
    static class TextDetectVO implements Serializable {

        private String content;
        private String event;
    }

    @Getter
    @Setter
    static class TextCheckType implements Serializable {

        private String displayName;
        private String event;
        private String contentField;
    }

    @Getter
    @Setter
    static class FieldNameMap implements Serializable {
        private String fieldName;
        private String ChineseName;
    }

}
