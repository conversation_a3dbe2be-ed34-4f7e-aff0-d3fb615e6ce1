package com.yupaopao.risk.console.web.controller.riskPunish;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.model.PunishBusiness;
import com.yupaopao.risk.common.model.PunishChannel;
import com.yupaopao.risk.console.service.PunishBusinessService;
import com.yupaopao.risk.console.service.PunishChannelService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.punish.bean.TagInfo;
import com.yupaopao.risk.punish.manage.PunishCmdService;
import com.yupaopao.risk.punish.request.manage.AbilityRecordRequest;
import com.yupaopao.risk.punish.request.manage.PkgBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * Description
 *
 * <AUTHOR>
 * @since Version
 * <p>
 * 2021/1/14 15:42
 */
@Slf4j
@RequestMapping("/riskPunish/abilityRecord")
@RestController
@AuthRequired(permissionName = "riskPunish:abilityRecord")
public class PunishRecordAbilityController {

    @DubboReference(timeout = 50000)
    private PunishCmdService cmdService;
    @Autowired
    private PunishChannelService punishChannelService;
    @Autowired
    private PunishBusinessService punishBusinessService;

    @RequestMapping("/getAllBusiness")
    public JsonResult getAllBusiness() {
        return JsonResult.success(punishBusinessService.selectAll());
    }

    /**
     * 获取处罚来源
     */
    @RequestMapping("/getAllChannels")
    public JsonResult getAllChannels() {
        List<PunishBusiness> businesses = punishBusinessService.selectAll();
        List<PunishChannel> channels = punishChannelService.selectAll();
        List<Map<String, String>> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channels)) {
            for (PunishChannel channel : channels) {
                Map<String, String> map = new HashMap<>();
                map.put("channel", channel.getCode());
                map.put("name", channel.getName());
                if (CollectionUtils.isNotEmpty(businesses)) {
                    for (PunishBusiness business : businesses) {
                        if (business.getId().equals(channel.getBid())) {
                            map.put("business", business.getCode());
                        }
                    }
                }
                list.add(map);
            }
        }
        return JsonResult.success(list);
    }

    /**
     * 获取处罚来源
     */
    @RequestMapping("/getAllObjTypes")
    public JsonResult getAllObjTypes() {
        Response<List<Map<String, String>>> response = cmdService.getAllObjTypes();
        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 获取所有惩罚项
     */
    @RequestMapping("/getAllAbilities")
    public JsonResult getAllAbilities() {
        Response<List<TagInfo>> response = cmdService.getAllAbilities(null);
        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 获取所有惩罚包
     */
    @RequestMapping("/getAllPkg")
    public JsonResult getAllPkg() {
        Response<List<PkgBO>> response = cmdService.getAllPkg();
        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        }
        return JsonResult.success(Collections.EMPTY_LIST);
    }

    /**
     * 查询惩罚项记录
     */
    @RequestMapping("/search")
    public JsonResult search(@RequestBody AbilityRecordRequest request) {
        //开启内部查询 可查询所有惩罚记录
        request.setIsInternal(true);
        return JsonResult.success(cmdService.search(request));
    }

    /**
     * 查询实际通知内容
     */
    @RequestMapping("/getNoticeReal")
    public JsonResult getNoticeReal(@RequestParam(value = "requestId") String requestId) {
        return JsonResult.success(cmdService.getNoticeReal(requestId));
    }

}
