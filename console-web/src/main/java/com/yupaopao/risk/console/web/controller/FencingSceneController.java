package com.yupaopao.risk.console.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.MapMaker;
import com.google.common.collect.Maps;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.common.enums.ToSystemType;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.vo.SceneVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/fencing/scene")
@AuthRequired(permissionName = "core:fencingscene:all")
public class FencingSceneController extends AbstractModelController<Scene, Scene, SceneService> {

    @Autowired
    private WordService wordService;

    @Autowired
    private SceneService service;

    @Autowired
    private WordSceneService wordSceneService;

    @Autowired
    private AtomRuleService atomRuleService;

    @Autowired
    private RuleRelationService ruleRelationService;

    @Autowired
    private EventService eventService;

    @Autowired
    private ThirdChannelService thirdChannelService;

    @Autowired
    private BizChannelService bizChannelService;

    @Autowired
    private BizTypeService bizTypeService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    /**
     * "风控人员"只能查询属于风控系统的场景，"审核人员"只能查询属于审核系统的场景，"既属于风控人员又属于审核人员"可以查询属于风控系统和审核系统的场景
     * @param user
     * @param scene
     * @return
     */
    @RequestMapping("/show")
    @AuthRequired(permissionName = "*")
    public JsonResult show(@CurrentUser User user, Scene scene) {
        if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT.getCode())||Objects.equals(user.getToSystem(),ToSystemType.AUDIT.getCode())){
            scene.setToSystem(user.getToSystem());
        }else if(Objects.equals(user.getToSystem(),ToSystemType.CREDIT_AUDIT.getCode())){
            scene.setToSystem(null);
        }

        return JsonResult.success(service.search(scene));
    }

    private void copy(List<Scene> scenes,List<SceneVO> sceneVOS){
        if(CollectionUtils.isNotEmpty(scenes)){
            for(Scene scene:scenes){
                SceneVO sceneVO = new SceneVO();
                BeanUtils.copyProperties(scene,sceneVO);
                sceneVOS.add(sceneVO);
            }
        }
    }


    /**
     * "风控人员"和"既属于风控人员又属于审核人员"可以查询所有的场景，"审核人员"只能查询属于审核系统的场景
     * @param user
     * @param scene
     * @param page
     * @param size
     * @return
     */
    @RequestMapping("/searchByToSystem")
    public JsonResult searchByToSystem(@CurrentUser User user,@RequestBody Scene scene, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        if(Objects.equals(user.getToSystem(),ToSystemType.AUDIT.getCode())){
            scene.setToSystem(user.getToSystem());
        }

        PageInfo scenePageInfo = service.search(scene, page, size);

        if(CollectionUtils.isEmpty(scenePageInfo.getList())){
            List<SceneVO> sceneVOS = new ArrayList<>();
            scenePageInfo.setList(sceneVOS);

            return JsonResult.success(scenePageInfo);
        }

        List<RuleRelation> ruleRelations = this.ruleRelationService.selectAll();
        Map<Long,Set<Long>> ruleToGroupMap = convertToRuleToGroupsMap(ruleRelations);

        List<Event> allEvents = this.eventService.selectAll();
        Map<Long,Set<Long>> groupToEventMap = convertToGroupToEventsMap(allEvents);
        Map<Long,Set<Long>> ruleToEventMap = convertToRuleToEventMap(ruleToGroupMap,groupToEventMap);
        Map<Long,Event> idToEventMap = allEvents.stream().collect(Collectors.toMap(Event::getId,Event->Event));

        Set<AtomRule> rulesA = Sets.newHashSet();
        rulesA.addAll(this.atomRuleService.getListRelatedScene());

        List<SceneVO> sceneVOS = Lists.newArrayList();
        copy(scenePageInfo.getList(),sceneVOS);

        //关联规则 基于场景
        this.relateRule(sceneVOS,rulesA,"scenes");

        //关联事件 基于场景
        this.relateEventByScene(sceneVOS,rulesA,"scenes",ruleToEventMap,idToEventMap);

        //关联通道和业务类型
        relateChannelAndBiz(sceneVOS);

        Set<AtomRule> rulesB = Sets.newHashSet();
        rulesB.addAll(this.atomRuleService.getListRelatedBizType());

        //关联规则 基于业务类型
        relateRuleByBiz(sceneVOS,rulesB,"bizTypeCode");

        //关联事件 基于业务类型
        this.relateEventByBiz(sceneVOS,rulesB,"bizTypeCode",ruleToEventMap,idToEventMap);

        scenePageInfo.setList(sceneVOS);

        return JsonResult.success(scenePageInfo);
    }


    //关联上规则 基于场景
    private void relateRule(List<SceneVO> sceneVOS,Set<AtomRule> rules,String scenes){
        if(CollectionUtils.isNotEmpty(sceneVOS)){
            Map<Long, Set<String>> ruleIdSceneNamesMap = convertToRuleIdKeysMap(rules,scenes);
            Map<String,Set<Long>> sceneNameRuleIdsMap = convertToKeyRuleIdsMap(ruleIdSceneNamesMap);

            for(SceneVO sceneVO:sceneVOS){
                Set<AtomRule> atomRules = new HashSet<>();

                Map<Long,AtomRule> idToAtomRuleMap = rules.stream().collect(Collectors.toMap(AtomRule::getId,atomRule -> atomRule));
                Set<Long> ruleIds = sceneNameRuleIdsMap.get(sceneVO.getName());
                if(CollectionUtils.isNotEmpty(ruleIds)){
                    for(Long ruleId:ruleIds){
                        atomRules.add(idToAtomRuleMap.get(ruleId));
                    }
                }

                sceneVO.setAtomRules(atomRules);
            }
        }
    }

    //关联上规则
    private void relateRuleByBiz(List<SceneVO> sceneVOS, Set<AtomRule> rules, String bizType){
        if(CollectionUtils.isNotEmpty(sceneVOS)){
            Map<Long, Set<String>> ruleIdSceneNamesMap = convertToRuleIdKeysMap(rules,bizType);
            Map<String,Set<Long>> bizTypeRuleIdsMap = convertToKeyRuleIdsMap(ruleIdSceneNamesMap);

            for(SceneVO sceneVO:sceneVOS){
                Map<Long,AtomRule> idToAtomRuleMap = rules.stream().collect(Collectors.toMap(AtomRule::getId,atomRule -> atomRule));
                Set<BizType> bizTypes = sceneVO.getBizTypes();
                if(CollectionUtils.isNotEmpty(bizTypes)){
                    for(BizType item:bizTypes){
                        Set<Long> ruleIds = bizTypeRuleIdsMap.get(item.getCode());
                        if(CollectionUtils.isEmpty(ruleIds)){
                            continue;
                        }

                        Set<AtomRule> relateRules = sceneVO.getAtomRules();
                        if(relateRules!=null){
                            for(Long ruleId:ruleIds){
                                AtomRule atomRule = idToAtomRuleMap.get(ruleId);
                                relateRules.add(atomRule);
                            }
                        }else{
                            Set<AtomRule> set = new HashSet<>();
                            for(Long ruleId:ruleIds){
                                AtomRule atomRule = idToAtomRuleMap.get(ruleId);
                                set.add(atomRule);
                            }

                            sceneVO.setAtomRules(set);
                        }
                    }
                }

            }
        }
    }

    //关联上事件 基于场景
    private void relateEventByScene(List<SceneVO> sceneVOS, Set<AtomRule> rules, String scenes,Map<Long,Set<Long>> ruleToEventMap,Map<Long,Event> idToEventMap){
        if(CollectionUtils.isNotEmpty(sceneVOS)){
            Map<Long, Set<String>> ruleIdSceneNamesMap = convertToRuleIdKeysMap(rules,scenes);
            Map<String,Set<Long>> sceneNameRuleIdsMap = convertToKeyRuleIdsMap(ruleIdSceneNamesMap);



            for(SceneVO sceneVO:sceneVOS){
                Set<Event> events = new HashSet<>();

                Set<Long> ruleIds = sceneNameRuleIdsMap.get(sceneVO.getName());
                if(ruleIds!=null){
                    relateEvents(ruleToEventMap, idToEventMap, ruleIds, events);
                }

                sceneVO.setEvents(events);
            }
        }
    }

    /**
     * 关联上事件 基于业务类型
     * @param sceneVOS
     * @param rules
     * @param bizType
     */
    private void relateEventByBiz(List<SceneVO> sceneVOS,Set<AtomRule> rules,String bizType,Map<Long,Set<Long>> ruleToEventMap,Map<Long,Event> idToEventMap){
        //1. 根据rules，转换得到 业务类型到规则集合的映射
        //2. 根据 sceneVOS中单个sceneVO关联的业务类型集合，得到 关联的规则
        //3. 再根据关联的规则，得到关联的事件

        if(CollectionUtils.isNotEmpty(sceneVOS)){
            //1. 根据rules，转换得到 业务类型到规则集合的映射
            Map<Long, Set<String>> ruleIdBizTypesMap = convertToRuleIdKeysMap(rules,bizType);
            Map<String,Set<Long>> bizTypeRuleIdsMap = convertToKeyRuleIdsMap(ruleIdBizTypesMap);


            //2. 根据 sceneVOS中单个sceneVO关联的业务类型集合，得到 关联的规则
            for(SceneVO sceneVO:sceneVOS){

                Set<BizType> bizTypes = sceneVO.getBizTypes();
                if(CollectionUtils.isNotEmpty(bizTypes)){
                    for(BizType item:bizTypes){
                        Set<Long> ruleIds = bizTypeRuleIdsMap.get(item.getCode());

                        //3. 再根据关联的规则，得到关联的事件
                        Set<Event> set = sceneVO.getEvents();
                        if(set!=null){
                            relateEvents(ruleToEventMap, idToEventMap, ruleIds, set);
                        }else{
                            Set<Event> newSet = new HashSet<>();
                            relateEvents(ruleToEventMap, idToEventMap, ruleIds, newSet);

                            sceneVO.setEvents(newSet);
                        }
                    }
                }
            }
        }
    }

    private void relateEvents(Map<Long, Set<Long>> ruleToEventMap, Map<Long, Event> idToEventMap, Set<Long> ruleIds, Set<Event> newSet) {
        if(MapUtils.isEmpty(ruleToEventMap)||MapUtils.isEmpty(idToEventMap)||CollectionUtils.isEmpty(ruleIds)){
            return;
        }

        for(Long ruleId:ruleIds){
            Set<Long> eventIds = ruleToEventMap.get(ruleId);
            if(CollectionUtils.isNotEmpty(eventIds)){
                for(Long eventId:eventIds){
                    Event event = idToEventMap.get(eventId);
                    newSet.add(event);
                }
            }
        }
    }

    /**
     *
     * @param sceneVOS
     */
    private void relateChannelAndBiz(List<SceneVO> sceneVOS){

        //关联通道
        //参数配置中包含有"scenes"关键字的通道
        List<ThirdChannel> thirdChannels =  thirdChannelService.hasConfig("scenes");
        if(CollectionUtils.isEmpty(thirdChannels)||CollectionUtils.isEmpty(sceneVOS)){
            return;
        }

        Map<String,Set<ThirdChannel>> maps = Maps.newHashMap();
        for(ThirdChannel thirdChannel:thirdChannels){
            String config = thirdChannel.getConfig();
            if(StringUtils.isNotBlank(config)){
                Map configMap =  JSONObject.parseObject(config,Map.class);
               if(configMap.containsKey("scenes")){
                  List<String> list =  JSONArray.parseArray(configMap.get("scenes").toString(),String.class);
                  if(CollectionUtils.isNotEmpty(list)){
                      for(String scene:list){
                          if(!maps.containsKey(scene)){
                              Set<ThirdChannel> sets = Sets.newHashSet();
                              sets.add(thirdChannel);

                              maps.put(scene,sets);
                          }else{
                              Set<ThirdChannel> sets = maps.get(scene);
                              sets.add(thirdChannel);
                          }
                      }
                  }
               }
            }
        }

        for(SceneVO sceneVO:sceneVOS){
            sceneVO.setChannels(maps.get(sceneVO.getName()));
        }

        //关联业务类型
        List<Long> thirdChannelIdList = thirdChannels.stream().map(ThirdChannel::getId).collect(Collectors.toList());
        List<BizChannel> bizChannels = bizChannelService.listByThirdChannelIds(thirdChannelIdList);
        if(CollectionUtils.isNotEmpty(bizChannels)){
            List<Long> bizTypeIds = bizChannels.stream().map(BizChannel::getBizTypeId).collect(Collectors.toList());
            List<BizType> bizTypes = this.bizTypeService.listByIds(bizTypeIds);
            if(CollectionUtils.isNotEmpty(bizTypes)){
                Map<Long,BizType> idBizType = bizTypes.stream().collect(Collectors.toMap(BizType::getId,bizType->bizType));

                Map<Long,List<BizChannel>> channelBizTypes = bizChannels.stream().collect(Collectors.groupingBy(BizChannel::getThirdChannelId));

                for(SceneVO sceneVO:sceneVOS){
                    Set<BizType> relateBizTypes = Sets.newHashSet();

                    Set<ThirdChannel> channels = sceneVO.getChannels();
                    if(CollectionUtils.isNotEmpty(channels)){
                        for(ThirdChannel channel:channels){
                            List<BizChannel> bizChannelList = channelBizTypes.get(channel.getId());
                            if(CollectionUtils.isNotEmpty(bizChannelList)){
                                for(BizChannel bizChannel:bizChannelList){
                                    BizType bizType = idBizType.get(bizChannel.getBizTypeId());
                                    relateBizTypes.add(bizType);
                                }
                            }
                        }
                    }

                    sceneVO.setBizTypes(relateBizTypes);
                }
            }
        }
    }

    private Map<Long,Set<Long>> convertToRuleToEventMap(Map<Long,Set<Long>> ruleToGroupMap,Map<Long,Set<Long>> groupToEventMap){
        Map<Long,Set<Long>> result = new HashMap<>();

        if(MapUtils.isNotEmpty(ruleToGroupMap)&&MapUtils.isNotEmpty(groupToEventMap)){
            for(Map.Entry<Long,Set<Long>> ruleToGroup:ruleToGroupMap.entrySet()){
                Long ruleId = ruleToGroup.getKey();

                Set<Long> groups = ruleToGroup.getValue();
                for(Long groupId:groups){
                    Set<Long> events = groupToEventMap.get(groupId);

                    if(CollectionUtils.isNotEmpty(events)){
                        if(result.containsKey(ruleId)){
                            result.get(ruleId).addAll(events);
                        }else{
                            result.put(ruleId,events);
                        }
                    }

                }
            }
        }

        return result;
    }

    private Map<Long,Set<Long>> convertToGroupToEventsMap(List<Event> events){
        Map<Long,Set<Long>> result = new HashMap<>();

        if(CollectionUtils.isNotEmpty(events)){
            for(Event event:events){
                if(StringUtil.isNotEmpty(event.getRuleGroupId())){
                    String[] ruleGroupIds = event.getRuleGroupId().split(",");
                    if(ArrayUtils.isNotEmpty(ruleGroupIds)){
                        for(String ruleGroupId:ruleGroupIds){
                            Long groupId = Long.parseLong(ruleGroupId);

                            if(result.containsKey(groupId)){
                                result.get(groupId).add(event.getId());
                            }else{
                                Set<Long> eventIds = new HashSet<>();
                                eventIds.add(event.getId());

                                result.put(groupId,eventIds);
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    private Map<Long,Set<Long>> convertToRuleToGroupsMap(List<RuleRelation> ruleRelations){
        Map<Long,Set<Long>> result = new HashMap<>();

        if(CollectionUtils.isNotEmpty(ruleRelations)){
            for(RuleRelation ruleRelation:ruleRelations){
                Long ruleId = ruleRelation.getRuleId();
                Long groupId = ruleRelation.getGroupId();

                if(result.containsKey(ruleId)){
                    result.get(ruleId).add(groupId);
                }else{
                    Set<Long> groupIds = new HashSet<>();
                    groupIds.add(groupId);

                    result.put(ruleId,groupIds);
                }
            }
        }

        return result;
    }

    private Map<String,Set<Long>> convertToKeyRuleIdsMap(Map<Long, Set<String>> ruleIdKeyNamesMap){
        Map<String,Set<Long>> result = new HashMap<>();

        if(MapUtils.isNotEmpty(ruleIdKeyNamesMap)){
            for(Map.Entry<Long,Set<String>> entry:ruleIdKeyNamesMap.entrySet()){
                Long ruleId = entry.getKey();
                Set<String> sceneNames = entry.getValue();

                if(CollectionUtils.isNotEmpty(sceneNames)){
                    for(String sceneName:sceneNames){
                        if(result.containsKey(sceneName)){
                            result.get(sceneName).add(ruleId);
                        }else{
                            Set<Long> ruleIds = new HashSet<>();
                            ruleIds.add(ruleId);
                            result.put(sceneName,ruleIds);
                        }
                    }
                }
            }
        }

        return result;
    }

    /**
     *
     * @param atomRules
     * @param key 取值为 "scenes" "bizTypeCode"
     * @return
     */
    private Map<Long,Set<String>> convertToRuleIdKeysMap(Set<AtomRule> atomRules, String key){

        Map<Long,Set<String>> result = new HashMap<>();

        if(CollectionUtils.isNotEmpty(atomRules)){
            for(AtomRule atomRule:atomRules){

                Set<String> sceneNameSet = new HashSet<>();

                String riskConst = atomRule.getRiskConst();

                if(StringUtils.isNotBlank(riskConst)){
                    JSONObject jsonObject = JSONObject.parseObject(riskConst);
                    Map<String,JSONObject> map = JSONObject.toJavaObject(jsonObject,Map.class);
                    for(Map.Entry<String,JSONObject> entry:map.entrySet()){
                        JSONObject value =  entry.getValue();
                        if(value.containsKey(key)){
                            if("scenes".equalsIgnoreCase(key)){
                                List<String> sceneList =  JSONArray.parseArray(value.get(key).toString(),String.class);

                                if(CollectionUtils.isNotEmpty(sceneList)){
                                    for(Object scene:sceneList){
                                        sceneNameSet.add(scene.toString());
                                    }
                                }
                            }else if("bizTypeCode".equalsIgnoreCase(key)&&StringUtils.isNotBlank(value.get(key).toString())){
                                sceneNameSet.add(value.get(key).toString());
                            }

                        }
                    }

                    result.put(atomRule.getId(),sceneNameSet);
                }

            }
        }

        return result;
    }


    /**
     * 查询隶属于某个系统类型列表
     */
    @RequestMapping("/getToSystemTypeList")
    public JsonResult getToSystemTypeList() {
        List<Map<String, String>> list = Lists.newArrayList();

        EnumUtils.getEnumList(ToSystemType.class).forEach(status -> {
            if(Objects.equals(ToSystemType.CREDIT.getCode(),status.getCode())||Objects.equals(ToSystemType.AUDIT.getCode(),status.getCode())){
                Map<String, String> temp = new MapMaker().makeMap();
                temp.put("code", String.valueOf(status.getCode()));
                temp.put("msg", String.valueOf(status.getMsg()));
                list.add(temp);
            }
        });
        return JsonResult.success(list);
    }

    @Override
    protected void addValid(Scene record, User user) {

    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public JsonResult add(@Valid @RequestBody Scene record, @CurrentUser User user) {
        record.setModifier(user.getName());
        addValid(record, user);
        if(service.insertSelective(record)){
            String key = String.format(ConsoleConstants.REDIS_CACHE_SCENE_LIST_PREFIX,record.getToSystem());
            deleteFromRedis(key);
        }

        return JsonResult.success(true);
    }

    @Override
    protected void updateValid(Scene record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
    }



    @RequestMapping(value = "/update/{id}", method = RequestMethod.POST)
    public JsonResult update(@PathVariable long id, @Valid @RequestBody Scene record, @CurrentUser User user) {
        record.setModifier(user.getName());
        updateValid(record, user);
        if(service.updateSelectiveById(record)){
            String key = String.format(ConsoleConstants.REDIS_CACHE_SCENE_LIST_PREFIX,record.getToSystem());
            deleteFromRedis(key);
        }

        return JsonResult.success(true);
    }

    @RequestMapping(value = "/checkSceneWordRelation/{id}",method = RequestMethod.POST)
    public JsonResult checkSceneWordRelation(@PathVariable long id){
        Map<String,Object> result = new MapMaker().makeMap();

        List<WordScene> wordScenes = this.wordSceneService.selectBySceneId(id);
        if(CollectionUtils.isNotEmpty(wordScenes)){
            result.put("hasRelation",true);
        }else{
            result.put("hasRelation",false);
        }

        return JsonResult.success(result);
    }

    @Override
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public JsonResult delete(@PathVariable long id, @CurrentUser User user) {
        Scene record = service.get(id);

        if(service.deleteById(id)){
            String key = String.format(ConsoleConstants.REDIS_CACHE_SCENE_LIST_PREFIX,record.getToSystem());
            deleteFromRedis(key);
        }

        return JsonResult.success(true);
    }

    private void deleteFromRedis(String key){
        if(redisService.del(key)<1){
            redisService.del(key);
        }
    }

}
