package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.RuleStatus;
import com.yupaopao.risk.common.model.GroupRule;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.GroupRuleVO;
import com.yupaopao.risk.console.ConstantsForApollo;
import com.yupaopao.risk.console.bean.ApolloConfigItem;
import com.yupaopao.risk.console.service.ApolloOpenApiService;
import com.yupaopao.risk.console.service.GroupRuleService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
@RestController
@RequestMapping("/rule/group")
@AuthRequired(permissionName = "core:grouprule:all")
@Slf4j
public class GroupRuleController extends AbstractModelController<GroupRule, GroupRuleVO, GroupRuleService> {

    @Autowired
    private ApolloOpenApiService apolloOpenApiService;

    @RequestMapping("/search")
    public JsonResult search(@RequestBody GroupRuleVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        PageInfo<GroupRule> result = getService().search(record, page, size);
        getService().relationFetchWithEvent(result.getList());
        return JsonResult.success(result);
    }

    protected void addValid(GroupRuleVO record, User user) {
        record.setAuthor(user.getName());
        record.setModifier(user.getName());
    }

    protected void updateValid(GroupRuleVO record, User user) {
        Assert.notNull(record.getId(), "请指定记录ID");
        Assert.notNull(record.getAtomRuleIds(), "请指定记录ID");
        record.setAuthor(null);
        record.setModifier(user.getName());
    }

    @RequestMapping(value = "/changeStatus/{id}", method = RequestMethod.POST)
    public JsonResult updateStatus(@PathVariable("id") long id, @Param("status") String status) {
        return JsonResult.success(getService().changeStatus(id, RuleStatus.nameOf(status)));
    }

    @RequestMapping("/build")
    public JsonResult build(@CurrentUser User user) {
        log.debug("user to rebuild rule engine , userName: {}", user.getName());
        ApolloConfigItem updateItem = ApolloConfigItem.builder()
                .appId(ConstantsForApollo.RISK_ENGINE)
                .namespace(ConstantsForApollo.APPLICATION)
                .itemKey(ConstantsForApollo.ENGINE_RELOAD_REFRESH)
                .itemValue(String.valueOf(System.currentTimeMillis()))
                .modifyUser(user.getName())
                .build();
        try {
            apolloOpenApiService.updateItem(updateItem);
        } catch (RuntimeException e1) {
            CommonUtil.apolloConfigException(e1);
        } catch (Exception e) {
            log.error("change apollo config to trigger rule engine rebuild", e);
            throw new RiskException(ErrorMessage.SYSTEM_ERROR.getCode(), "参数不正确");
        }
        return JsonResult.success();
    }

}
