package com.yupaopao.risk.console.web.controller.analysis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.operation.common.sdk.model.oprecord.OpTypeEnum;
import com.yupaopao.operation.common.sdk.oprecord.OpRecordReport;
import com.yupaopao.platform.audit.appeal.api.entity.response.AppealRecordResponse;
import com.yupaopao.platform.audit.appeal.api.entity.response.PunishRecordResponse;
import com.yupaopao.platform.audit.complain.api.entity.response.ComplainUserInfoResponse;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.common.utils.BitMapUtil;
import com.yupaopao.platform.passport.response.AccountDto;
import com.yupaopao.platform.passport.response.MobileInfo;
import com.yupaopao.platform.passport.sdk.enums.SourceTypeEnum;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.platform.user.api.enums.BitMap;
import com.yupaopao.risk.access.utils.DateUtil;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.enums.Dimensions;
import com.yupaopao.risk.common.enums.ErrorMessage;
import com.yupaopao.risk.common.enums.GrayListType;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.common.vo.JournalVO;
import com.yupaopao.risk.console.bean.AppealRecordVO;
import com.yupaopao.risk.console.bean.ComplainUserInfoVO;
import com.yupaopao.risk.console.bean.DataCleanReq;
import com.yupaopao.risk.console.bean.ExportedSearchRecord;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.service.impl.ConfigService;
import com.yupaopao.risk.console.utils.CommonUtil;
import com.yupaopao.risk.console.vo.LogSearchVO;
import com.yupaopao.risk.console.vo.RiskHitLogVO;
import com.yupaopao.risk.console.web.iptoregison.IPSearchService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatter;
import com.yupaopao.risk.console.web.support.utils.ExcelFormatterUtils;
import com.yupaopao.risk.console.web.support.utils.ExcelUtils;
import com.yupaopao.yuer.chatroom.dto.ChatroomInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@RestController
@RequestMapping("/detail/user")
@AuthRequired(permissionName = "system:detail:all")
public class UserAnalysisController {

    @Autowired
    private UserDetailService userDetailService;
    @Autowired
    private GrayGroupService grayGroupService;
    @Autowired
    private GrayListService grayListService;
    @Autowired
    private ConfigService configService;

    @Autowired
    private PayJournalService payJournalService;

    @Autowired
    private UserProductionActivitiesQueryService userProductionActivitiesQueryService;

    @Value("${gray.group.api:[17]}")
    private String grayGroupApi;

    @Value("${user.analysis.custom.times:1,2,3,4,5,6,7}")
    private String customTimes;

    @RequestMapping("")
    public JsonResult user(@RequestBody UserDetailService.UserQuery userQuery) {
        UserInfoDTO user = userDetailService.getUserInfo(userQuery);
        Map<String, Object> result = new HashMap<>();
        if (user != null) {
            result.put("raw", user);
            result.put("exp", userDetailService.getAllExpsByUid(user.getUid()));
            result.put("login", userDetailService.getHitLog(user.getUid()+"", userQuery.getTime() == null ? 0 : userQuery.getTime().getDays()));
            result.put("mobile", userDetailService.getMobileInfoByUserId(user.getUid()+""));
        } else {
            log.warn("未查找到指定用户:{}", userQuery);
        }
        return JsonResult.success(result);
    }

    // 用户信息
    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "风控用户分析", isReportResponse = false)
    @RequestMapping("info")
    public JsonResult info(@RequestBody UserDetailService.UserQuery userQuery) {
        UserInfoDTO userInfo = userDetailService.getUserInfo(userQuery);
        Map parse = null;
        if (!Objects.equals(null, userInfo)) {
            this.handleUserRegisterTime(userInfo);
            parse = (Map) JSONObject.parse(JSONObject.toJSONString(userInfo));
            parse.put("uid", userInfo.getUid() + "");
            parse.put("birthday", DateUtil.format(userInfo.getBirthday(), DateUtil.DATE_TIME_FORMAT));
            parse.put("god",BitMapUtil.getBitValue(userInfo.getBitMap(), BitMap.IS_GOD.getBit()) == 1);
            SourceTypeEnum sourceTypeEnum = userDetailService.getSource(userInfo.getUid());
            parse.put("sourceDesc",sourceTypeEnum == null ? "" : sourceTypeEnum.getDesc());
        }
        return JsonResult.success(parse);
    }

//    @AuthRequired(permissionName = "system:detail:portrait")
    @RequestMapping("portrait")
    public JsonResult queryPortrait(@RequestBody UserDetailService.UserRequest userRequest) {
        Map parse = Maps.newHashMap();
        Map<String, Object> portraitVO = userDetailService.getUserRiskPortraitData(userRequest.getValue(), userRequest.getStartTime(),userRequest.getEndTime());
        parse.put("portraitVO", portraitVO);

        return JsonResult.success(parse);
    }


    // 设置用户注册时间
    protected void handleUserRegisterTime(UserInfoDTO user) {
        user.setCreateTime(null); // 重置掉原有创建时间
        AccountDto accountDto = userDetailService.getAccountByUid(user.getUid());
        if (accountDto != null) {
            user.setCreateTime(accountDto.getCreateTime());
        }
    }

    // 经验等级
    @RequestMapping("exp")
    public JsonResult exp(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.getAllExpsByUid(uid));
    }

    // 获得个人资料卡
    @RequestMapping("dataCard")
    public JsonResult dataCard(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.getDataCard(uid));
    }

    // 风控历史
    @RequestMapping("hit")
    public JsonResult hit(@RequestBody UserDetailService.UserHitQuery userHitQuery) {
        return JsonResult.success(userDetailService.getHitLog(userHitQuery.getUserId(), userHitQuery.getTime() == null ? 0 : userHitQuery.getTime().getDays()));
    }

    // 大神信息
    @RequestMapping("biggieInfo")
    public JsonResult biggieInfo(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.queryBiggieInfo(uid));
    }

    // 帐号信息
    @RequestMapping("accountFreezeInfo")
    public JsonResult accountFreezeInfo(@RequestParam("uid") Long uid,@RequestParam("accountType") Integer accountType) {
        return JsonResult.success(userDetailService.getAccountFreezeInfos(uid,accountType));
    }

    // 黑名单
    @RequestMapping("blackGray")
    public JsonResult blackGray(@RequestParam("uid") Long uid){
        Assert.isTrue(uid != null && uid > 0, "uid非法");
        return JsonResult.success(userDetailService.getBlackGray(uid));
    }

    // 手机号信息
    @RequestMapping("mobile")
    public JsonResult mobile(@RequestParam("userId") String userId) {
        return JsonResult.success(userDetailService.getMobileInfoByUserId(userId));
    }

    // 查看脱敏手机号信息
    @RequestMapping("desensitizeMobile")
    public JsonResult desensitizeMobile(@RequestParam("userId") String userId) {
        MobileInfo mobileInfo = userDetailService.getMobileInfoByUserId(userId);
        if(mobileInfo!=null){
            String mobile = mobileInfo.getMobile();
            final String commonMobile = mobile.substring(0, 3) + "****" + mobile.substring(mobile.length() - 4);
            if(mobile.length()==11){
                mobile = commonMobile;
            }else if(mobile.length()==10){
                mobile = commonMobile;
            }else if(mobile.length()==9){
                mobile = commonMobile;
            }else if(mobile.length()==8){
                mobile = commonMobile;
            }else if(mobile.length()==7){
                mobile = mobile.substring(0,2)+"****"+mobile.substring(mobile.length()-3);
            }else{
                mobile = mobile.substring(0,2)+"****"+mobile.substring(mobile.length()-2);
            }

//           String desensitizeMobile =  mobileInfo.getMobile().substring(0,3)+"****"+mobileInfo.getMobile().substring(mobileInfo.getMobile().length()-4);
           mobileInfo.setMobile(mobile);
        }

        return JsonResult.success(mobileInfo);
    }

    // 微信、QQ绑定标识
    @RequestMapping("associate")
    public JsonResult associate(@RequestParam("uid") Long uid,@RequestParam("accountType") Integer accountType) {
        return JsonResult.success(userDetailService.getAssociateInfoByUid(uid,accountType));
    }

    @RequestMapping("relation/event")
    public JsonResult top(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        return JsonResult.success(userDetailService.getRelationEvent(userHitRequest.getUserId(),userHitRequest.getStartTime(),userHitRequest.getEndTime()));
    }

    @RequestMapping("relation/device")
    public JsonResult relationDevice(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        return JsonResult.success(userDetailService.getRelationDevice(userHitRequest.getUserId(),userHitRequest.getStartTime(),userHitRequest.getEndTime()));
    }

    @RequestMapping("relation/desensitizeMobile")
    public JsonResult relationDesensitizeMobile(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        Map<String, Object> mobileMap = userDetailService.getRelationMobile(userHitRequest.getUserId(), userHitRequest.getStartTime(),userHitRequest.getEndTime());

        Map<String, Object> newMobileMap = Maps.newHashMap();
        if(MapUtils.isNotEmpty(mobileMap)){
            mobileMap.forEach((key, value) -> {
                String newKey = "";
                if(key.length()>=4){
                    newKey = key.substring(0,3)+"****"+key.substring(key.length()-4);
                }else if(key.length()>=2){
                    newKey = key.substring(0,2)+"***"+key.substring(key.length()-2);
                }else if(key.length()>=1){
                    newKey = key.charAt(0)+"***"+key.substring(key.length()-1);
                }else{
                    newKey = key+"***"+key;
                }

                newMobileMap.put(newKey,value);
            });
        }

        return JsonResult.success(newMobileMap);
    }

    @RequestMapping("relation/mobileInfo")
    public JsonResult relationMobileInfo(@RequestBody UserDetailService.PhoneDetailRequest phoneDetailRequest) {
        Map<String, Object> maps = userDetailService.getRelationMobile(phoneDetailRequest.getUserId(), phoneDetailRequest.getStartTime(),phoneDetailRequest.getEndTime());
        AtomicReference<String> mobileDetail = new AtomicReference<>("");
        if(MapUtils.isNotEmpty(maps)){
            maps.forEach((key,value)->{
                if(StringUtils.isNotBlank(key)&&(value!=null)){
                    String newKey = key.substring(0,3)+"****"+key.substring(key.length()-4);
                    if(StringUtils.isNotBlank(phoneDetailRequest.getPhone())&&phoneDetailRequest.getPhone().equalsIgnoreCase(newKey)
                            &&Objects.equals(Integer.valueOf(value.toString()),phoneDetailRequest.getActiveDegree())){
                        mobileDetail.set(key);
                    }
                }
            });
        }
        return JsonResult.success(mobileDetail.get());
    }

    @RequestMapping("relation/mobile")
    public JsonResult relationMobile(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        return JsonResult.success(userDetailService.getRelationMobile(userHitRequest.getUserId(), userHitRequest.getStartTime(),userHitRequest.getEndTime()));
    }

    @RequestMapping("relation/ip")
    public JsonResult relationIP(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        return JsonResult.success(userDetailService.getRelationIP(userHitRequest.getUserId(), userHitRequest.getStartTime(),userHitRequest.getEndTime()));
    }

    @RequestMapping("relation/ip/list")
    public JsonResult relationIPList(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> data = userDetailService.getRelationIP(userHitRequest.getUserId(), userHitRequest.getStartTime(),userHitRequest.getEndTime());
        if (MapUtils.isNotEmpty(data)) {

            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String ip = entry.getKey();
                if (StringUtils.isNotBlank(ip)) {
                    Map<String, Object> da = new HashMap<>();
                    da.put("ip", ip);
                    da.put("count", entry.getValue());
                    da.put("detail", IPSearchService.getIpInfo(ip));
                    list.add(da);
                }
            }
        }
        return JsonResult.success(list);
    }

    @RequestMapping("risk/summary")
    public JsonResult riskSummary(@RequestBody UserDetailService.UserHitRequest userHitRequest) {
        return JsonResult.success(userDetailService.getRiskSummary(userHitRequest.getUserId(), userHitRequest.getStartTime(),userHitRequest.getEndTime()));
    }

    @RequestMapping("grayList")
    public JsonResult grayList(@RequestParam("uid") String uid) {

        List<String> values = Lists.newArrayListWithExpectedSize(2);
        if (StringUtils.isNotBlank(uid)) {
            values.add(uid);
            MobileInfo mobileInfo = userDetailService.getMobileInfoByUserId(uid);
            if(mobileInfo!=null&&StringUtils.isNotBlank(mobileInfo.getMobile())){
                values.add(mobileInfo.getMobile());
            }
        }

        return JsonResult.success(grayListService.searchByValueAndDimensions(values, Lists.newArrayList(Dimensions.USERID, Dimensions.MOBILENO)));
    }

    @RequestMapping("gray/group/all")
    public JsonResult grayGroupAll(GrayGroup record) {
        return JsonResult.success(grayGroupService.search(record));
    }

    @RequestMapping(value = "gray/list/add", method = RequestMethod.POST)
    public JsonResult grayListAdd(@Valid @RequestBody GrayList record) {
        Assert.hasText(record.getType(), "请指定名单类型");
        Assert.notNull(GrayListType.nameOf(record.getType()), "名单类型非法");
        Assert.hasText(record.getDimension(), "请指定名单维度");
        Assert.notNull(Dimensions.nameOf(record.getDimension()), "名单维度非法");
        Assert.hasText(record.getValue(), "请指定名单数据");
        Assert.notNull(record.getGroupId(), "请指定名单组");
        Assert.isTrue(CommonUtil.isExistInArr(grayGroupApi, record.getGroupId()), "名单组非法");
        if (record.getStartTime() == null) {
            record.setStartTime(new Date());
        }
        if (record.getExpireTime() == null) {
            record.setExpireTime(new Date(record.getStartTime().getTime() + configService.getDefaultExpiredTime() * 1000));
        } else {
            Assert.isTrue(record.getStartTime().before(record.getExpireTime()), "过期时间必须大于生效时间");
            Assert.isTrue(record.getExpireTime().after(new Date()), "过期时间必须大于当前时间");
        }
        record.setAuthor("api");
        return JsonResult.success(grayListService.save(record));
    }

    @RequestMapping("correction")
    @AuthRequired(permissionName = "system:detail:correction")
    public JsonResult correction(@CurrentUser User user,@RequestParam("uid") Long uid,@RequestBody DataCleanReq request) {
        //TODO 清洗
        Assert.isTrue(uid != null && uid > 0, "uid非法");
        return JsonResult.success(userDetailService.cleanData(user,uid.toString(),request));
    }

    @RequestMapping("deviceCleaning")
    @AuthRequired(permissionName = "system:detail:deviceCleaning")
    public JsonResult deviceCleaning(@CurrentUser User user, @RequestParam("deviceId") String deviceId) {
        log.info("接收到设备清洗请求, operator: {}, deviceId: {}", user.getChineseName(), deviceId);
        Assert.notNull(deviceId, "deviceId非法");
        boolean isSuccess = userDetailService.deviceCleaning(deviceId, user);
        if (isSuccess) {
            return JsonResult.success();
        } else {
            return JsonResult.error(new RiskException("画像清洗失败"));
        }
    }

    @RequestMapping("getChatroomInfoByRoomId")
    public JsonResult getChatroomListByRoomId(int appId, String roomId) {
        Assert.hasText(roomId, "房间ID不能为空");
        List<ChatroomInfoDTO> list = userDetailService.getChatroomInfoByRoomIds(appId, Lists.newArrayList(roomId));
        ChatroomInfoDTO infoDTO = null;
        if (list.size() > 0) {
            infoDTO = list.get(0);
        }
        return JsonResult.success(infoDTO);
    }

    @RequestMapping("getChatroomInfoByRoomNo")
    public JsonResult getChatroomInfoByRoomNo(int appId, Integer roomNo) {
        Assert.isTrue(roomNo != null && roomNo > 0, "房间短号非法");
        List<ChatroomInfoDTO> list = userDetailService.getChatroomInfoByRoomNos(appId, Lists.newArrayList(roomNo));
        ChatroomInfoDTO infoDTO = null;
        if (list.size() > 0) {
            infoDTO = list.get(0);
        }
        return JsonResult.success(infoDTO);
    }

    @RequestMapping("getChatroomListByRoomIds")
    public JsonResult getChatroomListByRoomIds(@RequestBody UserDetailService.ChatroomQuery chatroomQuery) {
        Assert.notEmpty(chatroomQuery.getRoomIds(), "房间ID不能为空");
        return JsonResult.success(userDetailService.getChatroomInfoByRoomIds(chatroomQuery.getAppId(), chatroomQuery.getRoomIds()));
    }

    @RequestMapping("getChatroomListByRoomNos")
    public JsonResult getChatroomListByRoomNos(@RequestBody UserDetailService.ChatroomQuery chatroomQuery) {
        Assert.notEmpty(chatroomQuery.getRoomNos(), "房间短号不能为空");
        return JsonResult.success(userDetailService.getChatroomInfoByRoomNos(chatroomQuery.getAppId(), chatroomQuery.getRoomNos()));
    }


    @RequestMapping("/queryCashJournal")
    public JsonResult cashJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.cashJournal(record);
        return JsonResult.success(pageInfo);
    }


    @RequestMapping("/queryIncomeJournal")
    public JsonResult incomeJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.incomeJournal(record);
        return JsonResult.success(pageInfo);
    }


    @RequestMapping("/queryDiamondJournal")
    public JsonResult diamondJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.diamondJournal(record);
        return JsonResult.success(pageInfo);
    }


    @RequestMapping("/queryCharmJournal")
    public JsonResult charmJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.charmJournal(record);
        return JsonResult.success(pageInfo);
    }


    @RequestMapping("/queryStarDiamondJournal")
    public JsonResult starDiamondJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.starDiamondJournal(record);
        return JsonResult.success(pageInfo);
    }


    @RequestMapping("/queryStarJournal")
    public JsonResult starJournal(@RequestBody JournalVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = payJournalService.starJournal(record);
        return JsonResult.success(pageInfo);
    }

    @RequestMapping("/orderRecord")
    public JsonResult orderRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = userProductionActivitiesQueryService.queryOrderRecord(record);
        return JsonResult.success(pageInfo);
    }

    @RequestMapping("/csdRecord")
    public JsonResult csdRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        PageInfo pageInfo = userProductionActivitiesQueryService.queryKFRecordList(record);
        return JsonResult.success(pageInfo);
    }

    @RequestMapping("/csdDetail")
    public JsonResult csdDetail(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        Response kfRecordQueryVOResponse = userProductionActivitiesQueryService.queryKFRecordDetail(record);
        return JsonResult.success(kfRecordQueryVOResponse);
    }

    @RequestMapping("/appealRecord")
    public JsonResult appealRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        Response<List<AppealRecordResponse>> response = userProductionActivitiesQueryService.queryAppealRecord(record);
        List result = new ArrayList();
        for (AppealRecordResponse appealRecordResponse : response.getResult()) {
            String images = appealRecordResponse.getImages();
            List imageList = new ArrayList();
            if (!StringUtils.isEmpty(images)){
                String[] split = images.split(",");
                for (int i = 0; i<split.length;i++) {
                    Map<String, Object> image = Maps.newHashMap();
                    image.put("id", i);
                    image.put("image", split[i]);
                    imageList.add(image);
                }
            }
            AppealRecordVO appealRecordVO = new AppealRecordVO();
            appealRecordVO.setAcceptType(appealRecordResponse.getAcceptType());
            appealRecordVO.setAcceptTypeName(appealRecordResponse.getAcceptTypeName());
            appealRecordVO.setAppealExplain(appealRecordResponse.getAppealExplain());
            appealRecordVO.setHandleCompleteTime(appealRecordResponse.getHandleCompleteTime());
            appealRecordVO.setCreateTime(appealRecordResponse.getCreateTime());
            appealRecordVO.setResult(appealRecordResponse.getResult());
            appealRecordVO.setResultType(appealRecordResponse.getResultType());
            appealRecordVO.setResultTypeName(appealRecordResponse.getResultTypeName());
            appealRecordVO.setType(appealRecordResponse.getType());
            appealRecordVO.setAppId(appealRecordResponse.getAppId());
            appealRecordVO.setImages(imageList);
            appealRecordVO.setTypeName(appealRecordResponse.getTypeName());
            result.add(appealRecordVO);
        }
        return JsonResult.success(result);
    }

    @RequestMapping("/punishRecord")
    public JsonResult punishRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        Response<List<PunishRecordResponse>> response = userProductionActivitiesQueryService.queryPunishRecord(record);
        return JsonResult.success(response);
    }

    @RequestMapping("/complainRecord")
    public JsonResult complainRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {
        Assert.hasText(record.getValue(), "uid不能为空");
        record.setPage(page);
        record.setSize(size);
        Response<List<ComplainUserInfoResponse>> response = userProductionActivitiesQueryService.queryComplainRecord(record);
        List result = new ArrayList<>();
        if(response!=null && CollectionUtils.isNotEmpty(response.getResult())){
            for (ComplainUserInfoResponse complainUserInfoResponse : response.getResult()) {
                ComplainUserInfoVO complainUserInfoVO = new ComplainUserInfoVO();
                complainUserInfoVO.setAuditTime(complainUserInfoResponse.getAuditTime());
                complainUserInfoVO.setBriefDesc(complainUserInfoResponse.getBriefDesc());
                complainUserInfoVO.setComplainTime(complainUserInfoResponse.getComplainTime());
                complainUserInfoVO.setFromUid(complainUserInfoResponse.getFromUid()+"");
                complainUserInfoVO.setHandleReasonCode(complainUserInfoResponse.getHandleReasonCode());
                complainUserInfoVO.setHandleReasonDesc(complainUserInfoResponse.getHandleReasonDesc());
                complainUserInfoVO.setReasonCode(complainUserInfoResponse.getReasonCode());
                complainUserInfoVO.setReasonDesc(complainUserInfoResponse.getReasonDesc());
                complainUserInfoVO.setResult(complainUserInfoResponse.getResult());
                complainUserInfoVO.setSourceCode(complainUserInfoResponse.getSourceCode());
                complainUserInfoVO.setSourceDesc(complainUserInfoResponse.getSourceDesc());
                complainUserInfoVO.setStatus(complainUserInfoResponse.getStatusDesc());
                complainUserInfoVO.setToUid(complainUserInfoResponse.getToUid()+"");
                complainUserInfoVO.setPicUrls(getPicUrls(complainUserInfoResponse.getPicUrls()));
                result.add(complainUserInfoVO);
            }
        }
        return JsonResult.success(result);
    }

    //todo
    @RequestMapping("/searchRecord")
    public JsonResult searchRecord(@RequestBody RiskHitLogVO record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size){
        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setStartTime(record.getStartTime());
        vo.setEndTime(record.getEndTime());
        vo.setPage(page);
        vo.setSize(size);

        HitResult query = new HitResult();
        query.setUserId(record.getValue());
        query.setEventCode("search");

        vo.setQuery(query);

        return JsonResult.success(this.userProductionActivitiesQueryService.querySearchRecordList(vo));
    }

    @OpRecordReport(opType = OpTypeEnum.QUERY, opDesc = "搜索明细导出",isReportResponse = false)
    @RequestMapping("download")
    public JsonResult download(@CurrentUser User user,@RequestBody RiskHitLogVO record){
        //本导出最多只能导出10000条
        Integer page = 1;
        Integer size = 10000;

        LogSearchVO.HitLogSearchVO vo = new LogSearchVO.HitLogSearchVO();
        vo.setStartTime(record.getStartTime());
        vo.setEndTime(record.getEndTime());
        vo.setPage(page);
        vo.setSize(size);

        HitResult query = new HitResult();
        query.setUserId(record.getValue());
        query.setEventCode("search");

        vo.setQuery(query);

        Map<String, ExcelFormatter> formatterMap = ExcelFormatterUtils.buildExcelFormatter(
                ExcelFormatter.builder("content").addName("搜索内容").build(),
                ExcelFormatter.builder("riskLevel").addName("机审结果").build(),
                ExcelFormatter.builder("time").addName("搜索时间").build()
        );

        try{
            String fileName = ExcelUtils.exportExcelByBean(ExportedSearchRecord.class,this.userProductionActivitiesQueryService.download(user,vo),"搜索明细表",formatterMap);
            return JsonResult.success(fileName);
        }catch(Throwable e){
            log.error("导出搜索明细异常",e);
            return JsonResult.error(ErrorMessage.SYSTEM_ERROR,e.getMessage());
        }

    }

    // 举报附加图片处理
    private List<Map<String, Object>> getPicUrls(String picUrls) {
        List<Map<String, Object>> rtnList = new ArrayList<>();
        try {
            if (StringUtils.isNotBlank(picUrls) && !picUrls.equals("null")
                    && picUrls.startsWith("[") && picUrls.endsWith("]")) {
                JSONArray array = JSON.parseArray(picUrls);
                if (array.size() > 0) {
                    for (int i = 0; i < array.size(); i++) {
                        String url = array.getString(i);
                        if (StringUtils.isNotBlank(url)) {
                            if (url.trim().toLowerCase().startsWith("http")) {
                                Map<String, Object> map = Maps.newHashMap();
                                map.put("image", url);
                                map.put("id", i);
                                rtnList.add(map);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("举报附加图片处理转换出错{}", picUrls);
        }
        return rtnList;
    }

    @Value("${analysis.url:[{\n" +
            "\t\"hisComplainUrl\": \"http://test-audit.yupaopao.com/#/record/queryReport\",\n" +
            "\t\"complainUrl\": \"http://test-audit.yupaopao.com/#/report/reportList\",\n" +
            "\t\"cashJournalUrl\": \"http://test-pay.yupaopao.com/account/details/balance-journal\",\n" +
            "\t\"incomeJournalUrl\": \"http://test-pay.yupaopao.com/account/details/income-journal\",\n" +
            "\t\"diamondJournalUrl\": \"http://test-pay.yupaopao.com/account/details/diamond-journal\",\n" +
            "\t\"charmJournalUrl\": \"http://test-pay.yupaopao.com/account/details/charm-journal\",\n" +
            "\t\"starDiamondJournalUrl\": \"http://test-pay.yupaopao.com/account/details/starDiamond-journal\",\n" +
            "\t\"starJournalUrl\": \"http://test-pay.yupaopao.com/account/details/star-journal\",\n" +
            "\t\"orderUrl\": \"http://test-order-tools.yupaopao.com/order/orderInfo.html\",\n" +
            "\t\"cdsUrl\": \"http://test-op.yupaopao.com/#/customer/16102\",\n" +
            "\t\"appealUrl\": \"http://audit.yupaopao.com/#/appeal/appealAccount\"\n" +
            "}]}")
    private String analysisMap;

    @RequestMapping("/queryAnalysisUrl")
    public JsonResult queryAnalysisUrl(){

        Map map;
        if (analysisMap.startsWith("[")){
            List list = JSONObject.parseObject(analysisMap, List.class);
            Object urlObj = list.get(0);
            map = JSONObject.parseObject(JSONObject.toJSONString(urlObj), Map.class);
        }else {
            map = JSONObject.parseObject(analysisMap, Map.class);
        }

        return JsonResult.success(map);
    }

    @Autowired
    private EventService eventService;
    private Map<String, String> eventCache;

    @RequestMapping("/queryEvent")
    public JsonResult queryEvent(@RequestParam(value = "code") String code){

        if ("all".equals(code)){
            return JsonResult.success(eventCache);
        }else {
            Event byCode = eventService.getByCode(code);
            if (Objects.nonNull(byCode) && StringUtils.isNotEmpty(byCode.getName())){
                eventCache.put(code, byCode.getName());
            }
            return JsonResult.success(eventCache);
        }
    }

    @PostConstruct
    private void initEventMap(){
        Map<String, Object> data = eventService.simpleAll();
        eventCache = (Map<String, String>) data.get("data");
    }

    // 是否聊天室主持人
    @RequestMapping("isChatroomHost")
    public JsonResult isChatroomHost(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.isChatroomHost(uid));
    }

    // 获取用户是否有聊天室官方房间、个人房间标签
    @RequestMapping("getChatroomRoomTag")
    public JsonResult getChatroomRoomTag(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.getChatroomRoomTag(uid));
    }

    // 是否鱼耳直播主播
    @RequestMapping("isAnchor")
    public JsonResult isAnchor(@RequestParam("uid") Long uid) {
        return JsonResult.success(userDetailService.isAnchor(uid));
    }

    // 是否是机器人
    @RequestMapping("isRobot")
    public JsonResult isRobot(@RequestParam("uid") Long uid) {
        Map<String,Boolean> robotMap = Maps.newHashMap();
        robotMap.put("liveRobot",userDetailService.isLiveRobot(uid));
        robotMap.put("yuerRobot",userDetailService.isYuerRobot(uid));
        return JsonResult.success(robotMap);
    }

    // 获取设备指纹数据
    @RequestMapping("deviceFinger")
    public JsonResult deviceFinger(@RequestParam("deviceId") String deviceId) {
        return JsonResult.success(userDetailService.deviceFinger(deviceId));
    }

    // 获取名单列表
    @RequestMapping("getGrayList")
    public JsonResult grayList(@RequestBody GrayList record) {
        return JsonResult.success(grayListService.select(record));
    }

    @RequestMapping("getLiveInfoByRoomId")
    public JsonResult getLiveListByRoomId(@RequestParam("roomId") Long roomId) {
        Assert.isTrue(roomId!=null && roomId>0, "房间ID非法");
        return JsonResult.success(userDetailService.getLiveInfoByRoomId(roomId));
    }

    @RequestMapping("getLiveInfoByLiveId")
    public JsonResult getLiveListByLiveId(@RequestParam("liveId") Long liveId) {
        Assert.isTrue(liveId!=null && liveId>0, "直播记录ID非法");
        return JsonResult.success(userDetailService.getLiveInfoByLiveId(liveId));
    }

    @RequestMapping("getLiveInfoByUid")
    public JsonResult getLiveListByUid(@RequestParam("uid") Long uid) {
        Assert.isTrue(uid!=null && uid>0, "主播UID非法");
        return JsonResult.success(userDetailService.getLiveInfoByUid(uid));
    }

    @RequestMapping("getCustomTimes")
    public JsonResult getCustomTimes() {
        return JsonResult.success(customTimes.split(","));
    }
}

