package com.yupaopao.risk.console.web.controller;

import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.console.service.RoleService;
import com.yupaopao.risk.console.vo.RolePermissionsVO;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 角色管理Controller
 *
 * <AUTHOR>
 * @date 2019-05-27
 */
@RestController
@RequestMapping("/role")
@Slf4j
@AuthRequired(permissionName = "system:role:all")
public class RoleController extends AbstractModelController<Role, Role, RoleService> {
    @RequestMapping(value = "{id}/permissions")
    @AuthRequired(role = {com.yupaopao.risk.common.enums.Role.NORMAL, com.yupaopao.risk.common.enums.Role.MANAGER, com.yupaopao.risk.common.enums.Role.CREDIT, com.yupaopao.risk.common.enums.Role.ADMIN})
    public JsonResult permissions(@PathVariable long id) {
        return JsonResult.success(getService().getPermissions(id));
    }


    @RequestMapping(value = "/update/{id}/permissions", method = RequestMethod.POST)
    public JsonResult updatePermissions(@PathVariable long id, @Valid @RequestBody RolePermissionsVO vo) {
        getService().updatePermissions(id,vo);
        return JsonResult.success();
    }

    @Override
    protected void addValid(Role record, User user) {

    }

    @Override
    protected void updateValid(Role record, User user) {

    }

}
