package com.yupaopao.risk.console.web.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.platform.common.utils.Md5Util;
import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.console.ConsoleConstants;
import com.yupaopao.risk.console.api.RiskWordService;
import com.yupaopao.risk.console.bean.RiskScene;
import com.yupaopao.risk.console.bean.RiskTag;
import com.yupaopao.risk.console.bean.RiskWord;
import com.yupaopao.risk.console.bean.RiskWordVO;
import com.yupaopao.risk.console.service.*;
import com.yupaopao.risk.console.utils.ObjectConversion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@Component
public class RiskWordServiceImpl implements RiskWordService {

    @Autowired
    private WordService wordService;

    @Autowired
    private WordTagService wordTagService;

    @Autowired
    private WordSceneService wordSceneService;

    @Autowired
    private TagService tagService;

    @Autowired
    private SceneService sceneService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;

    private Map<Long,List<RiskScene>> getScenes(List<Long> wordIds, Integer toSystem){

        Map<Long,List<RiskScene>> wordIdScenes = new ConcurrentHashMap<>();

        try{
            List<WordScene> wordScenes = this.wordSceneService.batchSearch(wordIds);
            if(CollectionUtils.isNotEmpty(wordScenes)){
                List<Long> sceneIds = wordScenes.stream().map(WordScene::getSceneId).collect(Collectors.toList());

                String key = String.format(ConsoleConstants.REDIS_CACHE_SCENE_LIST_PREFIX,toSystem);
                List<RiskScene> scenes = (List<RiskScene>)redisService.get(key);
                if(CollectionUtils.isEmpty(scenes)){
                    List<Scene> scenesDB = sceneService.findByToSystem(toSystem);
                    if(CollectionUtils.isNotEmpty(scenesDB)){
                        scenes = ObjectConversion.copy(scenesDB,RiskScene.class);
                        redisService.set(key,scenes,ConsoleConstants.REDIS_EXPIRE_TIME);
                    }
                }

                for(Long wordId:wordIds){
                    Set<Long> wordIdToSceneIds = new HashSet<>();
                    for(WordScene wordScene:wordScenes){
                        if(Objects.equals(wordScene.getWordId(),wordId)){
                            wordIdToSceneIds.add(wordScene.getSceneId());
                        }
                    }
                    List<RiskScene> riskScenes = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(wordIdToSceneIds)){
                        for(RiskScene scene:scenes){
                            if(wordIdToSceneIds.contains(scene.getId())){
                                riskScenes.add(scene);
                            }
                        }
                    }
                    wordIdScenes.put(wordId,riskScenes);
                }
            }
        }catch (Exception exp){
            log.error("获取场景失败",exp);
        }

        return wordIdScenes;
    }

    private List<RiskTag> getTagsByTagIdsAndToSystem(List<Long> tagIds){
        List<RiskTag> result = new ArrayList<>();

        List<Long> tagIdsDB = new ArrayList<>();
        for(Long tagId:tagIds){
            String key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_PREFIX,tagId);
            RiskTag riskTag = (RiskTag) redisService.get(key);
            if(riskTag!=null){
                result.add(riskTag);
            }else{
                tagIdsDB.add(tagId);
            }
        }

        if(CollectionUtils.isNotEmpty(tagIdsDB)){
            List<Tag> tags = this.tagService.batchSearch(tagIdsDB);
            if(CollectionUtils.isNotEmpty(tags)){
                List<RiskTag> riskTags = ObjectConversion.copy(tags,RiskTag.class);
                result.addAll(riskTags);
                for(RiskTag riskTag:riskTags){
                    String key = String.format(ConsoleConstants.REDIS_CACHE_SECOND_TAG_PREFIX,riskTag.getId());
                    redisService.set(key,riskTag,ConsoleConstants.REDIS_EXPIRE_TIME);
                }
            }
        }

        return result;

    }

    private Map<Long,List<RiskTag>> getSecondTags(List<Long> wordIds){
        Map<Long,List<RiskTag>> wordIdSecondTagIds = new ConcurrentHashMap<>();

        try{
            List<WordTag> wordTags = this.wordTagService.batchSearch(wordIds);
            if(CollectionUtils.isNotEmpty(wordTags)){
                List<Long> tagIds = wordTags.stream().map(WordTag::getTagId).collect(Collectors.toList());
//                List<Tag> tags = this.tagService.batchSearch(tagIds,toSystem);
                List<RiskTag> tags = this.getTagsByTagIdsAndToSystem(tagIds);

                for(Long wordId:wordIds){
                    List<Long> wordIdToTagIds = new ArrayList<>();
                    for(WordTag wordTag:wordTags){
                        if(Objects.equals(wordTag.getWordId(),wordId)){
                            wordIdToTagIds.add(wordTag.getTagId());
                        }
                    }
                    List<RiskTag> riskTags = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(wordIdToTagIds)){
                        for(RiskTag tag:tags){
                            if(wordIdToTagIds.contains(tag.getId())){
                                riskTags.add(tag);
                            }
                        }
                    }
                    wordIdSecondTagIds.put(wordId,riskTags);
                }
            }
        }catch (Exception exp){
            log.error("获取二级标签失败",exp);
        }

        return wordIdSecondTagIds;
    }

    private Map<Long,List<RiskTag>> getFirstTags( Map<Long,List<RiskTag>> wordIdToSecondTagsMap){
        Map<Long,List<RiskTag>> wordIdFirstTagIds = new ConcurrentHashMap<>();

        try{
            List<RiskTag> firstTags = (List<RiskTag>)redisService.get(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX);
            if(CollectionUtils.isEmpty(firstTags)){
                List<Tag> firstTagsDB = this.tagService.searchFirstTags();
                if(CollectionUtils.isNotEmpty(firstTagsDB)){
                    firstTags = ObjectConversion.copy(firstTagsDB,RiskTag.class);
                    redisService.set(ConsoleConstants.REDIS_CACHE_FIRST_TAG_LIST_PREFIX,firstTags,ConsoleConstants.REDIS_EXPIRE_TIME);
                }
            }

            if(MapUtils.isNotEmpty(wordIdToSecondTagsMap)){
                for(Map.Entry<Long,List<RiskTag>> entry:wordIdToSecondTagsMap.entrySet()){
                    Long wordId = entry.getKey();
                    List<RiskTag> secondTags = entry.getValue();
                    List<Long> firstIds = new ArrayList<>();
                    if(CollectionUtils.isNotEmpty(secondTags)){
                        firstIds = secondTags.stream().map(RiskTag::getpId).collect(Collectors.toList());
                    }

                    List<RiskTag> wordIdFirstTags = getTagsByIds(firstTags,new HashSet<>(firstIds));

                    wordIdFirstTagIds.put(wordId,wordIdFirstTags);
                }
            }

        }catch (Exception exp){
            log.error("获取一级标签失败",exp);
        }

        return wordIdFirstTagIds;
    }

    private List<RiskTag> getTagsByIds(List<RiskTag> tags,Set<Long> ids){
        List<RiskTag> result = new ArrayList<>();

        try{
            if(CollectionUtils.isNotEmpty(tags)&&CollectionUtils.isNotEmpty(ids)){
                for(Long id:ids){
                    for(RiskTag tag:tags){
                        if(Objects.equals(id,tag.getId())){
                            result.add(tag);
                            break;
                        }
                    }
                }
            }
        }catch(Exception exp){
            log.error("根据tagId列表获取tag实体列表失败",exp);
        }

        return result;
    }

    private List<RiskWordVO> loadRiskWordVO(List<String> wordContents, Integer toSystem){
        List<Word> wordsDB = wordService.batchSearch(wordContents);
        if(CollectionUtils.isNotEmpty(wordsDB)){
            List<Long> wordIds = wordsDB.stream().map(Word::getId).collect(Collectors.toList());

            Map<Long,List<RiskScene>>  wordIdToScenesMap = getScenes(wordIds,toSystem);
            if(MapUtils.isEmpty(wordIdToScenesMap)){
                return null;
            }

            Map<Long,List<RiskTag>> wordIdToSecondTagsMap = getSecondTags(wordIds);
            if(MapUtils.isEmpty(wordIdToSecondTagsMap)){
                return null;
            }

            Map<Long,List<RiskTag>> wordIdToFirstTagsMap = getFirstTags(wordIdToSecondTagsMap);
            if(MapUtils.isEmpty(wordIdToFirstTagsMap)){
                return null;
            }

            List<RiskWordVO> result = new ArrayList<>();
            try{
                for(Word word:wordsDB){
                    RiskWordVO riskWordVO = new RiskWordVO();

                    RiskWord riskWord = new RiskWord();
                    BeanUtils.copyProperties(riskWord,word);

                    List<RiskScene> scenes = wordIdToScenesMap.get(word.getId());
                    if(CollectionUtils.isEmpty(scenes)){
                        continue;
                    }

                    List<RiskTag> secondTags = wordIdToSecondTagsMap.get(word.getId());
                    if(CollectionUtils.isEmpty(secondTags)){
                        continue;
                    }

                    List<RiskTag> firstTags = wordIdToFirstTagsMap.get(word.getId());
                    if(CollectionUtils.isEmpty(firstTags)){
                        continue;
                    }

                    riskWordVO.setWord(riskWord);
                    riskWordVO.setScenes(scenes);
                    riskWordVO.setSecondTags(secondTags);
                    riskWordVO.setFirstTags(firstTags);

                    result.add(riskWordVO);
                }

                return result;

            }catch (Exception exp){
                log.error("Word实体转RiskWord实体失败",exp);
            }
        }

        return null;
    }

    @Override
    public List<RiskWordVO> search(List<String> wordContents, Integer toSystem) {

        List<RiskWordVO> result = new ArrayList<>();

        if(CollectionUtils.isNotEmpty(wordContents)){
            List<String> wordContentsDB = new ArrayList<>();

            for(String wordContent:wordContents){
                String key = String.format(ConsoleConstants.REDIS_CACHE_WORD_PREFIX,String.valueOf(toSystem)+":"+Md5Util.md5(wordContent));

                RiskWordVO riskWordVO = (RiskWordVO)redisService.get(key);
                if(riskWordVO!=null){
                    result.add(riskWordVO);
                }else{
                    wordContentsDB.add(wordContent);
                }
            }

            //从DB中查询结果
            if(CollectionUtils.isNotEmpty(wordContentsDB)){
                List<RiskWordVO> riskWordVosDB = loadRiskWordVO(wordContentsDB,toSystem);
                if(CollectionUtils.isNotEmpty(riskWordVosDB)){
                    for(RiskWordVO riskWordVO:riskWordVosDB){
                        String key = String.format(ConsoleConstants.REDIS_CACHE_WORD_PREFIX,String.valueOf(toSystem)+":"+Md5Util.md5(riskWordVO.getWord().getContent()));
                        redisService.set(key,riskWordVO,ConsoleConstants.REDIS_EXPIRE_TIME);
                    }

                    result.addAll(riskWordVosDB);
                }
            }

        }

        return result;
    }

}
