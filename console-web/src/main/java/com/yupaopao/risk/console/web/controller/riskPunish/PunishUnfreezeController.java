package com.yupaopao.risk.console.web.controller.riskPunish;

import com.google.common.collect.Lists;
import com.yupaopao.bixin.user.operation.api.service.AccountInfoApi;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.common.RiskException;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.common.utils.Assert;
import com.yupaopao.risk.console.service.UnfreezeCenterService;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import com.yupaopao.risk.console.web.support.annotaion.CurrentUser;
import com.yupaopao.risk.punish.api.RiskPunishConfigService;
import com.yupaopao.risk.punish.api.RiskPunishService;
import com.yupaopao.risk.punish.manage.PunishConfigService;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import com.yupaopao.risk.punish.request.SearchPunishPkgRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;


/**
 * 解冻中心
 *
 * <AUTHOR>
 * @date 2021/9/18 11:40
 */
@RestController
@Slf4j
@RequestMapping("/riskPunish/unfreeze")
@AuthRequired(permissionName = "riskPunish:unfreeze:all")
public class PunishUnfreezeController {

    private static final String CHANNEL = "REALTIME_RISK_UNFREEZE_CENTER";
    @DubboReference(check = false, timeout = 3000)
    private RiskPunishConfigService riskPunishConfigService;
    @DubboReference(check = false, timeout = 3000)
    private RiskPunishService riskPunishService;
    @Autowired
    private UnfreezeCenterService unfreezeCenterService;
    @DubboReference(check = false, timeout = 3000)
    private PunishConfigService punishConfigService;
    @DubboReference(check = false, timeout = 3000)
    public AccountInfoApi accountInfoApi;

    @RequestMapping("getPunishPackages")
    public JsonResult getPunishPackages() {
        SearchPunishPkgRequest request = new SearchPunishPkgRequest();
        request.setChannel(CHANNEL);
        return assembleResp(riskPunishConfigService.searchPunishPackagesSingleLayer(request));
    }

    @RequestMapping("punish")
    public JsonResult punish(@RequestBody UnfreezeCenterService.UnfreezeRequest param, @CurrentUser User user) {
        Assert.hasLength(param.getProofImgUrl(), "请上传图片凭证！");
        BatchPunishRequest request = new BatchPunishRequest();
        request.setChannel(CHANNEL);
        request.setPackageId(param.getPackageId());
        request.setUid(param.getUid());
        request.setDeviceId(param.getDeviceId());
        request.setRoomId(param.getRoomId());
        request.setInternalReason(param.getInternalReason());
        request.setExternalReason(param.getExternalReason());
        request.setOperator(user.getName());
        request.setImages(Lists.newArrayList(param.getProofImgUrl()));
        return assembleResp(riskPunishService.batchPunish(request));
    }

    /**
     * 选择惩罚包
     *
     * @param pid
     * @return
     */
    @RequestMapping("selPkg")
    public JsonResult selPkg(@RequestParam("pid") Long pid) {
        //查询惩罚包需要的惩罚对象 返回map格式
        Set<String> set = punishConfigService.getObjIdsByPkgId(pid);
        if (set == null) {
            throw new RiskException("惩罚包信息获取失败，请刷新后再试！");
        }
        if (set.size() == 0) {
            throw new RiskException("请确认该惩罚包中含可执行惩罚项！");
        }
        return JsonResult.success(set);
    }

    /**
     * 查询用户账号冻结状态 资质冻结状态
     * 查询登录设备列表
     *
     * @param param
     * @return
     */
    @RequestMapping("info")
    public JsonResult getUserInfo(@RequestBody UnfreezeCenterService.UserInfoRequest param) {
        UnfreezeCenterService.UserInfoResponse response = unfreezeCenterService.getUserInfo(param);
        if (response == null) {
            return JsonResult.error(new RiskException("未查询到该用户信息"));
        }
        return JsonResult.success(response);
    }

    /**
     * 获取手机号地区编码列表
     *
     * @return
     */
    @RequestMapping("nationCode")
    public JsonResult nationCode() {
        return assembleResp(accountInfoApi.getMobileNationCodeList());
    }

    /**
     * Response对象结果集通用处理
     *
     * @param response
     * @return
     */
    private JsonResult assembleResp(Response<?> response) {
        if (response == null) {
            return JsonResult.error(new RiskException("网络异常，请重试"));
        }
        if (response.isSuccess()) {
            return JsonResult.success(response.getResult());
        } else {
            return JsonResult.error(new RiskException(response.getMsg()));
        }
    }
}
