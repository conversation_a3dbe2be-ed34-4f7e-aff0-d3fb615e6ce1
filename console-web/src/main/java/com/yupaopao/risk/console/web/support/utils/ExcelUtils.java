package com.yupaopao.risk.console.web.support.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.model.User;
import com.yupaopao.risk.console.bean.TemplateWord;
import com.yupaopao.risk.console.enums.ImportExcelErrorType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel相关处理
 *
 * <AUTHOR>
 * @date 2019-4-29
 */
public class ExcelUtils {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtils.class);

    /**
     * 将list中的数据，按照指定的格式化器，组装成excel文件并存储到磁盘上
     *
     * @param list         导出数据集合
     * @param excelName    工作表的名称
     * @param formatterMap 需要导出的所有列的格式化器
     * @return 文件名称
     */
    public static String exportExcelByMap(List<Map<String, Object>> list, String excelName, Map<String, ExcelFormatter> formatterMap) throws Exception {
        OutputStream out = null;
        XSSFWorkbook workbook = null;
        try {
            // 产生工作薄对象
            workbook = new XSSFWorkbook();
            // excel2003中每个sheet中最多有65536行
            int sheetSize = 65536;
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                // 产生工作表对象
                XSSFSheet sheet = workbook.createSheet();
                //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                if (sheetNo == 0) {
                    workbook.setSheetName(index, excelName);
                } else {
                    // 设置工作表的名称.
                    workbook.setSheetName(index, excelName + index);
                }
                XSSFRow row;
                XSSFCell cell; // 产生单元格

                // 产生一行
                row = sheet.createRow(0);
                //列号，x轴
                int x = 0;
                // 写入各个字段的列头名称
                for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                    // 创建列
                    cell = row.createCell(x);
                    // 设置列中写入内容为String类型
                    cell.setCellType(CellType.STRING);
                    XSSFCellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                    XSSFFont font = workbook.createFont();
                    // 粗体显示
                    font.setBold(true);
                    // 选择需要用到的字体格式
                    cellStyle.setFont(font);
                    XSSFColor color = new XSSFColor(new java.awt.Color(215,228,188));
                    cellStyle.setFillForegroundColor(color);
                    // 设置列宽
                    sheet.setColumnWidth(x, (int) ((entry.getValue().getWidth() + 0.72) * 256));
                    //row.setHeight((short) (attr.height() * 20));

                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cellStyle.setWrapText(true);
                    cell.setCellStyle(cellStyle);

                    // 写入列名
                    cell.setCellValue(entry.getValue().getName());

                    x++;
                }

                int startNo = index * sheetSize;
                int endNo = Math.min(startNo + sheetSize, list.size());
                // 写入各条记录,每条记录对应excel表中的一行
                XSSFCellStyle cs = workbook.createCellStyle();
                cs.setAlignment(HorizontalAlignment.CENTER);
                cs.setVerticalAlignment(VerticalAlignment.CENTER);
                for (int i = startNo; i < endNo; i++) {
                    row = sheet.createRow(i + 1 - startNo);
                    // 得到导出对象.
                    Map<String, Object> map = list.get(i);
                    x = 0;
                    for (Map.Entry<String, ExcelFormatter> formatterEntry : formatterMap.entrySet()) {
                        ExcelFormatter formatter = formatterEntry.getValue();
                        if (formatter == null) {
                            continue;
                        }
                        cell = row.createCell(x);
                        cell.setCellStyle(cs);
                        cell.setCellType(CellType.STRING);

                        for (Map.Entry<String, Object> entry : map.entrySet()) {
                            if (formatter.getKey().equals(entry.getKey())) {
                                if ("image".equals(formatter.getType())) {
                                    Object value = entry.getValue() == null ? null : entry.getValue();
                                    if (value == null || !String.valueOf(value).contains("http")) {
                                        cell.setCellValue(entry.getValue() == null ? "" : String.valueOf(entry.getValue()));
                                        continue;
                                    }
                                    List<String> images = parseImages(value);
                                    int y = 0;
                                    row.setHeightInPoints(200);
                                    for (String image : images) {
                                        if (StringUtils.isNotBlank(image) && image.contains("http")) {
                                            //anchor主要用于设置图片的属性
                                            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 1023, 255, (short) (x + y), (i + 1 - startNo), (short) (x + y), (i + 1 - startNo));
                                            y++;
                                            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                                            try {
                                                //插入图片
                                                patriarch.createPicture(anchor, workbook.addPicture(getFileStream(image + "?imageslim"), XSSFWorkbook.PICTURE_TYPE_JPEG));
                                            } catch (Exception e) {
                                                log.error("导出Excel 插入图片异常", e);
                                            }
                                        }
                                    }

                                } else if (entry.getValue() instanceof Date && StringUtils.isNotEmpty(formatter.getDateFormat())) {
                                    cell.setCellValue(new SimpleDateFormat(formatter.getDateFormat()).format((Date) entry.getValue()));
                                } else {
                                    cell.setCellValue(entry.getValue() == null ? "" : String.valueOf(entry.getValue()));
                                }
                            }
                        }
                        x++;
                    }
                }
            }
            String filename = encodingFilename(excelName);
            out = new FileOutputStream(FileUtils.getAbsoluteFile(filename));
            workbook.write(out);
            return filename;
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new Exception("导出Excel失败，请联系网站管理员！");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    private static List<String> parseImages(Object value) {
        List<String> images = new ArrayList<>();
        if (value instanceof List) {
            images = (List<String>) value;
        } else {
            if (String.valueOf(value).startsWith("[") || String.valueOf(value).endsWith("[")) {
                images = JSON.parseArray(value.toString(), String.class);
            } else {
                images.add(value.toString());
            }
        }
        return images;
    }

    public static byte[] getFileStream(String url) {
        try {
            URL httpUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) httpUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5 * 1000);
            InputStream inStream = conn.getInputStream();//通过输入流获取图片数据
            byte[] btImg = readInputStream(inStream);//得到图片的二进制数据
            return btImg;
        } catch (Exception e) {
            log.error("导出Excel 获取文件流异常", e);
        }
        return null;
    }

    /**
     * 从输入流中获取数据
     *
     * @param inStream 输入流
     * @return
     * @throws Exception
     */
    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        try {
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            inStream.close();
            byte[] btImg = outStream.toByteArray();
            return btImg;
        } catch (Exception e) {
            if (outStream != null) {
                outStream.close();
            }
        }
        return null;
    }

    /**
     * 将list中的数据组装成excel文件并存储到磁盘上
     *
     * @param list      导出数据集合
     * @param excelName 工作表的名称
     * @return 文件名称
     */

    public static String exportExcelByBean(Class clazz, List list, String excelName, Map<String, ExcelFormatter> formatterMap) throws Exception {
        OutputStream out = null;
        HSSFWorkbook workbook = null;
        try {
            // 得到所有定义字段
            Field[] allFields = clazz.getDeclaredFields();
            Map<String, Field> allFieldsMap = Arrays.stream(allFields).collect(Collectors.toMap(Field::getName, f -> f));
            List<Field> fields = new ArrayList<Field>();
            // 得到所有field并存放到一个list中

            for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                if (allFieldsMap.containsKey(entry.getKey())) {
                    Field field = allFieldsMap.get(entry.getKey());
                    field.setAccessible(true);
                    fields.add(field);
                }
            }
            // 产生工作薄对象
            workbook = new HSSFWorkbook();
            // excel2003中每个sheet中最多有65536行
            int sheetSize = 65536;
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                // 产生工作表对象
                HSSFSheet sheet = workbook.createSheet();
                if (sheetNo == 0) {
                    workbook.setSheetName(index, excelName);
                } else {
                    // 设置工作表的名称.
                    workbook.setSheetName(index, excelName + index);
                }
                HSSFRow row;
                HSSFCell cell; // 产生单元格

                // 产生一行
                row = sheet.createRow(0);
                //列号，x轴
                int x = 0;
                // 写入各个字段的列头名称
                for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                    // 创建列
                    cell = row.createCell(x);
                    // 设置列中写入内容为String类型
                    cell.setCellType(CellType.STRING);
                    HSSFCellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                    HSSFFont font = workbook.createFont();
                    // 粗体显示
                    font.setBold(true);
                    // 选择需要用到的字体格式
                    cellStyle.setFont(font);
                    cellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_YELLOW.getIndex());
                    // 设置列宽
                    sheet.setColumnWidth(x, (int) ((entry.getValue().getWidth() + 0.72) * 256));

                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cellStyle.setWrapText(true);
                    cell.setCellStyle(cellStyle);

                    // 写入列名
                    cell.setCellValue(entry.getValue().getName());

                    x++;
                }

                int startNo = index * sheetSize;
                int endNo = Math.min(startNo + sheetSize, list.size());
                // 写入各条记录,每条记录对应excel表中的一行
                HSSFCellStyle cs = workbook.createCellStyle();
                cs.setAlignment(HorizontalAlignment.CENTER);
                cs.setVerticalAlignment(VerticalAlignment.CENTER);
                for (int i = startNo; i < endNo; i++) {
                    row = sheet.createRow(i + 1 - startNo);
                    // 得到导出对象.
                    Object vo = list.get(i);
                    x = 0;
                    for (Field field : fields) {
                        ExcelFormatter formatter = formatterMap.get(field.getName());
                        if (formatter == null) {
                            continue;

                        }
                        cell = row.createCell(x);
                        cell.setCellStyle(cs);
                        cell.setCellType(CellType.STRING);
                        Object value = field.get(vo);
                        if (value instanceof Date && StringUtils.isNotEmpty(formatter.getDateFormat())) {
                            cell.setCellValue(new SimpleDateFormat(formatter.getDateFormat()).format((Date) value));
                        } else if (MapUtils.isNotEmpty(formatter.getConverter())) {
                            Object o = formatter.getConverter().get(value);
                            if (o == null) {
                                cell.setCellValue("");
                            } else {
                                cell.setCellValue(o.toString());
                            }
                        } else {
                            cell.setCellValue(value == null ? "" : String.valueOf(value));
                        }
                        x++;
                    }
                }
            }
            String filename = encodingFilename(excelName);
            out = new FileOutputStream(FileUtils.getAbsoluteFile(filename));
            workbook.write(out);
            return filename;
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new Exception("导出Excel失败，请联系网站管理员！");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 将list中的数据组装成excel文件并存储到磁盘上
     *
     * @param list      导出数据集合
     * @param excelName 工作表的名称
     * @return 文件名称
     */
    public static String exportTemplatByBean(Class clazz, List list, String excelName, Map<String, ExcelFormatter> formatterMap) throws Exception {
        OutputStream out = null;
        XSSFWorkbook workbook = null;
        try {
            // 得到所有定义字段
            Field[] allFields = clazz.getDeclaredFields();
            Map<String, Field> allFieldsMap = Arrays.stream(allFields).collect(Collectors.toMap(Field::getName, f -> f));
            List<Field> fields = new ArrayList<Field>();
            // 得到所有field并存放到一个list中

            for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                if (allFieldsMap.containsKey(entry.getKey())) {
                    Field field = allFieldsMap.get(entry.getKey());
                    field.setAccessible(true);
                    fields.add(field);
                }
            }
            // 产生工作薄对象
            workbook = new XSSFWorkbook();
            // excel2003中每个sheet中最多有65536行
            int sheetSize = 65536;
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                // 产生工作表对象
                XSSFSheet sheet = workbook.createSheet();
                if (sheetNo == 0) {
                    workbook.setSheetName(index, excelName);
                } else {
                    // 设置工作表的名称.
                    workbook.setSheetName(index, excelName + index);
                }
                XSSFRow row;
                XSSFCell cell; // 产生单元格

                // 产生一行
                row = sheet.createRow(0);
                //列号，x轴
                int x = 0;
                // 写入各个字段的列头名称
                for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                    // 创建列
                    cell = row.createCell(x);
                    // 设置列中写入内容为String类型
                    cell.setCellType(CellType.STRING);
                    XSSFCellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                    XSSFFont font = workbook.createFont();
                    // 粗体显示
                    font.setBold(true);
                    // 选择需要用到的字体格式
                    cellStyle.setFont(font);

                    XSSFColor color = new XSSFColor(new java.awt.Color(215,228,188));
                    cellStyle.setFillForegroundColor(color);
                    // 设置列宽
                    sheet.setColumnWidth(x, (int) ((entry.getValue().getWidth() + 0.72) * 256));

                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cellStyle.setWrapText(true);
                    cell.setCellStyle(cellStyle);

                    // 写入列名
                    cell.setCellValue(entry.getValue().getName());

                    x++;
                }

                int startNo = index * sheetSize;
                int endNo = Math.min(startNo + sheetSize, list.size());
                // 写入各条记录,每条记录对应excel表中的一行
                XSSFCellStyle cs = workbook.createCellStyle();
                cs.setAlignment(HorizontalAlignment.CENTER);
                cs.setVerticalAlignment(VerticalAlignment.CENTER);
                for (int i = startNo; i < endNo; i++) {
                    row = sheet.createRow(i + 1 - startNo);
                    // 得到导出对象.
                    Object vo = list.get(i);
                    x = 0;
                    for (Field field : fields) {
                        ExcelFormatter formatter = formatterMap.get(field.getName());
                        if (formatter == null) {
                            continue;

                        }
                        cell = row.createCell(x);
                        cell.setCellStyle(cs);
                        cell.setCellType(CellType.STRING);
                        Object value = field.get(vo);
                        if (value instanceof Date && StringUtils.isNotEmpty(formatter.getDateFormat())) {
                            cell.setCellValue(new SimpleDateFormat(formatter.getDateFormat()).format((Date) value));
                        } else if (MapUtils.isNotEmpty(formatter.getConverter())) {
                            Object o = formatter.getConverter().get(value);
                            if (o == null) {
                                cell.setCellValue("");
                            } else {
                                cell.setCellValue(o.toString());
                            }
                        } else {
                            cell.setCellValue(value == null ? "" : String.valueOf(value));
                        }
                        x++;
                    }
                }
            }
            String filename = encodingFilename(excelName);
            out = new FileOutputStream(FileUtils.getAbsoluteFile(filename));
            workbook.write(out);
            return filename;
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new Exception("导出Excel失败，请联系网站管理员！");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    /**
     * 检测excel文件的合法性
     * @param clazz
     * @param url
     * @param formatterMap
     * @param maxRowCount
     * @return
     * @throws Exception
     */
    public static ImportExcelErrorType checkExcelByBean(Class clazz, String url, Map<String, ExcelFormatter> formatterMap,Integer maxRowCount) {
        XSSFWorkbook workbook = null;
        try {
            String suffer = url.substring(url.lastIndexOf('/')+1).split("\\.")[1];
            if (!"xls".equals(suffer)&&!"xlsx".equals(suffer)) {
                return ImportExcelErrorType.FILE_FORMAT_ERROR;
            }

            // 产生工作薄对象
//            InputStream inputStream = getInputStreamByUrl(url);
//            URL realUrl = new URL(url);
            InputStream inputStream = new URL(url).openStream();
            workbook = new XSSFWorkbook(inputStream);
            inputStream.close();

            // 判断sheet的数量的合法性
            int sheetNumber = workbook.getNumberOfSheets();
            if(sheetNumber==0){
                return ImportExcelErrorType.SHEET_COUNT_ZERO;
            }else if(sheetNumber>1){
                return ImportExcelErrorType.SHEET_COUNT_GREATER_THAN_ONE;
            }

            // 判断总行数的合法性
            XSSFSheet sheet = workbook.getSheetAt(0);
            int rowNumber = sheet.getLastRowNum();
            if(rowNumber==0){
                return ImportExcelErrorType.HEADER_ROW_COUNT_ZERO;
            }else if(rowNumber==1){
                return ImportExcelErrorType.CONTENT_ROW_COUNT_ZERO;
            }else if(rowNumber>maxRowCount){
                return ImportExcelErrorType.ROW_COUNT_ERROR;
            }

            // 判断总列数的合法性
            // 生成期望的列名
            List<String> columnNames = Lists.newArrayList();

            Map<String, Field> allFieldsMap = Arrays.stream(clazz.getDeclaredFields()).collect(Collectors.toMap(Field::getName, f -> f));
            for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                if (allFieldsMap.containsKey(entry.getKey())) {
                    columnNames.add(entry.getValue().getName());
                }
            }

            if(sheet.getRow(0).getLastCellNum()!=formatterMap.size()){
                return ImportExcelErrorType.COLUMN_COUNT_ERROR;
            }

            for(int index=0;index<sheet.getRow(0).getPhysicalNumberOfCells();index++){
                String columnHeaderName = sheet.getRow(0).getCell(index).getStringCellValue();
                if(!columnNames.contains(columnHeaderName)){
                    return ImportExcelErrorType.COLUMN_NAME_ERROR;
                }
            }

            return null;
        } catch (Exception e) {
            log.error("检测批量导入违禁词Excel异常", e);
            return ImportExcelErrorType.PARSE_EXCEL_ERROR;
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    private static boolean isFloat(String content){
        if(NumberUtils.isNumber(content)){
            return content.indexOf('.') != -1;
        }

        return false;
    }

    public static boolean isInteger(String str){
        boolean b = true;
        try{
            Integer.valueOf(str);
        }catch(Exception e){
            b = false;
        }
        return b;
    }

    /**
     * 检测excel文件的合法性
     * @param clazz
     * @param excelUrl
     * @param formatterMap
     * @return
     * @throws Exception
     */
    public static Map<String,Object> parseExcelByBean(Class clazz, User user,String excelUrl, Map<String, ExcelFormatter> formatterMap) {

        Map<String,Object> parseResult = Maps.newHashMap();

        List<TemplateWord> rightText = Lists.newArrayList();
        List<Integer>  errorTextRows = Lists.newArrayList();
        List<Integer> errorContentRows = Lists.newArrayList();

        Map<String,ColumnAtom> columnAtomMap = getColumnAtoms(clazz, formatterMap);

        XSSFWorkbook workbook = null;
        try {
            // 产生工作薄对象
            InputStream inputStream = getInputStreamByUrl(excelUrl);
            workbook = new XSSFWorkbook(inputStream);

            XSSFSheet sheet = workbook.getSheetAt(0);
            XSSFRow firstRow = sheet.getRow(0);

            for(int rIndex = 1; rIndex<=sheet.getLastRowNum(); rIndex++){
                XSSFRow row = sheet.getRow(rIndex);

                TemplateWord templateWord = new TemplateWord();
                for(int cIndex=0;cIndex<sheet.getRow(0).getLastCellNum();cIndex++){
                    String chineseName =  firstRow.getCell(cIndex).getStringCellValue();
                    ColumnAtom columnAtom = columnAtomMap.get(chineseName);

                    String contentStr = "";
                    XSSFCell cell = row.getCell(cIndex);
                    if(null != cell){
                        switch (cell.getCellType()){
                            case XSSFCell.CELL_TYPE_NUMERIC:
                                contentStr = row.getCell(cIndex).getNumericCellValue()+"";
                                break;
                            case XSSFCell.CELL_TYPE_STRING:
                                contentStr = row.getCell(cIndex).getStringCellValue()+"";
                                break;
                            case XSSFCell.CELL_TYPE_BOOLEAN:
                                contentStr = row.getCell(cIndex).getBooleanCellValue()+"";
                                break;
                            case XSSFCell.CELL_TYPE_FORMULA:
                                contentStr = row.getCell(cIndex).getCellFormula()+"";
                                break;
                            case XSSFCell.CELL_TYPE_BLANK:
                                contentStr = "";
                                break;
                            case XSSFCell.CELL_TYPE_ERROR:
                                contentStr = "";
                                break;
                            default:
                                log.error("解析excel存在未知类型!");
                                break;
                        }
                    }

                    if(columnAtom.getField().getType()==String.class){
                        if("content".equalsIgnoreCase(columnAtom.getField().getName())){
                            if(contentStr.startsWith("|")){
                                errorContentRows.add(rIndex);
                                break;
                            }else{
                                templateWord.setContent(contentStr);
                            }
                        }else if("toSystem".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setToSystem(contentStr);
                        }else if("wordLocationTypeName".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setWordLocationTypeName(contentStr);
                        }else if("firstTagNames".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setFirstTagNames(contentStr);
                        }else if("tagNames".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setTagNames(contentStr);
                        }else if("sceneNames".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setSceneNames(contentStr);
                        }else if("matchTypeName".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setMatchTypeName(contentStr);
                        }else if("source".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setSource(contentStr);
                        }else if("comment".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setComment(contentStr);
                        }else if("author".equalsIgnoreCase(columnAtom.getField().getName())){
                            templateWord.setAuthor(contentStr);
                        }else{
                            errorTextRows.add(rIndex);
                            break;
                        }
                    }else if(columnAtom.getField().getType()==Integer.class){
                        if(isInteger(contentStr)){
                            Integer weight =  Integer.parseInt(contentStr);
                            if(weight>10||weight<1){
                                errorTextRows.add(rIndex);
                                break;
                            }else{
                                templateWord.setWeight(Integer.parseInt(contentStr));
                            }
                        }else if(isFloat(contentStr)){
                            String[] segments = contentStr.split("\\.");

                            Integer decimalSeg = Integer.parseInt(segments[1]);
                            if(decimalSeg>0){
                                errorTextRows.add(rIndex);
                                break;
                            }

                            Integer integerSeg = Integer.parseInt(segments[0]);
                            if(integerSeg>10||integerSeg<1){
                                errorTextRows.add(rIndex);
                                break;
                            }else{
                                templateWord.setWeight(integerSeg);
                            }
                        }else{
                            errorTextRows.add(rIndex);
                            break;
                        }
                    }
                }

                templateWord.setAuthor(user.getChineseName());
                rightText.add(templateWord);
            }

            parseResult.put("rightRows",rightText);
            parseResult.put("errorRows",errorTextRows);
            parseResult.put("errorContentRows",errorContentRows);

            return parseResult;
        } catch (IllegalStateException e){
            log.error("excel表中不是所有的单元格格式都是字符串",e);
            return null;
        }catch (Exception e) {
            log.error("检测批量导入违禁词Excel异常", e);
            return null;
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    public static Map<String,ColumnAtom> getColumnAtoms(Class clazz, Map<String, ExcelFormatter> formatterMap){
        Map<String,ColumnAtom> columnAtomMap = Maps.newHashMap();

        // 得到所有定义字段
        Field[] allFields = clazz.getDeclaredFields();
        Map<String, Field> allFieldsMap = Arrays.stream(allFields).collect(Collectors.toMap(Field::getName, f -> f));

        for(Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()){
            String fieldCode = entry.getKey();
            ExcelFormatter formatter = entry.getValue();
            Field field = allFieldsMap.get(fieldCode);
            ColumnAtom columnAtom = new ColumnAtom(field,formatter);

            columnAtomMap.put(formatter.getName(),columnAtom);
        }

        return columnAtomMap;
    }

    /**
     * 将list中的数据，按照指定的格式化器，组装成excel文件并存储到磁盘上
     *
     * @param list         导出数据集合
     * @param excelName    工作表的名称
     * @param formatterMap 需要导出的所有列的格式化器
     * @return 文件名称
     */
    public static String exportExcelWithDepth(List<Map<String, Object>> list, String excelName, Map<String, ExcelFormatter> formatterMap) throws Exception {
        OutputStream out = null;
        HSSFWorkbook workbook = null;
        try {
            // 产生工作薄对象
            workbook = new HSSFWorkbook();
            // excel2003中每个sheet中最多有65536行
            int sheetSize = 65536;
            // 取出一共有多少个sheet.
            double sheetNo = Math.ceil(list.size() / sheetSize);
            for (int index = 0; index <= sheetNo; index++) {
                // 产生工作表对象
                HSSFSheet sheet = workbook.createSheet();
                if (sheetNo == 0) {
                    workbook.setSheetName(index, excelName);
                } else {
                    // 设置工作表的名称.
                    workbook.setSheetName(index, excelName + index);
                }
                HSSFRow row;
                HSSFCell cell; // 产生单元格

                // 产生一行
                row = sheet.createRow(0);
                //列号，x轴
                int x = 0;
                // 写入各个字段的列头名称
                for (Map.Entry<String, ExcelFormatter> entry : formatterMap.entrySet()) {
                    // 创建列
                    cell = row.createCell(x);
                    // 设置列中写入内容为String类型
                    cell.setCellType(CellType.STRING);
                    HSSFCellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                    HSSFFont font = workbook.createFont();
                    // 粗体显示
                    font.setBold(true);
                    // 选择需要用到的字体格式
                    cellStyle.setFont(font);
                    cellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_YELLOW.getIndex());
                    // 设置列宽
                    sheet.setColumnWidth(x, (int) ((entry.getValue().getWidth() + 0.72) * 256));
                    //row.setHeight((short) (attr.height() * 20));

                    cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                    cellStyle.setWrapText(true);
                    cell.setCellStyle(cellStyle);

                    // 写入列名
                    cell.setCellValue(entry.getValue().getName());

                    x++;
                }

                int startNo = index * sheetSize;
                int endNo = Math.min(startNo + sheetSize, list.size());
                // 写入各条记录,每条记录对应excel表中的一行
                HSSFCellStyle cs = workbook.createCellStyle();
                cs.setAlignment(HorizontalAlignment.CENTER);
                cs.setVerticalAlignment(VerticalAlignment.CENTER);
                for (int i = startNo; i < endNo; i++) {
                    row = sheet.createRow(i + 1 - startNo);
                    // 得到导出对象.
                    Map<String, Object> map = list.get(i);
                    x = 0;
                    for (Map.Entry<String, ExcelFormatter> formatterEntry : formatterMap.entrySet()) {
                        ExcelFormatter formatter = formatterEntry.getValue();
                        if (formatter == null) {
                            continue;
                        }
                        cell = row.createCell(x);
                        cell.setCellStyle(cs);
                        cell.setCellType(CellType.STRING);

                        String[] keys = formatter.getKey().split("\\.");
                        int len = keys.length;
                        cell.setCellValue(getCellValue(keys, map, formatter, 0, len));
                        x++;
                    }
                }
            }
            String filename = encodingFilename(excelName);
            out = new FileOutputStream(FileUtils.getAbsoluteFile(filename));
            workbook.write(out);
            return filename;
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new Exception("导出Excel失败，请联系网站管理员！");
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
    }

    private static String getCellValue(String[] keys, Map<String, Object> map, ExcelFormatter formatter, int i, int len) {
        String key = keys[i];
        Object value = MapUtils.getObject(map, key);
        if (null != value) {
            if (i == len - 1) {
                if (value instanceof Date && StringUtils.isNotEmpty(formatter.getDateFormat())) {
                    return new SimpleDateFormat(formatter.getDateFormat()).format((Date) value);
                } else {
                    return value == null ? "" : String.valueOf(value);
                }
            } else if (value instanceof Map) {
                return getCellValue(keys, (Map<String, Object>) value, formatter, i + 1, len);
            }
        }
        return "";
    }

    /**
     * 设置单元格上提示
     *
     * @param sheet         要设置的sheet.
     * @param promptTitle   标题
     * @param promptContent 内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     * @return 设置好的sheet.
     */
    public static HSSFSheet setHSSFPrompt(HSSFSheet sheet, String promptTitle, String promptContent, int firstRow,
                                          int endRow, int firstCol, int endCol) {
        // 构造constraint对象
        DVConstraint constraint = DVConstraint.createCustomFormulaConstraint("DD1");
        // 四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        HSSFDataValidation dataValidationView = new HSSFDataValidation(regions, constraint);
        dataValidationView.createPromptBox(promptTitle, promptContent);
        sheet.addValidationData(dataValidationView);
        return sheet;
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public static HSSFSheet setHSSFValidation(HSSFSheet sheet, String[] textlist, int firstRow, int endRow,
                                              int firstCol, int endCol) {
        // 加载下拉列表内容
        DVConstraint constraint = DVConstraint.createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        // 数据有效性对象
        HSSFDataValidation dataValidationList = new HSSFDataValidation(regions, constraint);
        sheet.addValidationData(dataValidationList);
        return sheet;
    }

    /**
     * 解析导出值的映射关系
     *
     * @param propertyValue 参数值
     * @param converterExp  翻译注解
     * @return 解析后值
     * @throws Exception
     */
    public static String convertByExp(String propertyValue, String converterExp) throws Exception {
        try {
            String[] convertSource = converterExp.split(",");
            for (String item : convertSource) {
                String[] itemArray = item.split("=");
                if (itemArray[0].equals(propertyValue)) {
                    return itemArray[1];
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return propertyValue;
    }

    /**
     * 编码文件名
     */
    public static String encodingFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xls";
        return filename;
    }

    public static InputStream getInputStreamByUrl(String excelUrl){
        HttpURLConnection conn = null;
        try {
            URL url = new URL(excelUrl);
            conn = (HttpURLConnection)url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(20 * 1000);
            final ByteArrayOutputStream output = new ByteArrayOutputStream();
            IOUtils.copy(conn.getInputStream(),output);
            return  new ByteArrayInputStream(output.toByteArray());
        } catch (Exception e) {
            log.error(e+"");
        }finally {
            try{
                if (conn != null) {
                    conn.disconnect();
                }
            }catch (Exception e){
                log.error(e+"");
            }
        }
        return null;
    }

}

@Setter
@Getter
@AllArgsConstructor
class ColumnAtom{

    private Field field;
    private ExcelFormatter excelFormatter;

}