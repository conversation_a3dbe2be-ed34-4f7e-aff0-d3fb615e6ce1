package com.yupaopao.risk.console.web.controller;

import com.github.pagehelper.PageInfo;
import com.yupaopao.platform.passport.response.MobileInfo;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.risk.common.model.GrayGroup;
import com.yupaopao.risk.common.model.GrayList;
import com.yupaopao.risk.console.bean.GrayListRequest;
import com.yupaopao.risk.console.service.GrayGroupService;
import com.yupaopao.risk.console.service.GrayListService;
import com.yupaopao.risk.console.service.UserDetailService;
import com.yupaopao.risk.console.vo.GrayListVo;
import com.yupaopao.risk.console.web.support.JsonResult;
import com.yupaopao.risk.console.web.support.annotaion.AuthRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static com.yupaopao.risk.console.service.UserDetailService.UserDimension.MOBILENO;

@RestController
@RequestMapping("/customer")
@AuthRequired(permissionName = "core:customerGrayList:all")
@Slf4j
public class CustomerGrayListController extends AbstractModelController<GrayList, GrayList, GrayListService>{

    @Autowired
    protected GrayListService grayListService;
    @Autowired
    private UserDetailService userDetailService;
    @Autowired
    private GrayGroupService grayGroupService;

    @Value("${customer.support.enable.delete.gray.group:8}")
    private String deleteGrayGroup;

    @RequestMapping("/searchGrayList")
    public JsonResult searchGrayList(@RequestBody GrayListRequest record, @RequestParam(value = "p", defaultValue = "1") Integer page, @RequestParam(value = "s", defaultValue = "10") Integer size) {

        List<GrayList> records = Lists.newArrayList();
        Set<String> deviceIds = new HashSet<>();

        if(record.getDimension()!=null && StringUtils.isNotEmpty(record.getValue())){
            //由UID 或 YPPNO 或 MOBILENO 查询用户信息
            UserDetailService.UserQuery userQuery = new UserDetailService.UserQuery();
            userQuery.setDimension(record.getDimension());
            userQuery.setValue(record.getValue());
            UserInfoDTO userInfo = userDetailService.getUserInfo(userQuery);
            if(userInfo!=null&&userInfo.getUid()!=null){
                GrayList uIdGray=new GrayList();
                uIdGray.setValue(userInfo.getUid().toString());
                uIdGray.setDimension("USERID");
                records.add(uIdGray);
                Date now = new Date();
                Map<String, Object> deviceMap = userDetailService.getRelationDevice(userInfo.getUid().toString(), DateUtils.addDays(now,-7), now);
                if(MapUtils.isNotEmpty(deviceMap)){
                    for(String deviceId:deviceMap.keySet()){
                        deviceIds.add(deviceId);
                    }
                }
            }

            String mobile = record.getValue();
            if(record.getDimension()!=MOBILENO){
                MobileInfo mobileInfo = userDetailService.getMobileInfoByUserId(userInfo == null?"":userInfo.getUid().toString());
                if(mobileInfo!=null){
                    mobile = mobileInfo.getMobile();
                }
            }

            if(StringUtils.isNotBlank(mobile)){
                Map<String,String> result = userDetailService.getNationCodeAndContent(mobile);
                if(StringUtils.isNotBlank(result.get("content"))){
                    GrayList mobileGray=new GrayList();
                    mobileGray.setValue(result.get("content"));
                    mobileGray.setDimension("MOBILENO");
                    records.add(mobileGray);
                }
            }
        }

        if(StringUtils.isNotBlank(record.getDeviceId())){
           deviceIds.add(record.getDeviceId());
        }

        if(CollectionUtils.isNotEmpty(deviceIds)){
            for(String deviceId:deviceIds){
                GrayList deviceIdGray=new GrayList();
                deviceIdGray.setValue(deviceId);
                deviceIdGray.setDimension("DEVICEID");
                records.add(deviceIdGray);
            }
        }

        PageInfo<GrayList> search = grayListService.search(records,page,size);

        List<GrayListVo> grayListVos = new ArrayList<>();

        List<GrayList> grayLists = search.getList();
        if(CollectionUtils.isNotEmpty(grayLists)){
            if(StringUtils.isNotBlank(deleteGrayGroup)){
                List<String> grayGroupIds = Arrays.asList(this.deleteGrayGroup.split(","));

                if(CollectionUtils.isNotEmpty(grayGroupIds)){
                    for(GrayList grayList:grayLists){
                        GrayListVo grayListVo = new GrayListVo(grayList);
                        if(grayGroupIds.contains(grayList.getGroupId().toString())){
                            grayListVo.setShow(true);
                        }

                        grayListVos.add(grayListVo);
                    }
                }
            }else{
                for(GrayList grayList:grayLists){
                    GrayListVo grayListVo = new GrayListVo(grayList);
                    grayListVos.add(grayListVo);
                }
            }
        }

        PageInfo<GrayListVo> result = new PageInfo<>();
        try{
            BeanUtils.copyProperties(result,search);
            result.setList(grayListVos);
        }catch (Exception exp){
            log.error("转换运营名单发生异常:{}",exp.getMessage());
        }

        return JsonResult.success(result);
    }

    @RequestMapping("/gray/group/all")
    public JsonResult all(GrayGroup record) {
        return JsonResult.success(grayGroupService.search(record));
    }

}
