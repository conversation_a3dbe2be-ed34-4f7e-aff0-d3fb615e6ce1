package com.yupaopao.risk.console.web.support;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.console.bean.OperationLog;
import com.yupaopao.risk.console.utils.RiskLogESUtil;
import com.yupaopao.risk.console.web.config.BodyReaderHttpServletRequestWrapper;
import com.yupaopao.risk.console.web.listener.OperationLogEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Slf4j
@ControllerAdvice(annotations = RestController.class)
public class ConsoleResponseBodyAdvice implements ResponseBodyAdvice {

    @Autowired
    private ApplicationEventPublisher publisher;

    @Value("${maxResponseLength:10000}")
    private int maxResponseLength;

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        return true;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        try {

            HttpServletRequest request = ((ServletServerHttpRequest) serverHttpRequest).getServletRequest();
            //过滤error请求
            if (request.getRequestURI().equals("/error")) {
                return o;
            }

            String ipChain = request.getHeader("x-forwarded-for");
            if (StringUtils.isBlank(ipChain)) {
                ipChain = request.getHeader("x-real-ip");
            }

            Object requestParam;
            if (request instanceof BodyReaderHttpServletRequestWrapper) {
                requestParam = ((BodyReaderHttpServletRequestWrapper) request).getBodyString();
            } else {
                requestParam = request.getParameterMap();
            }
            //response处理，过长时截取 默认长度1W
            String response = handleResponse(o);
            //异步记录操作日志
            Date createTime = new Date();
            String createAt = DateFormatUtils.format(createTime, RiskLogESUtil.DATE_FORMART);
            String queryString = request.getQueryString();
            StringBuffer url = request.getRequestURL();
            if (StringUtils.isNotEmpty(queryString)) {
                url.append("?").append(queryString);
            }

            publisher.publishEvent(new OperationLogEvent(new OperationLog((serverHttpRequest.getPrincipal() == null ? "未登录" : serverHttpRequest.getPrincipal().getName())
                    , request.getRequestURI(), request.getMethod(), handleResponse(requestParam.toString()), createTime, createAt, response, url.toString(), ipChain,
                    request.getHeader("user-agent"))));
        } catch (Exception e) {
            log.error("操作记录事件发送异常", e);
        }
        return o;
    }

    private String handleResponse(Object o) {
        String response = JSON.toJSONString(o);
        return response.length() > maxResponseLength ? response.substring(0, maxResponseLength) : response;
    }
}

