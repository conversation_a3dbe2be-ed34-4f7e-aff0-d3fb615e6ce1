package com.yupaopao.risk.parser.generator;

import com.yupaopao.risk.parser.model.*;
import com.yupaopao.risk.common.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 风控组件生成器
 * 基于解析后的策略数据生成风控系统的各个组件
 */
@Slf4j
@Component
public class RiskComponentGenerator {
    
    @Autowired
    private ComponentReuseService componentReuseService;
    
    /**
     * 生成完整的风控配置
     */
    public GeneratedRiskConfig generateRiskConfig(ParsedStrategy strategy) {
        log.info("开始生成风控配置，事件: {}", strategy.getEventInfo().getName());
        
        GeneratedRiskConfig config = new GeneratedRiskConfig();
        
        // 1. 生成或复用事件
        Event event = generateOrReuseEvent(strategy);
        config.setEvent(event);
        
        // 2. 生成因子
        List<Factor> factors = generateFactors(strategy);
        config.setFactors(factors);
        
        // 3. 生成属性
        List<Attribute> attributes = generateAttributes(strategy, factors);
        config.setAttributes(attributes);
        
        // 4. 生成策略原子
        List<AtomRule> atomRules = generateAtomRules(strategy, attributes);
        config.setAtomRules(atomRules);
        
        // 5. 生成策略组
        GroupRule groupRule = generateGroupRule(strategy, atomRules);
        config.setGroupRule(groupRule);
        
        // 6. 更新事件关联
        event.setRuleGroupId(groupRule.getId().toString());
        
        log.info("风控配置生成完成，包含 {} 个因子，{} 个属性，{} 个策略原子", 
                factors.size(), attributes.size(), atomRules.size());
        
        return config;
    }
    
    /**
     * 生成或复用事件
     */
    private Event generateOrReuseEvent(ParsedStrategy strategy) {
        EventInfo eventInfo = strategy.getEventInfo();
        
        // 检查是否已存在相同的事件
        Event existingEvent = componentReuseService.findEventByCode(eventInfo.getCode());
        if (existingEvent != null) {
            log.info("复用已存在的事件: {}", existingEvent.getName());
            return existingEvent;
        }
        
        // 创建新事件
        Event event = new Event();
        event.setName(eventInfo.getName());
        event.setCode(eventInfo.getCode());
        event.setBusinessCode(eventInfo.getBusinessCode());
        event.setAccessBy(eventInfo.getAccessBy());
        event.setComment(eventInfo.getComment());
        event.setDefaultLevel("PASS");
        event.setCanPunish(true);
        event.setRequiresResponse(true);
        event.setAccessMode(1);
        event.setTypes("1"); // 活动类型
        
        log.info("创建新事件: {}", event.getName());
        return event;
    }
    
    /**
     * 生成因子
     */
    private List<Factor> generateFactors(ParsedStrategy strategy) {
        List<Factor> factors = new ArrayList<>();
        
        for (AccumulationCondition condition : strategy.getAccumulationConditions()) {
            // 检查是否已存在相似的因子
            Factor existingFactor = componentReuseService.findSimilarFactor(
                    condition.getDimension(), condition.getTimeWindow());
            
            if (existingFactor != null) {
                log.info("复用已存在的因子: {}", existingFactor.getComment());
                factors.add(existingFactor);
                continue;
            }
            
            // 创建新因子
            Factor factor = new Factor();
            factor.setGroupKey(buildGroupKey(condition, strategy.getEventInfo().getCode()));
            factor.setAggKey("UserId");
            factor.setFunction("COUNT_DISTINCT");
            factor.setTimeSpan((long) condition.getTimeWindow());
            factor.setCondition(buildFactorCondition(strategy.getEventInfo().getCode()));
            factor.setComment(buildFactorComment(condition, strategy.getEventInfo().getName()));
            factor.setBusiness(1);
            factor.setWindowType(1);
            
            factors.add(factor);
            log.info("创建新因子: {}", factor.getComment());
        }
        
        return factors;
    }
    
    /**
     * 生成属性
     */
    private List<Attribute> generateAttributes(ParsedStrategy strategy, List<Factor> factors) {
        List<Attribute> attributes = new ArrayList<>();
        
        // 为每个因子生成对应的LOCAL属性
        for (int i = 0; i < factors.size(); i++) {
            Factor factor = factors.get(i);
            AccumulationCondition condition = strategy.getAccumulationConditions().get(i);
            
            String attributeName = buildAttributeName(condition, strategy.getEventInfo().getCode());
            
            // 检查是否已存在相同的属性
            Attribute existingAttribute = componentReuseService.findAttributeByName(attributeName);
            if (existingAttribute != null) {
                log.info("复用已存在的属性: {}", existingAttribute.getName());
                attributes.add(existingAttribute);
                continue;
            }
            
            // 创建新的LOCAL属性
            Attribute attribute = new Attribute();
            attribute.setName(attributeName);
            attribute.setType("LOCAL");
            attribute.setDependent(buildAttributeDependent(factor, condition));
            attribute.setFunction("getFactorValue");
            attribute.setResultKey("");
            
            attributes.add(attribute);
            log.info("创建新属性: {}", attribute.getName());
        }
        
        // 生成用户数据属性（如果需要）
        if (hasUserLevelConditions(strategy)) {
            Attribute userDataAttr = componentReuseService.findAttributeByName("userData");
            if (userDataAttr == null) {
                userDataAttr = createUserDataAttribute();
                log.info("创建用户数据属性: userData");
            } else {
                log.info("复用用户数据属性: userData");
            }
            attributes.add(userDataAttr);
        }
        
        return attributes;
    }
    
    /**
     * 生成策略原子
     */
    private List<AtomRule> generateAtomRules(ParsedStrategy strategy, List<Attribute> attributes) {
        List<AtomRule> atomRules = new ArrayList<>();
        
        for (int i = 0; i < strategy.getAccumulationConditions().size(); i++) {
            AccumulationCondition condition = strategy.getAccumulationConditions().get(i);
            
            AtomRule atomRule = new AtomRule();
            atomRule.setName(buildAtomRuleName(condition, strategy.getEventInfo().getName()));
            atomRule.setPriority(0);
            atomRule.setAccuracy(100);
            atomRule.setStatus("DISABLE"); // 初始状态为禁用
            atomRule.setCondition(buildAtomRuleCondition(condition, attributes));
            atomRule.setDependent(buildAtomRuleDependent(condition, attributes));
            atomRule.setReply(condition.getOuterReason() != null ? condition.getOuterReason() : "系统检测到异常行为");
            atomRule.setInnerReason(condition.getInnerReason() != null ? condition.getInnerReason() : "触发累计阈值检测");
            atomRule.setOuterReason(condition.getOuterReason() != null ? condition.getOuterReason() : "系统检测到异常行为");
            atomRule.setCanFuse(true);
            atomRule.setType(0);
            atomRule.setValidTime(0);
            
            atomRules.add(atomRule);
            log.info("创建策略原子: {}", atomRule.getName());
        }
        
        return atomRules;
    }
    
    /**
     * 生成策略组
     */
    private GroupRule generateGroupRule(ParsedStrategy strategy, List<AtomRule> atomRules) {
        GroupRule groupRule = new GroupRule();
        groupRule.setName(strategy.getEventInfo().getName() + "-检测");
        groupRule.setPriority(0);
        groupRule.setCondition("");
        groupRule.setDependent("");
        groupRule.setStatus("DISABLE"); // 初始状态为禁用
        groupRule.setComment("自动生成的策略组");
        groupRule.setReply("");
        groupRule.setBusinessCode("");
        
        log.info("创建策略组: {}", groupRule.getName());
        return groupRule;
    }
    
    // 辅助方法
    private String buildGroupKey(AccumulationCondition condition, String eventCode) {
        return "Event," + condition.getDimension() + ",type";
    }
    
    private String buildFactorCondition(String eventCode) {
        return String.format("#Event=='%s' && (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')", eventCode);
    }
    
    private String buildFactorComment(AccumulationCondition condition, String eventName) {
        String dimension = condition.getDimension().equals("DeviceId") ? "设备" : 
                          condition.getDimension().equals("ClientIp") ? "IP" : "用户";
        int hours = condition.getTimeWindow() / 60;
        return String.format("%s内同%s参与%s人数", hours + "小时", dimension, eventName);
    }
    
    private String buildAttributeName(AccumulationCondition condition, String eventCode) {
        String prefix = condition.getDimension().equals("DeviceId") ? "Device" : 
                       condition.getDimension().equals("ClientIp") ? "Ip" : "User";
        int hours = condition.getTimeWindow() / 60;
        return String.format("comm%sCountUidBy%sType%sh", prefix, 
                condition.getDimension().replace("Id", ""), hours);
    }
    
    private String buildAttributeDependent(Factor factor, AccumulationCondition condition) {
        return String.format("\"%d\"::::Event::::%s::::type", factor.getId(), condition.getDimension());
    }
    
    private boolean hasUserLevelConditions(ParsedStrategy strategy) {
        return !strategy.getAttributeConditions().isEmpty();
    }
    
    private Attribute createUserDataAttribute() {
        Attribute attribute = new Attribute();
        attribute.setName("userData");
        attribute.setType("REMOTE");
        attribute.setDependent("UserId::::cache+=");
        attribute.setFunction("userData");
        attribute.setResultKey("*");
        return attribute;
    }
    
    private String buildAtomRuleName(AccumulationCondition condition, String eventName) {
        String dimension = condition.getDimension().equals("DeviceId") ? "设备" : 
                          condition.getDimension().equals("ClientIp") ? "IP" : "用户";
        return String.format("%s-%s聚集检测", eventName, dimension);
    }
    
    private String buildAtomRuleCondition(AccumulationCondition condition, List<Attribute> attributes) {
        StringBuilder conditionBuilder = new StringBuilder();
        
        // 添加用户等级条件（如果有）
        conditionBuilder.append("userData != null && userData.vipLevel < 12 && userData.nobilityLevel < 3 && userData.planetUserLevel < 11");
        
        // 添加累计条件
        String attributeName = buildAttributeName(condition, "");
        conditionBuilder.append(" && ").append(attributeName)
                       .append(condition.getOperator())
                       .append(condition.getThreshold());
        
        return conditionBuilder.toString();
    }
    
    private String buildAtomRuleDependent(AccumulationCondition condition, List<Attribute> attributes) {
        List<String> dependencies = new ArrayList<>();
        dependencies.add("userData");
        dependencies.add(buildAttributeName(condition, ""));
        return String.join(",", dependencies);
    }
}
