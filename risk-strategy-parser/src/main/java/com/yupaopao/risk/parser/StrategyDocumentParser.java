package com.yupaopao.risk.parser;

import com.yupaopao.risk.parser.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 策略文档解析器
 * 解析策略文档，提取各种检测模块、阈值条件、属性条件等
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class StrategyDocumentParser {

    // 正则表达式模式
    private static final Pattern EVENT_PATTERN = Pattern.compile("名称：(.+?)\\s+code：(.+?)\\s");
    private static final Pattern THRESHOLD_PATTERN = Pattern.compile("(\\d+)、.*?([><=]+)(\\d+).*?(REJECT|REVIEW|PASS)");
    private static final Pattern TIME_WINDOW_PATTERN = Pattern.compile("(\\d+)(天|小时|分钟|日)内?");
    private static final Pattern DEVICE_PATTERN = Pattern.compile("设备|同设备|设备模型");
    private static final Pattern IP_PATTERN = Pattern.compile("IP|同IP");
    private static final Pattern USER_LEVEL_PATTERN = Pattern.compile("(MVP|比心vip|爵位|星爵)\\s*([><=]+)\\s*(\\d+)");
    private static final Pattern COUNT_PATTERN = Pattern.compile("人数|次数|账号");
    
    /**
     * 解析策略文档
     */
    public ParsedStrategy parseDocument(String document) {
        log.info("开始解析策略文档");
        
        ParsedStrategy strategy = new ParsedStrategy();
        
        // 1. 解析事件信息
        parseEventInfo(document, strategy);
        
        // 2. 解析检测模块
        parseDetectionModules(document, strategy);
        
        // 3. 解析累计条件
        parseAccumulationConditions(document, strategy);
        
        // 4. 解析属性条件
        parseAttributeConditions(document, strategy);
        
        // 5. 解析时间窗口
        parseTimeWindows(document, strategy);
        
        log.info("策略文档解析完成，共解析出 {} 个检测模块", strategy.getDetectionModules().size());
        
        return strategy;
    }
    
    /**
     * 解析事件信息
     */
    private void parseEventInfo(String document, ParsedStrategy strategy) {
        Matcher matcher = EVENT_PATTERN.matcher(document);
        if (matcher.find()) {
            EventInfo eventInfo = new EventInfo();
            eventInfo.setName(matcher.group(1).trim());
            eventInfo.setCode(matcher.group(2).trim());
            
            // 解析业务代码（默认为activity）
            eventInfo.setBusinessCode("activity");
            
            strategy.setEventInfo(eventInfo);
            log.info("解析到事件信息: name={}, code={}", eventInfo.getName(), eventInfo.getCode());
        }
    }
    
    /**
     * 解析检测模块
     */
    private void parseDetectionModules(String document, ParsedStrategy strategy) {
        List<DetectionModule> modules = new ArrayList<>();
        
        // 设备检测
        if (document.contains("设备检测") || DEVICE_PATTERN.matcher(document).find()) {
            DetectionModule deviceModule = new DetectionModule();
            deviceModule.setType(DetectionType.DEVICE);
            deviceModule.setName("设备检测");
            deviceModule.setDescription("检测设备相关的风险行为");
            modules.add(deviceModule);
        }
        
        // IP检测
        if (document.contains("IP检测") || IP_PATTERN.matcher(document).find()) {
            DetectionModule ipModule = new DetectionModule();
            ipModule.setType(DetectionType.IP);
            ipModule.setName("IP检测");
            ipModule.setDescription("检测IP相关的风险行为");
            modules.add(ipModule);
        }
        
        // 名单检测
        if (document.contains("名单检测") || document.contains("黑名单") || document.contains("白名单")) {
            DetectionModule listModule = new DetectionModule();
            listModule.setType(DetectionType.BLACKLIST);
            listModule.setName("名单检测");
            listModule.setDescription("检测黑白名单");
            modules.add(listModule);
        }
        
        // 账号检测
        if (document.contains("账号检测") || document.contains("用户等级")) {
            DetectionModule accountModule = new DetectionModule();
            accountModule.setType(DetectionType.ACCOUNT);
            accountModule.setName("账号检测");
            accountModule.setDescription("检测账号相关的风险行为");
            modules.add(accountModule);
        }
        
        // 业务检测
        if (document.contains("业务检测") || document.contains("活动")) {
            DetectionModule businessModule = new DetectionModule();
            businessModule.setType(DetectionType.BUSINESS);
            businessModule.setName("业务检测");
            businessModule.setDescription("检测业务相关的风险行为");
            modules.add(businessModule);
        }
        
        // 行为检测
        if (document.contains("行为检测") || document.contains("频次")) {
            DetectionModule behaviorModule = new DetectionModule();
            behaviorModule.setType(DetectionType.BEHAVIOR);
            behaviorModule.setName("行为检测");
            behaviorModule.setDescription("检测用户行为模式");
            modules.add(behaviorModule);
        }
        
        strategy.setDetectionModules(modules);
        log.info("解析到 {} 个检测模块", modules.size());
    }
    
    /**
     * 解析累计条件
     */
    private void parseAccumulationConditions(String document, ParsedStrategy strategy) {
        List<AccumulationCondition> conditions = new ArrayList<>();
        
        String[] lines = document.split("\n");
        for (String line : lines) {
            Matcher thresholdMatcher = THRESHOLD_PATTERN.matcher(line);
            if (thresholdMatcher.find()) {
                AccumulationCondition condition = new AccumulationCondition();
                condition.setRuleNumber(Integer.parseInt(thresholdMatcher.group(1)));
                condition.setOperator(thresholdMatcher.group(2));
                condition.setThreshold(Integer.parseInt(thresholdMatcher.group(3)));
                condition.setAction(thresholdMatcher.group(4));
                condition.setDescription(line.trim());
                
                // 解析维度
                if (DEVICE_PATTERN.matcher(line).find()) {
                    condition.setDimension("DeviceId");
                } else if (IP_PATTERN.matcher(line).find()) {
                    condition.setDimension("ClientIp");
                } else {
                    condition.setDimension("UserId");
                }
                
                // 解析时间窗口
                Matcher timeWindowMatcher = TIME_WINDOW_PATTERN.matcher(line);
                if (timeWindowMatcher.find()) {
                    int timeValue = Integer.parseInt(timeWindowMatcher.group(1));
                    String timeUnit = timeWindowMatcher.group(2);
                    condition.setTimeWindow(convertToMinutes(timeValue, timeUnit));
                }
                
                conditions.add(condition);
                log.info("解析到累计条件: {}", condition.getDescription());
            }
        }
        
        strategy.setAccumulationConditions(conditions);
    }
    
    /**
     * 解析属性条件
     */
    private void parseAttributeConditions(String document, ParsedStrategy strategy) {
        List<AttributeCondition> conditions = new ArrayList<>();
        
        Matcher userLevelMatcher = USER_LEVEL_PATTERN.matcher(document);
        while (userLevelMatcher.find()) {
            AttributeCondition condition = new AttributeCondition();
            condition.setAttributeName(mapUserLevelAttribute(userLevelMatcher.group(1)));
            condition.setOperator(userLevelMatcher.group(2));
            condition.setValue(Integer.parseInt(userLevelMatcher.group(3)));
            condition.setDescription(userLevelMatcher.group(0));
            
            conditions.add(condition);
            log.info("解析到属性条件: {}", condition.getDescription());
        }
        
        strategy.setAttributeConditions(conditions);
    }
    
    /**
     * 解析时间窗口
     */
    private void parseTimeWindows(String document, ParsedStrategy strategy) {
        Set<Integer> timeWindows = new HashSet<>();
        
        Matcher timeWindowMatcher = TIME_WINDOW_PATTERN.matcher(document);
        while (timeWindowMatcher.find()) {
            int timeValue = Integer.parseInt(timeWindowMatcher.group(1));
            String timeUnit = timeWindowMatcher.group(2);
            int minutes = convertToMinutes(timeValue, timeUnit);
            timeWindows.add(minutes);
        }
        
        strategy.setTimeWindows(new ArrayList<>(timeWindows));
        log.info("解析到时间窗口: {}", timeWindows);
    }
    
    /**
     * 转换时间单位为分钟
     */
    private int convertToMinutes(int value, String unit) {
        switch (unit) {
            case "分钟":
                return value;
            case "小时":
                return value * 60;
            case "天":
            case "日":
                return value * 24 * 60;
            default:
                return value * 24 * 60; // 默认按天计算
        }
    }
    
    /**
     * 映射用户等级属性
     */
    private String mapUserLevelAttribute(String levelType) {
        switch (levelType) {
            case "MVP":
            case "比心vip":
                return "vipLevel";
            case "爵位":
                return "nobilityLevel";
            case "星爵":
                return "planetUserLevel";
            default:
                return "userLevel";
        }
    }
}
