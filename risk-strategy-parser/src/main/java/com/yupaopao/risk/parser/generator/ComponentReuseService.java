package com.yupaopao.risk.parser.generator;

import com.yupaopao.risk.common.model.*;
import com.yupaopao.risk.console.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 组件复用服务
 * 检查数据库中是否已存在相同或相似的组件，优先复用现有组件
 */
@Slf4j
@Service
public class ComponentReuseService {
    
    @Autowired
    private EventService eventService;
    
    @Autowired
    private FactorService factorService;
    
    @Autowired
    private AttributeService attributeService;
    
    @Autowired
    private AtomRuleService atomRuleService;
    
    @Autowired
    private GroupRuleService groupRuleService;
    
    /**
     * 根据事件代码查找已存在的事件
     */
    public Event findEventByCode(String eventCode) {
        try {
            Event event = eventService.getByCode(eventCode);
            if (event != null) {
                log.info("找到已存在的事件: code={}, name={}", eventCode, event.getName());
                return event;
            }
        } catch (Exception e) {
            log.debug("未找到事件: {}", eventCode);
        }
        return null;
    }
    
    /**
     * 根据事件名称查找已存在的事件
     */
    public Event findEventByName(String eventName) {
        try {
            Event event = eventService.getByName(eventName);
            if (event != null) {
                log.info("找到已存在的事件: name={}, code={}", eventName, event.getCode());
                return event;
            }
        } catch (Exception e) {
            log.debug("未找到事件: {}", eventName);
        }
        return null;
    }
    
    /**
     * 查找相似的因子
     * 基于维度和时间窗口匹配
     */
    public Factor findSimilarFactor(String dimension, Integer timeWindow) {
        try {
            // 构建查询条件
            FactorReq req = new FactorReq();
            List<Factor> factors = factorService.list(req);
            
            for (Factor factor : factors) {
                if (isSimilarFactor(factor, dimension, timeWindow)) {
                    log.info("找到相似的因子: id={}, comment={}", factor.getId(), factor.getComment());
                    return factor;
                }
            }
        } catch (Exception e) {
            log.error("查找相似因子时出错", e);
        }
        return null;
    }
    
    /**
     * 根据属性名称查找已存在的属性
     */
    public Attribute findAttributeByName(String attributeName) {
        try {
            List<Attribute> attributes = attributeService.selectAll();
            for (Attribute attribute : attributes) {
                if (attributeName.equals(attribute.getName())) {
                    log.info("找到已存在的属性: name={}, type={}", attributeName, attribute.getType());
                    return attribute;
                }
            }
        } catch (Exception e) {
            log.debug("未找到属性: {}", attributeName);
        }
        return null;
    }
    
    /**
     * 查找相似的属性
     * 基于依赖关系匹配
     */
    public Attribute findSimilarAttribute(String dependent, String type) {
        try {
            List<Attribute> attributes = attributeService.selectAll();
            for (Attribute attribute : attributes) {
                if (type.equals(attribute.getType()) && isSimilarDependent(dependent, attribute.getDependent())) {
                    log.info("找到相似的属性: name={}, dependent={}", attribute.getName(), attribute.getDependent());
                    return attribute;
                }
            }
        } catch (Exception e) {
            log.error("查找相似属性时出错", e);
        }
        return null;
    }
    
    /**
     * 根据规则名称查找已存在的策略原子
     */
    public AtomRule findAtomRuleByName(String ruleName) {
        try {
            List<AtomRule> atomRules = atomRuleService.selectAll();
            for (AtomRule atomRule : atomRules) {
                if (ruleName.equals(atomRule.getName())) {
                    log.info("找到已存在的策略原子: name={}, id={}", ruleName, atomRule.getId());
                    return atomRule;
                }
            }
        } catch (Exception e) {
            log.debug("未找到策略原子: {}", ruleName);
        }
        return null;
    }
    
    /**
     * 查找相似的策略原子
     * 基于条件和依赖匹配
     */
    public AtomRule findSimilarAtomRule(String condition, String dependent) {
        try {
            List<AtomRule> atomRules = atomRuleService.selectAll();
            for (AtomRule atomRule : atomRules) {
                if (isSimilarCondition(condition, atomRule.getCondition()) && 
                    isSimilarDependent(dependent, atomRule.getDependent())) {
                    log.info("找到相似的策略原子: name={}, id={}", atomRule.getName(), atomRule.getId());
                    return atomRule;
                }
            }
        } catch (Exception e) {
            log.error("查找相似策略原子时出错", e);
        }
        return null;
    }
    
    /**
     * 根据策略组名称查找已存在的策略组
     */
    public GroupRule findGroupRuleByName(String groupName) {
        try {
            List<GroupRule> groupRules = groupRuleService.selectAll();
            for (GroupRule groupRule : groupRules) {
                if (groupName.equals(groupRule.getName())) {
                    log.info("找到已存在的策略组: name={}, id={}", groupName, groupRule.getId());
                    return groupRule;
                }
            }
        } catch (Exception e) {
            log.debug("未找到策略组: {}", groupName);
        }
        return null;
    }
    
    /**
     * 检查因子是否相似
     */
    private boolean isSimilarFactor(Factor factor, String dimension, Integer timeWindow) {
        if (factor.getTimeSpan() == null || timeWindow == null) {
            return false;
        }
        
        // 时间窗口匹配（允许一定误差）
        long factorTimeSpan = factor.getTimeSpan();
        long targetTimeSpan = timeWindow.longValue();
        if (Math.abs(factorTimeSpan - targetTimeSpan) > 60) { // 允许1小时误差
            return false;
        }
        
        // 维度匹配
        String groupKey = factor.getGroupKey();
        if (StringUtils.hasText(groupKey) && groupKey.contains(dimension)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查依赖关系是否相似
     */
    private boolean isSimilarDependent(String dependent1, String dependent2) {
        if (!StringUtils.hasText(dependent1) || !StringUtils.hasText(dependent2)) {
            return false;
        }
        
        // 简单的字符串相似度检查
        String[] deps1 = dependent1.split("[,:]");
        String[] deps2 = dependent2.split("[,:]");
        
        int matchCount = 0;
        for (String dep1 : deps1) {
            for (String dep2 : deps2) {
                if (dep1.trim().equals(dep2.trim())) {
                    matchCount++;
                    break;
                }
            }
        }
        
        // 如果匹配度超过50%，认为相似
        return matchCount > Math.min(deps1.length, deps2.length) * 0.5;
    }
    
    /**
     * 检查条件是否相似
     */
    private boolean isSimilarCondition(String condition1, String condition2) {
        if (!StringUtils.hasText(condition1) || !StringUtils.hasText(condition2)) {
            return false;
        }
        
        // 移除空格和换行符进行比较
        String normalized1 = condition1.replaceAll("\\s+", " ").trim();
        String normalized2 = condition2.replaceAll("\\s+", " ").trim();
        
        // 计算相似度（简单的包含关系检查）
        if (normalized1.length() > normalized2.length()) {
            return normalized1.contains(normalized2.substring(0, Math.min(normalized2.length(), 50)));
        } else {
            return normalized2.contains(normalized1.substring(0, Math.min(normalized1.length(), 50)));
        }
    }
    
    /**
     * 计算字符串相似度
     */
    private double calculateSimilarity(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0.0;
        }
        
        if (str1.equals(str2)) {
            return 1.0;
        }
        
        int maxLength = Math.max(str1.length(), str2.length());
        if (maxLength == 0) {
            return 1.0;
        }
        
        int editDistance = calculateEditDistance(str1, str2);
        return 1.0 - (double) editDistance / maxLength;
    }
    
    /**
     * 计算编辑距离
     */
    private int calculateEditDistance(String str1, String str2) {
        int[][] dp = new int[str1.length() + 1][str2.length() + 1];
        
        for (int i = 0; i <= str1.length(); i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= str2.length(); j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= str1.length(); i++) {
            for (int j = 1; j <= str2.length(); j++) {
                if (str1.charAt(i - 1) == str2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]) + 1;
                }
            }
        }
        
        return dp[str1.length()][str2.length()];
    }
}
