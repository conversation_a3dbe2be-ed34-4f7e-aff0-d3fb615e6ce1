package com.yupaopao.risk.parser.model;

import lombok.Data;
import java.util.List;

/**
 * 解析后的策略数据模型
 */
@Data
public class ParsedStrategy {
    
    /**
     * 事件信息
     */
    private EventInfo eventInfo;
    
    /**
     * 检测模块列表
     */
    private List<DetectionModule> detectionModules;
    
    /**
     * 累计条件列表
     */
    private List<AccumulationCondition> accumulationConditions;
    
    /**
     * 属性条件列表
     */
    private List<AttributeCondition> attributeConditions;
    
    /**
     * 时间窗口列表（分钟）
     */
    private List<Integer> timeWindows;
    
    /**
     * 策略级别（默认为3级）
     */
    private Integer level = 3;
    
    /**
     * 策略描述
     */
    private String description;
}

/**
 * 事件信息
 */
@Data
class EventInfo {
    private String name;
    private String code;
    private String businessCode;
    private String accessBy;
    private String comment;
}

/**
 * 检测模块
 */
@Data
class DetectionModule {
    private DetectionType type;
    private String name;
    private String description;
    private List<String> rules;
}

/**
 * 检测类型枚举
 */
enum DetectionType {
    DEVICE("设备检测"),
    IP("IP检测"),
    BLACKLIST("名单检测"),
    ACCOUNT("账号检测"),
    BUSINESS("业务检测"),
    BEHAVIOR("行为检测");
    
    private final String description;
    
    DetectionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

/**
 * 累计条件
 */
@Data
class AccumulationCondition {
    private Integer ruleNumber;
    private String dimension; // DeviceId, ClientIp, UserId
    private String operator; // >, >=, <, <=, ==
    private Integer threshold;
    private String action; // REJECT, REVIEW, PASS
    private Integer timeWindow; // 时间窗口（分钟）
    private String description;
    private String innerReason;
    private String outerReason;
}

/**
 * 属性条件
 */
@Data
class AttributeCondition {
    private String attributeName; // vipLevel, nobilityLevel, planetUserLevel
    private String operator; // >, >=, <, <=, ==
    private Integer value;
    private String description;
    private ConditionType type = ConditionType.USER_LEVEL;
}

/**
 * 条件类型
 */
enum ConditionType {
    USER_LEVEL("用户等级"),
    DEVICE_INFO("设备信息"),
    IP_INFO("IP信息"),
    BUSINESS_INFO("业务信息");
    
    private final String description;
    
    ConditionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
