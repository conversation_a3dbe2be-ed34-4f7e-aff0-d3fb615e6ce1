package com.yupaopao.risk.parser.generator;

import com.yupaopao.risk.common.model.*;
import lombok.Data;
import java.util.List;

/**
 * 生成的风控配置
 */
@Data
public class GeneratedRiskConfig {
    
    /**
     * 事件配置
     */
    private Event event;
    
    /**
     * 因子列表
     */
    private List<Factor> factors;
    
    /**
     * 属性列表
     */
    private List<Attribute> attributes;
    
    /**
     * 策略原子列表
     */
    private List<AtomRule> atomRules;
    
    /**
     * 策略组
     */
    private GroupRule groupRule;
    
    /**
     * 规则关系列表
     */
    private List<RuleRelation> ruleRelations;
    
    /**
     * 事件参数列表
     */
    private List<EventParam> eventParams;
    
    /**
     * 生成统计信息
     */
    private GenerationStats stats;
    
    /**
     * 生成的SQL脚本
     */
    private List<String> sqlStatements;
    
    /**
     * 验证结果
     */
    private ValidationResult validationResult;
}

/**
 * 生成统计信息
 */
@Data
class GenerationStats {
    private int newFactorsCount;
    private int reusedFactorsCount;
    private int newAttributesCount;
    private int reusedAttributesCount;
    private int newAtomRulesCount;
    private int reusedAtomRulesCount;
    private boolean eventReused;
    private boolean groupRuleReused;
    private long generationTimeMs;
}

/**
 * 验证结果
 */
@Data
class ValidationResult {
    private boolean valid;
    private List<String> errors;
    private List<String> warnings;
    private List<String> suggestions;
}

/**
 * 事件参数
 */
@Data
class EventParam {
    private String code;
    private String memo;
    private String eventCode;
    private String author;
}

/**
 * 规则关系
 */
@Data
class RuleRelation {
    private Long ruleId;
    private Long groupId;
}
