package com.yupaopao.risk.parser;

import com.yupaopao.risk.parser.model.ParsedStrategy;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 策略文档解析器测试
 */
@SpringBootTest
public class StrategyDocumentParserTest {
    
    @Autowired
    private StrategyDocumentParser parser;
    
    @Test
    public void testParseStrategyDocument() {
        String document = """
            接入说明：
            风控接入说明
            活动策略：场景分级3级。
            设备检测：设备模型
            1、（活动各环节共累积阈值），活动全程，同设备成功参与该活动（请求返回PASS\\REVIEW）人数>3，REJECT。
            对内外话术：同设备参与该活动的账号较多，请更换新设备尝试。
            2、设备命中异常标签：疑似伪造设备（MVP/比心vip >= 25 或 爵位>= 33 或 星爵>= 36级降级；其余返回REJECT），注销频度异常关联设备，低安装包版本&的操作系统设备，低设备内存且新生成设备指纹、低设备内存且剩余内存异常，REVIEW。
            
            IP检测：
            3、同IP一天内参与该活动人数>8，REJECT。
            对内外话术：同IP参与该活动的账号较多，请更换网络环境尝试。
            
            名单检测：
            4、命中黑名单，REJECT。
            
            账号检测：
            5、用户等级检测：MVP/比心vip < 12 且 爵位 < 3，进行严格检测。
            
            一、接入事件：
            名称：会员中心-抽奖      code：member-center-lottery
            参数文档：https://risk.yupaopao.com/index.html#/home/<USER>
            结果处理：
            返回 PASS：正常
            返回 REVIEW：同PASS
            返回 REJECT：获得低价值礼物
            """;
        
        ParsedStrategy strategy = parser.parseDocument(document);
        
        // 验证事件信息
        assertNotNull(strategy.getEventInfo());
        assertEquals("会员中心-抽奖", strategy.getEventInfo().getName());
        assertEquals("member-center-lottery", strategy.getEventInfo().getCode());
        assertEquals("activity", strategy.getEventInfo().getBusinessCode());
        
        // 验证检测模块
        assertNotNull(strategy.getDetectionModules());
        assertTrue(strategy.getDetectionModules().size() >= 4);
        
        // 验证累计条件
        assertNotNull(strategy.getAccumulationConditions());
        assertTrue(strategy.getAccumulationConditions().size() >= 2);
        
        // 验证第一个累计条件（设备检测）
        var deviceCondition = strategy.getAccumulationConditions().get(0);
        assertEquals(1, deviceCondition.getRuleNumber());
        assertEquals("DeviceId", deviceCondition.getDimension());
        assertEquals(">", deviceCondition.getOperator());
        assertEquals(3, deviceCondition.getThreshold());
        assertEquals("REJECT", deviceCondition.getAction());
        
        // 验证属性条件
        assertNotNull(strategy.getAttributeConditions());
        assertTrue(strategy.getAttributeConditions().size() >= 2);
        
        // 验证用户等级条件
        var vipCondition = strategy.getAttributeConditions().stream()
                .filter(c -> "vipLevel".equals(c.getAttributeName()))
                .findFirst();
        assertTrue(vipCondition.isPresent());
        assertEquals(">=", vipCondition.get().getOperator());
        assertEquals(25, vipCondition.get().getValue());
        
        // 验证时间窗口
        assertNotNull(strategy.getTimeWindows());
        assertTrue(strategy.getTimeWindows().contains(1440)); // 1天 = 1440分钟
        
        System.out.println("解析结果：");
        System.out.println("事件：" + strategy.getEventInfo().getName());
        System.out.println("检测模块数量：" + strategy.getDetectionModules().size());
        System.out.println("累计条件数量：" + strategy.getAccumulationConditions().size());
        System.out.println("属性条件数量：" + strategy.getAttributeConditions().size());
        System.out.println("时间窗口：" + strategy.getTimeWindows());
    }
    
    @Test
    public void testParseComplexConditions() {
        String document = """
            设备检测：
            1、活动全程，同设备成功参与该活动人数>3，REJECT。
            2、1小时内同设备登录失败次数>=5，REVIEW。
            3、10天内同设备注册账号数>10，REJECT。
            
            用户等级检测：
            MVP/比心vip >= 25 或 爵位>= 33 或 星爵>= 36
            """;
        
        ParsedStrategy strategy = parser.parseDocument(document);
        
        // 验证多个累计条件
        assertEquals(3, strategy.getAccumulationConditions().size());
        
        // 验证不同时间窗口
        assertTrue(strategy.getTimeWindows().contains(60));    // 1小时
        assertTrue(strategy.getTimeWindows().contains(14400)); // 10天
        
        // 验证多个用户等级条件
        assertTrue(strategy.getAttributeConditions().size() >= 3);
    }
    
    @Test
    public void testParseEventInfo() {
        String document = """
            一、接入事件：
            名称：春节活动-抽奖      code：spring-festival-lottery
            参数文档：https://risk.yupaopao.com/index.html#/home/<USER>
            """;
        
        ParsedStrategy strategy = parser.parseDocument(document);
        
        assertNotNull(strategy.getEventInfo());
        assertEquals("春节活动-抽奖", strategy.getEventInfo().getName());
        assertEquals("spring-festival-lottery", strategy.getEventInfo().getCode());
    }
    
    @Test
    public void testParseTimeWindows() {
        String document = """
            1、1分钟内同设备请求次数>10，REJECT。
            2、1小时内同IP登录次数>20，REVIEW。
            3、1天内同用户参与次数>5，REJECT。
            4、7日内同设备异常行为>3，REJECT。
            """;
        
        ParsedStrategy strategy = parser.parseDocument(document);
        
        assertTrue(strategy.getTimeWindows().contains(1));     // 1分钟
        assertTrue(strategy.getTimeWindows().contains(60));    // 1小时
        assertTrue(strategy.getTimeWindows().contains(1440));  // 1天
        assertTrue(strategy.getTimeWindows().contains(10080)); // 7天
    }
    
    @Test
    public void testParseUserLevelConditions() {
        String document = """
            用户等级检测：
            MVP/比心vip >= 25
            爵位 >= 33
            星爵 >= 36
            """;
        
        ParsedStrategy strategy = parser.parseDocument(document);
        
        assertEquals(3, strategy.getAttributeConditions().size());
        
        // 验证MVP条件
        var mvpCondition = strategy.getAttributeConditions().stream()
                .filter(c -> "vipLevel".equals(c.getAttributeName()))
                .findFirst();
        assertTrue(mvpCondition.isPresent());
        assertEquals(">=", mvpCondition.get().getOperator());
        assertEquals(25, mvpCondition.get().getValue());
        
        // 验证爵位条件
        var nobilityCondition = strategy.getAttributeConditions().stream()
                .filter(c -> "nobilityLevel".equals(c.getAttributeName()))
                .findFirst();
        assertTrue(nobilityCondition.isPresent());
        assertEquals(">=", nobilityCondition.get().getOperator());
        assertEquals(33, nobilityCondition.get().getValue());
        
        // 验证星爵条件
        var planetCondition = strategy.getAttributeConditions().stream()
                .filter(c -> "planetUserLevel".equals(c.getAttributeName()))
                .findFirst();
        assertTrue(planetCondition.isPresent());
        assertEquals(">=", planetCondition.get().getOperator());
        assertEquals(36, planetCondition.get().getValue());
    }
}
